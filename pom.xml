<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <!-- <version>2.2.6.RELEASE</version> -->
    <version>2.5.15</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>
  <artifactId>sanzhi-safe-service</artifactId>
  <packaging>jar</packaging>
  <name>sanzhi-safe-service</name>
  <description>Demo project for Spring Boot</description>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <elasticsearch.version>7.17.4</elasticsearch.version>
    <skipTests>true</skipTests>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.smartPark</groupId>
      <artifactId>common-libs</artifactId>
      <version>18.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.kafka</groupId>
          <artifactId>kafka-clients</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.kafka</groupId>
      <artifactId>kafka-clients</artifactId>
      <version>3.4.1</version>
    </dependency>
    <dependency>
      <groupId>com.github.davidmoten</groupId>
      <artifactId>geo</artifactId>
      <version>0.8.0</version>
    </dependency>
    <!-- <dependency> -->
    <!--   <groupId>org.springframework.kafka</groupId> -->
    <!--   <artifactId>spring-kafka</artifactId> -->
    <!-- </dependency> -->

    <!-- <dependency> -->
    <!--   <groupId>org.yaml</groupId> -->
    <!--   <artifactId>snakeyaml</artifactId> -->
    <!-- </dependency> -->

    <!--     <dependency> -->
    <!--       <groupId>com.isoftstone</groupId> -->
    <!--       <artifactId>bases-seaweed</artifactId> -->
    <!--       <version>1.0.0-SNAPSHOT</version> -->
    <!--     </dependency> -->
    <!--    <dependency>-->
    <!--      <groupId>com.isoftstone</groupId>-->
    <!--      <artifactId>bases-aliyun</artifactId>-->
    <!--      <version>1.0.0-SNAPSHOT</version>-->
    <!--    </dependency>-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-quartz</artifactId>
      <version>2.3.0.RELEASE</version>
      <!--      <groupId>org.quartz-scheduler</groupId>-->
      <!--      <artifactId>quartz</artifactId>-->
      <!--      <version>2.3.0</version>-->
    </dependency>
    <!-- websocket 引入 -->
    <!--    <dependency>-->
    <!--      <groupId>org.springframework.boot</groupId>-->
    <!--      <artifactId>spring-boot-starter-websocket</artifactId>-->
    <!--    </dependency>-->
    <!-- springboot集成thymeleaf的起步依赖 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-thymeleaf</artifactId>
    </dependency>
    <!-- <dependency> -->
    <!--   <groupId>org.springframework.session</groupId> -->
    <!--   <artifactId>spring-session-data-redis</artifactId> -->
    <!-- </dependency> -->

    <!--    before succ-->
    <!-- <dependency> -->
    <!--   <groupId>com.fasterxml.jackson.dataformat</groupId> -->
    <!--   <artifactId>jackson-dataformat-xml</artifactId> -->
    <!-- </dependency> -->

    <!-- <dependency> -->
    <!--   <groupId>com.alibaba</groupId> -->
    <!--   <artifactId>fastjson</artifactId> -->
    <!--   <version>1.2.58</version> -->
    <!-- </dependency> -->

    <!-- <dependency> -->
    <!--   <groupId>org.springframework.boot</groupId> -->
    <!--   <artifactId>spring-boot-starter-web</artifactId> -->
    <!-- </dependency> -->

    <!-- <dependency> -->
    <!--   <groupId>org.springframework.boot</groupId> -->
    <!--   <artifactId>spring-boot-starter-mail</artifactId> -->
    <!-- </dependency> -->

    <!-- <dependency> -->
    <!--   <groupId>org.mybatis.spring.boot</groupId> -->
    <!--   <artifactId>mybatis-spring-boot-starter</artifactId> -->
    <!--   <version>2.1.4</version> -->
    <!-- </dependency> -->

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--    2020-4-7  20:38-->
    <!-- <dependency> -->
    <!--   <groupId>com.alibaba</groupId> -->
    <!--   <artifactId>druid-spring-boot-starter</artifactId> -->
    <!--   <version>1.1.21</version> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>com.baomidou</groupId> -->
    <!--   <artifactId>mybatis-plus-boot-starter</artifactId> -->
    <!--   <version>3.1.2</version> -->
    <!-- </dependency> -->
    <!-- mybatis plus 代码生成器依赖 -->
    <!-- <dependency> -->
    <!--   <groupId>com.baomidou</groupId> -->
    <!--   <artifactId>mybatis-plus-generator</artifactId> -->
    <!--   <version>3.1.2</version> -->
    <!--   <exclusions> -->
    <!--     <exclusion> -->
    <!--       <artifactId>mybatis-spring</artifactId> -->
    <!--       <groupId>org.mybatis</groupId> -->
    <!--     </exclusion> -->
    <!--     <exclusion> -->
    <!--       <artifactId>mybatis</artifactId> -->
    <!--       <groupId>org.mybatis</groupId> -->
    <!--     </exclusion> -->
    <!--   </exclusions> -->
    <!-- </dependency> -->

    <!-- <dependency> -->
    <!--   <groupId>com.baomidou</groupId> -->
    <!--   <artifactId>mybatis-plus-support</artifactId> -->
    <!--   <version>2.3.3</version> -->
    <!-- </dependency> -->
    <!-- 代码生成器模板 -->
    <!-- <dependency> -->
    <!--   <groupId>org.freemarker</groupId> -->
    <!--   <artifactId>freemarker</artifactId> -->
    <!-- </dependency> -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>

    <!--office 相关 poi-->
<!--    <dependency>-->
<!--      <groupId>org.apache.poi</groupId>-->
<!--      <artifactId>poi</artifactId>-->
<!--      <version>3.14</version>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>org.apache.poi</groupId>-->
<!--      <artifactId>poi-ooxml</artifactId>-->
<!--      <version>3.14</version>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>org.apache.poi</groupId>-->
<!--      <artifactId>poi-scratchpad</artifactId>-->
<!--      <version>3.14</version>-->
<!--    </dependency>-->

    <!-- <dependency> -->
    <!--   <groupId>org.apache.commons</groupId> -->
    <!--   <artifactId>commons-lang3</artifactId> -->
    <!-- </dependency> -->
<!--    <dependency>-->
<!--      <groupId>org.springframework.boot</groupId>-->
<!--      <artifactId>spring-boot-starter-security</artifactId>-->
<!--      <version>2.2.6.RELEASE</version>-->
<!--    </dependency>-->
    <!--    <dependency>-->
    <!--      <groupId>site.morn.boot</groupId>-->
    <!--      <artifactId>morn-core</artifactId>-->
    <!--      <version>1.2.1-SNAPSHOT</version>-->
    <!--      <scope>compile</scope>-->
    <!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>site.morn.boot</groupId>-->
<!--      <artifactId>morn-boot-security</artifactId>-->
<!--      <version>1.2.1-SNAPSHOT</version>-->
<!--      <scope>compile</scope>-->
<!--    </dependency>-->
    <!--阿里文件服务-->
    <dependency>
      <groupId>com.aliyun.oss</groupId>
      <artifactId>aliyun-sdk-oss</artifactId>
      <version>2.8.3</version>
    </dependency>
    <!--国际化支持-->
    <dependency><!--webjars版本定位器 用于省略版本号-->
      <groupId>org.webjars</groupId>
      <artifactId>webjars-locator-core</artifactId>
    </dependency>

    <dependency><!--jQuery前端依赖-->
      <groupId>org.webjars</groupId>
      <artifactId>jquery</artifactId>
      <version>3.3.1</version>
    </dependency>

    <dependency><!--jQuery国际化插件-->
      <groupId>org.webjars.bower</groupId>
      <artifactId>jquery-i18n-properties</artifactId>
      <version>1.2.7</version>
    </dependency>
    <!-- <dependency> -->
    <!--   <groupId>org.projectlombok</groupId> -->
    <!--   <artifactId>lombok</artifactId> -->
    <!-- </dependency> -->


    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.7</version>
    </dependency>
    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <version>3.1</version>
    </dependency>

    <dependency>
      <groupId>org.mvel</groupId>
      <artifactId>mvel2</artifactId>
      <version>2.4.7.Final</version>
    </dependency>

    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger2</artifactId>
      <version>2.9.2</version>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- swagger-ui -->
    <dependency>
      <groupId>io.springfox</groupId>
      <artifactId>springfox-swagger-ui</artifactId>
      <version>2.9.2</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.14.2</version>
      <scope>compile</scope>
    </dependency>


    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>3.13.6</version>
      <exclusions>
        <exclusion>
          <artifactId>HdrHistogram</artifactId>
          <groupId>org.hdrhistogram</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>retrofit</artifactId>
      <version>2.9.0</version>
      <exclusions>
        <exclusion>
          <artifactId>okhttp</artifactId>
          <groupId>com.squareup.okhttp3</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>converter-scalars</artifactId>
      <version>2.9.0</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.retrofit2</groupId>
      <artifactId>converter-gson</artifactId>
      <version>2.9.0</version>
    </dependency>

    <dependency>
      <groupId>eu.bitwalker</groupId>
      <artifactId>UserAgentUtils</artifactId>
      <version>1.21</version>
    </dependency>
    <!-- mybatis mapper支持 -->

    <!-- 自动刷sql脚本工具包 2021-08-14 -->
    <!--    <dependency>-->
    <!--      <groupId>org.flywaydb</groupId>-->
    <!--      <artifactId>flyway-core</artifactId>-->
    <!--      <version>7.12.1</version>-->
    <!--    </dependency>-->
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.5.15</version>
        <configuration>
          <!-- 指定该Main Class为全局的唯一入口 -->
          <mainClass>com.smartPark.SafeServiceApplication</mainClass>
          <layout>ZIP</layout>
        </configuration>
        <executions>
          <execution>
            <goals>
              <!--可以把依赖的包都打包到生成的Jar包中-->
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!--不压缩docx ttc文件-->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <configuration>
          <nonFilteredFileExtensions>
            <nonFilteredFileExtension>docx</nonFilteredFileExtension>
            <nonFilteredFileExtension>ttc</nonFilteredFileExtension>
          </nonFilteredFileExtensions>
        </configuration>
      </plugin>

    </plugins>

    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <excludes>
          <exclude>application*.properties</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <includes>      <!-- 根据打包时激活的profile来包括对应的配置文件 -->
          <include>application.properties</include>
          <include>application-${profileActive}.properties</include>
        </includes>
      </resource>
    </resources>
  </build>

  <profiles>
    <profile>
      <id>dev</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <profileActive>dev</profileActive>
      </properties>
    </profile>
    <profile>
      <id>local</id>
      <properties>
        <profileActive>local</profileActive>
      </properties>
    </profile>
    <profile>
      <id>test</id>
      <properties>
        <profileActive>test</profileActive>
      </properties>
    </profile>
    <profile>
      <id>mirror</id>
      <properties>
        <profileActive>mirror</profileActive>
      </properties>
    </profile>
    <profile>
      <id>prd</id>
      <properties>
        <profileActive>prd</profileActive>
      </properties>
    </profile>
    <profile>
      <id>poc</id>
      <properties>
        <profileActive>poc</profileActive>
      </properties>
    </profile>
  </profiles>

</project>
