<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.smartPark</groupId>
    <artifactId>common-libs</artifactId>
    <version>18.0.0-SNAPSHOT</version>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <!-- <version>2.2.6.RELEASE</version> -->
        <version>2.5.15</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <elasticsearch.version>7.17.4</elasticsearch.version>
        <spring.boot.version>2.2.6.RELEASE</spring.boot.version>
        <skipTests>true</skipTests>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>site.morn.boot</groupId>-->
<!--            <artifactId>morn-core</artifactId>-->
<!--            <version>1.2.1-SNAPSHOT</version>-->
<!--            <scope>compile</scope>-->
<!--        </dependency>-->
        <dependency>
            <groupId>site.morn.framework</groupId>
            <artifactId>morn-framework-context</artifactId>
            <version>1.2.1-SNAPSHOT</version>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>morn-boot-autoconfigure</artifactId>-->
<!--                    <groupId>site.morn.boot</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
        <!--ES操作相关-->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.17.4</version>
        </dependency>

        <!-- <dependency> -->
        <!--     <groupId>org.elasticsearch.client</groupId> -->
        <!--     <artifactId>elasticsearch-rest-client</artifactId> -->
        <!--     <version>6.5.2</version> -->
        <!-- </dependency> -->

        <!-- <dependency> -->
        <!--     <groupId>org.elasticsearch</groupId> -->
        <!--     <artifactId>elasticsearch</artifactId> -->
        <!--     <version>6.5.2</version> -->
        <!--     <exclusions> -->
        <!--         <exclusion> -->
        <!--             <artifactId>jackson-core</artifactId> -->
        <!--             <groupId>com.fasterxml.jackson.core</groupId> -->
        <!--         </exclusion> -->
        <!--         <exclusion> -->
        <!--             <artifactId>snakeyaml</artifactId> -->
        <!--             <groupId>org.yaml</groupId> -->
        <!--         </exclusion> -->
        <!--     </exclusions> -->
        <!-- </dependency> -->
        <!-- 阿里云短信服务所需 -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.0.6</version>
            <exclusions>
                <exclusion>
                    <artifactId>activation</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <!--    2020-4-7  20:38-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.21</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.13</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.1.2</version>
        </dependency>
        <!-- mybatis plus 代码生成器依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.1.2</version>
        </dependency>

        <!-- <dependency> -->
        <!--     <groupId>com.baomidou</groupId> -->
        <!--     <artifactId>mybatis-plus-support</artifactId> -->
        <!--     <version>2.3.3</version> -->
        <!-- </dependency> -->

        <!--office 相关 poi-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>4.1.2</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.commons</groupId>-->
<!--            <artifactId>commons-lang3</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>site.morn.boot</groupId>
            <artifactId>morn-boot-security</artifactId>
            <version>1.2.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.8.7</version>
        </dependency>

        <!--阿里文件服务-->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>2.8.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.4.0</version>
            <!--      <scope>test</scope>-->
        </dependency>
        <!-- mybatis mapper支持 -->
<!--        <dependency>-->
<!--            <groupId>tk.mybatis</groupId>-->
<!--            <artifactId>mapper</artifactId>-->
<!--            <version>4.1.5</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.22</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- websocket 引入 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
            <!-- <exclusions> -->
            <!--     <exclusion> -->
            <!--         <artifactId>log4j-api</artifactId> -->
            <!--         <groupId>org.apache.logging.log4j</groupId> -->
            <!--     </exclusion> -->
            <!--     <exclusion> -->
            <!--         <artifactId>slf4j-api</artifactId> -->
            <!--         <groupId>org.slf4j</groupId> -->
            <!--     </exclusion> -->
            <!-- </exclusions> -->
        </dependency>
        <!-- 代码生成器模板 -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.30</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>30.0-android</version>
            <scope>compile</scope>
        </dependency>

        <!--文件上传-->
        <dependency>
            <groupId>com.easylinkin</groupId>
            <artifactId>oss-lib</artifactId>
            <version>0.2.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.4</version>
        </dependency>
        <!-- kafka suport -->
        <dependency>
          <groupId>org.springframework.kafka</groupId>
          <artifactId>spring-kafka</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>3.4.1</version>
            <!-- <exclusions> -->
            <!--   <exclusion> -->
            <!--     <groupId>org.xerial.snappy</groupId> -->
            <!--     <artifactId>snappy-java</artifactId> -->
            <!--   </exclusion> -->
            <!-- </exclusions> -->
        </dependency>
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
            <version>1.1.8.4</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- <dependency> -->
        <!--     <groupId>com.fasterxml.jackson.core</groupId> -->
        <!--     <artifactId>jackson-core</artifactId> -->
        <!--     <version>2.10.0</version> -->
        <!-- </dependency> -->
        <!-- <dependency> -->
        <!--     <groupId>com.fasterxml.jackson.core</groupId> -->
        <!--     <artifactId>jackson-databind</artifactId> -->
        <!--     <version>2.10.0</version> -->
        <!-- </dependency> -->

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>

        <!-- snack3支持 -->
        <dependency>
            <groupId>org.noear</groupId>
            <artifactId>snack3</artifactId>
            <version>3.2.66</version>
        </dependency>

        <!-- easyTrans支持 -->
        <dependency>
            <groupId>com.fhs-opensource</groupId>
            <artifactId>easy-trans-spring-boot-starter</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.fhs-opensource</groupId>
            <artifactId>easy-trans-mybatis-plus-extend</artifactId>
            <version>2.2.1</version>
        </dependency>



       <!-- <dependency> -->
       <!--     <groupId>io.minio</groupId> -->
       <!--     <artifactId>minio</artifactId> -->
       <!--     <version>7.0.2</version> -->
       <!--     <exclusions> -->
       <!--         <exclusion> -->
       <!--             <artifactId>guava</artifactId> -->
       <!--             <groupId>com.google.guava</groupId> -->
       <!--         </exclusion> -->
       <!--     </exclusions> -->
       <!-- </dependency> -->
        <!--    asyncexcel support    -->
        <dependency>
            <groupId>com.asyncexcel</groupId>
            <artifactId>async-excel-springboot-starter</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
            <version>2.4.7.Final</version>
        </dependency>

        <!-- Bean Searcher支持 -->
        <dependency>
            <groupId>cn.zhxu</groupId>
            <artifactId>bean-searcher-boot-starter</artifactId>
            <version>4.2.0</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jcommon</artifactId>
            <version>1.0.24</version>
        </dependency>
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>1.5.3</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>release</id>
            <name>emp-maven-release</name>
            <url>http://repo.easylinkin.net/repository/emp-maven-release/</url>
        </repository>
        <snapshotRepository>
            <id>snapshot</id>
            <name>emp-maven-snapshot</name>
            <url>http://repo.easylinkin.net/repository/emp-maven-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <finalName>common-libs</finalName>
        <plugins>
            <!-- compiler插件, 设定JDK版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <showWarnings>true</showWarnings>
                    <compilerArguments>
                        <verbose />
                        <bootclasspath>${java.home}/lib/rt.jar${path.separator}${java.home}/lib/jce.jar</bootclasspath>
                    </compilerArguments>
                </configuration>
            </plugin>
        </plugins>


    </build>

</project>
