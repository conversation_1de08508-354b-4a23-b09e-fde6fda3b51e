# 用户实体说明

## 示例

```json
{
  "id": 1,
  "username": "admin",
  "nickname": "系统管理员",
  "phone": "158xxxx1254",
  "roles": [
    {
      "id": 1,
      "code": "001",
      "name": "系统管理员",
      "description": "管理员账户"
    }
  ],
  "privileges": [
    {
      "id": 1,
      "parentId": null,
      "level": 1,
      "name": "系统管理",
      "type": 1,
      "code": "systemManagement",
      "url": "",
      "sort": 1,
      "description": "系统管理菜单"
    },
    {
      "id": 2,
      "parentId": 1,
      "level": 2,
      "name": "用户管理",
      "type": 1,
      "code": "systemManagement:userManagement",
      "url": "/systemManagement/userManagement",
      "sort": 2,
      "description": "用户管理菜单"
    },
    {
      "id": 3,
      "parentId": 2,
      "level": 3,
      "name": "新增",
      "type": 2,
      "code": "systemManagement:userManagement:add",
      "url": "",
      "sort": 2.01,
      "description": "用户新增按钮"
    }
  ]
}
```



### 属性说明

|名称|类型|说明|
|---|---|---|
|id|long|用户id|
|username|string|账户名称|
|nickname|string|显示名称|
|phone|string|联系方式|
|roles|[[RoleDTO](#schemaroledto)]|用户角色|
|privileges|[[PrivilegeDTO](#schemaprivilegedto)]|用户菜单权限|

<h2 id="tocS_PrivilegeDTO">PrivilegeDTO</h2>

用户菜单权限实体

```json
{
    "id": 2,
    "parentId": 1,
    "level": 2,
    "name": "用户管理",
    "type": 1,
    "code": "systemManagement:userManagement",
    "url": "/systemManagement/userManagement",
    "sort": 2,
    "description": "用户管理菜单"
}

```

### 属性说明

|名称|类型|说明|
|---|---|---|
|id|long|主键|
|parentId|long|父节点id (顶级节点parentId为null)|
|level|integer|层级(1,2,3级，依次类推)|
|name|string|菜单/按钮名称|
|type|integer|类型， 1:菜单 2:按钮|
|code|string|菜单/按钮编码|
|url|string|菜单跳转路径(type=1时才有值,  type=2 (即按钮) 无值)|
|sort|number|排序序号|
|description|string|描述|

<h2 id="tocS_RoleDTO">RoleDTO</h2>

用户所属角色

```json
{
    "id": 1,
    "code": "001",
    "name": "系统管理员",
    "description": "管理员角色"
}

```

### 属性

|名称|类型|说明|
|---|---|---|
|id|long|角色id|
|code|string|角色code|
|name|string|角色名称|
|description|string|角色描述|

