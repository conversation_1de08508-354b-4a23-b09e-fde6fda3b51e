package com.smartPark.common.elasticsearch.constant;

public class EsQueryString {
    public static final String projectId = "@projectId";
    public static final String getHasAttrs0 = "@getHasAttrs0";
    public static final String shouldTemplate = "@shouldTemplate";
    public static final String getQueryTimeStart = "@getQueryTimeStart";
    public static final String getQueryTimeEnd = "@getQueryTimeEnd";
    /**
     * 黄石四棵校园根据设备编号查询设备能耗使用分组
     */
//    public static final String queryPeriodTimeFirstItemGroupByDeviceCode = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"must\":[{\"term\":{\"projectId\":\"@projectId\"}}]}},{\"bool\":{\"should\":[{\"exists\":{\"field\":\"data.@getHasAttrs0\",\"boost\":1.0}}]}},{\"bool\":{\"should\":\"@shouldTemplate\"}},{\"range\":{\"createTime\":{\"from\":\"@getQueryTimeStart\",\"to\":\"@getQueryTimeEnd\",\"include_lower\":true,\"include_upper\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\",\"boost\":1.0}}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1,\"size\":400},\"aggs\":{\"top1\":{\"top_hits\":{\"size\":1,\"_source\":{\"includes\":[\"data.@getHasAttrs0\"]},\"sort\":[{\"createTime\":{\"order\":\"#orderVal\"}}]}}}}}}";
    public static final String queryPeriodTimeFirstItemGroupByDeviceCode = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"should\":\"@shouldTemplate\"}},{\"range\":{\"calculateTime\":{\"from\":\"@getQueryTimeStart\",\"to\":\"@getQueryTimeEnd\",\"include_lower\":true,\"include_upper\":true,\"boost\":1.0}}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1,\"size\":400},\"aggs\":{\"top1\":{\"sum\":{\"field\":\"calculateValue\"}}}}}}";
    /**
     * 四棵校园大屏查询水表读数排名前5
     */
    public static final String queryPeriodTimeFirstItemGroupByDeviceCodeTop = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"must\":[{\"term\":{\"projectId\":\"@projectId\"}}]}},{\"bool\":{\"should\":[{\"exists\":{\"field\":\"data.@getHasAttrs0\",\"boost\":1.0}}]}},{\"bool\":{\"should\":\"@shouldTemplate\"}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1},\"aggs\":{\"top1\":{\"top_hits\":{\"size\":1,\"_source\":{\"includes\":[\"data.@getHasAttrs0\"]},\"sort\":[{\"createTime\":{\"order\":\"#orderVal\"}}]}}}}}}";
    /**
     * 查询时间范围内的，先根据设备编号分组（第一层聚合），内部再根据时间格式yyyy-MM-dd分组（第二层聚合） ,第三层聚合求最值
     */
    public static final String queryPeriodTimeMaxGroupByDeviceByTime = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"must\":[{\"term\":{\"projectId\":\"@projectId\"}}]}},{\"bool\":{\"should\":[{\"exists\":{\"field\":\"data.@getHasAttrs0\",\"boost\":1.0}}]}},{\"bool\":{\"should\":\"@shouldTemplate\"}},{\"range\":{\"createTime\":{\"from\":\"@getQueryTimeStart\",\"to\":\"@getQueryTimeEnd\",\"include_lower\":true,\"include_upper\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\",\"boost\":1.0}}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1,\"size\":400},\"aggs\":{\"aggCollectTimeGroup\":{\"terms\":{\"script\":{\"lang\":\"painless\",\"inline\":\"def date = doc['collectTime'].value;def mont =date.getMonthOfYear();def date1=date.getDayOfMonth();return date.getYear()+'-'+(mont<10?'0'+mont:mont)+'-'+(date1<10?'0'+date1:date1)\"},\"size\":300,\"order\":[{\"_key\":\"asc\"}]},\"aggs\":{\"top1\":{\"max\":{\"field\":\"data.@getHasAttrs0\"}}}}}}}}";
    public static String queryPeriodTimeMaxGroupByDeviceByTime2 = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"must\":[{\"term\":{\"projectId\":\"@projectId\"}}]}},{\"bool\":{\"should\":[{\"exists\":{\"field\":\"data.@getHasAttrs0\",\"boost\":1.0}}]}},{\"bool\":{\"should\":\"@shouldTemplate\"}},{\"range\":{\"createTime\":{\"from\":\"@getQueryTimeStart\",\"to\":\"@getQueryTimeEnd\",\"include_lower\":true,\"include_upper\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\",\"boost\":1.0}}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1,\"size\":400},\"aggs\":{\"aggCollectTimeGroup\":{\"terms\":{\"script\":{\"lang\":\"painless\",\"inline\":\"def date = doc['collectTime'].value;def mont =date.getMonthOfYear();return date.getYear()+'-'+(mont<10?'0'+mont:mont)\"},\"size\":300,\"order\":[{\"_key\":\"asc\"}]},\"aggs\":{\"top1\":{\"max\":{\"field\":\"data.@getHasAttrs0\"}}}}}}}}";
    public static String queryPeriodTimeMaxGroupByDeviceByTime3 = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"must\":[{\"term\":{\"projectId\":\"@projectId\"}}]}},{\"bool\":{\"should\":[{\"exists\":{\"field\":\"data.@getHasAttrs0\",\"boost\":1.0}}]}},{\"bool\":{\"should\":\"@shouldTemplate\"}},{\"range\":{\"createTime\":{\"from\":\"@getQueryTimeStart\",\"to\":\"@getQueryTimeEnd\",\"include_lower\":true,\"include_upper\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\",\"boost\":1.0}}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1,\"size\":400},\"aggs\":{\"aggCollectTimeGroup\":{\"terms\":{\"script\":{\"lang\":\"painless\",\"inline\":\"def date = doc['collectTime'].value;return date.getYear()\"},\"size\":300,\"order\":[{\"_key\":\"asc\"}]},\"aggs\":{\"top1\":{\"max\":{\"field\":\"data.@getHasAttrs0\"}}}}}}}}";
    public static String collectPeriodTimeMaxGroupByDeviceByTime2 = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"should\":\"@shouldTemplate\"}},{\"range\":{\"createTime\":{\"from\":\"@getQueryTimeStart\",\"to\":\"@getQueryTimeEnd\",\"include_lower\":true,\"include_upper\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\",\"boost\":1.0}}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1,\"size\":400},\"aggs\":{\"aggCollectTimeGroup\":{\"terms\":{\"script\":{\"lang\":\"painless\",\"inline\":\"return doc['calculateTime'].value.substring(0,7);\"},\"size\":300,\"order\":[{\"_key\":\"asc\"}]},\"aggs\":{\"top1\":{\"sum\":{\"field\":\"calculateValue\"}}}}}}}}";
    public static String collectPeriodTimeMaxGroupByDeviceByTime3 = "{\"from\":0,\"size\":0,\"query\":{\"bool\":{\"filter\":[{\"bool\":{\"should\":\"@shouldTemplate\"}},{\"range\":{\"createTime\":{\"from\":\"@getQueryTimeStart\",\"to\":\"@getQueryTimeEnd\",\"include_lower\":true,\"include_upper\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\",\"boost\":1.0}}}],\"adjust_pure_negative\":true,\"boost\":1.0}},\"aggs\":{\"deviceCodeAgg\":{\"terms\":{\"field\":\"deviceCode\",\"min_doc_count\":1,\"size\":400},\"aggs\":{\"aggCollectTimeGroup\":{\"terms\":{\"script\":{\"lang\":\"painless\",\"inline\":\"return doc['calculateTime'].value.substring(0,4);\"},\"size\":300,\"order\":[{\"_key\":\"asc\"}]},\"aggs\":{\"top1\":{\"sum\":{\"field\":\"calculateValue\"}}}}}}}}";
}