package com.smartPark.common.elasticsearch.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.common.elasticsearch.util.EsUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;

/**
 * es工具控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("esUtil")
public class EsController extends ApiController {
    @Resource
    private EsUtil esUtil;


    @PostMapping("saveBatch")
    @ApiOperation("新增")
    public RestMessage insert(@RequestParam("index") String index, @RequestBody List<JSONObject> list) {
        esUtil.saveBatch(index, list);
        return RestBuilders.successBuilder().build();
    }


}

