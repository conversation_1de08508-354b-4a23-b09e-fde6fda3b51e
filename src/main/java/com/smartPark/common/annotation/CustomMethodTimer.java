package com.smartPark.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * class info :采用Spring 的JDK动态代理，可注解到控制器方法、Service重写方法上有效，内部方法无效，还需要优化，方向CGLIB代理
 *
 * <AUTHOR>
 * @date 2021/8/27 11:59
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CustomMethodTimer {
}
