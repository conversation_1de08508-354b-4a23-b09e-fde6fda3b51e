package com.smartPark.common.system.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_system_config")
public class SystemConfig extends Model<SystemConfig> {

    private static final long serialVersionUID = 1L;

    @TableId("sys_key_")
    private String sysKey;

    @TableField(value = "sys_value_", updateStrategy = FieldStrategy.IGNORED)
    private String sysValue;

    @TableField("type_")
    private Integer type;

    @TableField("description_")
    private String description;


    @Override
    protected Serializable pkVal() {
        return this.sysKey;
    }

}
