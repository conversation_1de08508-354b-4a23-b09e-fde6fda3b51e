package com.smartPark.common.alarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.common.alarm.entity.vo.AlarmVo;
import com.smartPark.common.alarm.entity.vo.DeviceVO;
import com.smartPark.common.alarm.mapper.DeviceAlarmMapper;
import com.smartPark.common.alarm.service.DeviceAlarmService;
import com.smartPark.common.base.model.RequestModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * @Description 园林土壤设备告警服务
 * <AUTHOR>
 * @Date 2023/3/23 17:38
 */
@Service
public class DeviceAlarmServiceImpl implements DeviceAlarmService {
    @Resource
    private DeviceAlarmMapper deviceAlarmMapper;



    /**
     * @Description: 根据区域查询告警情况
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @Override
    public Map<String, Object> findAlarmStatistics(DeviceVO gardenSoilDeviceVo) {
        Map<String, Object> map = new HashMap<>();
//        //查询实时告警数目
//        List<getDeviceObjInfoDevicePropertyStatusListInfoDeviceVo> gardenSoilDeviceVos = this.getgetDeviceObjInfoDevicePropertyStatusListInfoDeviceVos(gardenSoilDeviceVo);
//        long count = gardenSoilDeviceVos.stream().filter(m -> Integer.valueOf(1).equals(m.getAlarmState())).count();
//        map.put("alarm",count);
//        RequestModel<getDeviceObjInfoDevicePropertyStatusListInfoDeviceAlarmVo> requestModel = new RequestModel<>();
//        getDeviceObjInfoDevicePropertyStatusListInfoDeviceAlarmVo gardenSoilDeviceAlarmVo = new getDeviceObjInfoDevicePropertyStatusListInfoDeviceAlarmVo();
//        gardenSoilDeviceAlarmVo.setAreaPaths(gardenSoilDeviceVo.getAreaPaths());
//        requestModel.setCustomQueryParams(gardenSoilDeviceAlarmVo);
//        requestModel.setPage(new Page(1,5));
//        IPage<getDeviceObjInfoDevicePropertyStatusListInfoDeviceAlarmVo> alarmVoIPage = gardenSoilDeviceAlarmService.queryListByPage(requestModel);
//        //查询告警总数量
//        long total = gardenSoilDeviceVos.size();
//        map.put("alarmTotal",total-count);
//        //查询告警列表
//        map.put("alarmList",alarmVoIPage.getRecords());
        return map;
    }


    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @Override
    public IPage<AlarmVo> queryListByPage(RequestModel<AlarmVo> requestModel) {
        Page page = requestModel.getPage();
        AlarmVo alarmVo = requestModel.getCustomQueryParams();
        //todo 暂定园林土壤设备告警为1
//        gardenSoilDeviceAlarmVo.setModel("1");
        if (CollectionUtil.isNotEmpty(alarmVo.getModelIds())){
            //将modelIds转成,分割的字符串
            alarmVo.setModelIdStr(String.join(",", alarmVo.getModelIds()));
        }
        IPage<AlarmVo> gardenSoilDeviceList = deviceAlarmMapper.findDeviceObjInfoDevicePropertyStatusListInfoDeviceList(page, alarmVo);
        gardenSoilDeviceList.getRecords().forEach(m ->{
            //区域范围
            if (StringUtils.isNotBlank(m.getAreaPath())){
                m.setAreaPath(m.getAreaPath().replace("@","/"));
            }
        });
        return gardenSoilDeviceList;
    }

    /**
     * @Description: 根据设备编码查询园林土壤设备告警日志
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @Override
    public List<AlarmVo> findAlarmByDeviceCode(String deviceCode) {
        List<AlarmVo> records = getgetDeviceObjInfoDevicePropertyStatusListInfoDeviceAlarmVos((vo) -> vo.setDeviceCode(deviceCode));
        return records;
    }

    /**
     * @Description: 根据id查询单条设备告警详情
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @Override
    public AlarmVo findById(Long id) {
        List<AlarmVo> records = getgetDeviceObjInfoDevicePropertyStatusListInfoDeviceAlarmVos((vo) -> vo.setId(id));
        AlarmVo gardenSoilDeviceAlarmVo = records.get(0);
        return gardenSoilDeviceAlarmVo;
    }

    @Override
    public Long export(AlarmVo gardenSoilDeviceAlarmVo, HttpServletRequest request, HttpServletResponse response) {
        return null;
    }


    /**
     * 不分页查询告警集合
     * @param consumer 调用的地方自己设置值
     * @return
     */
    private List<AlarmVo> getgetDeviceObjInfoDevicePropertyStatusListInfoDeviceAlarmVos(Consumer<AlarmVo> consumer) {
        RequestModel<AlarmVo> requestModel = new RequestModel<>();
        AlarmVo gardenSoilDeviceAlarmVo = new AlarmVo();
        consumer.accept(gardenSoilDeviceAlarmVo);
        requestModel.setCustomQueryParams(gardenSoilDeviceAlarmVo);
        requestModel.setPage(new Page(0,-1));
        IPage<AlarmVo> alarmVoIPage = queryListByPage(requestModel);
        List<AlarmVo> records = alarmVoIPage.getRecords();
        return records;
    }
}
