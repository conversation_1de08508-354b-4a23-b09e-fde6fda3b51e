package com.smartPark.common.alarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.common.alarm.entity.AlarmNumber;
import com.smartPark.common.alarm.mapper.AlarmNumberMapper;
import com.smartPark.common.alarm.service.AlarmNumberService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 设备告警连续触发次数和沉默周期 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-23
 */
@Service
public class AlarmNumberServiceImpl extends ServiceImpl<AlarmNumberMapper, AlarmNumber> implements AlarmNumberService {

    @Override
    public AlarmNumber findOne(Long ruleId, String deviceCode) {
        LambdaQueryWrapper<AlarmNumber> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AlarmNumber::getRuleEngineId,ruleId).eq(AlarmNumber::getDeviceCode,deviceCode);
        List<AlarmNumber> alarmNumbers = baseMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(alarmNumbers)){
            return alarmNumbers.get(0);
        }
        return null;
    }

    /**
     * 更新停用时间
     * @param ruleEngineId
     * @param hour
     */
    @Override
    public void upTimeByRuleEngine(Long ruleEngineId) {
        baseMapper.upTimeByRuleEngine(ruleEngineId);
    }
}
