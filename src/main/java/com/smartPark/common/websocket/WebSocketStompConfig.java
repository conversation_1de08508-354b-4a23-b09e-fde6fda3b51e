package com.smartPark.common.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketTransportRegistration;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * websocket stomp协议配置类
 *
 **/
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketStompConfig implements WebSocketMessageBrokerConfigurer {

    /**
     * 添加这个Endpoint，这样在网页中就可以通过websocket连接上服务,
     * 也就是我们配置websocket的服务地址,并且可以指定是否使用socketjs
     *
     * @param registry
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        /*
         * 添加多个端点
         * 它的实现类是WebMvcStompEndpointRegistry ，
         * addEndpoint是添加到WebMvcStompWebSocketEndpointRegistration的集合中，
         * 所以可以添加多个端点
         */
        registry.addEndpoint("/stomp/websocket").setAllowedOriginPatterns("*").withSockJS();;
    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // 自定义调度器，用于控制心跳线程
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        // 线程池线程数，心跳连接开线程
        taskScheduler.setPoolSize(1);
        // 线程名前缀
        taskScheduler.setThreadNamePrefix("websocket-heartbeat-thread-");
        // 初始化
        taskScheduler.initialize();
        // 设置广播节点
        registry.enableSimpleBroker(WebSocketConstant.MAINTOPIC,WebSocketConstant.MAINUSER).setHeartbeatValue(new long[]{10000, 10000})
                .setTaskScheduler(taskScheduler);
        // 客户端向服务端发送消息需有/app 前缀
        registry.setApplicationDestinationPrefixes("/app");
        // 指定用户发送（一对一）的前缀 /user/
        registry.setUserDestinationPrefix(WebSocketConstant.MAINUSER);
    }

    /**
     * 配置发送与接收的消息参数，可以指定消息字节大小，缓存大小，发送超时时间
     *
     * @param registration
     */
    @Override
    public void configureWebSocketTransport(WebSocketTransportRegistration registration) {
        /*
         * 1. setMessageSizeLimit 设置消息缓存的字节数大小 字节
         * 2. setSendBufferSizeLimit 设置websocket会话时，缓存的大小 字节
         * 3. setSendTimeLimit 设置消息发送会话超时时间，毫秒
         */
        registration.setMessageSizeLimit(10240)
                .setSendBufferSizeLimit(10240)
                .setSendTimeLimit(10000);
    }

//    /**
//     * 配置客户端入站通道拦截器
//     * 设置输入消息通道的线程数，默认线程为1，可以自己自定义线程数，最大线程数，线程存活时间
//     *
//     * @param registration
//     */
//    @Override
//    public void configureClientInboundChannel(ChannelRegistration registration) {
//
//        /*
//         * 配置消息线程池
//         * 1. corePoolSize 配置核心线程池，当线程数小于此配置时，不管线程中有无空闲的线程，都会产生新线程处理任务
//         * 2. maxPoolSize 配置线程池最大数，当线程池数等于此配置时，不会产生新线程
//         * 3. keepAliveSeconds 线程池维护线程所允许的空闲时间，单位秒
//         */
//        registration.taskExecutor().corePoolSize(10)
//                .maxPoolSize(20)
//                .keepAliveSeconds(60);
//
////        registration.interceptors(new WebSocketUserInterceptor());
//    }
//
//    /**
//     * 设置输出消息通道的线程数，默认线程为1，可以自己自定义线程数，最大线程数，线程存活时间
//     *
//     * @param registration
//     */
//    @Override
//    public void configureClientOutboundChannel(ChannelRegistration registration) {
//        registration.taskExecutor().corePoolSize(10)
//                .maxPoolSize(20)
//                .keepAliveSeconds(60);
////        registration.interceptors(new WebSocketUserInterceptor());
//    }

    /**
     * sit morn 配置里有个bean用到下面这个bean 防止报错注册一个空的bean
     * @return
     */
    @Bean("applicationTaskExecutor")
    public AsyncListenableTaskExecutor asyncListenableTaskExecutor(){
        return new AsyncListenableTaskExecutor() {
            @Override
            public void execute(Runnable runnable) {

            }

            @Override
            public void execute(Runnable runnable, long l) {

            }

            @Override
            public Future<?> submit(Runnable runnable) {
                return null;
            }

            @Override
            public <T> Future<T> submit(Callable<T> callable) {
                return null;
            }

            @Override
            public ListenableFuture<?> submitListenable(Runnable runnable) {
                return null;
            }

            @Override
            public <T> ListenableFuture<T> submitListenable(Callable<T> callable) {
                return null;
            }
        };
    }

}