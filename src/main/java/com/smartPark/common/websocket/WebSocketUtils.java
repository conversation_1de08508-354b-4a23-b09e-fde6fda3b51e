package com.smartPark.common.websocket;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

/**
 * @Description webSocket消息推送工具类
 * <AUTHOR> yuan<PERSON>
 * @Date 2023/3/16 14:19
 */
@Component
public class WebSocketUtils  implements ApplicationContextAware {

    /**
     * 消息发送工具
     */
    private static SimpMessagingTemplate simpMessagingTemplate;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext){
        simpMessagingTemplate = applicationContext.getBean(SimpMessagingTemplate.class);
    }

    /**
     * <AUTHOR> yuanfeng
     * @Description 广播(默认用/topic/${subDestination})
     * @Date 2023/3/16
     */
    public static void convertAndSend(String subDestination,Object msg){
        //判断subDestination包不包含”/“
//        if (StringUtils.isNotBlank(subDestination) && !subDestination.startsWith("/")){
//            subDestination = "/" + subDestination;
//        }
        simpMessagingTemplate.convertAndSend(WebSocketConstant.MAINTOPIC+subDestination,msg);
    }

    /**
     * <AUTHOR> yuanfeng
     * @Description 点对点
     * @Date 2023/3/16
     */
    public static void convertAndSendToUser(String userId,String subDestination,Object msg){
        //判断subDestination包不包含”/“
//        if (StringUtils.isNotBlank(subDestination) && !subDestination.startsWith("/")){
//            subDestination = "/" + subDestination;
//        }
        simpMessagingTemplate.convertAndSendToUser(userId,subDestination,msg);
    }
}
