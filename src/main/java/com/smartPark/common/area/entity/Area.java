package com.smartPark.common.area.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 区域信息表
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_area")
public class Area extends Model<Area> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 地区编码
     */
    @TableField("code_")
    private String code;

    /**
     * 地区层级
     */
    @TableField("level_")
    private Integer level;

    /**
     * 地区名称
     */
    @TableField("name_")
    private String name;

    /**
     * 父级编码
     */
    @TableField("parent_code_")
    private String parentCode;

    /**
     * 祖辈编码，以，隔开
     */
    @TableField("parent_codes_")
    private String parentCodes;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time_")
    private Date updateTime;

    /**
     * 是否删除，0否，1是
     */
    @TableField("deleted_")
    private Integer deleted;

    /**
     * 同步时间
     */
    @TableField("sync_time_")
    private Date syncTime;

    /**
     * 经纬度集合
     */
    @TableField("point_list_")
    private String pointListStr;

    /**
     * 排序
     */
    @TableField("sort_")
    private Integer sort;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
