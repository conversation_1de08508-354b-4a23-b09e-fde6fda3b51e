package com.smartPark.common.job.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.common.job.entity.JobExecutionResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * JobExecutionResult表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/03/17
 */
@Mapper
public interface JobExecutionResultMapper extends BaseMapper<JobExecutionResult> {


    /**
     * 查询分页
     *
     * @param page                   分页参数对象
     * @param baseJobExecutionResult 过滤参数对象
     * @return 查询分页结果
     */
    IPage<JobExecutionResult> selectPage(Page page, @Param("baseJobExecutionResult") JobExecutionResult baseJobExecutionResult);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    JobExecutionResult getOneById(@Param("id") Serializable id);
}

