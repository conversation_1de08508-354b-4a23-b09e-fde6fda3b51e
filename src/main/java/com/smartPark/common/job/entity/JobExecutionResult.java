package com.smartPark.common.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_job_execution_result")
public class JobExecutionResult extends Model<JobExecutionResult> {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务执行耗时
     */

    private Long useMillisecond;

    /**
     * 任务实体id
     */

    private Integer jobEntityId;

    /**
     * 任务分组
     */

    private String jobGroup;

    /**
     * 任务名称
     */

    private String jobName;

    /**
     * 执行结果 true-成功，false-失败
     */
    private Boolean success;

    /**
     * 任务执行结果描述
     */
    private String msg;

    /**
     * 任务执行失败信息
     */

    private String errorMsg;

    /**
     * 执行进度
     */
    private Double progress;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建时间
     */
    private Date modifyTime;

    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 修改人id
     */
    private Long modifyId;

    @TableField(exist = false)
    private String modifier;


    public com.smartPark.common.job.JobExecutionResult getComJob() {
        com.smartPark.common.job.JobExecutionResult jobExecutionResult = new com.smartPark.common.job.JobExecutionResult();
        jobExecutionResult.setJobEntityId(this.getJobEntityId()).setJobGroup(this.getJobGroup()).setJobName(this.getJobName()).setId(this.getId());
        return jobExecutionResult;
    }
}
