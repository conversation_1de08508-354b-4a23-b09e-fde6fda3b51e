package com.smartPark.common.device.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.smartPark.common.device.mapper.*;
import com.smartPark.common.device.service.DeviceUnitPropertyService;
import com.smartPark.common.device.service.ProjectSchemeService;
import com.smartPark.common.entity.device.*;
import com.smartPark.common.monitor.service.MonitorService;
import com.smartPark.common.monitor.vo.MonitorQueryVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 设备工具类
 * <AUTHOR> yuan<PERSON>
 * @Date 2023/5/15 14:37
 */
@Component
public class DeviceUtils implements ApplicationContextAware {

    private static DeviceMapper deviceMapper;

    private static DeviceExtendInfoMapper deviceExtendInfoMapper;

    private static MonitorPointMapper monitorPointMapper;

    private static ObjInfoMapper objInfoMapper;

    private static ProjectSchemeService projectSchemeService;

    private static DeviceUnitPropertyService deviceUnitPropertyService;

    private static MonitorService monitorService;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        deviceMapper = applicationContext.getBean(DeviceMapper.class);
        deviceExtendInfoMapper = applicationContext.getBean(DeviceExtendInfoMapper.class);
        objInfoMapper = applicationContext.getBean(ObjInfoMapper.class);
        monitorPointMapper = applicationContext.getBean(MonitorPointMapper.class);
        projectSchemeService = applicationContext.getBean(ProjectSchemeService.class);
        deviceUnitPropertyService = applicationContext.getBean(DeviceUnitPropertyService.class);
        monitorService = applicationContext.getBean(MonitorService.class);
    }
    /**
     * 设置设备的属性(object中带有deviceCode)
     * @param object
     */
    public static void setDeviceDetail(Object object){
        //获取设备code
        String deviceCode = (String)ReflectUtil.getFieldValue(object, "deviceCode");
        setDeviceDetail(object, deviceCode);
    }

    /**
     * 设置设备属性设置设备的属性(object中没有有deviceCode,需求传入)
     * @param object
     * @param deviceCode
     */
    public static void setDeviceDetail(Object object, String deviceCode) {
        if (StringUtils.isNotBlank(deviceCode)){
            //设置设备基础属性 device
            QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", deviceCode);
            List<Device> devices = deviceMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(devices)){
                setFieldValue(object,"device",devices.get(0));
            }
            //设置设备扩展属性 deviceExtendInfo
            QueryWrapper<DeviceExtendInfo> example = new QueryWrapper<>();
            example.eq("device_id", deviceCode);
            List<DeviceExtendInfo> extendInfoList = deviceExtendInfoMapper.selectList(example);
            if (CollectionUtil.isNotEmpty(extendInfoList)){
                DeviceExtendInfo extendInfo = extendInfoList.get(0);
                if (StringUtils.isNotBlank(extendInfo.getAreaPath())){
                    extendInfo.setAreaPath(extendInfo.getAreaPath().replace("@","/"));
                }
                setFieldValue(object,"extendInfo", extendInfo);
                //可能属性名叫法不一致
                setFieldValue(object,"deviceExtendInfo", extendInfo);
                //设置监测点位
                if (null != extendInfo && StringUtils.isNotBlank(extendInfo.getDwbsm())){
                    MonitorPoint monitorPoint = monitorPointMapper.selectById(extendInfo.getDwbsm());
                    setFieldValue(object,"monitorPoint", monitorPoint);
                    //设置设备部件属性 objInfo 实际上位监测点位和部件标识码
                    ObjInfo objInfo = objInfoMapper.findByMonitorPointBsm(extendInfo.getDwbsm());
                    setFieldValue(object,"objInfo", objInfo);
//                    //设置设备部件属性 objInfo
//                    if (null != monitorPoint && StringUtils.isNotBlank(monitorPoint.getObjId())){
//                        ObjInfo objInfo = objInfoMapper.selectById(monitorPoint.getObjId());
//                        setFieldValue(object,"objInfo", objInfo);
//                    }
                }
            }
            //设置物模型
            MonitorQueryVO monitorQueryVO = new MonitorQueryVO();
            monitorQueryVO.setDeviceCode(deviceCode);
            JSONArray physicModel = monitorService.getPhysicModelPropAndFilter(monitorQueryVO);
            setFieldValue(object,"physicModel",physicModel);

            //设置设备基础物模型相关属性
            if (CollectionUtil.isNotEmpty(devices)){
                Device device = devices.get(0);
                DeviceUnitProperty deviceUnitProperty = new DeviceUnitProperty();
                // 按照型号id和项目id查询版本
                ProjectScheme projectScheme = projectSchemeService.findByDeviceUnitIdOrderByTime(device.getDeviceUnitId());
                if(projectScheme != null){
                    deviceUnitProperty.setDeviceVersionId(projectScheme.getDeviceVersionId());
                }
                deviceUnitProperty.setDeviceUnitId(device.getDeviceUnitId());
                List<DevicePropertyStatus> devicePropertyStatusList = deviceUnitPropertyService.getShowDeviceUnitProperty(deviceUnitProperty, device.getCode());
                setFieldValue(object,"devicePropertyStatusList", devicePropertyStatusList);
            }
        }
    }

    /**
     * 判断并设置属性值
     */
    public static void setFieldValue(Object object, String fieldName,Object value){
        Class<?> aClass = object.getClass();
        //判断是否有该属性
        if (ReflectUtil.hasField(aClass,fieldName)){
            //设置属性值
            ReflectUtil.setFieldValue(object,fieldName,value);
        }
    }
}
