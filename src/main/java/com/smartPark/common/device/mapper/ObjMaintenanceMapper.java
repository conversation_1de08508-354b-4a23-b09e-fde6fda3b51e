package com.smartPark.common.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.common.entity.ObjMaintenance;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 监测点位表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
public interface ObjMaintenanceMapper extends BaseMapper<ObjMaintenance> {


    IPage<ObjMaintenance> findList(@Param("page")Page page, @Param("objMaintenance") ObjMaintenance objMaintenance);
}
