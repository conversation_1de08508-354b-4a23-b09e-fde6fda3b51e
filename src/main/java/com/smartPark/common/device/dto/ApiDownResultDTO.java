package com.smartPark.common.device.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;


/**
 * EMP数据下行接口返回值
 *
 * <AUTHOR>
 */
@Data
@ApiModel("EMP数据下行接口返回对象")
public class ApiDownResultDTO {
    /**
     * HTTP响应状态码，0表示成功
     */
    @ApiModelProperty("响应状态码，0表示成功")
    private String code;
    /**
     * 状态描述
     */
    @ApiModelProperty("状态描述")
    private String msg;
    /**
     * 本次下行的请求ID，如不需要回应返回空
     */
    @ApiModelProperty("本次下行的请求ID，如不需要回应返回空")
    @JSONField(name = "request_id")
    private String requestId;

    @ApiModelProperty("请求信息")
    private Map<String, Object> requestMsg;
    //		很迷惑 有时返回的是msg  有时是message
    @ApiModelProperty("响应结果")
    private Boolean result;

    //		很迷惑 有时返回的是msg  有时是message
    @ApiModelProperty("响应消息")
    private String message;

    /**
     * 调用下行，一般的一些业务数据放在此对象，比如查询乐橙云的返回视频片段信息列表  modify by tongjie 2021/09/17
     */
    @ApiModelProperty("调用下行，一般的一些业务数据放在此对象，比如查询乐橙云的返回视频片段信息列表")
    private Object data;

    public boolean ok(){
        return code != null && code.equals("0");
    }

    public String nbMsg(){
        // Nb有时返回的是msg，有时是message
        if(msg != null){
            return msg;
        }

        if(message != null){
            return message;
        }
        return null;
    }

}
