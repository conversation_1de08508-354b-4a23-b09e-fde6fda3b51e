package com.smartPark.common.device.dto;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.asyncexcel.core.ExportRow;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.smartPark.common.entity.device.enums.ObjInfoEnums;
import com.smartPark.common.entity.device.vo.ObjInfoVo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Id;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: <PERSON>
 */
@Data
public class ObjInfoExportExcel extends ExportRow {

    /**
     * 部件标识码
     */
    @ExcelProperty("部件标识码")
    private String objId;

    /**
     * 部件名称
     */
    @ExcelProperty("部件名称")
    private String objName;

    /**
     * 部件类型
     */
    @ExcelProperty("部件类型")
    private String secondObjCategoryName;

    /**
     * 主管部门名称
     */
    @ExcelProperty("主管部门")
    private String deptName;

    /**
     * 权属企业名称
     */
    @ExcelProperty("权属单位")
    private String ownerEnterpriseName;

    /**
     * 养护企业名称
     */
    @ExcelProperty("养护单位")
    private String opEnterpricseName;

    /**
     * 部件状态：0#完好、1#破损、2#丢失、3#废弃、4#移除
     */
    @ExcelProperty("部件状态")
    private String objState;


    /**
     * 联系人
     */
    @ExcelProperty("联系人")
    private String contactPerson;


    /**
     * 联系方式
     */
    @ExcelProperty("联系电话")
    private String contactPhone;

    /**
     * 初始日期
     */
    @ExcelProperty("初始时间")
    private Date initDate;


    /**
     * 变更日期
     */
    @ExcelProperty("变更时间")
    private Date modifyDate;

    /**
     * 经纬度
     */
    @ExcelProperty("经纬度")
    private String zb;

    /**
     * 区域路径
     */
    @ExcelProperty("区域位置")
    private String areaPath;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String note;


    public static List<ObjInfoExportExcel> toExcel(List<ObjInfoVo> list){
        String[] bjzt = {"完好","破损","丢失","废弃","移除"};
        return list.stream().map((e)->{
            ObjInfoExportExcel exportExcel = BeanUtil.toBean(e, ObjInfoExportExcel.class);
            //坐标处理
            if (null != e.getObjX() || null != e.getObjY()){
                exportExcel.setZb("("+(null == e.getObjX()?"":e.getObjX())+","+(null == e.getObjY()?"":e.getObjY())+")");
            }
            //区域位置处理
            if (StringUtils.isNotBlank(e.getAreaPath())){
                exportExcel.setAreaPath(e.getAreaPath().replaceAll("@","/"));
            }
            exportExcel.setObjState(null != e.getObjState()?bjzt[e.getObjState()]:"");
            return exportExcel;
        }).collect(Collectors.toList());
    }
}
