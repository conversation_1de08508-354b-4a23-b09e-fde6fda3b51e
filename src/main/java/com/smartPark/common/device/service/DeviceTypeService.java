package com.smartPark.common.device.service;

import com.smartPark.common.device.vo.DeviceTypeUnitVo;
import com.smartPark.common.entity.device.DeviceType;

import java.util.List;

/**
 * 设备类型服务类 <br>
 *
 * <AUTHOR>
 * @version [版本号, 2018年7月6日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public interface DeviceTypeService extends CommonService {

    /**
     * 查询所有的产品类型
     * @return
     */
    List<DeviceType> queryAll();

    /**
     * 查询所有产品类型下的产品型号
     * @return
     */
    List<DeviceTypeUnitVo> queryAllUnit(Long id);

    /**
     * 查询大数据下所有产品类型和型号
     * @return
     */
    List<DeviceTypeUnitVo> queryAllDsjUnit();

    DeviceType getById(Long id);


    void saveOrUpdateDeviceType(DeviceType deviceType);

    List<DeviceType> findByTypeNames(List<String> typeNames);

    List<DeviceType> getByModelId(String modelId);
}
