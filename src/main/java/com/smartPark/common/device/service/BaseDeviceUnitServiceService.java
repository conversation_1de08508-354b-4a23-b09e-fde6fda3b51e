package com.smartPark.common.device.service;

import com.smartPark.common.device.dto.FieldSortDto;
import com.smartPark.common.entity.device.BaseDeviceUnitService;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.common.entity.device.DeviceVersion;

import java.util.List;

public interface BaseDeviceUnitServiceService extends IService<BaseDeviceUnitService>{

    /**
     * 更新服务
     * @param deviceVersion
     */
    void updateService(DeviceVersion deviceVersion);

    /**
     * 获取排序
     * @param deviceUnitId
     * @return
     */
    List<FieldSortDto> getSort(Long deviceUnitId);

    /**
     * 获取排序分数
     * @param deviceUnitId
     * @param fieldName
     * @return
     */
    Integer getSort(Long deviceUnitId, String fieldName);

    /**
     * 获取字段显示状态
     * @param deviceUnitId
     * @return
     */
    List<FieldSortDto> getShow(Long deviceUnitId);

    Integer getShow(Long deviceUnitId,String fieldName);

    /**
     * 更新排序规则
     * @param deviceUnitId
     * @param dto
     */
    void updatePropertySort(Long deviceUnitId,List<FieldSortDto> dto);

    /**
     * 更新显示
     * @param deviceUnitId
     * @param dto
     */
    void updatePropertyShow(Long deviceUnitId, List<FieldSortDto> dto);

    /**
     * 更新属性的排序
     */
    void updatePropertySort(Long deviceUnitId, Long deviceVersionId,List<FieldSortDto> dto);

}
