package com.smartPark.common.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.common.device.dto.DeviceStatisticsDTO;
import com.smartPark.common.entity.device.DeviceStatistics;

import java.util.List;

/**
 * <p>
 * 设备在线统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface DeviceStatisticsService extends IService<DeviceStatistics> {
    /**
     * 统计当天在线设备
     */
    void countDevice();

    /**
     * @Description: 根据区域查询井盖在线统计
     * <AUTHOR> yuan<PERSON>
     * @date 2020/11/04 11:42
     */
    DeviceStatisticsDTO countOnline(DeviceStatisticsDTO deviceStatisticsDTO);

    /**
     * @Description: 根据区域查询井盖在线情况
     * <AUTHOR> yuan<PERSON>
     * @date 2020/11/04 11:42
     */
    List<DeviceStatisticsDTO> countByDay(DeviceStatisticsDTO deviceStatisticsDTO);
}
