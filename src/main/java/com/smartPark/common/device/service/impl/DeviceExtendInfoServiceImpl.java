package com.smartPark.common.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.device.dto.DeviceManageDto;
import com.smartPark.common.device.dto.DeviceManageImportExcel;
import com.smartPark.common.device.mapper.DeviceApplicationModelDeviceUnitConfigMapper;
import com.smartPark.common.device.mapper.DeviceExtendInfoMapper;
import com.smartPark.common.device.mapper.DeviceUnitMapper;
import com.smartPark.common.device.service.DeviceExtendInfoService;
import com.smartPark.common.device.util.ValidateUtil;
import com.smartPark.common.device.vo.DeviceManageVo;
import com.smartPark.common.entity.device.*;
import com.smartPark.common.entity.device.vo.SimpleDeviceUnitVo;
import java.util.Arrays;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class DeviceExtendInfoServiceImpl implements DeviceExtendInfoService {

    @Autowired
    private DeviceApplicationModelDeviceUnitConfigMapper damdeviceUnitMapper;

    @Autowired
    private DeviceExtendInfoMapper deviceExtendInfoMapper;

    @Autowired
    private DeviceUnitMapper deviceUnitMapper;

    @Override
    public IPage<DeviceExtendInfoVo> findNotSelectByModelId(Page page,DeviceCommonDto damdConfig) {

        QueryWrapper<DeviceApplicationModelDeviceUnitConfig> qw = new QueryWrapper<>();
        qw.eq(ObjectUtils.isNotEmpty(damdConfig.getApplicationId()),"application_id_", damdConfig.getApplicationId());
        qw.eq("model_id_",damdConfig.getModelId());

        List<DeviceApplicationModelDeviceUnitConfig> deviceUnitConfigs = damdeviceUnitMapper.selectList(qw);

        List<Long> deviceUnitIds = new ArrayList<>();
//        List<Long> deviceTypeIds = new ArrayList<>();
        List<String> dsjDeviceUnitNames = new ArrayList<>();
//        List<String> dsjDeviceTypeNames = new ArrayList<>();
//        deviceTypeIds.add(0L);
        deviceUnitIds.add(0L);
        dsjDeviceUnitNames.add("空");
//        dsjDeviceTypeNames.add("空");
        if(CollectionUtil.isNotEmpty(deviceUnitConfigs)){
            deviceUnitConfigs.forEach(deviceUnitConfig->{
//                String deviceTypeId = deviceUnitConfig.getDeviceTypeId();
                String deviceUnitId = deviceUnitConfig.getDeviceUnitId();
//                if(StringUtils.isNotBlank(deviceTypeId)){
//                    String[] deviceTypes = deviceTypeId.split(",");
//                    for (int i = 0;i<deviceTypes.length;i++){
//                        try{
//                            deviceTypeIds.add(Long.valueOf(deviceTypes[i]));
//                        } catch (Exception e){
//                            dsjDeviceTypeNames.add(deviceTypes[i]);
//                        }
//                    }
//                }
                if(StringUtils.isNotBlank(deviceUnitId)){
                    String[] deviceUnits = deviceUnitId.split(",");
                    for (int i = 0;i<deviceUnits.length;i++){
                        try{
                            deviceUnitIds.add(Long.valueOf(deviceUnits[i]));
                        } catch (Exception e){
                            dsjDeviceUnitNames.add(deviceUnits[i]);
                        }
                    }
                }
            });
        }
        if(CollectionUtil.isNotEmpty(deviceUnitIds)){
            damdConfig.setDeviceUnitIds(deviceUnitIds);
        } else {
            damdConfig.setDeviceUnitIds(Arrays.asList(-1l));
        }
//        if(CollectionUtil.isNotEmpty(deviceTypeIds)){
//            damdConfig.setDeviceTypeIds(deviceTypeIds);
//        }
//        if(CollectionUtil.isNotEmpty(dsjDeviceTypeNames)){
//            damdConfig.setDeviceTypeNames(dsjDeviceTypeNames);
//        }
        if(CollectionUtil.isNotEmpty(dsjDeviceUnitNames)){
            damdConfig.setDsjDeviceUnitNames(dsjDeviceUnitNames);
        }

        return deviceExtendInfoMapper.findNotSelectByModelIdAndDeviceUnitId(page,damdConfig);
    }

    @Override
    public DeviceExtendInfo getDeviceExtendInfoByDeviceId(String deviceId) {
        return deviceExtendInfoMapper.selectById(deviceId);
    }

    @Override
    public List<DeviceUnitVo> getConfigDeviceUnit(DeviceCommonDto damdConfig) {
        QueryWrapper<DeviceApplicationModelDeviceUnitConfig> qw = new QueryWrapper<>();
        qw.eq(ObjectUtils.isNotEmpty(damdConfig.getApplicationId()),"application_id_", damdConfig.getApplicationId());
        qw.eq(null != damdConfig.getModelId(),"model_id_",damdConfig.getModelId());

        List<DeviceApplicationModelDeviceUnitConfig> deviceUnitConfigs = damdeviceUnitMapper.selectList(qw);
        List<Long> deviceUnitIds = new ArrayList<>();
//        List<Long> deviceTypeIds = new ArrayList<>();
        List<String> dsjDeviceUnitNames = new ArrayList<>();
//        List<String> dsjDeviceTypeNames = new ArrayList<>();
        deviceUnitIds.add(0L);
//        deviceTypeIds.add(0L);
        dsjDeviceUnitNames.add("空");
//        dsjDeviceTypeNames.add("空");
        if(CollectionUtil.isNotEmpty(deviceUnitConfigs)){
            deviceUnitConfigs.forEach(deviceUnitConfig->{
//                String deviceTypeId = deviceUnitConfig.getDeviceTypeId();
                String deviceUnitId = deviceUnitConfig.getDeviceUnitId();
//                String deviceSecondTypeName = deviceUnitConfig.getDeviceSecondTypeName();
//                if(StringUtils.isNotBlank(deviceTypeId)){
//                    String[] deviceTypes = deviceTypeId.split(",");
//                    for (int i = 0;i<deviceTypes.length;i++){
//                        try {
//                            deviceTypeIds.add(Long.valueOf(deviceTypes[i]));
//                        } catch (Exception e) {
//                            dsjDeviceTypeNames.add(deviceTypes[i]);
//                        }
//                    }
//                }
                if(StringUtils.isNotBlank(deviceUnitId)){
                    String[] deviceUnits = deviceUnitId.split(",");
                    for (int i = 0;i<deviceUnits.length;i++){
                        try{
                            deviceUnitIds.add(Long.valueOf(deviceUnits[i]));
                        } catch (Exception e){
                            dsjDeviceUnitNames.add(deviceUnits[i]);
                        }

                    }
                }
//                if(StringUtils.isNotBlank(deviceSecondTypeName)){
//                    String[] deviceSecondTypeNames = deviceSecondTypeName.split(",");
//                    for (int i = 0;i<deviceSecondTypeNames.length;i++){
//                        deviceSecondTypes.add(deviceSecondTypeNames[i]);
//                    }
//                }
            });
        }
        //获取参数中的deviceTypeIds，取交集
//        List<Long> paramTypeIds = damdConfig.getDeviceTypeIds();
//        if(CollectionUtil.isNotEmpty(deviceTypeIds)){
//            if(CollectionUtil.isNotEmpty(paramTypeIds)){
//                deviceTypeIds.retainAll(paramTypeIds);
//            }
//            damdConfig.setDeviceTypeIds(deviceTypeIds);
//        }
        if(CollectionUtil.isNotEmpty(deviceUnitIds)){
            damdConfig.setDeviceUnitIds(deviceUnitIds);
        }
        List<DeviceUnitVo> configDeviceUnit = deviceUnitMapper.getConfigDeviceUnit(damdConfig);
        //如果有大数据平台的型号，则创建一些这种型号
//        if(CollectionUtil.isNotEmpty(dsjDeviceTypeNames)){
//            //获取这些二级类型的设备中的型号
//            List<DeviceUnitVo> list = deviceExtendInfoMapper.findUnitByDeviceSecondTypes(dsjDeviceTypeNames);
//            if(CollectionUtil.isNotEmpty(list)){
//                list.forEach(item->{
//                    if(StringUtils.isNotBlank(item.getCode())){
//                        DeviceUnitVo deviceUnitVo = new DeviceUnitVo();
//                        deviceUnitVo.setCode(item.getCode());
//                        deviceUnitVo.setTypeName(item.getTypeName());
//                        configDeviceUnit.add(deviceUnitVo);
//                    }
//                });
//            }
//        }
        if(CollectionUtil.isNotEmpty(dsjDeviceUnitNames)){
            List<DeviceUnitVo> list = deviceExtendInfoMapper.findUnitByDeviceUnitNames(dsjDeviceUnitNames);
            if(CollectionUtil.isNotEmpty(list)){
                list.forEach(item->{
                    if(StringUtils.isNotBlank(item.getCode())){
                        DeviceUnitVo deviceUnitVo = new DeviceUnitVo();
                        deviceUnitVo.setCode(item.getCode());
                        deviceUnitVo.setTypeName(item.getTypeName());
                        configDeviceUnit.add(deviceUnitVo);
                    }
                });
            }
        }
        return configDeviceUnit;
    }

    /**
     * 查询已经被业务绑定的设备(物联网平台设备)
     * @return
     */
    @Override
    public IPage<DeviceExtendInfoVo> getSelected(RequestModel<DeviceCommonDto> requestModel) {
        Page page = requestModel.getPage();
        DeviceCommonDto deviceCommonDto = requestModel.getCustomQueryParams();
        IPage<DeviceExtendInfoVo> infoVoIPage = deviceExtendInfoMapper.getSelected(page,deviceCommonDto);
        return infoVoIPage;
    }

    /**
     * 查询设备类型
     *
     * @return
     */
    @Override
    public Set<String> getDeviceSecondTypeNames() {
        QueryWrapper<DeviceExtendInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("device_second_type_name");
        queryWrapper.groupBy("device_second_type_name");
        List<DeviceExtendInfo> deviceExtendInfos = deviceExtendInfoMapper.selectList(queryWrapper);
        return deviceExtendInfos.stream()
                .filter(d-> null != d && StringUtils.isNotBlank(d.getDeviceSecondTypeName()))
                .map(DeviceExtendInfo::getDeviceSecondTypeName).collect(Collectors.toSet());
    }

    @Override
    public List<DeviceExtendInfo> getSbxh(String deviceSecondTypeName) {
        return deviceExtendInfoMapper.selectList(Wrappers.<DeviceExtendInfo>lambdaQuery()
                .eq(DeviceExtendInfo::getDeviceSecondTypeName, deviceSecondTypeName)
                .groupBy(DeviceExtendInfo::getSbxh)
        );
    }

    @Override
    public List<SimpleDeviceUnitVo> getSbxhToDeviceUnit(String deviceSecondTypeName) {
        List<DeviceExtendInfo> sbxh = getSbxh(deviceSecondTypeName);
        return sbxh == null ? null : sbxh.stream()
                .filter(e-> e != null && StringUtils.isNotBlank(e.getSbxh()))
                .map(e ->{
                    SimpleDeviceUnitVo deviceUnit = new SimpleDeviceUnitVo();
                    deviceUnit.setId(e.getSbxh());
                    deviceUnit.setCode(e.getSbxh());
                    deviceUnit.setName(e.getSbxh());
                    return deviceUnit;
                }).collect(Collectors.toList());
    }

    @Override
    public int updateUseStatusById(DeviceExtendInfo deviceExtendInfo) {
        // 防止更新其他字段
        DeviceExtendInfo extendInfo = new DeviceExtendInfo();
        extendInfo.setDeviceId(deviceExtendInfo.getDeviceId());
        extendInfo.setUseStatus(deviceExtendInfo.getUseStatus());
        String operatePrivilegeCode = LogHelper.getBusinessLog().getOperatePrivilegeCode();
        boolean flag = deviceExtendInfo.getUseStatus()==1;
        LogHelper.setLogInfo(operatePrivilegeCode, flag ? LogConstant.LogOperateActionType.ENABLE:LogConstant.LogOperateActionType.DISABLE,(flag?"启用":"禁用")+"设备，设备编号:"+deviceExtendInfo.getDeviceId());
        return deviceExtendInfoMapper.updateById(extendInfo);
    }

    @Override
    public IPage<DeviceManageVo> selectDeviceManagePage(IPage<DeviceManageVo> page, DeviceManageDto vo) {
        return deviceExtendInfoMapper.selectDeviceManagePage(page,vo);
    }

    @Override
    public void deviceByCode(String deviceCode) {
        deviceExtendInfoMapper.deleteById(deviceCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateExcel(DeviceManageImportExcel deviceManageExcel) {
        if(deviceManageExcel == null){
            return;
        }
        // 参数校验，错误参数直接上抛
        ValidateUtil.validateParams(deviceManageExcel);
        try{
            DeviceExtendInfo convert = deviceManageExcel.convert();
            DeviceExtendInfo deviceExtendInfo = deviceExtendInfoMapper.selectById(convert.getDeviceId());
            if(deviceExtendInfo == null){
                deviceExtendInfoMapper.insert(convert);
            } else {
                deviceExtendInfoMapper.updateById(convert);
            }
        } catch (Exception ex) {
            throw new RuntimeException("未知的参数异常："+ex.getMessage(),ex);
        }
    }
}
