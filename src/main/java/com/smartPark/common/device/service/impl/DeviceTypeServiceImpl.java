package com.smartPark.common.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.smartPark.common.device.mapper.DeviceApplicationModelDeviceUnitConfigMapper;
import com.smartPark.common.device.mapper.DeviceTypeMapper;
import com.smartPark.common.device.service.DeviceExtendInfoService;
import com.smartPark.common.device.service.DeviceTypeService;
import com.smartPark.common.device.service.DeviceUnitService;
import com.smartPark.common.device.vo.DeviceTypeUnitVo;
import com.smartPark.common.entity.device.DeviceApplicationModelDeviceUnitConfig;
import com.smartPark.common.entity.device.DeviceType;
import com.smartPark.common.entity.device.DeviceUnit;
import com.smartPark.common.entity.device.vo.SimpleDeviceUnitVo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 设备类型实现类 <br>
 *
 * <AUTHOR>
 * @version [版本号, 2018年7月6日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Service("DeviceTypeService")
public class DeviceTypeServiceImpl  extends AbstractCommenServiceImpl implements DeviceTypeService {

    @Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private DeviceApplicationModelDeviceUnitConfigMapper damdcMapper;

    @Autowired
    private DeviceExtendInfoService deviceExtendInfoService;

    @Autowired
    private DeviceUnitService deviceUnitService;

    @Override
    public List<DeviceType> queryAll() {
        return deviceTypeMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<DeviceTypeUnitVo> queryAllUnit(Long id) {
        List<DeviceType> deviceTypes = deviceTypeMapper.selectList(Wrappers.<DeviceType>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(id), DeviceType::getId, id));

        List<DeviceTypeUnitVo> list = new LinkedList<>();

        if(deviceTypes != null){
            List<DeviceTypeUnitVo> collect = deviceTypes.stream().map(deviceType -> {
                DeviceTypeUnitVo deviceTypeUnitVo = new DeviceTypeUnitVo();
                deviceTypeUnitVo.setDeviceTypeId(String.valueOf(deviceType.getId()));
                deviceTypeUnitVo.setDeviceTypeName(deviceType.getName());
                List<DeviceUnit> byTypeId = deviceUnitService.findByTypeId(deviceType.getId());
                deviceTypeUnitVo.setDeviceUnit(SimpleDeviceUnitVo.convertToList(byTypeId));
                return deviceTypeUnitVo;
            }).collect(Collectors.toList());
            list.addAll(collect);
        }

        // 查询大数据下的产品类型和产品型号
//        list.addAll(queryAllDsjUnit());
        List<DeviceTypeUnitVo> deviceTypeUnitVos = queryAllDsjUnit();
        list = unionDeviceTypeUnitVo(list,deviceTypeUnitVos);

        return list;
    }

    /**
     * 合并大数据与物联网平台的设备类型
     * 以二级设备类型与设备型号字段进行匹配
     * @param list
     * @param deviceTypeUnitVos
     * @return
     */
    private List<DeviceTypeUnitVo> unionDeviceTypeUnitVo(List<DeviceTypeUnitVo> list, List<DeviceTypeUnitVo> deviceTypeUnitVos) {
        if(CollectionUtil.isEmpty(deviceTypeUnitVos)){
            return list;
        }
        if(CollectionUtil.isEmpty(list)){
            return deviceTypeUnitVos;
        }
        List<DeviceTypeUnitVo> result = new ArrayList<>();
        for (DeviceTypeUnitVo dsjDeviceTypevo : deviceTypeUnitVos) {
            String dsjTypeName = dsjDeviceTypevo.getDeviceTypeName();
            if(StringUtils.isBlank(dsjTypeName)){
                continue;
            }
            boolean existType = false;//是否已经存在大数据中
            for (DeviceTypeUnitVo manageTypeVo : list) {
                String manageTypeName = manageTypeVo.getDeviceTypeName();
                if(StringUtils.isBlank(manageTypeName)){
                    continue;
                }
                if(manageTypeName.equals(dsjTypeName)){
                    existType = true;
                    List<SimpleDeviceUnitVo> manageDeviceUnits = manageTypeVo.getDeviceUnit();
                    List<SimpleDeviceUnitVo> dsjDeviceTypevoDeviceUnit = dsjDeviceTypevo.getDeviceUnit();
                    //根据code字段进行判断是否一致
                    dsjDeviceTypevoDeviceUnit.forEach(dsjDeviceUnit->{
                        boolean existUnit = false;
                        String dsjCode = dsjDeviceUnit.getCode();
                        for (SimpleDeviceUnitVo manageDeviceUnit:manageDeviceUnits) {
                            if (manageDeviceUnit.getCode().equalsIgnoreCase(dsjCode)){
                                existUnit = true;
                                break;
                            }
                        }
                        if (!existUnit){
                            manageDeviceUnits.add(dsjDeviceUnit);
                        }
                    });
                }
            }
            //如果物联网平台不存在这个类型名称
            if (!existType){
                list.add(dsjDeviceTypevo);
            }
        }
        return list;
    }

    @Override
    public List<DeviceTypeUnitVo> queryAllDsjUnit(){
        Set<String> deviceSecondTypeNames = deviceExtendInfoService.getDeviceSecondTypeNames();
        if(deviceSecondTypeNames == null){
            return null;
        }
        List<DeviceTypeUnitVo> list = new LinkedList<>();
        for (String deviceSecondTypeName : deviceSecondTypeNames) {
            DeviceTypeUnitVo deviceTypeUnitVo = new DeviceTypeUnitVo();
            deviceTypeUnitVo.setDeviceTypeId(deviceSecondTypeName);
            deviceTypeUnitVo.setDeviceTypeName(deviceSecondTypeName);
            deviceTypeUnitVo.setDeviceUnit(deviceExtendInfoService.getSbxhToDeviceUnit(deviceSecondTypeName));
            list.add(deviceTypeUnitVo);
        }
        return list;
    }

    @Override
    public DeviceType getById(Long id) {
        return deviceTypeMapper.selectById(id);
    }

    @Override
    public void saveOrUpdateDeviceType(DeviceType deviceType) {
        DeviceType exist = deviceTypeMapper.selectById(deviceType.getId());
        if(exist!=null){
            deviceTypeMapper.updateById(deviceType);
        }else {
            deviceTypeMapper.insert(deviceType);
        }
    }

    @Override
    public List<DeviceType> findByTypeNames(List<String> typeNames) {
        return deviceTypeMapper.findByTypeNames(typeNames);
    }

    @Override
    public List<DeviceType> getByModelId(String modelId) {
        List<DeviceType> result = new ArrayList<>();
        QueryWrapper qw = new QueryWrapper();
        qw.eq("model_id_",modelId);
        List<DeviceApplicationModelDeviceUnitConfig> deviceUnitConfigs = damdcMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(deviceUnitConfigs)){
            List<Long> deviceTypeIds = new ArrayList<>();
            deviceUnitConfigs.forEach(item->{
                String deviceTypeId = item.getDeviceTypeId();
                if(StringUtils.isNotBlank(deviceTypeId)){
                    String[] typeId = deviceTypeId.split(",");
                    for (int i=0;i<typeId.length;i++){
                        deviceTypeIds.add(Long.valueOf(typeId[i]));
                    }
                }
            });
            result = deviceTypeMapper.findByIds(deviceTypeIds);
        }
        return result;
    }
}
