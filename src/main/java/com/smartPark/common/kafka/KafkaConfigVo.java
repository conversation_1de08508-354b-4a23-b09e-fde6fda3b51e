package com.smartPark.common.kafka;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class KafkaConfigVo {


    /**
     * kafka服务器ip
     */
    private String serverIp;

    /**
     * kafka服务器端口
     */
    private String port;

    /**
     * key序列器
     */
    private String keySerializer;

    /**
     * value序列器
     */
    private String valueSerializer;

    /**
     * 生产者配置
     */
    private Map<String, String> producerProps;
    /**
     * 消费者配置
     */
    private Map<String, String> consumerProps;

}
