package com.smartPark.common.businessLog.entity.vo;

import com.smartPark.common.businessLog.entity.BusinessLog;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BusinessLog实体类Vo
 *
 * <AUTHOR>
 * @date 2023/04/13
 */

@Data
@Accessors(chain = true)
public class BusinessLogVo extends BusinessLog {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public BusinessLogVo(BusinessLog baseBusinessLog) {
        //this.setName(baseBusinessLog.getName());
    }
}
