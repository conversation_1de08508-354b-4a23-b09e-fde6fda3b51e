package com.smartPark.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_application")
public class BaseApplication extends Model<BaseApplication> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 应用名称
     */
    @TableField("name_")
    private String name;

    /**
     * 应用代码(应用在base的唯一标识)
     */
    @TableField("code_")
    private String code;

    /**
     * 应用访问地址(预留字段，也可以配置在环境变量中)
     */
    @TableField("url_")
    private String url;

    /**
     * 第三方登录成功之后，跳转的地址
     */
    @TableField("login_success_url_")
    private String loginSuccessUrl;

    /**
     * 第三方登录鉴权完成之后跳转的地址
     */
    @TableField("login_url_")
    private String loginUrl;

    /**
     * 应用接口访问地址(预留字段，也可以配置在环境变量中)
     */
    @TableField("api_url_")
    private String apiUrl;

    /**
     * 类别(1三智内部服务 2外部服务)
     */
    @TableField("type_")
    private Integer type;

    /**
     * 统一认证平台对应的appKey，统一登录跳转用
     */
    @TableField("auth_center_app_key_")
    private String appKey;

    /**
     * 统一认证平台对应的appSecret，统一登录跳转用
     */
    @TableField("auth_center_app_secret_")
    private String appSecret;

    /**
     * 统一认证平台对应的clientId, 统一登录跳转用
     */
    @TableField("auth_center_client_id_")
    private String clientId;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
