package com.smartPark.common.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/14
 * @description
 */
@DynamicInsert
@DynamicUpdate
@Data
@TableName("base_device_version")
public class DeviceVersion {

    //主键
    @TableId(value = "id",type = IdType.INPUT)
    private Long id;

    @TableField(value ="device_unit_id")
    private Long deviceUnitId;

    @TableField(value ="physics_model")
    private String physicsModel;

    @TableField(value ="device_version")
    private String deviceVersion;

    @TableField(value ="device_version_desc")
    private String deviceVersionDesc;

    @TableField(value ="rule_id")
    private Long ruleId;

    @TableField(exist=false)
    private Parserule parserule;

    @TableField(value ="create_time")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value ="modify_time")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 修改人编号
     */
    @TableField(value ="reviser_id")
    private Long reviserId;

    /**
     * 修改人名称
     */
    @TableField(value ="reviser_name")
    private String reviserName;

    /**
     * 创建人编号
     */
    @TableField(value ="creator_id")
    private Long creatorId;

    /**
     * 创建人名称
     */
    @TableField(value ="creator_name")
    private String creatorName;

}
