package com.smartPark.common.entity.device;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.smartPark.common.annotation.GaodeText;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 设备信息
 */
@GaodeText
@TableName("base_device")
@DynamicInsert
@DynamicUpdate
@Data
@Accessors(chain = true)
public class Device {

    /**
     * 设备编码
     */
    @TableId(value = "code", type = IdType.INPUT)
    @Size(max = 32, message = "设备编码不能超过32个字符")
    private String code;


    /**
     * 使用状态(1启用0禁用)
     */
    @TableField(exist = false)
    private Integer useStatus;

    /**
     * 设备名称
     */
    @TableField(value = "name")
    @Size(max = 32, message = "设备名称不能超过32个字符")
    private String name;

    /**
     * 设备状态
     *
     * @apiNote 0:正常; 1:告警
     */
    @TableField(value = "alarm_state")
    private Integer alarmState;

    /**
     * 暂时用作分组信息的存储载体
     * */
    @TableField(value = "community_id")
    private Long communityId;

    @TableField(value = "longitude")
    private Double longitude;

    @TableField(value = "latitude")
    private Double latitude;


    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;


    /**
     * 项目编号
     */
    @TableField(value = "project_id")
    private Long projectId;


    /**
     * 设备型号
     */
    @TableField(value = "device_unit_id")
    private Long deviceUnitId;

    /**
     * 报文最后推送时间
     */
    @TableField(value = "last_push_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastPushTime;


    /**
     * NB平台注册设备号
     */
    @TableField(value = "nb_device_id")
    private String nbDeviceId;

    /**
     * NB平台注册标准接入设备密钥
     */
    @TableField(value = "std_device_secret")
    private String stdDeviceSecret;

    /**
     * NB平台注册标准接入设备密钥
     */
    @TableField(value = "std_product_key")
    private String stdProductKey;

    /**
     * NB平台注册标准接入设备密钥
     */
    @TableField(value = "std_product_secret")
    private String stdProductSecret;

    /**
     * NB电信平台设备应用id
     */
    @TableField(value = "appid")
    private String appId;

    /**
     * NB电信平台设备应用id
     */
    @TableField(value = "app_secret")
    private String appSecret;

    /**
     * NB电信平台设备应用id密码
     */
    @TableField(value = "appid_Psd")
    private String appIdPsd;

    /**
     * NB移动平台设备apiKey
     */
    @TableField(value = "api_Key")
    private String apiKey;

    /**
     * product_id
     * */
    @TableField(value = "product_id")
    private String productId;

    /**
     * NB移动平台设备imsi
     */
    @TableField(value = "imsi")
    private String imsi;

    @TableField(value = "is_virtual_device")
    private Byte isVirtualDevice;

    /**
     * 是否已在厂家注册 0.已注册 1.未注册
     * 默认为已注册 不能修改
     */
    @TableField(value = "register")
    private String register;


    @TableField(value = "push_switch")
    private Boolean pushSwitch;

    @TableField(value = "push_period")
    private Integer pushPeriod;

    //设备是否已接入
    @TableField(value = "is_device_on")
    private Byte isDeviceOn;

    /**
     * 设备状态  0：禁用 1：在线  2：离线
     */
    @TableField(value = "status")
    private Integer status;

    @TableField(value = "last_device_flow_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date lastDeviceFlowTime;

    /**
     * 设备离线告警开关(1：开启 0：关闭)
     */
    @TableField(value = "offline_alarm_switch")
    private Integer offlineAlarmSwitch;


    /**
     * 设备特有信息
     */
    @TableField(value = "device_config")
    private String deviceConfig;

    /**
     * 设备个性化参数
     */
    @TableField(value = "personalize_parameters")
    private String personalizeParameters;

    //通讯地址
    @TableField(value = "device_comm_id")
    private String deviceCommId;
    //所属网关
    @TableField(value = "device_parent",updateStrategy = FieldStrategy.IGNORED)
    private String deviceParent;

    /**
     * 电信AEP设备ID
     */
    @TableField(value = "ctwing_device_id")
    private String ctwingDeviceId;


    @TableField(value = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    @Temporal(TemporalType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 修改人编号
     */
    @TableField(value = "reviser_id")
    private Long reviserId;

    /**
     * 修改人名称
     */
    @TableField(value = "reviser_name")
    private String reviserName;

    /**
     * 创建人编号
     */
    @TableField(value = "creator_id")
    private Long creatorId;

    /**
     * 创建人名称
     */
    @TableField(value = "creator_name")
    private String creatorName;

}
