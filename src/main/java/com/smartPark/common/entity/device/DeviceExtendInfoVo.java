package com.smartPark.common.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Id;

/**
 * 设备扩展信息表，来源于大数据
 */
@Getter
@Setter
public class DeviceExtendInfoVo extends DeviceExtendInfo{

    private String deviceUnitName;

    private String deviceTypeName;

    private String deviceUnitCode;

    /**
     * 设备所属模块id(多个逗号分隔)
     */
    private String modelIds;
}
