package com.smartPark.common.entity.device;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 应用模块型号配置表
 */
@TableName("base_application_model_device_unit_config")
@DynamicInsert
@DynamicUpdate
@Data
public class DeviceApplicationModelDeviceUnitConfig {

    /**
     * ID
     */
    @TableId(value ="id_",type = IdType.AUTO)
    private Long id;


    /**
     * 应用id
     */
    @TableField(value ="application_id_")
    private Long applicationId;

    /**
     * 大模块
     */
    @TableField(value ="model_")
    private String model;

    /**
     * 模块id
     */
    @TableField(value ="model_id_")
    private Integer modelId;

    @TableField(value ="device_type_id_")
    private String deviceTypeId;

    /**
     * 型号id
     */
    @TableField(value ="device_unit_id_")
    private String deviceUnitId;

}
