package com.smartPark.common.security.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.common.security.entity.UserRefRole;
import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.security.entity.Privilege;
import com.smartPark.common.security.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 角色Mapper
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/23
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

  
  /**
   * 按用户查询
   *
   * @param userId 用户编号
   * @return 角色集合
   */
  @Select("select a.role_id_ as id,a.* from base_role a JOIN base_user_ref_role b where b.role_id = a.role_id_ and b.user_id = #{userId}")
  List<Role> findByUsersId(Long userId);
  
  
  /**
   * 当前租户下的角色列表
   *
   * @return 角色集合
   */
  List<Role> findRoles(@Param("role") Role role);
  
  /**
   * 当前租户下的角色列表分页
   *
   * @return 角色集合
   */
  List<Role> findRoles(Page page,@Param("role") Role role);

  void deleteRole2Spaces(Long userId);

  List<BaseappUser> selectUserByRole(@Param("id") Long id);
	
  void deleteRole2Users(@Param("roleId")Long roleId);
	
  void insertRole2Users(UserRefRole userRefRole);

  /**
   *
   * @param id
   * @return
   */
    Role selectByRoleId(Long id);

  /**
   * 查询角色的权限
   * @param id 角色id
   * @return 权限
   */
  List<Privilege> selectPrivilegeByRoleId(Long id);

  /**
   * 查询用户角色
   * @param userId 用户id
   * @return 用户已有角色
   */
  List<Role> selectUserRoleByUserId(Long userId);

  /**
   * 查询用户角色分页
   * @param userId 用户id
   * @return 用户已有角色
   */
  List<Role> selectUserRolePageByUserId(Page page, @Param("userId") Long userId);

  /**
   * 统计应用下的用户数量
   * @param applicationId 应用id
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return 用户数量
   */
  Integer countApplicationUser(@Param("applicationId") Long applicationId, @Param("startTime") Date startTime, @Param("endTime")Date endTime);;

    Collection<Role> getByUserIdAndPrivilegeCode(@Param("userId") Long userId, @Param("privilegeCode") String privilegeCode);

    List<Role> getByPrivilegeCode(@Param("privilegeCode") String privilegeCode);
}
