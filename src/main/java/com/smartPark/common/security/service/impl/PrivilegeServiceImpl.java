package com.smartPark.common.security.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.CommonConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.security.entity.Privilege;
import com.smartPark.common.security.entity.Role;
import com.smartPark.common.security.entity.RoleRefPrivilege;
import com.smartPark.common.security.mapper.PrivilegeMapper;
import com.smartPark.common.security.mapper.UserMapper;
import com.smartPark.common.security.repository.PrivilegeRepository;
import com.smartPark.common.security.service.PrivilegeService;
import com.smartPark.common.security.service.RoleService;
import com.smartPark.sanzhi.util.PrivilegeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import site.morn.boot.data.CrudServiceSupport;
import site.morn.framework.context.AccountContext;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限服务
 *
 * <AUTHOR>
 * @since 0.1.1,  2019/08/28
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class PrivilegeServiceImpl extends
    CrudServiceSupport<Privilege, Long, PrivilegeRepository> implements PrivilegeService {

  @Autowired
  RoleService roleService;
  
  @Autowired
  PrivilegeMapper privilegeMapper;
  @Autowired
  BaseUserContextProducer baseUserContextProducer;

  @Resource
  private CommonService commonService;

  @Resource
  private UserMapper userMapper;

  @Override
  public List<Privilege> selectPrivilegeAll(Privilege linkappPrivilege){
	  return privilegeMapper.selectPrivilegeAll(linkappPrivilege);
  }


  @Override
  public Collection<Privilege> getCurrent() {
    // 获取当前用户
	  BaseappUser user = AccountContext.currentUser();
    Collection<Role> roles = roleService.getByUserId(user.getId());
    return roles.stream().flatMap(role -> role.getPrivileges().stream()).collect(Collectors.toList());
  }

  @Override
  public List<Privilege> selectPrivilegeByRole(String roleId, Integer type){
	  return privilegeMapper.selectPrivilegeByRole(roleId, type);
  }

    @Override
    public TreeSet<Privilege> selectPrivilegeByUser(String username, Integer flag, Integer applicationId) {
        String temp = username;
        if (flag == null) {
            flag = 0;
        }
        //超管，admin为超管
        if("admin".equals(username)){
            temp = "";
        }
        TreeSet<Privilege> treeSet = privilegeMapper.selectPrivilegeByUser(temp, flag, applicationId,null);
        if("admin".equals(username) && applicationId==1) {
            // 菜单管理不可见，写死在代码里
            Privilege menuPrivilege = new Privilege();
            menuPrivilege.setLevel(1);
            menuPrivilege.setSort(Double.MAX_VALUE);
            menuPrivilege.setType(1);
            menuPrivilege.setAccessType(1);
            menuPrivilege.setName("菜单管理");
            menuPrivilege.setUrl("/menu");
            menuPrivilege.setCode("menu");
            menuPrivilege.setIconName("ios-analytics");
            menuPrivilege.setFlag(0);
            menuPrivilege.setApplicationId(1L);
            menuPrivilege.setAccessType(1);
            treeSet.add(menuPrivilege);
        }
        return treeSet;
    }

    @Override
    public List<Privilege> selectPrivilegeTreeByUser(Privilege linkappPrivilege) {
        BaseappUser user = baseUserContextProducer.getCurrent();
        //返回的还是要判断下，上来就用还是有可能NPE
        Assert.notNull(user,"用户登录信息不存在");
        Integer applicationId = null;
        if(!ObjectUtils.isEmpty(linkappPrivilege.getApplicationId())) {
            applicationId = linkappPrivilege.getApplicationId().intValue();
        }
        String username = user.getUsername();
        //超管，admin为超管
        if("admin".equals(username)){
            username = "";
        }
        TreeSet<Privilege> treeSet = privilegeMapper.selectPrivilegeByUser(username, linkappPrivilege.getFlag(), applicationId,linkappPrivilege.getType());
        return PrivilegeUtils.buildTree(treeSet);
    }

  
  @Override
  public void privilege2Role(Role role, List<Privilege> privileges, Integer type) {
	privilegeMapper.deletePrivilege2Role(role.getId(), type);
	for(Privilege privilege :privileges) {
		RoleRefPrivilege roleRefPrivilege = new RoleRefPrivilege();
		roleRefPrivilege.setPrivilegeId(privilege.getId().toString());
		roleRefPrivilege.setRoleId(role.getId().toString());
		roleRefPrivilege.setId(UUID.randomUUID().toString().replaceAll("-", ""));
		privilegeMapper.insertPrivilege2Role(roleRefPrivilege);
	}
  }

    @Override
    public void addPrivilege(Privilege linkappPrivilege) {
        // if(!StringUtils.isEmpty(linkappPrivilege.getUrl()) && !linkappPrivilege.getUrl().contains("manage" + CommonConstant.DIAGONAL_BAR_STR)){
        //     throw new IllegalArgumentException("url路径参数不符合规范");
        // }
        if(!StringUtils.isEmpty(linkappPrivilege.getUrl()) && checkPrivilegeUrlRepeat(linkappPrivilege)){
            throw new IllegalArgumentException("权限菜单路由地址重复");
        }
        // if(checkPrivilegeSeqRepeat(linkappPrivilege)){
        //     throw new IllegalArgumentException("权限菜单排序号重复");
        // }
        if(checkPrivilegeCodeRepeat(linkappPrivilege)){
            throw new IllegalArgumentException("权限菜单标识码重复");
        }

        Long id_ = null;
        List<Privilege> privilegeMaxId = privilegeMapper.getPrivilegeMaxId();
        if(privilegeMaxId == null || privilegeMaxId.size() <= 0){
          id_ = 1L;
        }else {
            Privilege linkappPrivilege1 = privilegeMaxId.get(0);
            Long id = linkappPrivilege1.getId();
            id_ = id + 1;
        }
        linkappPrivilege.setId(id_);
        commonService.setCreateAndModifyInfo(linkappPrivilege);

        if(linkappPrivilege.getFlag() == null){
            //默认PC端
            linkappPrivilege.setFlag(0);
        }
        if(StringUtils.isEmpty(linkappPrivilege.getCode())){
            Assert.notNull(linkappPrivilege.getUrl(), "url 不能为空");
            String subUrl = linkappPrivilege.getUrl().replaceAll("manage/","");
            String code = subUrl.replaceAll(CommonConstant.DIAGONAL_BAR_STR, CommonConstant.COLON_STR);
            linkappPrivilege.setCode(code);
        }
        privilegeMapper.insertPrivilegeExt(linkappPrivilege);
    }

    @Override
    public void updatePrivilege(Privilege linkappPrivilege) {
        if(!StringUtils.isEmpty(linkappPrivilege.getUrl()) && checkPrivilegeUrlRepeat(linkappPrivilege)){
            throw new IllegalArgumentException("权限菜单路由地址重复");
        }
        // if(checkPrivilegeSeqRepeat(linkappPrivilege)){
        //     throw new IllegalArgumentException("权限菜单排序号重复");
        // }
        if(checkPrivilegeCodeRepeat(linkappPrivilege)){
            throw new IllegalArgumentException("权限菜单标识码重复");
        }

        commonService.setModifyInfo(linkappPrivilege);

        if(linkappPrivilege.getFlag() == null){
            //默认PC端
            linkappPrivilege.setFlag(0);
        }
        if(StringUtils.isEmpty(linkappPrivilege.getCode())){
            Assert.notNull(linkappPrivilege.getUrl(), "url 不能为空");
            String subUrl = linkappPrivilege.getUrl().replaceAll("manage/","");
            String code = subUrl.replaceAll(CommonConstant.DIAGONAL_BAR_STR, CommonConstant.COLON_STR);
            linkappPrivilege.setCode(code);
        }
        privilegeMapper.updatePrivilegeExt(linkappPrivilege);
    }

    @Override
    public void deletePrivilege(List<Long> idList) {

        // TreeSet<Privilege> treeSet = privilegeMapper.selectPrivilegeByUser("", 0, null);
        // PrivilegeUtils.getChildrenList(treeSet,idList)
        privilegeMapper.deletePrivilegeExt(idList);

    }

    @Override
    public TreeSet<Long> selectApplicationIds(Long userId) {
        //查询下用户，判断用户类型
        BaseappUser user = userMapper.selectById(userId);
        if(user != null){
            String type = user.getType();
            //1.管理员 2.普通用户
            if("1".equals(type)){
                //查询所有应用
                userId = null;
            }
        }
        return privilegeMapper.selectApplicationIds(userId);
    }

    /**
     * 验证url是否重复
     * 如果数据库中存在这个id,且url没有发生变化，则直接返回false
     * 如果数据库没有这个id或者url存在变化，则跟其他菜单进行对比
     * @param linkappPrivilege
     * @return  false未重复， true已存在重复
     */
    private boolean checkPrivilegeUrlRepeat(Privilege linkappPrivilege){
        boolean flag = false;
        //如果数据库中存在这个id,且url没有发生变化，则直接返回false
        if (linkappPrivilege.getId()!=null){
            Privilege privilege = privilegeMapper.selectById(linkappPrivilege.getId());
            if (privilege!=null && Objects.equals(linkappPrivilege.getUrl(),privilege.getUrl())){
                return false;
            }
        }
        //如果数据库没有这个id或者url存在变化，则跟其他菜单进行对比
        List<Privilege> linkappPrivileges = privilegeMapper.checkPrivilegeUrlIsRepeat(linkappPrivilege);
        if(linkappPrivileges != null && linkappPrivileges.size() > 0){
            flag = true;
        }
        return flag;
    }

    private boolean checkPrivilegeSeqRepeat(Privilege linkappPrivilege){
        boolean flag = false;
        Integer resultCount = privilegeMapper.checkPrivilegeSeqIsRepeat(linkappPrivilege);
        if(resultCount != null && resultCount > 0){
            flag = true;
        }
        return flag;
    }

    /***
     * 效验code是否存在相同
     * @param linkappPrivilege
     * @return
     */
    private boolean checkPrivilegeCodeRepeat(Privilege linkappPrivilege){
        Assert.notNull(linkappPrivilege.getCode(),"code 不能为空");
        boolean flag = false;
        QueryWrapper<Privilege> linkappPrivilegeQueryWrapper = new QueryWrapper<>();
        linkappPrivilegeQueryWrapper.eq("deleted_",0).eq(!StringUtils.isEmpty(linkappPrivilege.getCode()),"privilege_code_", linkappPrivilege.getCode())
                .ne(!StringUtils.isEmpty(linkappPrivilege.getId()),"id_", linkappPrivilege.getId());
        Integer resultCount = privilegeMapper.selectCount(linkappPrivilegeQueryWrapper);
        if(resultCount != null && resultCount > 0){
            flag = true;
        }
        return flag;
    }

}
