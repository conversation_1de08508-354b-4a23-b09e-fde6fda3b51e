package com.smartPark.common.security.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.common.security.entity.Worker;
import com.smartPark.common.security.mapper.WorkerMapper;
import com.smartPark.common.security.service.WorkerService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 同步过来的工作人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-11
 */
@Service
public class WorkerServiceImpl extends ServiceImpl<WorkerMapper, Worker> implements WorkerService {

    @Override
    public IPage<Worker> selectPage(Page page, Worker customQueryParams) {
        return baseMapper.findPage(page, customQueryParams);
    }

    @Override
    public Worker findByMobile(String account) {
        return baseMapper.findByMobile(account);
    }
}
