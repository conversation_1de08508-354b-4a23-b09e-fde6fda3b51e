package com.smartPark.common.security.service.impl;

import static site.morn.constant.DigestConstant.Algorithms.SPRING_B_CRYPT;
import static site.morn.framework.context.CommonConstant.Caches.USER;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.smartPark.common.aliyun.AliyunSmsUtils;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.security.constant.LinkappUserConstant;
import com.smartPark.common.security.constant.UserConstant.Data;
import com.smartPark.common.security.constant.UserConstant.Error;
import com.smartPark.common.security.dto.PrivilegeDTO;
import com.smartPark.common.security.dto.RoleDTO;
import com.smartPark.common.security.dto.UserDto;
import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.security.entity.Cache;
import com.smartPark.common.security.entity.Privilege;
import com.smartPark.common.security.entity.ResetPasswordVo;
import com.smartPark.common.security.entity.Role;
import com.smartPark.common.security.entity.UserRefRole;
import com.smartPark.common.security.entity.UserRefRoleDTO;
import com.smartPark.common.security.mapper.RoleMapper;
import com.smartPark.common.security.mapper.UserMapper;
import com.smartPark.common.security.mapper.UserRefRoleMapper;
import com.smartPark.common.security.repository.UserRepository;
import com.smartPark.common.security.service.PrivilegeService;
import com.smartPark.common.security.service.RoleService;
import com.smartPark.common.security.service.UserService;
import com.smartPark.common.utils.RedisUtil;
import com.smartPark.common.utils.VerifyCodeUtils;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.context.ApplicationListener;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.boot.data.DisplayableServiceSupport;
import site.morn.boot.jpa.SpecificationBuilder;
import site.morn.cache.CacheGroup;
import site.morn.core.CriteriaMap;
import site.morn.exception.ApplicationMessages;
import site.morn.framework.context.AccountContext;
import site.morn.framework.entity.BaseUser;
import site.morn.framework.entity.BaseUser.Fields;
import site.morn.log.OperateArguments;
import site.morn.rest.RestModel;
import site.morn.util.GenericUtils;
import site.morn.util.MessageDigestUtils;
import site.morn.validate.persistent.PersistFunctionUtils;


/**
 * 用户服务
 *
 * <AUTHOR>
 * @since 1.0.0, 2017/9/28
 */
@Slf4j
@Service
@Component
@Transactional
@CacheConfig(cacheNames = {"getCode"})
public class UserServiceImpl extends
    DisplayableServiceSupport<BaseappUser, Long, UserRepository> implements
        UserService, ApplicationListener<ApplicationReadyEvent> {

    @Resource
    protected UserMapper linkappUserMapper;

    @Resource
    protected Cache cache;

    @Resource
    private RoleMapper linkappRoleMapper;

    @Resource
    private RoleService roleService;

    @Resource
    private UserRefRoleMapper userRefRoleMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private PrivilegeService privilegeService;

    @Override
    public BaseappUser getCurrentTenantAdmin() {
        QueryWrapper<BaseappUser> qw = new QueryWrapper<>();
        qw.eq("type", LinkappUserConstant.ADMIN_TYPE);
        List<BaseappUser> list = linkappUserMapper.selectList(qw);
        if (ObjectUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<BaseappUser> findByUsernameBatch(List<String> userNameList){
        QueryWrapper qw = new QueryWrapper();
        qw.in("nickname", userNameList);
        List<BaseappUser> list = linkappUserMapper.selectList(qw);
        return list;
    }

    @Override
    public BaseappUser findByUsername(String username) {
        if (username == null) {
            return null;
        }
        Object obj = AccountContext.cacheGroup().get(USER, username);

        if (obj != null) {
            BaseappUser user = (BaseappUser) obj;
            //获取redis中的对象
            BaseappUser redisUser = (BaseappUser) redisUtil.hget(RedisConstant.USER_PRE, String.valueOf(user.getId()));
            if (redisUser != null){
                user.setAuthorization(redisUser.getAuthorization());
                user.setClientId(redisUser.getClientId());
            }
            return user;
        } else {
            AccountContext.cacheGroup().clear(USER, username);
            QueryWrapper<BaseappUser> wrapper = new QueryWrapper<>();
            wrapper.eq("username", username);
            BaseappUser linkappUser = linkappUserMapper.selectOne(wrapper);
            if (linkappUser != null) {
                BaseappUser redisUser = (BaseappUser) redisUtil.hget(RedisConstant.USER_PRE, String.valueOf(linkappUser.getId()));
                if (redisUser != null){
                    linkappUser.setAuthorization(redisUser.getAuthorization());
                    linkappUser.setClientId(redisUser.getClientId());
                }
                AccountContext.cacheGroup().put(USER, username, linkappUser);
            }
            return linkappUser;
        }
    }

    @Override
    public void resetPassword(ResetPasswordVo vo) {
        BaseUser baseUser = AccountContext.currentUser();
        // 管理员密码错误
        if (!MessageDigestUtils
            .matches(SPRING_B_CRYPT, vo.getPasswordAuthentication(), baseUser.getPassword())) {
            //throw ApplicationMessages.translateException(Error.MANAGER_SECRET_IS_WRONG);
        }
        // 获取用户信息
        BaseappUser user = super.get(vo.getId());
        if (Objects.isNull(user)) {
            throw ApplicationMessages.translateException(Error.USER_NO_FOUND, vo.getId());
        }
        updatePassword(vo, user);
        // 重置密码处理
        //BeanFunctionUtils.processes(UserResetProcessor.class, user, Functions.TAG_RESET);
    }

    @Override
    public void updatePassword(ResetPasswordVo vo) {
        // 获取用户信息
    	BaseappUser user = super.get(vo.getId());
        if (Objects.isNull(user)) {
            throw ApplicationMessages.translateException(Error.USER_NO_FOUND, vo.getId());
        }
        // 旧密码错误
        if (!MessageDigestUtils
            .matches(SPRING_B_CRYPT, vo.getPasswordAuthentication(), user.getPassword())) {
            //throw ApplicationMessages.translateException(Error.ORIGINAL_SECRET_IS_WRONG);
        }
        updatePassword(vo, user);
        //先清除缓存
        AccountContext.cacheGroup().clear(USER, user.getUsername());
        // 修改密码处理
        AccountContext.cacheGroup().put(USER, user.getUsername(), user);
    }

    @Override
    public BaseappUser updateProfile(BaseappUser user) {
        Assert.notNull(user, Error.USER_NUMBER_NOT_NULL);
        Long id = user.getId();
        Assert.notNull(id, Error.USER_NUMBER_NOT_NULL);
//        BaseappUser persist = get(id);
        BaseappUser persist = repository().findById(id).orElse(null);
        Assert.notNull(persist, Error.USER_NUMBER_NOT_NULL);
        // 修改个人信息不允许影响角色
        user.setRoles(persist.getRoles());
        user.setType(persist.getType());
        user.setDisplay(persist.getDisplay());
        user.setLocked(persist.getLocked());
        user.setUsername(persist.getUsername());
        user.setCreateTime(persist.getCreateTime());
        user.setUserClass(persist.getUserClass());
        // 删除缓存 管理员类型用户名
//        AccountContext.cacheGroup().clear("findAdminTypeUserNameByTenantId", persist.getTenantId());
        return update(user);
    }

    @Override
    public boolean existsByRole(Long roleId) {
        Assert.notNull(roleId, "角色编号不能为空");
        long count = repository().countByRolesId(roleId);
        return count > 0;
    }

    @Override
    public <S extends BaseappUser> S unlock(Long id) {
        S s = toggleLock(id, false);
//        此操作会导致 解锁后用户关联角色被清空
//        userLoginInfoService.clearErrorCount(id);
        return s;
    }

    @Override
    public <S extends BaseappUser> S toggleLock(Long id, boolean isLock) {
        Assert.notNull(id, "参数id不能为空");
        BaseappUser linkappUser = repository().findById(id).orElse(null);
        Assert.notNull(linkappUser, "找不到该用户");
        if (Objects.nonNull(linkappUser)) {
            linkappUser.setLocked(isLock);
            update(linkappUser);
        }
        return GenericUtils.castFrom(linkappUser);
    }

    @Override
    public <S extends BaseappUser> S add(RestModel<S> restModel) {
        S model = restModel.getModel();
        initModel(model);
        OperateArguments.add(model.getUsername());
        validateModel(model); // 校验重复项
        return repository().save(model);
    }

    @Override
    public <S extends BaseappUser> S update(S model) {
        Assert.notNull(model.getId(), "用户编号不能为空");
        OperateArguments.add(model.getUsername());
        validateModel(model); // 校验重复项
        BaseappUser persist = repository().findById(model.getId()).orElse(null);
        // 更新用户不允许影响密码,租户id,角色
        if (Objects.nonNull(persist)) {
            model.setPassword(persist.getPassword());
            model.setRoles(persist.getRoles());
        }
        super.update(model);
        // 更新用户缓存
//        model.setRoles(Collections.emptyList());
        CacheGroup cacheGroup = AccountContext.cacheGroup(); // 角色单独缓存
        cacheGroup.put(USER, model.getUsername(), model);
        return model;
    }

    @Override
    public void delete(Long id) {
        Optional<BaseappUser> optional = this.repository().findById(id);
        if (optional.isPresent()) {
            PersistFunctionUtils.validateDelete(optional.get());
            OperateArguments.add(optional.get().getUsername());
            repository().deleteById(id);
        } else {
            log.warn("数据不存在：[id={}]", id);
        }
    }


    @Override
    protected Specification<BaseappUser> searchSpecification(BaseappUser model, CriteriaMap attach) {
//        Long departmentId = attach.getLong(FILTER_DEPARTMENT_ID);
//        //如果未选组织树，则机构为当前用户的机构
//        if (null == departmentId || 0 == departmentId) {
//            BaseUser baseUser = AccountContext.currentUser();
//            departmentId = baseUser.getDepartmentId();
//        }
//        return SpecificationBuilder.withParameter(model, attach)
//                .specification((reference, predicate, condition) -> {
//                    CriteriaBuilder builder = reference.builder();
//                    User currentUser = AccountContext.currentUser(); // 当前登录用户
//                    // 过滤当前用户
//                    JpaConditionSupport<User> conditionSupport = (JpaConditionSupport<User>) condition;
//                    Predicate filterCurrent = conditionSupport.innerBuilder()
//                            .mapPredicate(Fields.username, currentUser.getUsername(), builder::notEqual);
//                    // 按组织机构匹配
//                    Predicate department = condition.eq(OrganizedEntity.Fields.departmentId);
//                    // 按用户名/姓名模糊搜索
//                    String[] searchAttributes = {Fields.username, Fields.nickname};
//                    Predicate[] containsKeyword = condition.contains(searchAttributes, Attach.KEYWORD);
//                    predicate.appendAnd(filterCurrent, department, predicate.mergeOr(containsKeyword));
//                }).and(DataPermissionUtils.subOrganizations(departmentId))
//                .and(DataPermissionUtils.display());
        return null;
    }

    /**
     * 初始化数据模型
     *
     * @param model 数据模型
     */
    private void initModel(BaseappUser model) {
        //密码加密
        model.setPassword(MessageDigestUtils.encrypt(SPRING_B_CRYPT, model.getPassword()));
        model.setDisplay(true);
        model.setLocked(model.getLocked());
    }

    /**
     * 校验重复项
     *
     * @param model 数据模型
     */
    private void validateModel(BaseappUser model) {
        BaseappUser user = sameOne(model);
        if (Objects.isNull(user)) {
            return;
        }
        if (Objects.equals(user.getUsername(), model.getUsername())) {
            throw ApplicationMessages.translateException(Error.ACCOUNT_REPEAT);
        }
    }

    /**
     * 查询名称相同的数据
     *
     * @param model 数据模型
     * @return 实例
     */
    private BaseappUser sameOne(BaseappUser model) {
        Optional<BaseappUser> optional = repository().findOne(SpecificationBuilder.withParameter(model)
                .specification((reference, predicate, condition) -> {
                    Predicate id = condition.notEqual(Fields.id);
                    CriteriaBuilder builder = reference.builder();
                    Root root = reference.root();
                    Predicate username = builder.equal(root.get(Fields.username), model.getUsername());
                    Predicate display = builder.equal(root.get(Data.DISPLAY), true);
                    predicate.appendAnd(id, username, display);
                }));
        return optional.orElse(null);
    }

    /**
     * 修改密码
     */
    private void updatePassword(ResetPasswordVo vo, BaseappUser user) {
        // 两次密码不一致
        String password = vo.getPassword();
        if (!Objects.equals(password, vo.getPasswordConfirm())) {
            throw ApplicationMessages.translateException(Error.SECRET_CONFIRM_FAILURE);
        }
        userCheck(password, user);

        Long id = user.getId();
        Assert.notNull(id, Error.USER_NUMBER_NOT_NULL);
        BaseappUser persist = get(id);
        // 修改个人信息不允许影响角色
        if (Objects.nonNull(persist)) {
            user.setRoles(persist.getRoles());
            user.setType(persist.getType());
        }
        repository().save(user);
    }

    /**
     * 修改密码,重置密码公共代码
     */
    private void userCheck(String password, BaseappUser user) {
        // 获取用户信息
        if (Objects.isNull(user)) {
            throw ApplicationMessages.translateException(Error.FORGET_USER_NO_FOUND);
        }
        // 新密码与旧密码一样
        if (MessageDigestUtils.matches(SPRING_B_CRYPT, password, user.getPassword())) {
            //throw ApplicationMessages.translateException(Error.SECRET_REPEAT);
        }
        // 使用BCrypt加密
        String passwordEncoded = MessageDigestUtils.encrypt(SPRING_B_CRYPT, password);
        //BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        //String passwordEncoded = bCryptPasswordEncoder.encode(password);
        user.setPassword(passwordEncoded);
        user.setLocked(user.getLocked());
    }

    /**
     * 忘记密码，重置密码
     */
    @Override
    public void forgetPassword(String username, String password) {
        BaseappUser user = this.findByUsername(username);
        userCheck(password, user);
        Long id = user.getId();
        Assert.notNull(id, Error.USER_NUMBER_NOT_NULL);
        BaseappUser persist = get(id);
        // 修改个人信息不允许影响角色
        if (Objects.nonNull(persist)) {
            user.setRoles(persist.getRoles());
            user.setType(persist.getType());
        }
        repository().save(user);
        //删除缓存中的验证码
        cache.deleteSession();
        Object obj = AccountContext.cacheGroup().get(USER, username);
        if(obj != null){
            AccountContext.cacheGroup().clear(USER, username);
        }
    }

    @Override
    public String sendVerificationCode(String username, String phone, String imageVode) {
        String code;
        //阿里云短信服务模板，后期可以根据实际需求传值
        String templateCode = "SMS_174020720";
        if (!"".equals(username) && !"".equals(phone) && !"".equals(imageVode)) {
            BaseappUser user = findByUsername(username);
            if (Objects.isNull(user)) {
                throw ApplicationMessages.translateException(Error.FORGET_USER_NO_FOUND);
            }
            if (!phone.equals(user.getPhone())) {
                throw ApplicationMessages.translateException(Error.USERNAME_NOT_MATCH_PHONE).exception();
            }
            if (cache.setImageCode().equalsIgnoreCase(imageVode)) {
                cache.deleteSession();
                code = cache.getCode();
                //短信发送接口
                try {
                    SendSmsResponse sendSmsResponse = AliyunSmsUtils
                        .sendSms(phone, code, templateCode);
                } catch (ClientException e) {
                    throw ApplicationMessages.translateException(Error.CAPTCHA_ACQUISITION_FAILED)
                            .exception();
                }
            } else {
                throw ApplicationMessages.translateException(Error.IMAGE_VERIFICATION_CODE_NOT_MATCH)
                        .exception();
            }
        } else {
            throw ApplicationMessages.translateException(Error.PLEASE_ENTER_USER_INFORMATION).exception();
        }
        return code;
    }


    @Override
    public boolean verificationMessages(String username, String phone, String imageVode,
                                        String code) {
        if (!(StringUtils.isEmpty(username) && StringUtils.isEmpty(phone) && StringUtils
                .isEmpty(imageVode) && StringUtils.isEmpty(code))) {
            BaseappUser user = findByUsername(username);
            if (Objects.isNull(user)) {
                throw ApplicationMessages.translateException(Error.FORGET_USER_NO_FOUND);
            }
            if (!phone.equals(user.getPhone())) {
                throw ApplicationMessages.translateException(Error.USERNAME_NOT_MATCH_PHONE).exception();
            }
            if (!cache.setImageCode().equalsIgnoreCase(imageVode)) {
                throw ApplicationMessages.translateException(Error.IMAGE_VERIFICATION_CODE_NOT_MATCH)
                        .exception();
            }
            if (!cache.getCode().equals(code)) {
                throw ApplicationMessages.translateException(Error.SMS_VERIFICATION_CODE_NOT_MATCH)
                        .exception();
            }
        } else {
            throw ApplicationMessages.translateException(Error.PLEASE_ENTER_USER_INFORMATION).exception();
        }
        return true;
    }

    @Override
    public String getImage() {
        String pngBase64 = null;
        //删除缓存
        cache.deleteImageCode();
        //存入缓存
        cache.setImageCode();
        //生成图片
        int width = 100;//宽
        int height = 35;//高
        try {
            pngBase64 = VerifyCodeUtils.outputImage(width, height, cache.setImageCode());
        } catch (IOException e) {
            throw ApplicationMessages.translateException(Error.IMG_NOT_FOUND).exception();
        }
        return pngBase64;
    }

    @Override
    public Integer auditUser(String date, String dateExp, String tenantId) {
        return linkappUserMapper.auditUser(date, dateExp, tenantId);
    }

    @Override
    public List<BaseappUser> selectUsersSorted(BaseappUser user) {
        return linkappUserMapper.selectUsersSorted(user);
    }

    @Override
    public List<BaseappUser> selectUsers(BaseappUser user) {
        return linkappUserMapper.selectUsers(user);
    }

    @Override
    public IPage<BaseappUser> selectUsersPage(Page page, BaseappUser user) {
        //不查超管,1管理员 2普通用户
        user.setType("2");
        List<BaseappUser> list = linkappUserMapper.selectUsersSorted(page, user);
        if(CollectionUtil.isNotEmpty(list)){
            list.stream().forEach(c->{
                //查询用户角色
                Long userId = c.getId();
                List<Role> roleList = linkappRoleMapper.selectUserRoleByUserId(userId);
                c.setRoles(roleList);

            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public void user2Roles(BaseappUser user, List<Role> roles) {
        linkappUserMapper.deleteUser2Roles(user.getId());
        for (Role role : roles) {
            UserRefRole userRefRole = new UserRefRole();
            userRefRole.setUserId(user.getId().toString());
            userRefRole.setRoleId(role.getId().toString());
            userRefRole.setId(UUID.randomUUID().toString().replaceAll("-", ""));
            userRefRole.setCreateTime(DateUtil.date());
            linkappUserMapper.insertUser2Roles(userRefRole);
        }
    }

    @Override
    public IPage<BaseappUser> selectUserByRolePage(Page page, Role role) {
        List<BaseappUser> list = linkappUserMapper.selectUserByRolePage(page, role);
        page.setRecords(list);
        return page;
    }

    /***
     * 修改用户大屏图标显示
     * @param user
     */
    @Override
    public void updateUserIsShowScreen(BaseappUser user) {
        Assert.notNull(user, Error.USER_NUMBER_NOT_NULL);
        Long id = user.getId();
        Assert.notNull(id, Error.USER_NUMBER_NOT_NULL);
        BaseappUser persist = get(id);
        if (org.springframework.util.StringUtils.isEmpty(persist)) {
            Assert.notNull(id, Error.USER_NO_FOUND);
        }
        new LambdaUpdateChainWrapper<>(linkappUserMapper)
                .set(BaseappUser::getIsShowScreen, user.getIsShowScreen())
                .eq(BaseappUser::getId, user.getId())
                .update();
    }

    @Override
    public String findAdminTypeUserNameByTenantId(String tenantId) {

        Object obj = AccountContext.cacheGroup().get("findAdminTypeUserNameByTenantId", tenantId, () -> {
            QueryWrapper<BaseappUser> qw = new QueryWrapper<>();
            qw.eq("tenant_id", tenantId);
            qw.eq("type", LinkappUserConstant.ADMIN_TYPE);
            qw.select("username");
            List<BaseappUser> list = linkappUserMapper.selectList(qw);
            if (list.size() > 0) {
                return list.get(0).getUsername();
            }
            return null;
        });

        if (obj != null) {
            return obj.toString();
        } else {
            return null;
        }
    }

    @Override
    public List<BaseappUser> findAdminTypeUserNameByTenantIds(List<String> tenantIds) {
            QueryWrapper<BaseappUser> qw = new QueryWrapper<>();
            qw.in("tenant_id", tenantIds);
            qw.eq("type", LinkappUserConstant.ADMIN_TYPE);
            qw.select("id","username","tenant_id");
            List<BaseappUser> list = linkappUserMapper.selectList(qw);
            return list;
    }

    @Override
    public void initAndCheckUser(BaseappUser user) {
        cn.hutool.core.lang.Assert.isTrue(!StringUtils.isEmpty(user.getPhone()),"用户手机号为空");
        //其实手机号可以加到原先查用户名的地方去
        String phone = user.getPhone();
        QueryWrapper<BaseappUser> qw = new QueryWrapper<>();
        qw.eq("phone",phone);
        Long id = user.getId();
        if(id != null){
            //是修改
            qw.ne("id",id);
        }else {
            //新建，设置修改时间为创建时间
            user.setModifyTime(user.getCreateTime());
        }
        int count = linkappUserMapper.selectCount(qw);
        Assert.isTrue(count == 0,"用户手机号重复");

        String userName = checkUsername();
        user.setUsername(userName);
    }

    @Override
    public IPage<Role> selectUserRolePage(Page page, BaseappUser user) {
        Long userId = user.getId();
        Assert.isTrue(userId != null,"用户id为空");

        List<Role> rolePage = linkappRoleMapper.selectUserRolePageByUserId(page,userId);
        page.setRecords(rolePage);

        return page;
    }

    @Override
    public void saveUserRoles(UserRefRoleDTO userRefRoleDTO) {
        //用户
        BaseappUser user = userRefRoleDTO.getUser();
        Assert.isTrue(user != null, "用户为空");
        Long userId = user.getId();
        Assert.isTrue(userId != null, "用户id为空");
        List<Role> roleList = userRefRoleDTO.getRoles();
        if (CollectionUtil.isNotEmpty(roleList)) {
            //就迭代处理，懒得写批量
            roleList.stream().forEach(role -> {
                QueryWrapper<UserRefRole> qw = new QueryWrapper<>();
                qw.eq("user_id", userId);
                Long roleId = role.getId();
                Long applicationId = role.getApplicationId();
                if (roleId != null){
                    qw.eq("role_id", role.getId());
                    int count = userRefRoleMapper.selectCount(qw);
                    if (count == 0) {
                        UserRefRole userRefRole = new UserRefRole();
                        userRefRole.setUserId(String.valueOf(userId));
                        userRefRole.setRoleId(String.valueOf(role.getId()));
                        userRefRole.setId(IdUtil.simpleUUID());
                        userRefRole.setCreateTime(DateUtil.date());
                        userRefRoleMapper.insert(userRefRole);
                    }
                }else{
                    if(applicationId != null){
                        //未传，就是application下的角色全选
                        QueryWrapper<Role> applicationRoleQw = new QueryWrapper<>();
                        applicationRoleQw.eq("application_id_", applicationId);
                        applicationRoleQw.select("*,role_id_ as id");
                        List<Role> applicationRoleList = linkappRoleMapper.selectList(applicationRoleQw);
                        if(CollectionUtil.isNotEmpty(applicationRoleList)){
                            applicationRoleList.stream().forEach(role2 -> {
                                qw.eq("role_id", role2.getId());
                                int count = userRefRoleMapper.selectCount(qw);
                                if (count == 0) {
                                    UserRefRole userRefRole = new UserRefRole();
                                    userRefRole.setUserId(String.valueOf(userId));
                                    userRefRole.setRoleId(String.valueOf(role2.getId()));
                                    userRefRole.setId(IdUtil.simpleUUID());
                                    userRefRole.setCreateTime(DateUtil.date());
                                    userRefRoleMapper.insert(userRefRole);
                                }
                            });
                        }
                    }
                }
            });
        }
    }

    /**
     * 创建租户管理员帐号  admin@+5位随机数字   不足5位补0
     */
    public String checkUsername() {
        boolean loop = true;
        String username = "admin@";
        while(loop) {
            int randomInt = (int)((Math.random()*99999));
            String randomNo = String.format("%5d", randomInt).replace(" ", "0");
            username = username + randomNo;
            log.info("checkUsername:" + username);
            BaseappUser linkappUser =  this.findByUsername(username);
            if(linkappUser == null) {
                loop = false;
            }else{
                username = "admin@";
            }
        }
        return username;
    }

    private BaseappUser getDefault(BaseappUser u,String applicationId) {
        initAndCheckUser(u);
//        u.setPassword("easylinkin@2020");
//        String passwordEncoded = MessageDigestUtils.encrypt(SPRING_B_CRYPT, "easylinkin@2020");
        String passwordEncoded = MessageDigestUtils.encrypt(SPRING_B_CRYPT, "hlh@123");
        u.setPassword(passwordEncoded);
        QueryWrapper<Role> qw = new QueryWrapper<>();
        qw.eq("code_", "default");
        qw.eq("application_id_", applicationId);
        qw.select("role_id_ as id","code_","name_");
        List<Role> list = linkappRoleMapper.selectList(qw);
        u.setRoles(list);
        return u;
    }

    private BaseappUser getRoleCodeUser(BaseappUser u,String roleCode) {
        initAndCheckUser(u);
        String passwordEncoded = MessageDigestUtils.encrypt(SPRING_B_CRYPT, "hlh@123");
        u.setPassword(passwordEncoded);
        QueryWrapper<Role> qw = new QueryWrapper<>();
        qw.eq("code_", roleCode);
        qw.select("role_id_ as id","code_","name_");
        List<Role> list = linkappRoleMapper.selectList(qw);
        u.setRoles(list);
        return u;
    }

    @Override
    public void addDefaultUser(BaseappUser user,String applicationId) {
        BaseappUser u = new BaseappUser();
        u.setPhone(user.getPhone());
        BaseappUser userNew = getDefault(u,applicationId);
        userNew.setNickname(user.getNickname());
        userNew.setPhone(user.getPhone());
        userNew.setUserClass(1);
        userNew.setType("2");
        List<Role> roles = new ArrayList<>(userNew.getRoles());
        userNew.setRoles(null);
        BaseappUser add = add(userNew);
        if (roles != null && roles.size() > 0) {
            user2Roles(userNew, roles);
        }
        user.setUsername(userNew.getUsername());
        user.setPassword(userNew.getPassword());
        user.setId(add.getId());

    }

    @Override
    public void addUserInRole(BaseappUser user,String roleCode) {
        BaseappUser u = new BaseappUser();
        u.setPhone(user.getPhone());
        BaseappUser userNew = getRoleCodeUser(u,roleCode);
        userNew.setNickname(user.getNickname());
        userNew.setPhone(user.getPhone());
        userNew.setUserClass(1);
        userNew.setType("2");
        List<Role> roles = new ArrayList<>(userNew.getRoles());
        userNew.setRoles(null);
        BaseappUser add = add(userNew);
        if (roles != null && roles.size() > 0) {
            user2Roles(userNew, roles);
        }
        user.setUsername(userNew.getUsername());
        user.setPassword(userNew.getPassword());
        user.setId(add.getId());
        user.setRoles(roles);
        redisUtil.hset(RedisConstant.USER_PRE, String.valueOf(user.getId()),user);
    }



    @Override
    public void delUserRoles(UserRefRoleDTO userRefRoleDTO) {
        //用户
        BaseappUser user = userRefRoleDTO.getUser();
        Assert.isTrue(user != null, "用户为空");
        Long userId = user.getId();
        Assert.isTrue(userId != null, "用户id为空");
        List<Role> roleList = userRefRoleDTO.getRoles();
        if (CollectionUtil.isNotEmpty(roleList)) {
            //就迭代处理，懒得写批量
            roleList.stream().forEach(role -> {
                QueryWrapper<UserRefRole> qw = new QueryWrapper<>();
                qw.eq("user_id", userId);
                qw.eq("role_id", role.getId());
                int delNum = userRefRoleMapper.delete(qw);
            });
        }
    }

    /**
     * 查找用户信息提供给智慧出行
     */
    @Override
    public UserDto findByUserName4Travel(String username) {
        BaseappUser baseappUser = findByUsername(username);
        baseappUser.setRoles(null);
        UserDto userDto = BeanUtil.toBean(baseappUser, UserDto.class);
        //查询用户对应角色
        List<Role> roles = linkappRoleMapper.findByUsersId(userDto.getId());
        if (CollectionUtil.isNotEmpty(roles)){
            List<RoleDTO> roleDTOS = BeanUtil.copyToList(roles, RoleDTO.class);
            userDto.setRoles(roleDTOS);
        }
        //查询用户对应的权限
        TreeSet<Privilege> treeSet = privilegeService.selectPrivilegeByUser(username, 0, 2);
        if (CollectionUtil.isNotEmpty(treeSet)){
            //只取智慧出行和车路协同
            Set<Privilege> set = treeSet.stream().filter(t ->
                            "travel_service".equals(t.getServiceCode()) || "vehicle_service".equals(t.getServiceCode()))
                    .collect(Collectors.toSet());
            List<PrivilegeDTO> privilegeDTOS = BeanUtil.copyToList(set, PrivilegeDTO.class);
            userDto.setPrivileges(privilegeDTOS);
        }
        return userDto;
    }

    /**
     * bean加载完成把user数据保存到redis
     * @param applicationReadyEvent
     */
    @Override
    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
//        allUserToRedis();
    }

    @Override
    public void allUserToRedis() {
        new Thread(() ->{
            List<BaseappUser> baseappUsers = linkappUserMapper.selectList(new QueryWrapper<>());
            baseappUsers.forEach(b->{
                //查找对应角色
                Collection<Role> roles = roleService.getByUserId(b.getId());
                b.setRoles(roles.stream().collect(Collectors.toList()));
                redisUtil.hset(RedisConstant.USER_PRE, String.valueOf(b.getId()),b);
            });
        }).start();
    }

    @Override
    public BaseappUser selectUser(Long id) {
        BaseappUser query = new BaseappUser();
        query.setId(id);
        List<BaseappUser> users = selectUsers(query);
        if(ObjectUtils.isNotEmpty(users)) {
            //查找角色对应用户
            BaseappUser baseappUser = users.get(0);
            Collection<Role> roles = roleService.getByUserId(baseappUser.getId());
            baseappUser.setRoles(roles.stream().collect(Collectors.toList()));
            return baseappUser;
        }else {
            return null;
        }
    }

    @Override
    public void updateDefaultRole(BaseappUser baseappUser, String applicationId) {
        Collection<Role> roles = roleService.getByUserId(baseappUser.getId());
        //判断是否有相关应用下的角色
        List<Role> collect = roles.stream().filter(role -> role.getApplicationId().toString().equals(applicationId)).collect(Collectors.toList());
        //如果没有相关应用的角色，则添加默认角色
        if (CollectionUtil.isEmpty(collect)) {
            //没有则添加默认角色
            QueryWrapper<Role> qw = new QueryWrapper<>();
            qw.eq("code_", "default");
            qw.eq("application_id_", applicationId);
            qw.select("role_id_ as id","code_","name_");
            List<Role> list = linkappRoleMapper.selectList(qw);
            roles.addAll(list);
            List<Role> newRoles = new ArrayList<>(roles);
            baseappUser.setRoles(newRoles);
            UserRefRoleDTO userRefRoleDTO = new UserRefRoleDTO();
            userRefRoleDTO.setUser(baseappUser);
            userRefRoleDTO.setRoles(newRoles);
            saveUserRoles(userRefRoleDTO);
        }
    }

    @Override
    public void updateRoleByCode(Long userId, String roleCode) {
        BaseappUser existUser = new BaseappUser();
        existUser.setId(userId);
        Collection<Role> roles = roleService.getByUserId(existUser.getId());
        //判断是否有相关应用下的角色
        List<Role> collect = roles.stream().filter(role -> role.getCode().equals(roleCode)).collect(Collectors.toList());
        //如果没有相关应用的角色，则添加默认角色
        if (CollectionUtil.isEmpty(collect)) {
            //没有则添加默认角色
            QueryWrapper<Role> qw = new QueryWrapper<>();
            qw.eq("code_", roleCode);
            qw.select("role_id_ as id","code_","name_");
            List<Role> list = linkappRoleMapper.selectList(qw);
            roles.addAll(list);
            List<Role> newRoles = new ArrayList<>(roles);
            existUser.setRoles(newRoles);
            UserRefRoleDTO userRefRoleDTO = new UserRefRoleDTO();
            userRefRoleDTO.setUser(existUser);
            userRefRoleDTO.setRoles(newRoles);
            saveUserRoles(userRefRoleDTO);
        }
    }
}


