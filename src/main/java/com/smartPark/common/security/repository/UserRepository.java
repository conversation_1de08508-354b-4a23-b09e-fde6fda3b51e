package com.smartPark.common.security.repository;

import com.smartPark.common.security.entity.BaseappUser;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import site.morn.boot.data.jpa.JpaRepository;

/**
 * 用户数据访问
 *
 * <AUTHOR>
 * @since 1.0.0, 2017/9/28
 */
@Repository
public interface UserRepository extends JpaRepository<BaseappUser, Long> {

  /**
   * 按用户编码查询
   *
   * @param code 用户编码
   * @return 用户
   */
	BaseappUser findByCode(String code);

  /**
   * 按用户名查询
   *
   * @param username 用户名
   * @return 用户
   */
	BaseappUser findByUsername(String username);

  /**
   * 按用户名删除
   *
   * @param username 用户名
   */
  @Modifying
  @Query("delete from BaseappUser where username = :username")
  void deleteByUsername(@Param("username") String username);

  /**
   * 查询属于该角色的用户数量
   *
   * @param roleId 角色编号
   * @return 用户数量
   */
  long countByRolesId(Long roleId);

}
