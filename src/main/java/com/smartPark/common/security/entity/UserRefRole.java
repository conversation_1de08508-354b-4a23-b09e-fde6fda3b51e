package com.smartPark.common.security.entity;



import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_user_ref_role")
public class UserRefRole  extends Model<UserRefRole> {


	@TableId(value = "id", type = IdType.INPUT)
	private String id;

	@TableField("user_id")
	private String userId;

	@TableField("role_id")
	private String roleId;
	
	private String tenantId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;
	
}
