package com.smartPark.common.security.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 重置密码视图对象
 *
 * <AUTHOR>
 * @since 1.0.0, 2019/8/26
 */
@Data
public class ResetPasswordVo {

  /**
   * 重置用户id
   */
  @ApiModelProperty("用户编号")
  @NotNull
  private Long id;

  /**
   * 新密码
   */
  @ApiModelProperty("新密码")
  @NotNull
  private String password;

  /**
   * 确认密码
   */
  @ApiModelProperty("确认密码")
  @NotNull
  private String passwordConfirm;

  /**
   * 认证密码
   */
  @ApiModelProperty("认证密码")
  @NotNull
  private String passwordAuthentication;
}
