package com.smartPark.common.security.entity;

import com.smartPark.common.utils.VerifyCodeUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Random;

@Service
@CacheConfig(cacheNames = {"getCode"})
public class Cache {

  Random random = new Random();

  /**
   * 缓存图片验证码
   *
   */
  @Cacheable(key = "'imageCode'")
  public String setImageCode() {
    //生成随机字串
    return VerifyCodeUtils.generateVerifyCode(4).toLowerCase();
  }

  /**
   * 删除图片验证码缓存
   *
   */
  @CacheEvict(key = "'imageCode'")
  public void deleteImageCode() {
    // Do nothing
  }

  @Cacheable(key = "'code_name'")
  public String getCode() {
    return String.valueOf((int) ((random.nextDouble() * 9 + 1) * 1000));
  }

  @CacheEvict(key = "'code_name'")
  public void deleteSession() {
    // Do nothing
  }
}

