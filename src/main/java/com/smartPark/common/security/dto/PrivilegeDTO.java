package com.smartPark.common.security.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 菜单权限
 * <AUTHOR>
 * @Date 2023/4/13 14:32
 */
@Data
public class PrivilegeDTO implements Serializable {

    /**
     * @serialField
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    protected Long id;

    /**
     * 父节点id
     */
    protected Long parentId;

    /**
     * 层级
     */
    protected Integer level;

    /**
     * 权限名
     */
    protected String name;

    /**
     * 类型，0:菜单目录 1:菜单 2:按钮
     */
    protected Integer type;

    /**
     * 权限码
     */
    protected String code;

    /**
     * 路径(type=1时才有值)
     */
    protected String url;

    /**
     * 排序的序号
     */
    protected Double sort;

    /**
     * 描述
     */
    protected String description;

}
