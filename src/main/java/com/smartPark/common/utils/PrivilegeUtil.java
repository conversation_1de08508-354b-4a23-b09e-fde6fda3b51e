package com.smartPark.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.security.entity.Role;
import com.smartPark.common.security.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public final class PrivilegeUtil implements ApplicationContextAware {


    static BaseUserContextProducer baseUserContextProducer;

    static RoleService roleService;

    /**
     * 根据菜单code查询是否进行数据隔离
     * 获取当前用户的角色列表，查询拥有这个菜单的角色中是否有三智管理员角色类型
     * @param privilegeCode
     * @return true 为需要隔离  false为不需要隔离
     */
    public static Boolean isDataIsolation(String privilegeCode){
        //获取当前用户
        BaseappUser current = baseUserContextProducer.getCurrent();
        if (current!=null){
            log.info("当前获取的菜单code为："+privilegeCode + " 当前用户id为："+ current.getId());
            Long userId = current.getId();
            if (userId!=null){
                Collection<Role> roles = roleService.getByUserIdAndPrivilegeCode(userId,privilegeCode);
                if (CollectionUtil.isNotEmpty(roles)){
                    List<Role> collect = roles.stream().filter(item -> item.getRoleType() != null && item.getRoleType() == 1).collect(Collectors.toList());
                    return collect.size()==0;
                }
            }
        }
        return false;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        BaseUserContextProducer baseUserContextProducer = applicationContext.getBean(BaseUserContextProducer.class);
        RoleService roleService = applicationContext.getBean(RoleService.class);
        PrivilegeUtil.baseUserContextProducer=baseUserContextProducer;
        PrivilegeUtil.roleService=roleService;
    }
}