package com.smartPark.common.utils.energy;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description 能耗统计工具
 * <AUTHOR> yuan<PERSON>
 * @Date 2023/7/20 10:21
 */
public class EnergyUtils {
    /**
     * 转page
     */
    public static IPage toPage(List recordList,Page page){
        long current = page.getCurrent();
        long size = page.getSize();
        //处理时间
        IPage resPage = new Page<>();
        //recordTimeList逆序
        resPage.setTotal(recordList.size());
        recordList = recordList.subList((int) ((current - 1) * size), (size >= recordList.size() || (current * size) >=recordList.size()) ? recordList.size() : (int) (current * size));
        resPage.setRecords(recordList);
        return resPage;
    }

    /**
     * 只计算本期
     */
    public static List<DateTime> rangeBqToList(Date startTime, Date endTime, Integer queryType){
        Date time = startTime;
        DateField dateField = getDateFieldByQueryType(queryType);
        //相差天数
        if (DateField.YEAR.equals(dateField)){
            time = DateUtil.beginOfYear(startTime);
        }else if (DateField.MONTH.equals(dateField)){
            time = DateUtil.beginOfMonth(startTime);
        }
        List<DateTime> times = DateUtil.rangeToList(time, endTime, dateField);
        return times;
    }

    /**
     * 计算相差时间(同期上期全算出来)
     * @return
     */
    public static List<EnergyDate> rangeToList(Date startTime, Date endTime, Integer queryType){
        Date time = startTime;
        DateField dateField = getDateFieldByQueryType(queryType);
        //相差天数
        int dayNum = ((int)DateUtil.between(startTime, endTime, DateUnit.DAY, false)+1);
        if (DateField.YEAR.equals(dateField)){
            time = DateUtil.beginOfYear(startTime);
        }else if (DateField.MONTH.equals(dateField)){
            time = DateUtil.beginOfMonth(startTime);
        }
        List<DateTime> times = DateUtil.rangeToList(time, endTime, dateField);

        List<EnergyDate> energyDateList = new ArrayList<>();
        //组织数据
        for (int i = 0; i < times.size(); i++) {
            DateTime dateTime = times.get(i);
            //开始时间特殊处理
            if (i==0){
                dateTime = new DateTime(startTime);
            }
            Date end;
            if (DateField.YEAR.equals(dateField)){
                end = DateUtil.endOfYear(dateTime);
            }else if (DateField.MONTH.equals(dateField)){
                end = DateUtil.endOfMonth(dateTime);
            }else if (DateField.DAY_OF_MONTH.equals(dateField)){
                end = DateUtil.endOfDay(dateTime);
            }else {
                end = DateUtil.endOfHour(dateTime);
            }
            //结束时间特殊处理
            if (i == (times.size()-1)){
                end = new DateTime(endTime);
            }
            energyDateList.add(new EnergyDate(dateTime,end,dayNum));
        }
        return energyDateList;
    }

    /**
     * 计算相差时间(同期上期全算出来)--- 时间类型不同  处理方式不同
     * @return
     */
    public static List<EnergyDate> rangeToList4Type(Date startTime, Date endTime, Integer queryType,int dayNum){
        Date time = startTime;
        DateField dateField = getDateFieldByQueryType(queryType);
        if (DateField.YEAR.equals(dateField)){
            time = DateUtil.beginOfYear(startTime);
        }else if (DateField.MONTH.equals(dateField)){
            time = DateUtil.beginOfMonth(startTime);
        }
        List<DateTime> times = DateUtil.rangeToList(time, endTime, dateField);

        List<EnergyDate> energyDateList = new ArrayList<>();
        //组织数据
        for (int i = 0; i < times.size(); i++) {
            DateTime dateTime = times.get(i);
            //开始时间特殊处理
            if (i==0){
                dateTime = new DateTime(startTime);
            }
            Date end;
            if (DateField.YEAR.equals(dateField)){
                end = DateUtil.endOfYear(dateTime);
            }else if (DateField.MONTH.equals(dateField)){
                end = DateUtil.endOfMonth(dateTime);
            }else {
                end = DateUtil.endOfDay(dateTime);
            }
            //结束时间特殊处理
            if (i == (times.size()-1)){
                end = new DateTime(endTime);
            }
            energyDateList.add(new EnergyDate(queryType,dateTime,end,dayNum));
        }
        return energyDateList;
    }

    /**
     * 获取时间类型
     * @return
     */
    private static DateField getDateFieldByQueryType(Integer queryType) {
        DateField dateField = DateField.DAY_OF_MONTH;
        switch (queryType) {
            case 1:
                dateField = DateField.HOUR_OF_DAY;
                break;
            case 2:
                dateField = DateField.DAY_OF_MONTH;
                break;
            case 3:
                dateField = DateField.MONTH;
                break;
            case 4:
                dateField = DateField.YEAR;
                break;
            default:
                break;
        }
        return dateField;
    }

    /**
     * 计算比率
     * 本期和上期或者同期的比率
     */
    public static Double calComRatio(Object bq, Object tqOrSq){
        Double part = null;
        if (null != bq && null != tqOrSq){
            Double newBq;
            Double newTqOrSq;
            if (bq instanceof Integer) {
                newBq = ((Integer) bq).doubleValue();
            }else if (bq instanceof Long) {
                newBq = ((Long) bq).doubleValue();
            }else if (bq instanceof BigDecimal){
                newBq = ((BigDecimal)bq).doubleValue();
            }else {
                newBq = (Double) bq;
            }
            if (tqOrSq instanceof Integer) {
                newTqOrSq = ((Integer) tqOrSq).doubleValue();
            }else if (tqOrSq instanceof Long){
                newTqOrSq = ((Long) tqOrSq).doubleValue();
            }else if (tqOrSq instanceof BigDecimal){
                newTqOrSq = ((BigDecimal)tqOrSq).doubleValue();
            }else {
                newTqOrSq = (Double) tqOrSq;
            }
            if (newTqOrSq == 0) {
                if (newBq==0){
                    part = 0d;
                }else {
                    part = newBq *100;
                }
            } else {
                part = (newBq - newTqOrSq) / newTqOrSq * 100;
            }
        }
        return null != part?new BigDecimal(part).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue():null;
    }

    /**
     * 计算比率
     * 本期和上期或者同期的比率
     */
    public static String calComRatioStr(Object bq, Object tqOrSq){
        Double aDouble = calComRatio(bq, tqOrSq);
        return partToString(aDouble);
    }

    /**
     * 计算百分比
     */
    public static Double calPart(Object percent, Object sum){
        Double part = null;
        if (null != percent && null != sum){
            Double newPercent;
            Double newSum;
            if (percent instanceof Integer){
                newPercent = ((Integer) percent).doubleValue();
            }else {
                newPercent = (Double) percent;
            }
            if (sum instanceof Integer){
                newSum = ((Integer) sum).doubleValue();
            }else {
                newSum = (Double) sum;
            }
            if (newSum == 0) {
                part = 0d;
            } else {
                part = newPercent/newSum * 100;
            }
        }
        return null != part?new BigDecimal(part).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue():null;
    }

    /**
     * 百分比保留两为小数 并且拼接百分号
     */
    public static String partToString(Double part){
        if (null == part){
            return StringUtils.EMPTY;
        }else if (part==0){
            return "0%";
        }else {
            return String.format("%.2f", part)+"%";
        }
    }

    /**
     * 时间转字符串
     */
    public static String timeToStr(Date time,Integer type){
        String recordTime = null;
        switch (type) {
            case 1:
                recordTime = DateUtil.format(time, "yyyy-MM-dd HH:00");
                break;
            case 2:
                recordTime = DateUtil.format(time, "yyyy-MM-dd");
                break;
            case 3:
                recordTime = DateUtil.format(time, "yyyy-MM");
                break;
            case 4:
                recordTime = DateUtil.format(time, "yyyy");
                break;
            default:
                break;
        }
        return recordTime;
    }

    /**
     * double保留两位小数
     */
    public static Double formatDouble(Double value){
        if (null == value){
            return value;
        }
        DecimalFormat format2 = new DecimalFormat("#.00");
        return Double.parseDouble(format2.format(value));
    }

    /**
     * 拼接同比字符串导出
     */
    public static String setCom4Export(String comStr){
        String result = "";
        if (StringUtils.isNotBlank(comStr)){
            if (comStr.startsWith("-")){
                String replace = comStr.replace("-", StringUtils.EMPTY);
                result = "下降"+replace;
            }else if (!"0%".equals(comStr)){
                result = "上升"+comStr;
            }
        }
        return result;
    }

    /**
     * 按不同的类型(1日2周3月4年)计算本期同期时间
     */
    public static EnergyDate getDateByType(Date date,Integer type){
        //本期
        Date bqStartTime;
        Date bqEndTime = date;
        Date sqStartTime;
        Date sqEndTime;
        if (Integer.valueOf(1).equals(type)){
            bqStartTime = DateUtil.beginOfDay(date);
            sqStartTime = DateUtil.beginOfDay(DateUtil.yesterday());
            sqEndTime =  DateUtil.offsetDay(date, -1);
        }else if (Integer.valueOf(2).equals(type)){
            bqStartTime = DateUtil.beginOfWeek(date);
            Date weekTime = DateUtil.offsetWeek(date, -1);
            sqStartTime = DateUtil.beginOfWeek(weekTime);
            sqEndTime = DateUtil.endOfDay(weekTime);
        }else if (Integer.valueOf(3).equals(type)){
            bqStartTime = DateUtil.beginOfMonth(date);
            Date monthTime = DateUtil.offsetMonth(date, -1);
            sqStartTime = DateUtil.beginOfMonth(monthTime);
            sqEndTime = DateUtil.endOfDay(monthTime);
        }else {
            bqStartTime = DateUtil.beginOfYear(date);
            Date yearTime = DateUtil.offset(date, DateField.YEAR,-1);
            sqStartTime = DateUtil.beginOfYear(yearTime);
            sqEndTime = DateUtil.endOfDay(yearTime);
        }
        EnergyDate energyDate = new EnergyDate();
        energyDate.setBqStartTime(bqStartTime);
        energyDate.setBqEndTime(bqEndTime);
        energyDate.setSqStartTime(sqStartTime);
        energyDate.setSqEndTime(sqEndTime);
        return energyDate;
    }
}
