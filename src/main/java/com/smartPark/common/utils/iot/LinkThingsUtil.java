package com.smartPark.common.utils.iot;

import java.security.MessageDigest;

/**
 * linkthings 工具
 */
public class LinkThingsUtil {

    private String apiSecret;

    private String apiKey;

    private String nonce;
    private String timestamp;
    private String signature;


    public LinkThingsUtil(String apiKey, String apiSecret) {
        this.apiKey = apiKey;
        this.apiSecret = apiSecret;
        this.update();
    }

    private String nonceGen() {
        String res = "";
        for (int i = 0; i < 8; i++) {
            res += (int) (Math.random() * 10);
        }
        return res;
    }

    private static String getSha1(String str) {
        char[] hexDigits = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));
            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] buf = new char[j * 2];
            int k = 0;

            for (int i = 0; i < j; ++i) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 15];
                buf[k++] = hexDigits[byte0 & 15];
            }

            return new String(buf);
        } catch (Exception var9) {
            return null;
        }
    }

    public void update() {
        nonce = nonceGen();
        timestamp = System.currentTimeMillis() + "";
        signature = getSha1(nonce + timestamp + apiSecret);
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
