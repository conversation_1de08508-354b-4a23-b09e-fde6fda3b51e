package com.smartPark.common.translate;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;

import com.smartPark.business.baseApplication.mapper.BaseApplicationMapper;
import com.smartPark.common.entity.BaseApplication;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy(false)
public class BaseApplicationTranslate implements CodeTranslateor {

	@Autowired
	private BaseApplicationMapper baseApplicationMapper;

	private static final TimedCache<Long, String> dictCache =
			CacheUtil.newTimedCache(TimeUnit.SECONDS.toMillis(60));
	
	@PostConstruct
	public void init() {
		CodeTranslateorFactory.register(this.getClass(), this);
	}

	@Override
	public String translate(Object obj, Object value, Code2Text ann) {
		if (null != value){
			Long id;
			if (value instanceof Long) {
				id = (Long) value;
			} else {
				id = Long.valueOf(value.toString());
			}
			String applicationName = dictCache.get(id, false);
			if (StringUtils.isBlank(applicationName)){
				BaseApplication baseApplication = baseApplicationMapper.selectById(id);
				if (null != baseApplication){
					applicationName = baseApplication.getName();
					dictCache.put(id,applicationName);
				}
			}
			return applicationName;
		}
		return null;
	}
}
