package com.smartPark.common.translate;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.smartPark.common.alarm.entity.EventType;
import com.smartPark.common.alarm.mapper.EventTypeMapper;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy(false)
public class EventTypeTranslator implements CodeTranslateor {

	@Resource
	private EventTypeMapper eventTypeMapper;

	private static final TimedCache<String, String> dictCache = CacheUtil
			.newTimedCache(TimeUnit.SECONDS.toMillis(30));

	@PostConstruct
	public void init() {
		CodeTranslateorFactory.register(this.getClass(), this);
	}

	public static void updateMap(String id) {
		dictCache.remove(id);
	}

	public static TimedCache<String, String> getCache() {
		return dictCache;
	}

	public String get(String id) {
		String name = dictCache.get(id, false);
		if (name == null) {
			EventType p = eventTypeMapper.selectById(id);
			if (p == null) {
				return null;
			}
			name = p.getName();
			if(p.getType() == 1){
				String[] split = p.getFullPathName().split("/");
				if(split.length == 2){
					String parentName = split[0];
					String sonName = split[1];
					name = sonName + "（"+ parentName +"）"; // 智慧畅行（园区畅行）
				}
			}
			dictCache.put(id, name);
		}
		return name;
	}

	@Override
	public String translate(Object obj, Object value, Code2Text ann) {
		if (null == value) {
			return null;
		}
		String name = get(value.toString());
		return name;
	}

}
