package com.smartPark.business.ruleEngine.mapper;

import com.smartPark.business.ruleEngine.entity.RuleTrigger;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smartPark.common.entity.device.Device;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 规则触发器 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface RuleTriggerMapper extends BaseMapper<RuleTrigger> {

}
