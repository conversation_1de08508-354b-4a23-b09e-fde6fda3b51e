package com.smartPark.business.ruleEngine.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.smartPark.common.alarm.entity.EventType;
import com.smartPark.common.translate.Code2Text;
import com.smartPark.common.translate.EventTypeTranslator;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 规则引擎
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_rule_engine")
public class RuleEngine extends Model<RuleEngine> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 规则编号 系统生成的唯一标识码，系统自动生成，GZGJ+8位序号
     */
    @TableField("code_")
    private String code;

    /**
     * 规则名称
     */
    @TableField("name_")
    private String name;

    /**
     * 大类code 如0801 智慧畅行（园区畅行）来自 事件类型表小类代码
     */
    @Code2Text(translateor = EventTypeTranslator.class)
    @TableField("model_code_")
    private String modelCode;

    /**
     * 细分小类code 如080101 违规使用远光灯告警 来自 事件类型表细分小类代码
     */
    @Code2Text(translateor = EventTypeTranslator.class)
    @TableField("event_code_")
    private String eventCode;

    /**
     * 告警等级 1-紧急告警 2-重要告警 3-次要告警 4-提示告警
     */
    @TableField("alarm_level_")
    private Integer alarmLevel;

    /**
     * 状态 0-停用 1-启用
     */
    @TableField("status_")
    private Integer status;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 沉默周期
     */
    @TableField("stop_cycle_")
    private Integer stopCycle;

    /**
     * 连续触发阈值
     */
    @TableField("threshold_")
    private Integer threshold;

    /**
     * 停用时长值
     */
    @TableField("stop_value_")
    private Integer stopValue;

    /**
     * 停用单位(1小时2日3月4年)
     */
    @TableField(value = "stop_unit_", updateStrategy = FieldStrategy.IGNORED)
    private Integer stopUnit;

    /**
     * 消息类型 1站内消息 2短信 支持多选 逗号隔开
     */
    @TableField("message_type")
    private String messageType;

    /**
     * 通知人类型 0按角色 1按用户
     */
    @TableField("notification_type")
    private Integer notificationType;

    /**
     * 触发器
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "触发器", example = "")
    private List<RuleTrigger> ruleTriggers;


    /**
     * id集合
     */
    @TableField(exist = false)
    private List<Long> ids;

    /**
     * 当前规则所属模块id
     */
    @TableField(exist = false)
    private Integer modelId;

    /**
     * 通知人关联id
     */
    @TableField(exist = false)
    private List<String> notificationIds;

    /**
     * 事件详情
     */
    @TableField(exist = false)
    private EventType eventType;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
