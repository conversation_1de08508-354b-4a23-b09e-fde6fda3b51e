package com.smartPark.business.ruleEngine.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 规则联动配置关联设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("baes_rule_trigger_ref_device")
public class RuleTriggerRefDevice extends Model<RuleTriggerRefDevice> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 触发器id
     */
    @TableField("rule_trigger_id")
    private Long ruleTriggerId;

    /**
     * 设备编号
     */
    @TableField("device_code")
    private String deviceCode;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
