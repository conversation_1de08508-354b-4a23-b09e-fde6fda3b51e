package com.smartPark.business.rainfallMonitoring.device.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 排水控制-湖渠监测-统计表
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_drainage_rain_monitoring_statistics")
public class DrainageRainMonitoringStatistic extends Model<DrainageRainMonitoringStatistic> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 物联网平台设备id
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 统计日期
     */
    @TableField("record_time_")
    private Date recordTime;

    /**
     * 累计降雨量（一整天的）
     */
    @TableField("rainfall_")
    private Double rainfall;

    /**
     * 最低水位
     */
    @TableField("liquid_level_value_min_")
    private Double liquidLevelValueMin;

    /**
     * 平均水位
     */
    @TableField("liquid_level_value_avg_")
    private Double liquidLevelValueAvg;

    /**
     * 最高水位
     */
    @TableField("liquid_level_value_max_")
    private Double liquidLevelValueMax;

    /**
     * 上报次数
     */
    @TableField("flow_count_")
    private Long flowCount;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }


    public Double computeLiquidLevelValueSum() {
        return this.getLiquidLevelValueAvg()*this.getFlowCount();
    }


}
