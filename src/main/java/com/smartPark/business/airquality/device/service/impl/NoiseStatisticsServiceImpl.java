package com.smartPark.business.airquality.device.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smartPark.business.airquality.device.constant.AirQualityDeviceConstant;
import com.smartPark.business.airquality.device.entity.NoiseStatistics;
import com.smartPark.business.airquality.device.entity.vo.NoiseCountDTO;
import com.smartPark.business.airquality.device.mapper.NoiseStatisticsMapper;
import com.smartPark.business.airquality.device.service.AirEnvAnalysisService;
import com.smartPark.business.airquality.device.service.NoiseStatisticsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.noise.device.entity.vo.NoiseDeviceVo;
import com.smartPark.business.noise.device.service.NoiseDeviceService;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.entity.device.DeviceStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 噪声统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Service
public class NoiseStatisticsServiceImpl extends ServiceImpl<NoiseStatisticsMapper, NoiseStatistics> implements NoiseStatisticsService {

    @Autowired
    private AirEnvAnalysisService airEnvAnalysisService;
    @Autowired
    private DeviceStatusMapper deviceStatusMapper;
    @Autowired
    private NoiseDeviceService noiseDeviceService;

    /**
     * 汇总统计
     */
    @Override
    public NoiseCountDTO getSummary() {
        NoiseCountDTO noiseCountDTO = new NoiseCountDTO();
        Date now = new Date();
        //当前开始时间
        DateTime startTime = DateUtil.beginOfDay(now);
        //当前结束时间
        DateTime endTime = DateUtil.endOfDay(now);
        NoiseStatistics noiseStatistics = airEnvAnalysisService.getNoiseStatistics(startTime, endTime);

        if (noiseStatistics != null) {
            noiseCountDTO.setNoiseAvg(noiseStatistics.getNoise());
            noiseCountDTO.setNoiseMax(noiseStatistics.getNoiseMax());
            noiseCountDTO.setNoiseMin(noiseStatistics.getNoiseMin());
        }else {
            noiseCountDTO.setNoiseAvg(0.0);
            noiseCountDTO.setNoiseMax(0.0);
            noiseCountDTO.setNoiseMin(0.0);
        }
        //查询最新的的噪音数据
        // 1. 查询设备列表
        NoiseDeviceVo requestModel = new NoiseDeviceVo();
        requestModel.setDeviceUnitCode(AirQualityDeviceConstant.DeviceUnitCodeEnum.ZS_DEVICE_UNIT.getDeviceUnitCode());
        List<NoiseDeviceVo> record = noiseDeviceService.queryList(requestModel);
        List<String> deviceCodes = record.stream().map(NoiseDeviceVo::getDeviceCode).collect(Collectors.toList());
        List<DeviceStatus> deviceStatuses = deviceStatusMapper.selectList(new LambdaQueryWrapper<DeviceStatus>()
                .eq(DeviceStatus::getProp, AirQualityDeviceConstant.NOISE)
                .in(CollectionUtil.isNotEmpty(deviceCodes),DeviceStatus::getDeviceCode, deviceCodes)
                .orderByDesc(DeviceStatus::getModifyTime)
                .last("limit 1"));
        if (CollectionUtil.isNotEmpty(deviceStatuses)) {
            DeviceStatus deviceStatus = deviceStatuses.get(0);
            noiseCountDTO.setNoise(StringUtils.isNotBlank(deviceStatus.getValue())?Double.parseDouble(deviceStatus.getValue()):0.0);
        }else {
            noiseCountDTO.setNoise(0.0);
        }
        return noiseCountDTO;
    }
}
