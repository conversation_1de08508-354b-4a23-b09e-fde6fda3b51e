package com.smartPark.business.airquality.device.entity.dto;

import com.smartPark.business.airquality.device.entity.AirEnvironmentDayStatistics;
import com.smartPark.business.airquality.statistics.entity.AirEnvironmentQualityStatistics;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AirEnvironmentDayStatisticsDTO extends AirEnvironmentDayStatistics {

    /**
     * 属性详情
     */
    private List<AirEnvironmentQualityStatistics> propStatisticsList;
}
