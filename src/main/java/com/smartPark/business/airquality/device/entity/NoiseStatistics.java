package com.smartPark.business.airquality.device.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 噪声统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_noise_statistics")
public class NoiseStatistics extends Model<NoiseStatistics> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期
     */
    @TableField("record_time_")
    private Date recordTime;

    /**
     * 平均噪声
     */
    @TableField("noise_")
    private Double noise;

    /**
     * 最大噪声
     */
    @TableField("noise_max_")
    private Double noiseMax;

    /**
     * 上报次数
     */
    @TableField("flow_count_")
    private Long flowCount;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;


    /**
     * 最小噪声
     */
    @TableField(exist = false)
    private Double noiseMin;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
