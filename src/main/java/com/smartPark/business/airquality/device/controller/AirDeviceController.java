package com.smartPark.business.airquality.device.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.airquality.device.constant.AirQualityDeviceConstant.DeviceUnitCodeEnum;
import com.smartPark.business.airquality.device.entity.AirQualityDevice;
import com.smartPark.business.airquality.device.entity.vo.AirQualityDeviceDeviceDTO;
import com.smartPark.business.airquality.device.entity.vo.AirQualityDeviceVo;
import com.smartPark.business.airquality.device.service.AirQualityDeviceService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.entity.device.dto.DeviceObjInfoDevicePropertyStatusListInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;

/**
 * 空气质量/设备管理
 * @Description: 空气质量/设备管理
 */
@RestController
@RequestMapping("airQualityDevice")
@Api(tags = "空气质量设备管理")
public class AirDeviceController {
  
  @Autowired
  private AirQualityDeviceService airQualityDeviceService;

  /**
   * @Description: 查询设备信息
   * <AUTHOR> @date
   */
  @GetMapping("device/{deviceCode}")
  @ApiOperation("根据设备编码查询设备信息")
  public RestMessage findDeviceByDeviceId(@PathVariable("deviceCode") String deviceCode){
    //参数验证
    Assert.hasLength(deviceCode,"设备编码不能为空");
    AirQualityDeviceDeviceDTO gardenSoilDeviceDeviceDTO = airQualityDeviceService.findDeviceByDeviceId(deviceCode,true);
    return RestBuilders.successBuilder().data(gardenSoilDeviceDeviceDTO).build();
  }

  /**
   * @Description: 增加空气质量设备
   * <AUTHOR> @date
   */
  @PostMapping
  @ApiOperation("增加空气质量设备")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "environmentMonitoring:airQuality:deviceManage:add", desc = "增加空气质量设备")
  public RestMessage insert(@RequestBody List<AirQualityDevice> gardenSoilDevice){
    //参数验证
    airQualityDeviceService.insert(gardenSoilDevice);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除空气质量设备（包含批量删除）
   */
  @DeleteMapping
  @ApiOperation("删除空气质量设备（包含批量删除）")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "environmentMonitoring:airQuality:deviceManage:batchDelete", desc = "删除空气质量设备")
  public RestMessage delBatch(@RequestBody AirQualityDevice gardenSoilDevice){
    Assert.notEmpty(gardenSoilDevice.getIds(),"id不能为空");
    airQualityDeviceService.delBatch(gardenSoilDevice.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑空气质量设备
   * <AUTHOR> @date
   */
  @PutMapping
  @ApiOperation("编辑空气质量设备")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "environmentMonitoring:airQuality:deviceManage:edit", desc = "编辑空气质量设备")
  public RestMessage updateById(@RequestBody AirQualityDevice gardenSoilDevice){
    Assert.notNull(gardenSoilDevice.getId(),"id不能为空");
    airQualityDeviceService.updateOne(gardenSoilDevice);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询空气质量设备详情
   * <AUTHOR> @date
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询空气质量设备详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    AirQualityDeviceDeviceDTO GardenSoilDeviceDeviceDTO = airQualityDeviceService.findById(id);
    return RestBuilders.successBuilder().data(GardenSoilDeviceDeviceDTO).build();
  }


  /**
   * @Description: 根据id查询空气质量设备详情
   * <AUTHOR> @date
   */
  @GetMapping("getDeviceObjInfoDevicePropertyStatusListInfo")
  @ApiOperation("根据id查询空气质量设备详情")
  public RestMessage getDeviceObjInfoDevicePropertyStatusListInfo(@RequestParam("deviceCode") String deviceCode) {
    Assert.notNull(deviceCode, "deviceCode 不能为空");
    DeviceObjInfoDevicePropertyStatusListInfo GardenSoilDeviceDeviceDTO = airQualityDeviceService.getDeviceObjInfoDevicePropertyStatusListInfo(deviceCode);
    return RestBuilders.successBuilder().data(GardenSoilDeviceDeviceDTO).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> @date
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<AirQualityDeviceVo> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<AirQualityDeviceVo> record =  airQualityDeviceService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  @PostMapping("listAirQualityDevice")
  @ApiOperation("查询空气质量设备列表")
  public RestMessage queryAirQualityDeviceList(){
    AirQualityDeviceVo requestModel = new AirQualityDeviceVo();
    requestModel.setDeviceUnitCode(DeviceUnitCodeEnum.KQZL_DEVICE_UNIT.getDeviceUnitCode());
    List<AirQualityDeviceVo> record = airQualityDeviceService.queryList(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  // 查询空气质量设备属性值
  @GetMapping("getDevicePropertyStatusListByDeviceId/{id}")
  @ApiOperation("查询空气质量设备属性值")
  public RestMessage getDevicePropertyStatusListByDeviceId(@PathVariable("id")Long id){
    Assert.notNull(id,"id不能为空");
    List<DevicePropertyStatus> devicePropertyStatusList = airQualityDeviceService.getDevicePropertyStatusListByDeviceId(id);
    return RestBuilders.successBuilder().data(devicePropertyStatusList).build();
  }

  @GetMapping("queryListAndDevicePropertyStatusList")
  @ApiOperation("查询所有空气质量设备列表和属性值")
  public RestMessage queryListAndDevicePropertyStatusList(){
    List<AirQualityDeviceDeviceDTO> airQualityDeviceVoList = airQualityDeviceService.queryListAndDevicePropertyStatusList();
    return RestBuilders.successBuilder().data(airQualityDeviceVoList).build();
  }

}

