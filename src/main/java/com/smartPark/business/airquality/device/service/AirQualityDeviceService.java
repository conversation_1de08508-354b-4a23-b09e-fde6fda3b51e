package com.smartPark.business.airquality.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.airquality.device.entity.vo.AirQualityDeviceVo;
import com.smartPark.business.airquality.device.entity.AirQualityDevice;
import com.smartPark.business.airquality.device.entity.vo.AirQualityDeviceDeviceDTO;
import com.smartPark.business.airquality.device.entity.vo.AirUnitDeviceNodeVo;
import com.smartPark.business.meteorological.device.entity.vo.MeteorologicalDeviceDTO;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.entity.device.dto.DeviceObjInfoDevicePropertyStatusListInfo;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 园林土壤设备表 服务类
 * </p>
 *
 * <AUTHOR> @since
 */
public interface AirQualityDeviceService extends IService<AirQualityDevice> {

  IPage<AirQualityDeviceVo> queryListByPage(RequestModel<AirQualityDeviceVo> requestModel);


  void insert(List<AirQualityDevice> gardenSoilDevice);

  AirQualityDeviceDeviceDTO findDeviceByDeviceId(String deviceCode, Boolean flag);

  void delBatch(Set<Long> ids);

  void updateOne(AirQualityDevice gardenSoilDevice);

  AirQualityDeviceDeviceDTO findById(Long id);

  DeviceObjInfoDevicePropertyStatusListInfo getDeviceObjInfoDevicePropertyStatusListInfo(
      String deviceCode);

  DeviceExtendInfo getDeviceExtendInfos(String deviceCode, Boolean flag);

  List<DevicePropertyStatus> getDevicePropertyStatusListByDeviceId(Long id);

  List<AirQualityDeviceDeviceDTO> queryListAndDevicePropertyStatusList();

  List<AirQualityDeviceVo> queryList(AirQualityDeviceVo requestModel);

  /**
   * 查询型号设备转换成树型元素
   *
   * @param requestModel 请求参数
   * @return 树型元素page
   */
  List<AirUnitDeviceNodeVo> queryUnitDeviceTreeData(RequestModel<AirQualityDeviceVo> requestModel);
}
