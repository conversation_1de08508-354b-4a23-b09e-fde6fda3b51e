package com.smartPark.business.airquality.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smartPark.business.airquality.device.entity.PmStatistics;
import com.smartPark.business.airquality.device.entity.vo.AirEnvAnalysisDTO;
import com.smartPark.business.airquality.device.entity.vo.PmCountDTO;
import java.util.List;

/**
 * <p>
 * pm统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface PmStatisticsMapper extends BaseMapper<PmStatistics> {

  List<PmCountDTO> getPmTrend(AirEnvAnalysisDTO airEnvAnalysisDTO);
}
