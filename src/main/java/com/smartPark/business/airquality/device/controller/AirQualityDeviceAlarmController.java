package com.smartPark.business.airquality.device.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.airquality.device.entity.vo.AirQualityDeviceAlarmVo;
import com.smartPark.business.airquality.device.service.AirQualityDeviceAlarmService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 空气质量设备/空气质量设备告警
 * @Description 空气质量设备/空气质量设备告警
 * <AUTHOR> @Date 
 */
@RestController
@RequestMapping("airQualityDeviceAlarm")
@Api(tags = "空气质量设备告警")
public class AirQualityDeviceAlarmController {
    @Autowired
    private AirQualityDeviceAlarmService airQualityDeviceAlarmService;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR> @date
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<AirQualityDeviceAlarmVo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<AirQualityDeviceAlarmVo> record =  airQualityDeviceAlarmService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 根据id查询单条设备告警详情
     * <AUTHOR> @date
     */
    @GetMapping("{id}")
    @ApiOperation("根据id查询单条设备告警详情")
    public RestMessage findById(@PathVariable("id") Long id){
        Assert.notNull(id, "id 不能为空");
        AirQualityDeviceAlarmVo AirQualityDeviceAlarmVo =  airQualityDeviceAlarmService.findById(id);
        return RestBuilders.successBuilder().data(AirQualityDeviceAlarmVo).build();
    }

}
