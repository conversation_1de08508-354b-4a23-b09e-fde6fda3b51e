package com.smartPark.business.airquality.statistics.entity.dto.excel;

import lombok.Data;

/**
 * @Description 空气质量指数导出实体
 * <AUTHOR> <PERSON><PERSON>
 * @Date 2023/6/29 16:48
 */
@Data
public class QualityStatisticsExcelDTO {
    /**
     * 区间数组
     */
    private String[] ranges = {"","[0,50]","[51,100]","[101,150]","[151,200]","[201,300]",">300"};
    /**
     * 等级数组
     */
    private String[] levels = {"","一级","二级","三级","四级","五级","六级"};
    /**
     * 类别数组
     */
    private String[] types = {"","优","良","轻度污染","中度污染","重度污染","严重污染"};
    /**
     * 时间字符串
     */
    private String recordTimeStr;

    /**
     * 本期空气质量指数
     */
    private Double bqAqi;

    /**
     * 本期所属区间
     */
    private String bqRange;

    /**
     * 本期空气质量等级
     */
    private String bqLevel;

    /**
     * 本期空气质量指数类别
     */
    private String bqType;

    /**
     * 本期首要污染物
     */
    private String bqTopPollutePropKey;

    /**
     * 本期超标污染物
     */
    private String bqExceedsPollutePropKeys;

    /**
     * 同期空气质量指数
     */
    private Double tqAqi;

    /**
     * 同期所属区间
     */
    private String tqRange;

    /**
     * 同期空气质量等级
     */
    private String tqLevel;

    /**
     * 同期空气质量指数类别
     */
    private String tqType;

    /**
     * 同期首要污染物
     */
    private String tqTopPollutePropKey;

    /**
     * 同期超标污染物
     */
    private String tqExceedsPollutePropKeys;

    /**
     * 上期空气质量指数
     */
    private Double sqAqi;

    /**
     * 上期所属区间
     */
    private String sqRange;

    /**
     * 上期空气质量等级
     */
    private String sqLevel;

    /**
     * 上期空气质量指数类别
     */
    private String sqType;

    /**
     * 上期首要污染物
     */
    private String sqTopPollutePropKey;

    /**
     * 上期超标污染物
     */
    private String sqExceedsPollutePropKeys;

    /**
     * 设置区间，等级，类别
     */
    public void compute(){
        //本期
        Integer byAqi = getByAqi(bqAqi);
        bqRange = ranges[byAqi];
        bqLevel = levels[byAqi];
        bqType = types[byAqi];
        //同期
        Integer tyAqi = getByAqi(tqAqi);
        tqRange = ranges[tyAqi];
        tqLevel = levels[tyAqi];
        tqType = types[tyAqi];
        //上期
        Integer syAqi = getByAqi(sqAqi);
        sqRange = ranges[syAqi];
        sqLevel = levels[syAqi];
        sqType = types[syAqi];
    }

    /**
     * 计算所属区间，等级和类别
     */
    public Integer getByAqi(Double aqi) {
        //本期
        if (null != aqi){
            //计算区间
            if (aqi>=0 && aqi<=50){
                return 1;
            }else if (aqi>50 && aqi<=100){
                return 2;
            }else if (aqi>100 && aqi<=150){
                return 3;
            }else if (aqi>150 && aqi<=200){
                return 4;
            }else if (aqi>200 && aqi<=300){
                return 5;
            }else {
                return 6;
            }
        }else {
            return 0;
        }
    }
}
