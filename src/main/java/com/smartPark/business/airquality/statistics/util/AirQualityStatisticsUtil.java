package com.smartPark.business.airquality.statistics.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.smartPark.business.airquality.device.constant.AirQualityDeviceConstant;
import com.smartPark.business.airquality.device.entity.AirEnvironmentDayStatistics;
import com.smartPark.business.airquality.device.entity.AirEnvironmentHourStatistics;
import com.smartPark.business.airquality.device.entity.dto.AirEnvironmentDayStatisticsDTO;
import com.smartPark.business.airquality.device.entity.dto.AirEnvironmentHourStatisticsDTO;
import com.smartPark.business.airquality.statistics.entity.AirEnvironmentQualityStatistics;
import com.smartPark.business.airquality.statistics.entity.vo.AirEnvQualityQueryVo;
import com.smartPark.business.airquality.statistics.entity.vo.AirEnvQualityReachVo;
import com.smartPark.business.airquality.statistics.entity.vo.AirEnvQualityResultVo;
import com.smartPark.business.airquality.statistics.entity.vo.AirEnvQualitySummaryVo;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 空气质量统计工具类
 * 本来不想写这个类的，现在感觉很多计算没有收拢，方法不宜写的太过聚合，但是serviceImp已经写了太多了，自己都有点昏头了，所以还是写一个工具类吧
 *
 * <AUTHOR>
 */
public class AirQualityStatisticsUtil {
    /**
     * 获取属性值
     * @param queryType 查询类型
     * @param p 属性编码
     * @param statisticsObj 统计对象
     * @return 属性值
     */
    public static Double getPropBy(String queryType, String p, Object statisticsObj) {
        if (statisticsObj == null) {
            return null;
        }
        double prop = 0;
        if (statisticsObj instanceof AirEnvironmentDayStatistics) {
            AirEnvironmentDayStatistics dayStatistics = (AirEnvironmentDayStatistics) statisticsObj;
            switch (p) {
                case "co":
                    if (dayStatistics != null && dayStatistics.getCo() != null) {
                        prop = dayStatistics.getCo();
                    }
                    break;
                case "no2":
                    if (dayStatistics != null && dayStatistics.getNo2() != null) {
                        prop = dayStatistics.getNo2();
                    }
                    break;
                case "so2":
                    if (dayStatistics != null && dayStatistics.getSo2() != null) {
                        prop = dayStatistics.getSo2();
                    }

                    break;
                case "o3":
                    if (dayStatistics != null && dayStatistics.getO3OneHourMax() != null) {
                        prop = dayStatistics.getO3OneHourMax();
                    }

                    break;
                case "pm_10":
                    if (dayStatistics != null && dayStatistics.getPm10() != null) {
                        prop = dayStatistics.getPm10();
                    }

                    break;
                case "pm_25":
                    if (dayStatistics != null && dayStatistics.getPm25() != null) {
                        prop = dayStatistics.getPm25();
                    }
                    break;
                default:
                    break;
            }
        }
        if (statisticsObj instanceof AirEnvironmentHourStatistics) {
            AirEnvironmentHourStatistics dayStatistics = (AirEnvironmentHourStatistics) statisticsObj;
            switch (p) {
                case "co":
                    if (dayStatistics != null && dayStatistics.getCo() != null) {
                        prop = dayStatistics.getCo();
                    }
                    break;
                case "no2":
                    if (dayStatistics != null && dayStatistics.getNo2() != null) {
                        prop = dayStatistics.getNo2();
                    }
                    break;
                case "so2":
                    if (dayStatistics != null && dayStatistics.getSo2() != null) {
                        prop = dayStatistics.getSo2();
                    }

                    break;
                case "o3":
                    if (dayStatistics != null && dayStatistics.getO3() != null) {
                        prop = dayStatistics.getO3();
                    }

                    break;
                case "pm_10":
                    if (dayStatistics != null && dayStatistics.getPm10() != null) {
                        prop = dayStatistics.getPm10();
                    }

                    break;
                case "pm_25":
                    if (dayStatistics != null && dayStatistics.getPm25() != null) {
                        prop = dayStatistics.getPm25();
                    }
                    break;
                default:
                    break;
            }
        }
        return prop;
    }

    /**
     * 获取时间区间（利用反射）
     *
     * @param startTime
     * @param endTime
     * @return
     * @Param object
     */
    public static Map<String, Date> getDateRangeByQueryType(Object object, Date startTime, Date endTime) {
        //相差天数
        long btDays = DateUtil.between(startTime, endTime, DateUnit.DAY, false) + 1L;

        //上期 （开始时间为结束时间，往前推对应天数）
        Date sqStartDate = DateUtil.offsetDay(startTime, -(int) btDays);
        Date sqEndDate = DateUtil.offsetDay(endTime, -(int) btDays);
        //同期 （去年）
        Date tqStartDate = DateUtil.offset(startTime, DateField.YEAR, -1);
        Date tqEndDate = DateUtil.offset(endTime, DateField.YEAR, -1);
        if (null != object) {
            setFieldValue(object, "bqStartTime", startTime);
            setFieldValue(object, "bqEndTime", endTime);

            setFieldValue(object, "sqStartTime", sqStartDate);
            setFieldValue(object, "sqEndTime", sqEndDate);

            setFieldValue(object, "tqStartTime", tqStartDate);
            setFieldValue(object, "tqEndTime", tqEndDate);
            return null;
        } else {
            Map<String, Date> map = new HashMap<>();
            map.put("bqStartDate", startTime);
            map.put("bqEndDate", endTime);

            map.put("sqStartDate", sqStartDate);
            map.put("sqEndDate", sqEndDate);

            map.put("tqStartDate", tqStartDate);
            map.put("tqEndDate", tqEndDate);
            return map;
        }
    }

    private static void setFieldValue(Object obj, String fieldName, Object value) {
        if (ReflectUtil.hasField(obj.getClass(), fieldName)) {
            ReflectUtil.setFieldValue(obj, fieldName, value);
        }
    }

    /**
     * @param queryType
     * @return
     */
    public static DateField getDateFieldByQueryType(String queryType) {
        DateField dateField = DateField.DAY_OF_MONTH;
        switch (queryType) {
            case "hour":
                dateField = DateField.HOUR_OF_DAY;
                break;
            case "day":
                dateField = DateField.DAY_OF_MONTH;
                break;
            case "month":
                dateField = DateField.MONTH;
                break;
            case "year":
                dateField = DateField.YEAR;
                break;
            default:
                break;
        }
        return dateField;
    }

    /**
     * @param startTime
     * @param dateField
     * @return
     */
    public static String getRecordTimeBy(Date startTime, DateField dateField) {
        String recordTime = DateUtil.format(startTime, "yyyy-MM-dd");
        switch (dateField) {
            case HOUR_OF_DAY:
                recordTime = DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss");
                break;
            case DAY_OF_MONTH:
                recordTime = DateUtil.format(startTime, "yyyy-MM-dd");
                break;
            default:
                break;
        }
        return recordTime;
    }

    /**
     * 获取补全的统计对象
     * @param starTime 开始时间
     * @param queryType 统计类型:day日统计，hour小时统计
     * @return 统计对象
     */
    public static Object getCompStatisticsBy(Date starTime, String queryType) {
        Object statisticsObj = null;
        if ("day".equals(queryType)) {
            AirEnvironmentDayStatistics dayStatistics = new AirEnvironmentDayStatistics();
            dayStatistics.setRecordTime(starTime);
            statisticsObj = dayStatistics;
        }
        if ("hour".equals(queryType)) {
            AirEnvironmentHourStatistics hourStatistics = new AirEnvironmentHourStatistics();
            hourStatistics.setRecordTime(starTime);
            statisticsObj = hourStatistics;
        }
        return statisticsObj;
    }

    /**
     * 获取偏移时间
     * @param starTime 开始时间
     * @param betDays 相差天数
     * @param tq 期别：sq上期，bq本期，tq同期
     * @return 偏移时间
     */
    public static Date getOffsetRecordTimeBy(Date starTime, Integer betDays, String tq) {
        Date offsetTime = starTime;
        switch (tq) {
            case "sq":
                offsetTime = DateUtil.beginOfHour(DateUtil.offset(starTime, DateField.DAY_OF_MONTH, -betDays));
                break;
            case "bq":
                break;
            case "tq":
                offsetTime = DateUtil.beginOfHour(DateUtil.offset(starTime, DateField.YEAR, -1));
                break;
            default:
                break;
        }
        return offsetTime;
    }

    /**
     * 根据基础时间，类型等获取记录时间
     *
     * @param startTime
     * @param dateField
     * @param queryType
     * @return
     */
    public static String getRecordTimeBy(Date startTime, DateField dateField, String queryType) {
        String recordTime = DateUtil.format(startTime, "yyyy-MM-dd");
        switch (queryType) {
            case "hour":
                recordTime = DateUtil.format(startTime, "yyyy-MM-dd HH:mm:ss");
                break;
            case "day":

                break;
            case "month":
                recordTime = DateUtil.format(startTime, "yyyy-MM");
                break;
            case "year":
                recordTime = DateUtil.format(startTime, "yyyy");
                break;
            default:
                break;
        }
        return recordTime;
    }

    /**
     * 检查设置超标污染物信息
     *
     * @param summaryVo
     * @param dto
     * @param type
     */
    public static void checkSetExceedPolluteInfo(AirEnvQualitySummaryVo summaryVo, Object dto, String type) {
        List<AirEnvironmentQualityStatistics> propLs = null;
        if (dto instanceof AirEnvironmentDayStatisticsDTO) {
            AirEnvironmentDayStatisticsDTO dayStatisticsDTO = (AirEnvironmentDayStatisticsDTO) dto;
            propLs = dayStatisticsDTO.getPropStatisticsList();
            dayStatisticsDTO.setPropStatisticsList(null);
            dto = dayStatisticsDTO;
        }
        if (dto instanceof AirEnvironmentHourStatisticsDTO) {
            AirEnvironmentHourStatisticsDTO hourStatisticsDTO = (AirEnvironmentHourStatisticsDTO) dto;
            propLs = hourStatisticsDTO.getPropStatisticsList();
            hourStatisticsDTO.setPropStatisticsList(null);
            dto = hourStatisticsDTO;
        }
        StringJoiner topSj = new StringJoiner(",");
        StringJoiner exceedSj = new StringJoiner(",");
        if (CollectionUtil.isNotEmpty(propLs)) {
            propLs.stream().filter(p -> Integer.valueOf(1).equals(p.getTopPollute())).forEach(p -> {
                topSj.add(p.getPropertiesKey());
            });
            propLs.stream().filter(p -> Integer.valueOf(1).equals(p.getExceedsPollutes())).forEach(p -> {
                exceedSj.add(p.getPropertiesKey());
            });
        }
        if ("bq".equals(type)) {
            summaryVo.setBqTopPollutePropKey(topSj.toString());
            summaryVo.setBqExceedsPollutePropKeys(exceedSj.toString());
            summaryVo.setBqStatisticsDto(dto);
        }
        if ("tq".equals(type)) {
            summaryVo.setTqTopPollutePropKey(topSj.toString());
            summaryVo.setTqExceedsPollutePropKeys(exceedSj.toString());
            summaryVo.setTqStatisticsDto(dto);
        }
        if ("sq".equals(type)) {
            summaryVo.setSqTopPollutePropKey(topSj.toString());
            summaryVo.setSqExceedsPollutePropKeys(exceedSj.toString());
            summaryVo.setSqStatisticsDto(dto);
        }

    }


    /**
     * 计算比率
     * 本期和上期或者同期的比率
     */
    public static Double calRatio(Integer bq, Integer tqOrSq) {
        Double part = null;
        if (null != bq && null != tqOrSq) {
            if (tqOrSq == 0) {
                part = (bq == 0 ? 0 : 100d);
            } else {
                part = (bq.doubleValue() - tqOrSq.doubleValue()) / tqOrSq.doubleValue() * 100;
            }
        }
        return part;
    }

    /**
     * 计算比率
     * 本期和上期或者同期的比率
     */
    public static Double calRatio(Double bq, Double tqOrSq) {
        Double part = null;
        if (null != bq && null != tqOrSq) {
            if (tqOrSq == 0) {
                part = (bq == 0 ? 0 : 100d);
            } else {
                part = (bq - tqOrSq) / tqOrSq * 100;
            }
        }
        return part;
    }

    /**
     * 计算达标数和达标率
     */
    public static AirEnvQualityReachVo getReachAndReachRate(AirEnvQualityResultVo reachVo, AirEnvQualityResultVo noReachVo) {
        //计算结束放在vo的bq结果中
        AirEnvQualityReachVo vo = new AirEnvQualityReachVo();
        if (null != reachVo || null != noReachVo) {
            vo.setBqReachNum(reachVo != null ? reachVo.getNum() : 0);
            if (noReachVo == null) {
                //不达标为空，表示全达标
                vo.setBqReachRate(100.0);
            } else {
                Integer total = vo.getBqReachNum() + noReachVo.getNum();
                vo.setBqReachRate(NumberUtil.round((vo.getBqReachNum().doubleValue() / total.doubleValue()) * 100, 2).doubleValue());
            }
        }
        return vo;
    }

    /**
     * 获取设备型号属性
     *
     * @param unitCode 型号编码
     * @return 型号属性
     */
    public static List<String> getPropList(String unitCode) {
        List<String> propList = AirQualityDeviceConstant.PROPERTY_LIST;
        if (StringUtils.isNotBlank(unitCode)) {
            if (unitCode.equals(AirQualityDeviceConstant.DeviceUnitCodeEnum.KQZL_DEVICE_UNIT.getDeviceUnitCode())) {
                propList = AirQualityDeviceConstant.DeviceUnitCodeEnum.KQZL_DEVICE_UNIT.getPropertyList();
            }
            if (unitCode.equals(AirQualityDeviceConstant.DeviceUnitCodeEnum.PM25_DEVICE_UNIT.getDeviceUnitCode())) {
                propList = AirQualityDeviceConstant.DeviceUnitCodeEnum.PM25_DEVICE_UNIT.getPropertyList();
            }
        }
        return propList;
    }

    /**
     * 获取统计查询条件QueryWrapper
     * @param airEnvQualityQueryVo
     * @return
     */
    public static QueryWrapper getQueryWrapper(AirEnvQualityQueryVo airEnvQualityQueryVo) {
        String deviceCode = airEnvQualityQueryVo.getDeviceCode();
        String unitCode = airEnvQualityQueryVo.getUnitCode();
        Integer statisticsType = airEnvQualityQueryVo.getStatisticsType();
        QueryWrapper statisticsQw = new QueryWrapper<>();
        if (StringUtils.isNotBlank(deviceCode)) {
            statisticsQw.eq("device_code_", deviceCode);
        }
        if (statisticsType != null) {
            statisticsQw.eq("statistics_type_", statisticsType);
            if (statisticsType.intValue() == 3 && StringUtils.isNotBlank(unitCode)) {
                statisticsQw.eq("unit_code_", unitCode);
            }
        }

        return statisticsQw;
    }

    /**
     * 获取查询条件
     * @param deviceCode
     * @param unitCode
     * @param statisticsType 统计类型，1全设备，2单设备，3单型号
     * @return
     */
    public static QueryWrapper getLimitQueryWrapper(String deviceCode, String unitCode, Integer statisticsType) {
        QueryWrapper qw = new QueryWrapper<>();
        if (StringUtils.isNotBlank(unitCode)) {
            //统计类型，1全设备，2单设备，3单型号，单型号时，unitCode才有效
            if(statisticsType != null && statisticsType == 3){
                qw.eq("unit_code_", unitCode);
            }
        }
        if (StringUtils.isNotBlank(deviceCode)) {
            qw.eq("device_code_", deviceCode);
        }
        if (statisticsType != null) {
            qw.eq("statistics_type_", statisticsType);
        }
        qw.last("limit 1");
        return qw;
    }
}
