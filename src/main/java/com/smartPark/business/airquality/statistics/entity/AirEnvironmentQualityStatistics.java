package com.smartPark.business.airquality.statistics.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 空气环境质量统计
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_air_environment_quality_statistics")
public class AirEnvironmentQualityStatistics extends Model<AirEnvironmentQualityStatistics> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 配置标准id
     */
    @TableField("standard_id_")
    private Long standardId;

    /**
     * 统计类型，1小时，2日
     */
    @TableField("statistics_type_")
    private Integer statisticsType;

    /**
     * 统计id
     */
    @TableField("statistics_id_")
    private Long statisticsId;

    /**
     * 属性key，与标准属性一直，来源物模型
     */
    @TableField("properties_key_")
    private String propertiesKey;

    /**
     * 属性名称，与标准属性一直，来源物模型
     */
    @TableField("properties_name_")
    private String propertiesName;

    /**
     * 极值
     */
    @TableField("ave_peak_")
    private Double avePeak;

    /**
     * 是否达标，0否，1是
     */
    @TableField("reach_")
    private Integer reach;

    /**
     * 首要污染物,0否，1是
     */
    @TableField("top_pollute_")
    private Integer topPollute;

    /**
     * 超标污染物,0否，1是
     */
    @TableField("exceeds_pollute_")
    private Integer exceedsPollutes;

    /**
     * 超标污染物,0否，1是
     */
    @TableField("exceeds_multiple_")
    private Double exceedsMultiple;

    /**
     * 设备编码，多个逗号隔开
     */
    @TableField("device_codes_")
    private String deviceCodes;

    /**
     * iaqi空气质量分指数
     */
    @TableField("iaqi_")
    private Double iaqi;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，0否，1是
     */
    @TableField("deleted_")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
