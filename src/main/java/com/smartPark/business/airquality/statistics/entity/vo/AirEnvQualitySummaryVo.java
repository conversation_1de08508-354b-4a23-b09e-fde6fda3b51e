package com.smartPark.business.airquality.statistics.entity.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AirEnvQualitySummaryVo {
    private Date recordTime;

    /**
     * 时间字符串
     */
    private String recordTimeStr;

    private Object bqStatisticsDto;

    private Object tqStatisticsDto;

    private Object sqStatisticsDto;

    private String bqTopPollutePropKey;

    private String bqExceedsPollutePropKeys;

    private String tqTopPollutePropKey;
    private String tqExceedsPollutePropKeys;

    private String sqTopPollutePropKey;

    private String sqExceedsPollutePropKeys;

}
