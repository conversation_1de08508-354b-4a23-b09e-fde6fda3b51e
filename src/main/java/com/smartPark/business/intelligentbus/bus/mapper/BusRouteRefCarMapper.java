package com.smartPark.business.intelligentbus.bus.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.intelligentbus.bus.entity.BusRouteRefCar;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * BusRouteRefCar表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/05/12
 */
public interface BusRouteRefCarMapper extends BaseMapper<BusRouteRefCar> {


    /**
     * 查询分页
     *
     * @param page        分页参数对象
     * @param trafficBusRouteRefCar 过滤参数对象
     * @return 查询分页结果
     */
    IPage<BusRouteRefCar> selectPage(Page page, @Param("trafficBusRouteRefCar") BusRouteRefCar trafficBusRouteRefCar);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    BusRouteRefCar getOneById(@Param("id") Serializable id);

    List<BusRouteRefCar> listWithBusNum(@Param("busRouteRefCar") BusRouteRefCar busRouteRefCar);
}

