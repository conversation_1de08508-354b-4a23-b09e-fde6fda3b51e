package com.smartPark.business.intelligentbus.bus.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.intelligentbus.bus.entity.dto.BusStopSnapshotRecordDTO;
import com.smartPark.business.intelligentbus.bus.entity.vo.BusStopSnapshotVo;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.business.intelligentbus.bus.entity.BusStopSnapshotRecord;
import com.smartPark.business.intelligentbus.bus.service.BusStopSnapshotRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * 智慧公交/公交站乘客、机动车监控
 *
 * <AUTHOR>
 * @since 2023/10/09
 */
@Slf4j
@RestController
@RequestMapping("/busStopSnapshot")
public class BusStopSnapshotRecordController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private BusStopSnapshotRecordService busStopSnapshotRecordService;


    /**
     * 造数据新增
     *
     * @param busStopSnapshotRecord 实体对象
     * @return 新增结果
     */
    @PostMapping
    public RestMessage insert(@RequestBody BusStopSnapshotRecord busStopSnapshotRecord) {
        return RestBuilders.successBuilder().success((this.busStopSnapshotRecordService.saveOne(busStopSnapshotRecord))).build();
    }


    /**
     * 分页查询dto数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/selectDtoPage")
    public RestMessage selectDtoPage(@RequestBody RequestModel<BusStopSnapshotVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<BusStopSnapshotRecordDTO> record = busStopSnapshotRecordService.selectDtoPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 顶部统计数据
     *
     * @param busStopSnapshotVo 查询参数vo
     * @return 统计数据
     */
    @PostMapping("/topStatisticsData")
    public RestMessage topStatisticsData(@RequestBody BusStopSnapshotVo busStopSnapshotVo) {
        Assert.notNull(busStopSnapshotVo, "参数不能为空");
        return busStopSnapshotRecordService.topStatisticsData(busStopSnapshotVo);
    }
}

