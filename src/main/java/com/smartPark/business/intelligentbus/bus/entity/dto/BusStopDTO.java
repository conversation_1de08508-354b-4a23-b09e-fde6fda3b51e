package com.smartPark.business.intelligentbus.bus.entity.dto;

import com.smartPark.business.intelligentbus.bus.entity.BusStop;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BusStop实体类DTO
 *
 * <AUTHOR>
 * @date 2023/05/12
 */

@Data
@Accessors(chain = true)
public class BusStopDTO extends BusStop {
    /**
     * 人流统计
     */
    private Integer personNum;
    /**
     * 即将入站车辆数
     *
     * @param trafficBusStop
     */
    private Integer nearestInBusNum;
    /**
     * 刚驶出车辆数
     */
    private Integer justOutTimeBusNum;

    private Integer droveNum;
    /**
     * 抓拍图片
     */
    private String grabImg;

    public BusStopDTO(BusStop trafficBusStop) {
        //this.setName(trafficBusStop.getName());
    }

    public BusStopDTO() {
    }
}
