package com.smartPark.business.intelligentbus.bus.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 公交路线关联公交站
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("traffic_bus_route_driving_car")
@ApiModel(value = "BusRouteDrivingCar对象", description = "公交路线关联公交站")
public class BusRouteDrivingCar implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "公交路线id")
    @TableField(value = "bus_route_id_")
    private Long busRouteId;

    @ApiModelProperty(value = "最近驶过的站id")
    @TableField(value = "last_stop_recently_bus_stop_id_")
    private Long lastStopRecentlyBusStopId;

    @ApiModelProperty(value = "最近驶过的站的序号")
    @TableField(value = "last_stop_recently_sort_no_")
    private Integer lastStopRecentlySortNo;

    @ApiModelProperty(value = "即将到达的站的id")
    @TableField(value = "next_stop_soon_bus_stop_id_")
    private Long nextStopSoonBusStopId;

    @ApiModelProperty(value = "即将到达的站的序号")
    @TableField(value = "next_stop_soon_sort_no_")
    private Integer nextStopSoonSortNo;

    @ApiModelProperty(value = "序号")
    @TableField(value = "sort_no_")
    private Integer sortNo;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "creator_id_")
    private Long creatorId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time_")
    private Date createTime;

    @ApiModelProperty(value = "修改人id")
    @TableField(value = "modify_id_")
    private Long modifyId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "modify_time_")
    private Date modifyTime;

    @ApiModelProperty(value = "是否删除 0存在,非0-删除")
    @TableLogic(value = "0")
    @TableField(value = "deleted_")
    private Long deleted = 0L;


    @ApiModelProperty(value = "车牌号")
    @TableField(value = "car_no_")
    private String carNo;


}
