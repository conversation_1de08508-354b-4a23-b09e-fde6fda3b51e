package com.smartPark.business.intelligentbus.bus.controller;


import com.smartPark.business.intelligentbus.bus.entity.BusStop;
import com.smartPark.business.intelligentbus.bus.mapper.BusStopMapper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.HashMap;

/**
 * 智慧公交/公交站点广告
 * <AUTHOR>
 * @date 2023/06/19
 */
@Slf4j
@RestController
@RequestMapping("trafficBusStopAd")
public class BusStopAdController {

    /**
     * 服务对象
     */
    @Resource
    private BusStopMapper busStopMapper;

    /**
     * 通过objId查询单条数据
     * @param objId objId
     * @return 单条数据
     */
    @GetMapping("objId/{objId}")
    @ApiOperation("通过objId查询单条")
    public RestMessage selectOneByObjId(@PathVariable Serializable objId) {
        Assert.notNull(objId, "参数为空");
        // LambdaQueryWrapper<BusStop> queryWrapper = new LambdaQueryWrapper<>();
        // queryWrapper.eq(BusStop::getObjId,objId).eq(BusStop::getDeleted,0);
        // BusStop busStop = busStopMapper.selectOne(queryWrapper);
        // Assert.notNull(busStop, "公交站不存在");

        // 以后会有张表获取广告信息
        BusStop busStop = new BusStop();

        HashMap<String, Object> map = new HashMap<>();
        map.put("adNum",busStop.getAdNum());
        return RestBuilders.successBuilder(map).build();
    }

}

