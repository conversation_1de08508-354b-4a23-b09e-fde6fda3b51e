package com.smartPark.business.intelligentbus.bus.entity.dto;

import com.smartPark.business.intelligentbus.bus.entity.BusRouteRefBusStop;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BusRouteRefBusStop实体类DTO
 *
 * <AUTHOR>
 * @date 2023/05/12
 */

@Data
@Accessors(chain = true)
public class BusRouteRefBusStopDTO extends BusRouteRefBusStop {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public BusRouteRefBusStopDTO(BusRouteRefBusStop trafficBusRouteRefBusStop) {
        //this.setName(trafficBusRouteRefBusStop.getName());
    }
}
