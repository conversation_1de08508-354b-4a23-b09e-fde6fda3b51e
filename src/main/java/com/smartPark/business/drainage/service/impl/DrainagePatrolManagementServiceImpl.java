package com.smartPark.business.drainage.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.exporter.DataExportParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.drainage.entity.DrainagePatrolManagement;
import com.smartPark.business.drainage.entity.vo.DrainagePatrolManagementVo;
import com.smartPark.business.drainage.excel.handler.DrainagePatrolManagementExportHandler;
import com.smartPark.business.drainage.mapper.DrainagePatrolManagementMapper;
import com.smartPark.business.drainage.service.DrainagePatrolManagementService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.exceptions.BusinessException;
import com.asyncexcel.springboot.ExcelService;
import com.smartPark.common.utils.RedisUtil;
import com.smartPark.common.security.context.BaseUserContextProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class DrainagePatrolManagementServiceImpl extends ServiceImpl<DrainagePatrolManagementMapper, DrainagePatrolManagement> implements DrainagePatrolManagementService {

    @Resource
    private CommonService commonService;

    @Resource
    private ObjInfoMapper objInfoMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ExcelService excelService;

    @Resource
    private BaseUserContextProducer baseUserContextProducer;

    @Override
    public boolean saveOne(DrainagePatrolManagement drainagePatrolManagement) {
        StringBuilder sj = new StringBuilder();
        sj.append("新增巡检记录,泵站档案id:");
        sj.append(drainagePatrolManagement.getPumpStationObjId());
        commonService.setCreateAndModifyInfo(drainagePatrolManagement);
        validParamRequired(drainagePatrolManagement);
//        validRepeat(drainageHouseholdArchives);
        validParamFormat(drainagePatrolManagement);
        drainagePatrolManagement.setPatrolNo(generateCheckNo());
        save(drainagePatrolManagement);
        LogHelper.setLogInfo("drainageControl:patrolManagement:add", JSON.toJSONString(drainagePatrolManagement), null,JSON.toJSONString(drainagePatrolManagement),sj.toString());
        return true;
    }

    private String generateCheckNo() {
        //获取当前年月日
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String format = simpleDateFormat.format(new Date());
        long result = redisUtil.incr("XJ"+format, 1);
        //如果不足4位，则用0补齐
        String str = String.format("%4d", result).replace(" ", "0");
        redisUtil.expire("XJ"+format,24*60*60);
        //GW+YYYYMMDD+4位顺序码
        return "XJ"+format+str;
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(DrainagePatrolManagement drainagePatrolManagement) {
//        Assert.isTrue(drainagePatrolManagement.getPumpStationObjId() == null || (drainagePatrolManagement.getPumpStationObjId().length() == 16 && drainagePatrolManagement.getPumpStationObjId().startsWith("0105", 6)), "部件码校验不通过");
        Assert.isTrue(drainagePatrolManagement.getPumpStationObjId() == null || null != objInfoMapper.findByMonitorPointBsm(drainagePatrolManagement.getPumpStationObjId()), "部件码不存在");
    }

    /**
     * 校验参数必填
     */
    private void validParamRequired(DrainagePatrolManagement drainagePatrolManagement) {
        Assert.notNull(drainagePatrolManagement, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(drainagePatrolManagement.getPumpStationObjId()), "泵站档案id为空");
        Assert.isTrue(drainagePatrolManagement.getPumpStationInnerObjType()!=null, "巡检设施为空");
        Assert.isTrue(drainagePatrolManagement.getDrainagePatrolType()!=null, "巡检类型为空");
    }

    @Override
    public boolean updateOne(DrainagePatrolManagement drainagePatrolManagement) {
        Assert.notNull(drainagePatrolManagement.getId(), "id不能为空");
        commonService.setModifyInfo(drainagePatrolManagement);
        DrainagePatrolManagement archivesInfo = baseMapper.selectById(drainagePatrolManagement.getId());
        if(archivesInfo==null){
            throw new BusinessException("当前记录不存在");
        }
        validParamFormat(drainagePatrolManagement);
        StringBuilder sj = new StringBuilder();
        sj.append("修改检查记录,检查编号:");
        sj.append(drainagePatrolManagement.getPatrolNo());
        LogHelper.setLogInfo("drainageControl:patrolManagement:edit", JSON.toJSONString(archivesInfo), JSON.toJSONString(archivesInfo), JSON.toJSONString(drainagePatrolManagement), sj.toString());
        return saveOrUpdate(drainagePatrolManagement);
    }

    @Override
    public DrainagePatrolManagementVo getInfo(Long id) {
        return baseMapper.selectByCheckId(id);
    }

    @Override
    public IPage<DrainagePatrolManagementVo> selectPage(Page page, DrainagePatrolManagementVo drainagePatrolManagementVo) {
        return baseMapper.selectPage(page, drainagePatrolManagementVo);
    }

    @Override
    public boolean deleteByIds(List<Long> idList) {
        StringBuilder sj = new StringBuilder();
        sj.append("删除记录,记录编号:");
        for (Long id : idList) {
            DrainagePatrolManagement drainagePatrolManagement = baseMapper.selectById(id);
            sj.append(drainagePatrolManagement.getPatrolNo());
            removeById(id);
        }
        LogHelper.setLogInfo("drainageControl:patrolManagement:delete", sj.toString(), null, null, sj.toString());
        return true;
    }

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", 1).eq("id_", id).update();
    }

    @Override
    public Long export(DrainagePatrolManagementVo drainagePatrolManagementVo, HttpServletRequest request, HttpServletResponse response) {
        Long userId = baseUserContextProducer.getCurrent().getId();
        DataExportParam dataExportParam = new DataExportParam();
        dataExportParam.setParam(drainagePatrolManagementVo);
        dataExportParam.setExportFileName("巡检管理" + DateUtil.format(new Date(), "yyyy-MM-dd"));
        dataExportParam.setTenantCode("drainage");
        dataExportParam.setBusinessCode("drainagePatrolManagement");
        dataExportParam.setCreateUserCode(userId.toString());

        Long taskId = excelService.doExport(dataExportParam, DrainagePatrolManagementExportHandler.class);

        String msg = "导出巡检管理数据，任务id：" + taskId;
        LogHelper.setLogInfo("drainageControl:patrolManagement:export", msg, null, null, msg);

        return taskId;
    }
}
