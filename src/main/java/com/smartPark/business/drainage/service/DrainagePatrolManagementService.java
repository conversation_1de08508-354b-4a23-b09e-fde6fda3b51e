package com.smartPark.business.drainage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.drainage.entity.DrainagePatrolManagement;
import com.smartPark.business.drainage.entity.vo.DrainagePatrolManagementVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface DrainagePatrolManagementService extends IService<DrainagePatrolManagement> {

    boolean saveOne(DrainagePatrolManagement drainageHouseholdCheck);

    boolean updateOne(DrainagePatrolManagement drainageHouseholdCheck);

    DrainagePatrolManagementVo getInfo(Long id);

    IPage<DrainagePatrolManagementVo> selectPage(Page page, DrainagePatrolManagementVo customQueryParams);

    boolean deleteByIds(List<Long> idList);

    /**
     * 导出巡检管理数据
     *
     * @param drainagePatrolManagementVo 查询条件
     * @param request 请求对象
     * @param response 响应对象
     * @return 任务ID
     */
    Long export(DrainagePatrolManagementVo drainagePatrolManagementVo, HttpServletRequest request, HttpServletResponse response);
}
