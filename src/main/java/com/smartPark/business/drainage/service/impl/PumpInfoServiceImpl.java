package com.smartPark.business.drainage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.drainage.entity.PumpInfo;
import com.smartPark.business.drainage.mapper.PumpInfoMapper;
import com.smartPark.business.drainage.service.PumpInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PumpInfoServiceImpl extends ServiceImpl<PumpInfoMapper, PumpInfo> implements PumpInfoService {

    @Resource
    private PumpInfoMapper pumpInfoMapper;

    @Override
    public void deleteByArchivesObjId(String objId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("archives_obj_id_",objId);
        pumpInfoMapper.delete(queryWrapper);
    }

    @Override
    public IPage<PumpInfo> getByObjId(Page page, String archivesObjId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("archives_obj_id_",archivesObjId);
        return pumpInfoMapper.selectPage(page,queryWrapper);
    }
}
