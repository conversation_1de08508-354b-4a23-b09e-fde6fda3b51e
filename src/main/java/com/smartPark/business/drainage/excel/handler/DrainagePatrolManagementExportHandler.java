package com.smartPark.business.drainage.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.drainage.entity.vo.DrainagePatrolManagementVo;
import com.smartPark.business.drainage.excel.model.DrainagePatrolManagementExportModel;
import com.smartPark.business.drainage.service.DrainagePatrolManagementService;
import com.smartPark.common.asyncexcel.handler.CommonExportHandler;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * 排水巡检管理导出处理器
 *
 * <AUTHOR>
 * @since 2024
 */
@Slf4j
@ExcelHandle
public class DrainagePatrolManagementExportHandler extends CommonExportHandler<DrainagePatrolManagementExportModel> {

    @Resource
    private DrainagePatrolManagementService drainagePatrolManagementService;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        // 初始化导出上下文
        ExportContext context = (ExportContext) ctx;
        //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
        WriteSheet sheet = EasyExcel.writerSheet(0, "巡检管理").head(DrainagePatrolManagementExportModel.class).build();
        context.setWriteSheet(sheet);
    }

    @Override
    public ExportPage<DrainagePatrolManagementExportModel> exportData(int startPage, int limit, DataExportParam param) {
        DrainagePatrolManagementVo drainagePatrolManagementVo = (DrainagePatrolManagementVo) param.getParam();

        // 分页查询数据
        IPage<DrainagePatrolManagementVo> page = drainagePatrolManagementService.selectPage(new Page(startPage, limit), drainagePatrolManagementVo);
        
        // 转换为导出模型
        List<DrainagePatrolManagementExportModel> exportList = DrainagePatrolManagementExportModel.getList4Export(page.getRecords());
        
        ExportPage<DrainagePatrolManagementExportModel> result = new ExportPage<>();
        result.setTotal(page.getTotal());
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setRecords(exportList);
        return result;
    }
}
