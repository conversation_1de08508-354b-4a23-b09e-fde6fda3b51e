package com.smartPark.business.drainage.excel.model;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.asyncexcel.core.ExportRow;
import com.smartPark.business.drainage.entity.vo.DrainagePatrolManagementVo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 排水巡检管理导出模型
 *
 * <AUTHOR>
 * @since 2024
 */
@Data
@ContentFontStyle(fontName = "宋体 (正文)", fontHeightInPoints = 11)
@HeadFontStyle(fontName = "宋体 (标题)", fontHeightInPoints = 11, bold = BooleanEnum.TRUE)
public class DrainagePatrolManagementExportModel extends ExportRow {

    @ExcelProperty(value = "巡检编号")
    @ColumnWidth(value = 20)
    private String patrolNo;

    @ExcelProperty(value = "泵站名称")
    @ColumnWidth(value = 25)
    private String pumpStationName;

    @ExcelProperty(value = "巡检日期")
    @DateTimeFormat("yyyy-MM-dd")
    @ColumnWidth(value = 15)
    private Date patrolDate;

    @ExcelProperty(value = "巡检设施")
    @ColumnWidth(value = 15)
    private String pumpStationInnerObjTypeName;

    @ExcelProperty(value = "巡检类型")
    @ColumnWidth(value = 15)
    private String drainagePatrolTypeName;

    @ExcelProperty(value = "巡检结果")
    @ColumnWidth(value = 15)
    private String patrolResultName;

    @ExcelProperty(value = "创建日期")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(value = 20)
    private Date createTime;

    /**
     * 对象转换
     * @param list
     * @return
     */
    public static List<DrainagePatrolManagementExportModel> getList4Export(List<DrainagePatrolManagementVo> list) {
        List<DrainagePatrolManagementExportModel> exportModels = new ArrayList<>();
        list.forEach(vo -> {
            DrainagePatrolManagementExportModel exportModel = BeanUtil.toBean(vo, DrainagePatrolManagementExportModel.class);
            
            // 设置泵站名称
            if (vo.getObjInfo() != null && StringUtils.isNotBlank(vo.getObjInfo().getObjName())) {
                exportModel.setPumpStationName(vo.getObjInfo().getObjName());
            } else if (StringUtils.isNotBlank(vo.getPumpStationName())) {
                exportModel.setPumpStationName(vo.getPumpStationName());
            }
            
            // 设置巡检类型名称
            if (StringUtils.isNotBlank(vo.getDrainagePatrolTypeName())) {
                exportModel.setDrainagePatrolTypeName(vo.getDrainagePatrolTypeName());
            } else if (vo.getDrainagePatrolType() != null) {
                // 根据字典值设置名称
                switch (vo.getDrainagePatrolType()) {
                    case 1:
                        exportModel.setDrainagePatrolTypeName("日常巡检");
                        break;
                    case 2:
                        exportModel.setDrainagePatrolTypeName("专项巡检");
                        break;
                    default:
                        exportModel.setDrainagePatrolTypeName("未知");
                        break;
                }
            }
            
            // 设置巡检设施名称
            if (StringUtils.isNotBlank(vo.getPumpStationInnerObjTypeName())) {
                exportModel.setPumpStationInnerObjTypeName(vo.getPumpStationInnerObjTypeName());
            } else if (vo.getPumpStationInnerObjType() != null) {
                // 根据字典值设置名称
                switch (vo.getPumpStationInnerObjType()) {
                    case 1:
                        exportModel.setPumpStationInnerObjTypeName("主水泵");
                        break;
                    case 2:
                        exportModel.setPumpStationInnerObjTypeName("备用水泵");
                        break;
                    case 3:
                        exportModel.setPumpStationInnerObjTypeName("控制系统");
                        break;
                    case 4:
                        exportModel.setPumpStationInnerObjTypeName("电气设备");
                        break;
                    default:
                        exportModel.setPumpStationInnerObjTypeName("其他");
                        break;
                }
            }
            
            // 设置巡检结果名称
            if (StringUtils.isNotBlank(vo.getPatrolResultName())) {
                exportModel.setPatrolResultName(vo.getPatrolResultName());
            } else if (vo.getPatrolResult() != null) {
                // 根据字典值设置名称
                switch (vo.getPatrolResult()) {
                    case 0:
                        exportModel.setPatrolResultName("正常");
                        break;
                    case 1:
                        exportModel.setPatrolResultName("异常");
                        break;
                    default:
                        exportModel.setPatrolResultName("未知");
                        break;
                }
            }
            
            exportModels.add(exportModel);
        });
        return exportModels;
    }
}
