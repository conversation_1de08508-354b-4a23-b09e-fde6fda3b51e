package com.smartPark.business.drainage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.drainage.entity.DrainageDevice;
import com.smartPark.business.drainage.entity.DrainagePumpStationArchives;
import com.smartPark.business.drainage.entity.vo.DrainageDeviceAlarmVo;
import com.smartPark.business.drainage.entity.vo.DrainageDeviceVo;
import com.smartPark.business.drainage.entity.vo.DrainagePumpStationArchivesVo;
import com.smartPark.common.entity.deviceArea.DeviceArea;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 泵站档案 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
public interface DrainagePumpStationArchivesMapper extends BaseMapper<DrainagePumpStationArchives> {


    IPage<DrainagePumpStationArchivesVo> selectPage(Page page, @Param("drainagePumpStationArchivesVo")DrainagePumpStationArchivesVo drainagePumpStationArchivesVo);

    DrainagePumpStationArchivesVo getArchivesInfo(Long id);

    DrainagePumpStationArchivesVo getArchivesInfoByArchivesName(String archivesName);
}
