package com.smartPark.business.drainage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.drainage.entity.DrainageMaintenanceManagement;
import com.smartPark.business.drainage.entity.vo.DrainageMaintenanceManagementVo;
import org.apache.ibatis.annotations.Param;

public interface DrainageMaintenanceManagementMapper extends BaseMapper<DrainageMaintenanceManagement> {

    IPage<DrainageMaintenanceManagementVo> selectPage(Page page, DrainageMaintenanceManagementVo drainageMaintenanceManagementVo);

    DrainageMaintenanceManagementVo selectByCheckId(@Param("id") Long id);
}
