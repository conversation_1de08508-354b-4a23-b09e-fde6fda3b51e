package com.smartPark.business.drainage.entity.vo;

import com.smartPark.business.drainage.entity.DrainageLakeMonitoringStatistic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排水控制-湖渠监测-统计表
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DrainageLakeMonitoringStatisticVo extends DrainageLakeMonitoringStatistic {

    // /**
    //  * 按月统计的日期
    //  */
    // private Date recordTimeByMonth;

}
