package com.smartPark.business.drainage.entity.vo;

import com.smartPark.business.drainage.entity.DrainagePatrolManagement;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 排水控制巡检管理实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DrainagePatrolManagementVo extends DrainagePatrolManagement {

    /**
     * 查询开始时间
     */
    private Date queryTimeStart;

    /**
     * 查询结束时间
     */
    private Date queryTimeEnd;

    /**
     * 泵站名称
     */
    private String pumpStationName;

    /**
     * 巡检类型名称 (JSON转换后字段)
     */
    private String drainagePatrolTypeName;

    /**
     * 巡检设施名称 (JSON转换后字段)
     */
    private String pumpStationInnerObjTypeName;

    /**
     * 巡检结果名称 (JSON转换后字段)
     */
    private String patrolResultName;
}
