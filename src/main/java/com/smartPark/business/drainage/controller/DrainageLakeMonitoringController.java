package com.smartPark.business.drainage.controller;

import com.smartPark.business.drainage.constant.DrainageDeviceConstant;
import com.smartPark.business.drainage.entity.vo.DrainageDeviceVo;
import com.smartPark.business.drainage.entity.vo.DrainageLakeMonitoringStatisticVo;
import com.smartPark.business.drainage.service.DrainageDeviceService;
import com.smartPark.business.drainage.service.DrainageLakeMonitoringService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;
import java.util.Map;

/**
 * 排水控制/湖渠监测
 */
@RestController
@RequestMapping("drainageLakeMonitoring")
@Api(tags = "排水控制 湖渠监测")
public class DrainageLakeMonitoringController {
    @Autowired
    private DrainageDeviceService drainageDeviceService;

    @Autowired
    private DrainageLakeMonitoringService drainageLakeMonitoringService;

    @PostMapping("deviceStatus")
    @ApiOperation("根据区域查询湖渠设备实时统计数据")
    public RestMessage deviceStatus(@RequestBody DrainageDeviceVo drainageDeviceVo){
        // 设置设备类型
        drainageDeviceVo.setType(DrainageDeviceConstant.LAKE_TYPE);
        drainageDeviceVo.setDeviceTypeName("湖渠监测传感器");
        Map<String,Object> status = drainageDeviceService.findDeviceStatistics(drainageDeviceVo);
        return RestBuilders.successBuilder().data(status).build();
    }

    /**
     * 近30天趋势，设备维度
     */
    @GetMapping("dayTrend")
    @ApiOperation("近30天趋势")
    public RestMessage dayTrend(@RequestParam("id") Long id) {
        List<DrainageLakeMonitoringStatisticVo> record = drainageLakeMonitoringService.dayTrend(id);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * 同期+本期趋势
     */
    @GetMapping("monthTrend")
    @ApiOperation("近24个月趋势(同期+本期)")
    public RestMessage monthTrend() {
        List<List<DrainageLakeMonitoringStatisticVo>> record = drainageLakeMonitoringService.monthTrend();
        return RestBuilders.successBuilder().data(record).build();
    }
}
