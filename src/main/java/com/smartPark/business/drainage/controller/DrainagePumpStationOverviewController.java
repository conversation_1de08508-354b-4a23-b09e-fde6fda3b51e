package com.smartPark.business.drainage.controller;

import com.smartPark.business.drainage.entity.DrainagePumpStationArchives;
import com.smartPark.business.drainage.entity.vo.DrainagePumpStationArchivesVo;
import com.smartPark.business.drainage.service.DrainageDeviceService;
import com.smartPark.business.drainage.service.DrainagePumpStationArchivesService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.Map;

/**
 * 排水控制/泵站概览
 * @Model sanzhi-safe-service
 */
@Slf4j
@RestController
@RequestMapping("draingePumpStationMaps")
public class DrainagePumpStationOverviewController {

    @Autowired
    private DrainageDeviceService drainageDeviceService;

    @PostMapping("deviceStatus")
    @ApiOperation("根据区域查询设备情况")
    public RestMessage deviceStatus(@RequestBody DrainagePumpStationArchivesVo pumpStationArchives){
        Map<String,Object> map = drainageDeviceService.findDeviceStatistics(pumpStationArchives);
        return RestBuilders.successBuilder().data(map).build();
    }

}
