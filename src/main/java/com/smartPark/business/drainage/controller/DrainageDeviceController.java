package com.smartPark.business.drainage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.drainage.entity.DrainageDevice;
import com.smartPark.business.drainage.entity.vo.DrainageDeviceAlarmVo;
import com.smartPark.business.drainage.entity.vo.DrainageDeviceDTO;
import com.smartPark.business.drainage.entity.vo.DrainageDeviceVo;
import com.smartPark.business.drainage.service.DrainageDeviceService;
import com.smartPark.business.smartNetwork.entity.DrainageHouseholdArchives;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;


/**
 * 排水控制/排水设备管理
 */
@RestController
@RequestMapping("drainageDevice")
@Api(tags = "排水户设备管理")
public class DrainageDeviceController {

    @Autowired
    private DrainageDeviceService drainageDeviceService;

    /**
     * @Description: 增加设备(批量)
     */
    @PostMapping("batch")
    @ApiOperation("增加设备(批量)")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD,menuCode = "drainageControl:equipmentManagement:add",desc = "关联设备")
    public RestMessage insertBatch(@RequestBody DrainageDeviceVo drainageDeviceVo){
        //参数验证
        Assert.notEmpty(drainageDeviceVo.getDeviceCodes(),"设备id不能为空");
        drainageDeviceService.insertBatch(drainageDeviceVo.getDeviceCodes());
        return RestBuilders.successBuilder().build();
    }
    /**
     * @Description: 增加设备
     */
    @PostMapping
    @ApiOperation("增加设备")
    public RestMessage insert(@RequestBody DrainageDevice drainageDevice){
        //参数验证
        Assert.hasLength(drainageDevice.getDeviceCode(),"设备id不能为空");
        drainageDeviceService.insert(drainageDevice);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 删除设备（包含批量删除）
     */
    @DeleteMapping
    @ApiOperation("删除设备（包含批量删除）")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL,menuCode = "drainageControl:equipmentManagement:delete",desc = "删除设备")
    public RestMessage delBatch(@RequestBody DrainageDevice drainageDevice){
        Assert.notEmpty(drainageDevice.getIds(),"id不能为空");
        drainageDeviceService.delBatch(drainageDevice.getIds());
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 编辑设备
     */
    @PutMapping
    @ApiOperation("编辑设备")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "drainageControl:equipmentManagement:edit",desc = "编辑设备")
    public RestMessage updateById(@RequestBody DrainageDevice drainageDevice){
        Assert.notNull(drainageDevice.getId(),"id不能为空");
        drainageDeviceService.updateOne(drainageDevice);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 根据id查询设备信息
     */
    @GetMapping("{id}")
    @ApiOperation("根据id查询设备详情")
    public RestMessage findById(@PathVariable("id")Long id) {
        Assert.notNull(id,"id不能为空");
        DrainageDeviceDTO drainageDeviceDTO = drainageDeviceService.findById(id);
        return RestBuilders.successBuilder().data(drainageDeviceDTO).build();
    }

    /**
     * @Description: 根据条件，分页(不分页)查询
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<DrainageDeviceVo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DrainageDeviceVo> record =  drainageDeviceService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * 根据条件查询告警信息
     * @param requestModel
     * @return
     */
    @PostMapping("queryAlarmPage")
    @ApiOperation("根据条件查询告警信息，分页(不分页)查询")
    public RestMessage queryAlarmPage(@RequestBody RequestModel<DrainageDeviceAlarmVo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DrainageDeviceAlarmVo> record =  drainageDeviceService.queryAlarmPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * 查询已关联或未关联设备的设备信息
     * @return 所有数据
     */
    @PostMapping("getRelatedOrNotDevice")
    @ApiOperation("查询已关联或未关联设备的设备信息")
    public RestMessage getRelatedOrNotDevice(@RequestBody RequestModel<DrainageDeviceVo> requestModel) {
        IPage<DrainageDeviceVo> drainageDeviceIPage = drainageDeviceService.getRelatedOrNotDevice(requestModel);
        return RestBuilders.successBuilder(drainageDeviceIPage).build();
    }

    /**
     * 关联设备
     * @return 所有数据
     */
    @PostMapping("relateDevice")
    @ApiOperation("关联设备")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.JOIN_DEVICE,menuCode = "drainageControl:pumpStationsArchives:relate",desc = "关联设备")
    public RestMessage relateDevice(@RequestBody DrainageDeviceVo drainageDeviceVo) {
        Assert.notNull(drainageDeviceVo.getPumpStationArchivesObjId(), "泵站档案不能为空");
        drainageDeviceService.relateDevice(drainageDeviceVo);
        return RestBuilders.successBuilder().build();
    }
}
