package com.smartPark.business.drainage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.drainage.entity.DrainagePatrolManagement;
import com.smartPark.business.drainage.entity.vo.DrainagePatrolManagementVo;
import com.smartPark.business.drainage.service.DrainagePatrolManagementService;
import com.smartPark.business.drainage.service.DrainagePumpStationArchivesService;
import com.smartPark.business.smartNetwork.entity.DrainageHouseholdCheck;
import com.smartPark.business.smartNetwork.entity.vo.DrainageHouseholdCheckVo;
import com.smartPark.business.smartNetwork.service.DrainageHouseholdCheckService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 排水控制/巡检管理
 */
@Slf4j
@RestController
@RequestMapping("drainagePatrolManagement")
public class DrainagePatrolManagementController {

    @Autowired
    private DrainagePatrolManagementService drainagePatrolManagementService;

    /**
     * 新增数据
     *
     * @param drainagePatrolManagement 实体对象
     * @return 新增结果
     */
    @PostMapping
    @ApiOperation("新增")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD,menuCode = "drainageControl:patrolManagement:add",desc = "新增记录")
    public RestMessage insert(@RequestBody DrainagePatrolManagement drainagePatrolManagement) {
        return RestBuilders.successBuilder().success((this.drainagePatrolManagementService.saveOne(drainagePatrolManagement))).build();
    }

    /**
     * 修改数据
     *
     * @param drainagePatrolManagement 实体对象
     * @return 修改结果
     */
    @PutMapping
    @ApiOperation("修改单条")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "drainageControl:patrolManagement:edit",desc = "修改检查")
    public RestMessage update(@RequestBody DrainagePatrolManagement drainagePatrolManagement) {
        return RestBuilders.successBuilder().success(this.drainagePatrolManagementService.updateOne(drainagePatrolManagement)).build();
    }

    /**
     * 查询单条记录
     *
     * @param id 主键
     * @return 修改结果
     */
    @GetMapping("/{id}")
    @ApiOperation("查询单条记录")
    public RestMessage getInfo(@PathVariable("id") Long id) {
        return RestBuilders.successBuilder(this.drainagePatrolManagementService.getInfo(id)).build();
    }

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("getPage")
    @ApiOperation("查询分页")
    public RestMessage selectPage(@RequestBody RequestModel<DrainagePatrolManagementVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DrainagePatrolManagementVo> record = drainagePatrolManagementService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL,menuCode = "drainageControl:patrolManagement:delete",desc = "删除检查")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(drainagePatrolManagementService.deleteByIds(idList)).build();
    }

}
