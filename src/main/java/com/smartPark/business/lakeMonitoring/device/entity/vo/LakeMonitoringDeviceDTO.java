package com.smartPark.business.lakeMonitoring.device.entity.vo;

import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.entity.device.MonitorPoint;
import com.smartPark.common.entity.device.ObjInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description 湖渠监测设备设备的信息详情
 * <AUTHOR> @Date
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LakeMonitoringDeviceDTO extends LakeMonitoringDeviceVo {
    /**
     * 设备基础信息
     */
    private DeviceExtendInfo extendInfo;

    /**
     * 监测点位信息
     */
    private MonitorPoint monitorPoint;

    /**
     *部件码基本信息
     */
    private ObjInfo objInfo;

    /**
     * 设备属性
     */
    private List<DevicePropertyStatus> devicePropertyStatusList;
}
