package com.smartPark.business.deepFoundationMonitoring.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.deepFoundationMonitoring.entity.MonitoringData;
import com.smartPark.business.deepFoundationMonitoring.entity.vo.MonitoringDataVo;
import com.smartPark.business.deepFoundationMonitoring.mapper.MonitoringDataMapper;
import com.smartPark.business.deepFoundationMonitoring.service.MonitoringDataService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.common.device.mapper.DeviceMapper;
import com.smartPark.common.entity.device.Device;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class MonitoringDataServiceImpl extends ServiceImpl<MonitoringDataMapper, MonitoringData> implements MonitoringDataService {

    @Autowired
    private DeviceMapper deviceMapper;

    @Override
    public void flowDataProcess(FlowPushData flowData) {
        //取设备编码
        String deviceCode = flowData.getDevice_id();
        //流水时间
        Long timestamp = flowData.getTimestamp();
        //查询设备信息
        Device device = deviceMapper.selectById(deviceCode);
        if (device == null){
            log.warn("设备不存在,deviceCode:{}",deviceCode);
            return;
        }
        Object dataObj = flowData.getData();
        if (dataObj == null){
            log.warn("流水数据为空,deviceCode:{}",deviceCode);
            return;
        }
        cn.hutool.json.JSONObject dataJson = JSONUtil.parseObj(dataObj);
        
        // 封装监测数据
        MonitoringData monitoringData = new MonitoringData();
        monitoringData.setDeviceCode(deviceCode);
        if (timestamp != null) {
            monitoringData.setCreateTime(new Date(timestamp));
        }else {
            monitoringData.setCreateTime(new Date());
        }

        // 属性转换
        if (dataJson.containsKey("water_pressure")) {
            monitoringData.setWaterPressure(dataJson.getStr("water_pressure"));
        }
        if (dataJson.containsKey("water_level")) {
            monitoringData.setWaterLevel(dataJson.getStr("water_level"));
        }
        if (dataJson.containsKey("stress")) {
            monitoringData.setStress(dataJson.getStr("stress"));
        }
        if (dataJson.containsKey("tilt")) {
            monitoringData.setTilt(dataJson.getStr("tilt"));
        }
        if (dataJson.containsKey("settlement")) {
            monitoringData.setSettlement(dataJson.getStr("settlement"));
        }

        // 保存监测数据
        this.save(monitoringData);
    }

    @Override
    public IPage<MonitoringDataVo> queryListByPage(RequestModel<MonitoringDataVo> requestModel) {
        Page page = requestModel.getPage();
        MonitoringDataVo params = requestModel.getCustomQueryParams();
        //处理排序
        if (StringUtils.isBlank(params.getOrder())){
            params.setOrder("desc");
        }
        return baseMapper.queryListByPage(page, params);
    }
}