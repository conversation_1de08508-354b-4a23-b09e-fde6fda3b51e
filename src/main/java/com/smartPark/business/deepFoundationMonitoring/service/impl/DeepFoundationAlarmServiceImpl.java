package com.smartPark.business.deepFoundationMonitoring.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.deepFoundationMonitoring.entity.vo.DeepFoundationAlarmVo;
import com.smartPark.business.deepFoundationMonitoring.entity.vo.DeepFoundationDeviceVo;
import com.smartPark.business.deepFoundationMonitoring.mapper.DeepFoundationAlarmMapper;
import com.smartPark.business.deepFoundationMonitoring.service.DeepFoundationAlarmService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.DeviceModelConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Service
public class DeepFoundationAlarmServiceImpl implements DeepFoundationAlarmService {

    @Autowired
    private DeepFoundationAlarmMapper deepFoundationAlarmMapper;

    @Override
    public Map findAlarmStatistics(DeepFoundationDeviceVo fireMonitoringDeviceVo) {
        return null;
    }

    @Override
    public IPage<DeepFoundationAlarmVo> queryListByPage(RequestModel<DeepFoundationAlarmVo> requestModel) {
        Page page = requestModel.getPage();
        DeepFoundationAlarmVo deepFoundationAlarmVo = requestModel.getCustomQueryParams();
        deepFoundationAlarmVo.setModel(DeviceModelConstant.FOUNDATION_MONITORING.toString());
        IPage<DeepFoundationAlarmVo> deepFoundationDeviceList = deepFoundationAlarmMapper.findAlarmList(page,
                deepFoundationAlarmVo);
        deepFoundationDeviceList.getRecords().forEach(m ->{
            //区域范围
            if (StringUtils.isNotBlank(m.getAreaPath())){
                m.setAreaPath(m.getAreaPath().replace("@","/"));
            }
        });
        return deepFoundationDeviceList;
    }

    @Override
    public List<DeepFoundationAlarmVo> findAlarmByDeviceCode(String deviceCode) {
        List<DeepFoundationAlarmVo> records = getDeepFoundationDeviceAlarmVos((vo) -> vo.setDeviceCode(deviceCode));
        return records;
    }

    @Override
    public DeepFoundationAlarmVo findById(Long id) {
        List<DeepFoundationAlarmVo> records = getDeepFoundationDeviceAlarmVos((vo) -> vo.setId(id));
        DeepFoundationAlarmVo deepFoundationAlarmVo = records.get(0);
        return deepFoundationAlarmVo;
    }

    /**
     * 不分页查询告警集合
     * @param consumer 调用的地方自己设置值
     * @return
     */
    private List<DeepFoundationAlarmVo> getDeepFoundationDeviceAlarmVos(Consumer<DeepFoundationAlarmVo> consumer) {
        RequestModel<DeepFoundationAlarmVo> requestModel = new RequestModel<>();
        DeepFoundationAlarmVo deepFoundationAlarmVo = new DeepFoundationAlarmVo();
        consumer.accept(deepFoundationAlarmVo);
        requestModel.setCustomQueryParams(deepFoundationAlarmVo);
        requestModel.setPage(new Page(0,-1));
        IPage<DeepFoundationAlarmVo> alarmVoIPage = queryListByPage(requestModel);
        List<DeepFoundationAlarmVo> records = alarmVoIPage.getRecords();
        return records;
    }
}
