package com.smartPark.business.accessControl.entity.dto;

import com.smartPark.business.accessControl.entity.AccessControl;
import com.smartPark.common.annotation.GaodeText;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 门禁设备详情DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@GaodeText
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AccessControlDeviceDTO extends AccessControl {

    /**
     * 设备名称
     */
    private String sbmc;

    /**
     * 设备标识码
     */
    private String bsm;

    /**
     * 所在街道
     */
    private String szjd;

    /**
     * 所在社区
     */
    private String szsq;

    /**
     * 所在单元网格
     */
    private String szdywg;

    /**
     * 区域全路径
     */
    private String areaPath;

    /**
     * 区域全路径集合
     */
    private List<String> areaPaths;

    /**
     * 在线状态（1在线0离线）
     */
    private Integer status;

    /**
     * 告警状态
     * @apiNote 0:正常; 1:告警
     */
    private Integer alarmState;

    /**
     * 位置定位信息X坐标
     */
    private Double objX;

    /**
     * 位置定位信息Y坐标
     */
    private Double objY;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 属性状态列表
     */
    private List<DevicePropertyStatus> devicePropertyStatusList;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备状态名称
     */
    private String deviceStatusName;

    /**
     * 控制状态名称
     */
    private String controlStatusName;

    /**
     * 通讯状态名称
     */
    private String communicationStatusName;

    /**
     * 今日进入人数
     */
    private Integer todayEnterCount;

    /**
     * 今日外出人数
     */
    private Integer todayExitCount;

    /**
     * 当前授权人数
     */
    private Integer currentAuthCount;

    /**
     * 最后进出时间
     */
    private Date lastAccessTime;

    /**
     * 最后进出人员
     */
    private String lastAccessPerson;

    /**
     * 最后进出类型(1:进入,2:外出)
     */
    private Integer lastAccessType;

    /**
     * 设备固件版本
     */
    private String firmwareVersion;

    /**
     * 设备IP地址
     */
    private String deviceIp;

    /**
     * 设备MAC地址
     */
    private String deviceMac;

    /**
     * 最后心跳时间
     */
    private Date lastHeartbeatTime;

    /**
     * 信号强度
     */
    private Integer signalStrength;

    /**
     * 电池电量(百分比)
     */
    private Integer batteryLevel;
}
