package com.smartPark.business.accessControl.entity.dto;

import com.smartPark.business.accessControl.entity.AccessControl;
import com.smartPark.common.annotation.GaodeText;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 门禁设备VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@GaodeText
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AccessControlVo extends AccessControl {

    /**
     * 设备标识码
     */
    private String bsm;

    /**
     * 所在街道
     */
    private String szjd;

    /**
     * 所在社区
     */
    private String szsq;

    /**
     * 所在单元网格
     */
    private String szdywg;

    /**
     * 区域全路径
     */
    private String areaPath;

    /**
     * 区域全路径集合
     */
    private List<String> areaPaths;

    /**
     * 在线状态（1在线0离线）
     */
    private Integer status;

    /**
     * 在线状态集合
     */
    private List<Integer> statusList;

    /**
     * 告警状态
     * @apiNote 0:正常; 1:告警
     */
    private Integer alarmState;

    /**
     * 位置定位信息X坐标
     */
    private Double objX;

    /**
     * 位置定位信息Y坐标
     */
    private Double objY;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 设备编号模糊查询
     */
    private String deviceCodeKey;

    /**
     * 设备名称模糊查询
     */
    private String deviceNameKey;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 设备类型集合
     */
    private List<Integer> deviceTypeList;

    /**
     * 设备状态集合
     */
    private List<Integer> deviceStatusList;

    /**
     * 控制状态集合
     */
    private List<Integer> controlStatusList;

    /**
     * 通讯状态集合
     */
    private List<Integer> communicationStatusList;

    /**
     * 今日进入人数
     */
    private Integer todayEnterCount;

    /**
     * 今日外出人数
     */
    private Integer todayExitCount;

    /**
     * 当前授权人数
     */
    private Integer currentAuthCount;

    /**
     * 最后进出时间
     */
    private Date lastAccessTime;

    /**
     * 最后进出人员
     */
    private String lastAccessPerson;

    /**
     * 设备类型名称
     */
    private String deviceTypeName;

    /**
     * 设备状态名称
     */
    private String deviceStatusName;

    /**
     * 控制状态名称
     */
    private String controlStatusName;

    /**
     * 通讯状态名称
     */
    private String communicationStatusName;
}
