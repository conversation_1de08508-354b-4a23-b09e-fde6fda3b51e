package com.smartPark.business.accessControl.entity.dto;

import com.smartPark.business.accessControl.entity.AccessHistory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 进出门历史VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AccessHistoryVo extends AccessHistory {

    /**
     * 人员姓名模糊查询
     */
    private String personNameKey;

    /**
     * 身份证号模糊查询
     */
    private String personIdCardKey;

    /**
     * 手机号模糊查询
     */
    private String personPhoneKey;

    /**
     * 设备编码模糊查询
     */
    private String deviceCodeKey;

    /**
     * 设备名称模糊查询
     */
    private String deviceNameKey;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 进出类型集合
     */
    private List<Integer> accessTypeList;

    /**
     * 识别方式集合
     */
    private List<Integer> recognitionMethodList;

    /**
     * 进出状态集合
     */
    private List<Integer> accessStatusList;

    /**
     * 门禁设备ID集合
     */
    private List<Long> accessControlIdList;

    /**
     * 人员ID集合
     */
    private List<Long> personIdList;

    /**
     * 区域全路径
     */
    private String areaPath;

    /**
     * 区域全路径集合
     */
    private List<String> areaPaths;

    /**
     * 所在街道
     */
    private String szjd;

    /**
     * 所在社区
     */
    private String szsq;

    /**
     * 所在单元网格
     */
    private String szdywg;

    /**
     * 进出类型名称
     */
    private String accessTypeName;

    /**
     * 识别方式名称
     */
    private String recognitionMethodName;

    /**
     * 进出状态名称
     */
    private String accessStatusName;

    /**
     * sql 查询按时间分组 格式字符串
     * %Y-%m-%d
     * %Y-%m-%d %H
     */
    private String dateFormat;

    /**
     * 体温是否正常(1:正常,0:异常)
     */
    private Integer temperatureNormal;

    /**
     * 体温范围查询-最小值
     */
    private Double temperatureMin;

    /**
     * 体温范围查询-最大值
     */
    private Double temperatureMax;
}
