package com.smartPark.business.accessControl.entity.dto;

import com.smartPark.business.accessControl.entity.AccessAuth;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 门禁授权VO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AccessAuthVo extends AccessAuth {

    /**
     * 门禁设备名称
     */
    private String deviceName;

    /**
     * 安装位置
     */
    private String installLocation;

    /**
     * 权属单位名称
     */
    private String ownerEnterpriseName;

    /**
     * 人员姓名模糊查询
     */
    private String personNameKey;

    /**
     * 身份证号模糊查询
     */
    private String personIdCardKey;

    /**
     * 手机号模糊查询
     */
    private String personPhoneKey;

    /**
     * 设备编码模糊查询
     */
    private String deviceCodeKey;

    /**
     * 设备名称模糊查询
     */
    private String deviceNameKey;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 授权类型集合
     */
    private List<Integer> authTypeList;

    /**
     * 授权状态集合
     */
    private List<Integer> authStatusList;

    /**
     * 时间限制类型集合
     */
    private List<Integer> timeLimitTypeList;

    /**
     * 门禁设备ID集合
     */
    private List<Long> accessControlIdList;

    /**
     * 人员ID集合
     */
    private List<Long> personIdList;

    /**
     * 授权类型名称
     */
    private String authTypeName;

    /**
     * 授权状态名称
     */
    private String authStatusName;

    /**
     * 时间限制类型名称
     */
    private String timeLimitTypeName;

    /**
     * 是否即将过期(7天内过期)
     */
    private Boolean willExpire;

    /**
     * 剩余有效天数
     */
    private Integer remainingDays;

    /**
     * 最后使用时间
     */
    private Date lastUseTime;

    /**
     * 使用次数
     */
    private Integer useCount;
}
