package com.smartPark.business.accessControl.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * 进出门历史记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_access_history")
public class AccessHistory extends Model<AccessHistory> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 门禁设备ID
     */
    @TableField("access_control_id_")
    private Long accessControlId;

    /**
     * 门禁设备编码
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 门禁设备名称
     */
    @TableField("device_name_")
    private String deviceName;

    /**
     * 人员ID
     */
    @TableField("person_id_")
    private Long personId;

    /**
     * 人员姓名
     */
    @TableField("person_name_")
    private String personName;

    /**
     * 人员身份证号
     */
    @TableField("person_id_card_")
    private String personIdCard;

    /**
     * 人员手机号
     */
    @TableField("person_phone_")
    private String personPhone;

    /**
     * 进出类型(1:进入,2:外出)
     */
    @TableField("access_type_")
    private Integer accessType;

    /**
     * 识别方式(1:刷卡,2:人脸识别,3:指纹,4:密码,5:其他)
     */
    @TableField("recognition_method_")
    private Integer recognitionMethod;

    /**
     * 进出状态(1:成功,0:失败)
     */
    @TableField("access_status_")
    private Integer accessStatus;

    /**
     * 失败原因
     */
    @TableField("failure_reason_")
    private String failureReason;

    /**
     * 进出时间
     */
    @TableField("access_time_")
    private Date accessTime;

    /**
     * 体温(摄氏度,保留一位小数)
     */
    @TableField("temperature_")
    private Double temperature;

    /**
     * 是否戴口罩(1:是,0:否)
     */
    @TableField("wear_mask_")
    private Integer wearMask;

    /**
     * 抓拍照片路径
     */
    @TableField("capture_image_path_")
    private String captureImagePath;

    /**
     * 安装位置
     */
    @TableField("install_location_")
    private String installLocation;

    /**
     * 权属单位名称
     */
    @TableField("owner_enterprise_name_")
    private String ownerEnterpriseName;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，0未删除，1已删除
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private Set<Long> ids;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
