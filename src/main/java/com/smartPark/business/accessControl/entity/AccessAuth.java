package com.smartPark.business.accessControl.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * 门禁授权记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_access_auth")
public class AccessAuth extends Model<AccessAuth> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 门禁设备ID
     */
    @TableField("access_control_id_")
    private Long accessControlId;

    /**
     * 门禁设备编码
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 授权人员ID
     */
    @TableField("person_id_")
    private Long personId;

    /**
     * 授权人员姓名
     */
    @TableField("person_name_")
    private String personName;

    /**
     * 授权人员身份证号
     */
    @TableField("person_id_card_")
    private String personIdCard;

    /**
     * 授权人员手机号
     */
    @TableField("person_phone_")
    private String personPhone;

    /**
     * 授权类型(1:临时授权,2:长期授权,3:访客授权)
     */
    @TableField("auth_type_")
    private Integer authType;

    /**
     * 授权状态(1:有效,0:已撤销,2:已过期)
     */
    @TableField("auth_status_")
    private Integer authStatus;

    /**
     * 授权开始时间
     */
    @TableField("auth_start_time_")
    private Date authStartTime;

    /**
     * 授权结束时间
     */
    @TableField("auth_end_time_")
    private Date authEndTime;

    /**
     * 进入权限(1:允许进入,0:不允许进入)
     */
    @TableField("enter_permission_")
    private Integer enterPermission;

    /**
     * 外出权限(1:允许外出,0:不允许外出)
     */
    @TableField("exit_permission_")
    private Integer exitPermission;

    /**
     * 时间限制类型(1:全天,2:工作时间,3:自定义时间)
     */
    @TableField("time_limit_type_")
    private Integer timeLimitType;

    /**
     * 自定义时间限制(JSON格式存储)
     */
    @TableField("custom_time_limit_")
    private String customTimeLimit;

    /**
     * 授权人ID
     */
    @TableField("authorizer_id_")
    private Long authorizerId;

    /**
     * 授权人姓名
     */
    @TableField("authorizer_name_")
    private String authorizerName;

    /**
     * 撤销人ID
     */
    @TableField("revoker_id_")
    private Long revokerId;

    /**
     * 撤销人姓名
     */
    @TableField("revoker_name_")
    private String revokerName;

    /**
     * 撤销时间
     */
    @TableField("revoke_time_")
    private Date revokeTime;

    /**
     * 撤销原因
     */
    @TableField("revoke_reason_")
    private String revokeReason;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，0未删除，1已删除
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private Set<Long> ids;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
