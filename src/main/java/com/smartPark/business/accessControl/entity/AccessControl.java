package com.smartPark.business.accessControl.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * 门禁设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_access_control")
public class AccessControl extends Model<AccessControl> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 物联网平台设备id
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 门禁设备名称
     */
    @TableField("device_name_")
    private String deviceName;

    /**
     * 门禁设备类型(1:刷卡门禁,2:人脸识别门禁,3:指纹门禁,4:密码门禁,5:综合门禁)
     */
    @TableField("device_type_")
    private Integer deviceType;

    /**
     * 安装位置
     */
    @TableField("install_location_")
    private String installLocation;

    /**
     * 使用状态(1启用0禁用)
     */
    @TableField(exist = false)
    private Integer useStatus;

    /**
     * 设备状态(1正常,2故障,3维修中)
     */
    @TableField("device_status_")
    private Integer deviceStatus;

    /**
     * 门禁控制状态(1开启,0关闭)
     */
    @TableField("control_status_")
    private Integer controlStatus;

    /**
     * 通讯状态(1在线,0离线)
     */
    @TableField("communication_status_")
    private Integer communicationStatus;

    /**
     * 监控设备编码
     */
    @TableField("monitor_device_code_")
    private String monitorDeviceCode;

    /**
     * 权属单位ID
     */
    @TableField("owner_enterprise_id_")
    private Long ownerEnterpriseId;

    /**
     * 权属单位名称
     */
    @TableField("owner_enterprise_name_")
    private String ownerEnterpriseName;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，0未删除，1已删除
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private Set<Long> ids;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
