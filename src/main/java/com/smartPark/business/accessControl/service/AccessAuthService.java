package com.smartPark.business.accessControl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.accessControl.entity.AccessAuth;
import com.smartPark.business.accessControl.entity.dto.AccessAuthVo;
import com.smartPark.common.base.model.RequestModel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 门禁授权记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AccessAuthService extends IService<AccessAuth> {

    /**
     * 分页查询门禁授权记录列表
     *
     * @param requestModel 请求参数
     * @return 分页结果
     */
    IPage<AccessAuthVo> queryListByPage(RequestModel<AccessAuthVo> requestModel);

    /**
     * 新增门禁授权
     *
     * @param accessAuth 门禁授权信息
     */
    void insert(AccessAuth accessAuth);

    /**
     * 根据id编辑门禁授权
     *
     * @param accessAuth 门禁授权信息
     */
    void updateOne(AccessAuth accessAuth);

    /**
     * 根据ID查询门禁授权详情
     *
     * @param id 授权ID
     * @return 授权详情
     */
    AccessAuthVo findById(Long id);

    /**
     * 删除门禁授权（包含批量删除）
     *
     * @param ids 授权ID集合
     */
    void delBatch(Set<Long> ids);

    /**
     * 批量授权
     *
     * @param accessAuthList 授权信息列表
     */
    void batchAuth(List<AccessAuth> accessAuthList);

    /**
     * 撤销门禁授权
     *
     * @param id 授权ID
     * @param revokeReason 撤销原因
     */
    void revokeAuth(Long id, String revokeReason);

    /**
     * 批量撤销门禁授权
     *
     * @param ids 授权ID集合
     * @param revokeReason 撤销原因
     */
    void batchRevokeAuth(Set<Long> ids, String revokeReason);

    /**
     * 延期授权
     *
     * @param id 授权ID
     * @param extendDays 延期天数
     */
    void extendAuth(Long id, Integer extendDays);

    /**
     * 批量延期授权
     *
     * @param ids 授权ID集合
     * @param extendDays 延期天数
     */
    void batchExtendAuth(Set<Long> ids, Integer extendDays);

    /**
     * 根据授权类型统计数量
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByAuthType(AccessAuthVo accessAuthVo);

    /**
     * 根据授权状态统计数量
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByAuthStatus(AccessAuthVo accessAuthVo);

    /**
     * 根据设备统计授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByDevice(AccessAuthVo accessAuthVo);

    /**
     * 根据时间统计授权趋势
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countTrendByTime(AccessAuthVo accessAuthVo);

    /**
     * 统计即将过期的授权数量(7天内过期)
     *
     * @param accessAuthVo 查询条件
     * @return 即将过期的授权数量
     */
    Long countWillExpire(AccessAuthVo accessAuthVo);

    /**
     * 统计已过期的授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 已过期的授权数量
     */
    Long countExpired(AccessAuthVo accessAuthVo);

    /**
     * 统计有效授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 有效授权数量
     */
    Long countValid(AccessAuthVo accessAuthVo);

    /**
     * 统计总授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 总授权数量
     */
    Long countTotal(AccessAuthVo accessAuthVo);

    /**
     * 查询即将过期的授权列表
     *
     * @param accessAuthVo 查询条件
     * @return 即将过期的授权列表
     */
    List<AccessAuthVo> queryWillExpireList(AccessAuthVo accessAuthVo);

    /**
     * 导出门禁授权数据
     *
     * @param accessAuthVo 查询条件
     * @param request 请求对象
     * @param response 响应对象
     * @return 导出结果
     */
    Long export(AccessAuthVo accessAuthVo, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询导出excel数据
     *
     * @param accessAuthVo 查询条件
     * @return 导出数据列表
     */
    List<AccessAuthVo> queryList4Export(AccessAuthVo accessAuthVo);

    /**
     * 门禁授权导入
     *
     * @param file 导入文件
     * @return 导入结果
     * @throws IOException IO异常
     */
    Long imports(MultipartFile file) throws IOException;
}
