package com.smartPark.business.accessControl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.accessControl.entity.AccessHistory;
import com.smartPark.business.accessControl.entity.dto.AccessHistoryVo;
import com.smartPark.common.base.model.RequestModel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 进出门历史记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AccessHistoryService extends IService<AccessHistory> {

    /**
     * 分页查询进出门历史记录列表
     *
     * @param requestModel 请求参数
     * @return 分页结果
     */
    IPage<AccessHistoryVo> queryListByPage(RequestModel<AccessHistoryVo> requestModel);

    /**
     * 新增进出门历史记录
     *
     * @param accessHistory 进出门历史记录信息
     */
    void insert(AccessHistory accessHistory);

    /**
     * 根据ID查询进出门历史记录详情
     *
     * @param id 记录ID
     * @return 记录详情
     */
    AccessHistoryVo findById(Long id);

    /**
     * 根据进出类型统计数量
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByAccessType(AccessHistoryVo accessHistoryVo);

    /**
     * 根据识别方式统计数量
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByRecognitionMethod(AccessHistoryVo accessHistoryVo);

    /**
     * 根据进出状态统计数量
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByAccessStatus(AccessHistoryVo accessHistoryVo);

    /**
     * 根据设备统计进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByDevice(AccessHistoryVo accessHistoryVo);

    /**
     * 根据时间统计进出趋势
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countTrendByTime(AccessHistoryVo accessHistoryVo);

    /**
     * 根据人员统计进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByPerson(AccessHistoryVo accessHistoryVo);

    /**
     * 统计今日进入人数
     *
     * @param accessHistoryVo 查询条件
     * @return 今日进入人数
     */
    Long countTodayEnter(AccessHistoryVo accessHistoryVo);

    /**
     * 统计今日外出人数
     *
     * @param accessHistoryVo 查询条件
     * @return 今日外出人数
     */
    Long countTodayExit(AccessHistoryVo accessHistoryVo);

    /**
     * 统计总进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 总进出次数
     */
    Long countTotal(AccessHistoryVo accessHistoryVo);

    /**
     * 统计成功进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 成功进出次数
     */
    Long countSuccess(AccessHistoryVo accessHistoryVo);

    /**
     * 统计失败进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 失败进出次数
     */
    Long countFailed(AccessHistoryVo accessHistoryVo);

    /**
     * 查询最近进出记录
     *
     * @param accessHistoryVo 查询条件
     * @param limit 限制条数
     * @return 最近进出记录
     */
    List<AccessHistoryVo> queryRecentList(AccessHistoryVo accessHistoryVo, Integer limit);

    /**
     * 根据设备编码查询最后进出记录
     *
     * @param deviceCode 设备编码
     * @return 最后进出记录
     */
    AccessHistoryVo findLastAccessByDeviceCode(String deviceCode);

    /**
     * 导出进出门历史数据
     *
     * @param accessHistoryVo 查询条件
     * @param request 请求对象
     * @param response 响应对象
     * @return 导出结果
     */
    Long export(AccessHistoryVo accessHistoryVo, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询导出excel数据
     *
     * @param accessHistoryVo 查询条件
     * @return 导出数据列表
     */
    List<AccessHistoryVo> queryList4Export(AccessHistoryVo accessHistoryVo);
}
