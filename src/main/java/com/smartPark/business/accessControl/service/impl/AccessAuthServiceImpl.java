package com.smartPark.business.accessControl.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.accessControl.entity.AccessAuth;
import com.smartPark.business.accessControl.entity.dto.AccessAuthVo;
import com.smartPark.business.accessControl.mapper.AccessAuthMapper;
import com.smartPark.business.accessControl.service.AccessAuthService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 门禁授权记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class AccessAuthServiceImpl extends ServiceImpl<AccessAuthMapper, AccessAuth> implements AccessAuthService {

    @Autowired
    private CommonService commonService;

    @Override
    public IPage<AccessAuthVo> queryListByPage(RequestModel<AccessAuthVo> requestModel) {
        Page page = requestModel.getPage();
        AccessAuthVo accessAuthVo = requestModel.getCustomQueryParams();
        IPage<AccessAuthVo> accessAuthIPage = baseMapper.queryListByPage(page, accessAuthVo);
        
        List<AccessAuthVo> records = accessAuthIPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(this::processAccessAuthVo);
        }
        
        return accessAuthIPage;
    }

    @Transactional
    @Override
    public void insert(AccessAuth accessAuth) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        // 验证授权信息
        validateAccessAuth(accessAuth);
        
        // 验证是否已存在相同的授权
        checkExist(accessAuth);
        
        coreParamSj.add("门禁授权人员:" + accessAuth.getPersonName());
        coreParamSj.add("设备编码:" + accessAuth.getDeviceCode());
        sj.add("门禁授权人员:" + accessAuth.getPersonName());
        sj.add("设备编码:" + accessAuth.getDeviceCode());
        
        // 设置基本属性
        setBase(accessAuth);
        accessAuth.setAuthStatus(1); // 默认有效状态
        save(accessAuth);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void updateOne(AccessAuth accessAuth) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        Assert.notNull(accessAuth.getId(), "id不能为空");
        
        // 验证授权信息
        validateAccessAuth(accessAuth);
        
        coreParamSj.add("门禁授权人员:" + accessAuth.getPersonName());
        coreParamSj.add("设备编码:" + accessAuth.getDeviceCode());
        sj.add("门禁授权人员:" + accessAuth.getPersonName());
        sj.add("设备编码:" + accessAuth.getDeviceCode());
        
        // 设置修改信息
        setModify(accessAuth);
        updateById(accessAuth);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Override
    public AccessAuthVo findById(Long id) {
        Assert.notNull(id, "id不能为空");
        
        AccessAuth accessAuth = getById(id);
        if (accessAuth == null) {
            throw new BusinessException("授权记录不存在");
        }
        
        AccessAuthVo accessAuthVo = new AccessAuthVo();
        // TODO: 使用BeanUtil.copyProperties或类似工具进行对象转换
        // BeanUtil.copyProperties(accessAuth, accessAuthVo);
        
        processAccessAuthVo(accessAuthVo);
        
        return accessAuthVo;
    }

    @Transactional
    @Override
    public void delBatch(Set<Long> ids) {
        Assert.notEmpty(ids, "删除的id不能为空");
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        // 查询要删除的授权信息
        List<AccessAuth> accessAuths = listByIds(ids);
        if (CollectionUtil.isNotEmpty(accessAuths)) {
            List<String> personNames = accessAuths.stream()
                    .map(AccessAuth::getPersonName)
                    .collect(Collectors.toList());
            
            coreParamSj.add("删除门禁授权人员:" + String.join(",", personNames));
            sj.add("删除门禁授权人员:" + String.join(",", personNames));
        }
        
        // 逻辑删除
        removeByIds(ids);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void batchAuth(List<AccessAuth> accessAuthList) {
        Assert.notEmpty(accessAuthList, "授权信息不能为空");
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        // 验证并设置授权信息
        for (AccessAuth accessAuth : accessAuthList) {
            validateAccessAuth(accessAuth);
            setBase(accessAuth);
            accessAuth.setAuthStatus(1); // 默认有效状态
        }
        
        saveBatch(accessAuthList);
        
        coreParamSj.add("批量授权数量:" + accessAuthList.size());
        sj.add("批量授权数量:" + accessAuthList.size());
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void revokeAuth(Long id, String revokeReason) {
        Assert.notNull(id, "授权ID不能为空");
        
        AccessAuth accessAuth = getById(id);
        if (accessAuth == null) {
            throw new BusinessException("授权记录不存在");
        }
        
        if (accessAuth.getAuthStatus() == 0) {
            throw new BusinessException("授权已被撤销");
        }
        
        // 设置撤销信息
        accessAuth.setAuthStatus(0); // 已撤销
        accessAuth.setRevokeTime(new Date());
        accessAuth.setRevokeReason(revokeReason);
        // TODO: 设置撤销人信息
        // accessAuth.setRevokerId(getCurrentUserId());
        // accessAuth.setRevokerName(getCurrentUserName());
        
        setModify(accessAuth);
        updateById(accessAuth);
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        coreParamSj.add("撤销门禁授权:" + accessAuth.getPersonName());
        sj.add("撤销门禁授权:" + accessAuth.getPersonName());
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void batchRevokeAuth(Set<Long> ids, String revokeReason) {
        Assert.notEmpty(ids, "授权ID不能为空");
        
        // 使用Mapper的批量撤销方法
        // TODO: 获取当前用户信息
        Long revokerId = 1L; // getCurrentUserId();
        String revokerName = "系统管理员"; // getCurrentUserName();
        
        int affectedRows = baseMapper.batchRevokeAuth(new ArrayList<>(ids), revokerId, revokerName, revokeReason);
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        coreParamSj.add("批量撤销门禁授权数量:" + affectedRows);
        sj.add("批量撤销门禁授权数量:" + affectedRows);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void extendAuth(Long id, Integer extendDays) {
        Assert.notNull(id, "授权ID不能为空");
        Assert.notNull(extendDays, "延期天数不能为空");
        Assert.isTrue(extendDays > 0, "延期天数必须大于0");
        
        AccessAuth accessAuth = getById(id);
        if (accessAuth == null) {
            throw new BusinessException("授权记录不存在");
        }
        
        if (accessAuth.getAuthStatus() != 1) {
            throw new BusinessException("只能延期有效的授权");
        }
        
        // 延期授权结束时间
        Date newEndTime = DateUtil.offsetDay(accessAuth.getAuthEndTime(), extendDays);
        accessAuth.setAuthEndTime(newEndTime);
        
        setModify(accessAuth);
        updateById(accessAuth);
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        coreParamSj.add("延期门禁授权:" + accessAuth.getPersonName() + ",延期天数:" + extendDays);
        sj.add("延期门禁授权:" + accessAuth.getPersonName() + ",延期天数:" + extendDays);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void batchExtendAuth(Set<Long> ids, Integer extendDays) {
        Assert.notEmpty(ids, "授权ID不能为空");
        Assert.notNull(extendDays, "延期天数不能为空");
        Assert.isTrue(extendDays > 0, "延期天数必须大于0");
        
        List<AccessAuth> accessAuths = listByIds(ids);
        if (CollectionUtil.isEmpty(accessAuths)) {
            throw new BusinessException("未找到授权记录");
        }
        
        // 批量延期
        for (AccessAuth accessAuth : accessAuths) {
            if (accessAuth.getAuthStatus() == 1) {
                Date newEndTime = DateUtil.offsetDay(accessAuth.getAuthEndTime(), extendDays);
                accessAuth.setAuthEndTime(newEndTime);
                setModify(accessAuth);
            }
        }
        
        updateBatchById(accessAuths);
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        coreParamSj.add("批量延期门禁授权数量:" + accessAuths.size() + ",延期天数:" + extendDays);
        sj.add("批量延期门禁授权数量:" + accessAuths.size() + ",延期天数:" + extendDays);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Override
    public Map<String, Map<String, Long>> countByAuthType(AccessAuthVo accessAuthVo) {
        List<Map<String, Object>> result = baseMapper.countByAuthType(accessAuthVo);
        return processCountResult(result);
    }

    @Override
    public Map<String, Map<String, Long>> countByAuthStatus(AccessAuthVo accessAuthVo) {
        List<Map<String, Object>> result = baseMapper.countByAuthStatus(accessAuthVo);
        return processCountResult(result);
    }

    @Override
    public Map<String, Map<String, Long>> countByDevice(AccessAuthVo accessAuthVo) {
        List<Map<String, Object>> result = baseMapper.countByDevice(accessAuthVo);
        return processCountResult(result);
    }

    @Override
    public List<Map<String, Object>> countTrendByTime(AccessAuthVo accessAuthVo) {
        return baseMapper.countTrendByTime(accessAuthVo);
    }

    @Override
    public Long countWillExpire(AccessAuthVo accessAuthVo) {
        return baseMapper.countWillExpire(accessAuthVo);
    }

    @Override
    public Long countExpired(AccessAuthVo accessAuthVo) {
        return baseMapper.countExpired(accessAuthVo);
    }

    @Override
    public Long countValid(AccessAuthVo accessAuthVo) {
        return baseMapper.countValid(accessAuthVo);
    }

    @Override
    public Long countTotal(AccessAuthVo accessAuthVo) {
        return baseMapper.countTotal(accessAuthVo);
    }

    @Override
    public List<AccessAuthVo> queryWillExpireList(AccessAuthVo accessAuthVo) {
        List<AccessAuthVo> result = baseMapper.queryWillExpireList(accessAuthVo);
        if (CollectionUtil.isNotEmpty(result)) {
            result.forEach(this::processAccessAuthVo);
        }
        return result;
    }

    @Override
    public Long export(AccessAuthVo accessAuthVo, HttpServletRequest request, HttpServletResponse response) {
        // TODO: 实现导出功能
        return 0L;
    }

    @Override
    public List<AccessAuthVo> queryList4Export(AccessAuthVo accessAuthVo) {
        return baseMapper.queryList4Export(accessAuthVo);
    }

    @Override
    public Long imports(MultipartFile file) throws IOException {
        // TODO: 实现导入功能
        return 0L;
    }

    /**
     * 验证授权信息
     */
    private void validateAccessAuth(AccessAuth accessAuth) {
        Assert.hasLength(accessAuth.getDeviceCode(), "设备编码不能为空");
        Assert.hasLength(accessAuth.getPersonName(), "人员姓名不能为空");
        Assert.notNull(accessAuth.getAuthType(), "授权类型不能为空");
        Assert.notNull(accessAuth.getAuthStartTime(), "授权开始时间不能为空");
        Assert.notNull(accessAuth.getAuthEndTime(), "授权结束时间不能为空");
        
        if (accessAuth.getAuthEndTime().before(accessAuth.getAuthStartTime())) {
            throw new BusinessException("授权结束时间不能早于开始时间");
        }
    }

    /**
     * 验证是否已存在相同的授权
     */
    private void checkExist(AccessAuth accessAuth) {
        LambdaQueryWrapper<AccessAuth> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccessAuth::getDeviceCode, accessAuth.getDeviceCode());
        wrapper.eq(AccessAuth::getPersonIdCard, accessAuth.getPersonIdCard());
        wrapper.eq(AccessAuth::getAuthStatus, 1); // 有效状态
        
        AccessAuth existAuth = getOne(wrapper);
        if (existAuth != null) {
            throw new BusinessException("该人员在此设备上已有有效授权");
        }
    }

    /**
     * 设置基本属性
     */
    private void setBase(AccessAuth accessAuth) {
        commonService.setCreateAndModifyInfo(accessAuth);
    }

    /**
     * 设置修改信息
     */
    private void setModify(AccessAuth accessAuth) {
        commonService.setModifyInfo(accessAuth);
    }

    /**
     * 处理授权VO对象
     */
    private void processAccessAuthVo(AccessAuthVo accessAuthVo) {
        // 计算是否即将过期
        if (accessAuthVo.getAuthEndTime() != null) {
            Date now = new Date();
            Date expireDate = DateUtil.offsetDay(now, 7);
            accessAuthVo.setWillExpire(accessAuthVo.getAuthEndTime().before(expireDate) && accessAuthVo.getAuthEndTime().after(now));
            
            // 计算剩余天数
            if (accessAuthVo.getAuthEndTime().after(now)) {
                long diffDays = DateUtil.betweenDay(now, accessAuthVo.getAuthEndTime(), false);
                accessAuthVo.setRemainingDays((int) diffDays);
            } else {
                accessAuthVo.setRemainingDays(0);
            }
        }
        
        // 设置状态名称
        setStatusNames(accessAuthVo);
    }

    /**
     * 设置状态名称
     */
    private void setStatusNames(AccessAuthVo accessAuthVo) {
        // TODO: 根据字典或枚举设置状态名称
        if (accessAuthVo.getAuthType() != null) {
            switch (accessAuthVo.getAuthType()) {
                case 1:
                    accessAuthVo.setAuthTypeName("临时授权");
                    break;
                case 2:
                    accessAuthVo.setAuthTypeName("长期授权");
                    break;
                case 3:
                    accessAuthVo.setAuthTypeName("访客授权");
                    break;
                default:
                    accessAuthVo.setAuthTypeName("未知");
                    break;
            }
        }
        
        if (accessAuthVo.getAuthStatus() != null) {
            switch (accessAuthVo.getAuthStatus()) {
                case 1:
                    accessAuthVo.setAuthStatusName("有效");
                    break;
                case 0:
                    accessAuthVo.setAuthStatusName("已撤销");
                    break;
                case 2:
                    accessAuthVo.setAuthStatusName("已过期");
                    break;
                default:
                    accessAuthVo.setAuthStatusName("未知");
                    break;
            }
        }
    }

    /**
     * 处理统计结果
     */
    private Map<String, Map<String, Long>> processCountResult(List<Map<String, Object>> result) {
        Map<String, Map<String, Long>> processedResult = new HashMap<>();
        
        if (CollectionUtil.isNotEmpty(result)) {
            for (Map<String, Object> item : result) {
                String key = String.valueOf(item.get("key"));
                Long count = Long.valueOf(String.valueOf(item.get("count")));
                
                Map<String, Long> countMap = processedResult.computeIfAbsent(key, k -> new HashMap<>());
                countMap.put("count", count);
            }
        }
        
        return processedResult;
    }
}
