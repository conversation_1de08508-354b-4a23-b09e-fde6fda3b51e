package com.smartPark.business.accessControl.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.accessControl.entity.AccessHistory;
import com.smartPark.business.accessControl.entity.dto.AccessHistoryVo;
import com.smartPark.business.accessControl.mapper.AccessHistoryMapper;
import com.smartPark.business.accessControl.service.AccessHistoryService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * 进出门历史记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class AccessHistoryServiceImpl extends ServiceImpl<AccessHistoryMapper, AccessHistory> implements AccessHistoryService {

    @Autowired
    private CommonService commonService;

    @Override
    public IPage<AccessHistoryVo> queryListByPage(RequestModel<AccessHistoryVo> requestModel) {
        Page page = requestModel.getPage();
        AccessHistoryVo accessHistoryVo = requestModel.getCustomQueryParams();
        IPage<AccessHistoryVo> accessHistoryIPage = baseMapper.queryListByPage(page, accessHistoryVo);
        
        List<AccessHistoryVo> records = accessHistoryIPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            records.forEach(this::processAccessHistoryVo);
        }
        
        return accessHistoryIPage;
    }

    @Transactional
    @Override
    public void insert(AccessHistory accessHistory) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        // 验证进出门记录信息
        validateAccessHistory(accessHistory);
        
        coreParamSj.add("进出门记录:" + accessHistory.getPersonName());
        coreParamSj.add("设备编码:" + accessHistory.getDeviceCode());
        coreParamSj.add("进出类型:" + (accessHistory.getAccessType() == 1 ? "进入" : "外出"));
        sj.add("进出门记录:" + accessHistory.getPersonName());
        sj.add("设备编码:" + accessHistory.getDeviceCode());
        sj.add("进出类型:" + (accessHistory.getAccessType() == 1 ? "进入" : "外出"));
        
        // 设置基本属性
        setBase(accessHistory);
        if (accessHistory.getAccessTime() == null) {
            accessHistory.setAccessTime(new Date());
        }
        save(accessHistory);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Override
    public AccessHistoryVo findById(Long id) {
        Assert.notNull(id, "id不能为空");
        
        AccessHistory accessHistory = getById(id);
        if (accessHistory == null) {
            throw new BusinessException("进出门记录不存在");
        }
        
        AccessHistoryVo accessHistoryVo = new AccessHistoryVo();
        // TODO: 使用BeanUtil.copyProperties或类似工具进行对象转换
        // BeanUtil.copyProperties(accessHistory, accessHistoryVo);
        
        processAccessHistoryVo(accessHistoryVo);
        
        return accessHistoryVo;
    }

    @Override
    public Map<String, Map<String, Long>> countByAccessType(AccessHistoryVo accessHistoryVo) {
        List<Map<String, Object>> result = baseMapper.countByAccessType(accessHistoryVo);
        return processCountResult(result);
    }

    @Override
    public Map<String, Map<String, Long>> countByRecognitionMethod(AccessHistoryVo accessHistoryVo) {
        List<Map<String, Object>> result = baseMapper.countByRecognitionMethod(accessHistoryVo);
        return processCountResult(result);
    }

    @Override
    public Map<String, Map<String, Long>> countByAccessStatus(AccessHistoryVo accessHistoryVo) {
        List<Map<String, Object>> result = baseMapper.countByAccessStatus(accessHistoryVo);
        return processCountResult(result);
    }

    @Override
    public Map<String, Map<String, Long>> countByDevice(AccessHistoryVo accessHistoryVo) {
        List<Map<String, Object>> result = baseMapper.countByDevice(accessHistoryVo);
        return processCountResult(result);
    }

    @Override
    public List<Map<String, Object>> countTrendByTime(AccessHistoryVo accessHistoryVo) {
        return baseMapper.countTrendByTime(accessHistoryVo);
    }

    @Override
    public Map<String, Map<String, Long>> countByPerson(AccessHistoryVo accessHistoryVo) {
        List<Map<String, Object>> result = baseMapper.countByPerson(accessHistoryVo);
        return processCountResult(result);
    }

    @Override
    public Long countTodayEnter(AccessHistoryVo accessHistoryVo) {
        return baseMapper.countTodayEnter(accessHistoryVo);
    }

    @Override
    public Long countTodayExit(AccessHistoryVo accessHistoryVo) {
        return baseMapper.countTodayExit(accessHistoryVo);
    }

    @Override
    public Long countTotal(AccessHistoryVo accessHistoryVo) {
        return baseMapper.countTotal(accessHistoryVo);
    }

    @Override
    public Long countSuccess(AccessHistoryVo accessHistoryVo) {
        return baseMapper.countSuccess(accessHistoryVo);
    }

    @Override
    public Long countFailed(AccessHistoryVo accessHistoryVo) {
        return baseMapper.countFailed(accessHistoryVo);
    }

    @Override
    public List<AccessHistoryVo> queryRecentList(AccessHistoryVo accessHistoryVo, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10; // 默认查询10条
        }
        
        List<AccessHistoryVo> result = baseMapper.queryRecentList(accessHistoryVo, limit);
        if (CollectionUtil.isNotEmpty(result)) {
            result.forEach(this::processAccessHistoryVo);
        }
        
        return result;
    }

    @Override
    public AccessHistoryVo findLastAccessByDeviceCode(String deviceCode) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        
        AccessHistoryVo accessHistoryVo = baseMapper.findLastAccessByDeviceCode(deviceCode);
        if (accessHistoryVo != null) {
            processAccessHistoryVo(accessHistoryVo);
        }
        
        return accessHistoryVo;
    }

    @Override
    public Long export(AccessHistoryVo accessHistoryVo, HttpServletRequest request, HttpServletResponse response) {
        // TODO: 实现导出功能
        return 0L;
    }

    @Override
    public List<AccessHistoryVo> queryList4Export(AccessHistoryVo accessHistoryVo) {
        return baseMapper.queryList4Export(accessHistoryVo);
    }

    /**
     * 验证进出门记录信息
     */
    private void validateAccessHistory(AccessHistory accessHistory) {
        Assert.hasLength(accessHistory.getDeviceCode(), "设备编码不能为空");
        Assert.hasLength(accessHistory.getPersonName(), "人员姓名不能为空");
        Assert.notNull(accessHistory.getAccessType(), "进出类型不能为空");
        Assert.notNull(accessHistory.getRecognitionMethod(), "识别方式不能为空");
        Assert.notNull(accessHistory.getAccessStatus(), "进出状态不能为空");
        
        // 验证进出类型
        if (accessHistory.getAccessType() != 1 && accessHistory.getAccessType() != 2) {
            throw new BusinessException("进出类型只能是1(进入)或2(外出)");
        }
        
        // 验证识别方式
        if (accessHistory.getRecognitionMethod() < 1 || accessHistory.getRecognitionMethod() > 5) {
            throw new BusinessException("识别方式不正确");
        }
        
        // 验证进出状态
        if (accessHistory.getAccessStatus() != 0 && accessHistory.getAccessStatus() != 1) {
            throw new BusinessException("进出状态只能是0(失败)或1(成功)");
        }
        
        // 如果进出失败，必须有失败原因
        if (accessHistory.getAccessStatus() == 0 && StringUtils.isBlank(accessHistory.getFailureReason())) {
            throw new BusinessException("进出失败时必须填写失败原因");
        }
        
        // 体温验证
        if (accessHistory.getTemperature() != null) {
            if (accessHistory.getTemperature() < 30.0 || accessHistory.getTemperature() > 45.0) {
                throw new BusinessException("体温数值不合理");
            }
        }
    }

    /**
     * 设置基本属性
     */
    private void setBase(AccessHistory accessHistory) {
        // 进出门历史记录只需要设置创建时间
        if (accessHistory.getCreateTime() == null) {
            accessHistory.setCreateTime(new Date());
        }
    }

    /**
     * 处理进出门历史VO对象
     */
    private void processAccessHistoryVo(AccessHistoryVo accessHistoryVo) {
        // 区域范围处理
        if (StringUtils.isNotBlank(accessHistoryVo.getAreaPath())) {
            accessHistoryVo.setAreaPath(accessHistoryVo.getAreaPath().replace("@", "/"));
        }
        
        // 设置状态名称
        setStatusNames(accessHistoryVo);
        
        // 设置体温是否正常
        if (accessHistoryVo.getTemperature() != null) {
            // 正常体温范围：36.0-37.5度
            accessHistoryVo.setTemperatureNormal(
                accessHistoryVo.getTemperature() >= 36.0 && accessHistoryVo.getTemperature() <= 37.5 ? 1 : 0
            );
        }
    }

    /**
     * 设置状态名称
     */
    private void setStatusNames(AccessHistoryVo accessHistoryVo) {
        // TODO: 根据字典或枚举设置状态名称
        if (accessHistoryVo.getAccessType() != null) {
            switch (accessHistoryVo.getAccessType()) {
                case 1:
                    accessHistoryVo.setAccessTypeName("进入");
                    break;
                case 2:
                    accessHistoryVo.setAccessTypeName("外出");
                    break;
                default:
                    accessHistoryVo.setAccessTypeName("未知");
                    break;
            }
        }
        
        if (accessHistoryVo.getRecognitionMethod() != null) {
            switch (accessHistoryVo.getRecognitionMethod()) {
                case 1:
                    accessHistoryVo.setRecognitionMethodName("刷卡");
                    break;
                case 2:
                    accessHistoryVo.setRecognitionMethodName("人脸识别");
                    break;
                case 3:
                    accessHistoryVo.setRecognitionMethodName("指纹");
                    break;
                case 4:
                    accessHistoryVo.setRecognitionMethodName("密码");
                    break;
                case 5:
                    accessHistoryVo.setRecognitionMethodName("其他");
                    break;
                default:
                    accessHistoryVo.setRecognitionMethodName("未知");
                    break;
            }
        }
        
        if (accessHistoryVo.getAccessStatus() != null) {
            switch (accessHistoryVo.getAccessStatus()) {
                case 1:
                    accessHistoryVo.setAccessStatusName("成功");
                    break;
                case 0:
                    accessHistoryVo.setAccessStatusName("失败");
                    break;
                default:
                    accessHistoryVo.setAccessStatusName("未知");
                    break;
            }
        }
    }

    /**
     * 处理统计结果
     */
    private Map<String, Map<String, Long>> processCountResult(List<Map<String, Object>> result) {
        Map<String, Map<String, Long>> processedResult = new HashMap<>();
        
        if (CollectionUtil.isNotEmpty(result)) {
            for (Map<String, Object> item : result) {
                String key = String.valueOf(item.get("key"));
                Long count = Long.valueOf(String.valueOf(item.get("count")));
                
                Map<String, Long> countMap = processedResult.computeIfAbsent(key, k -> new HashMap<>());
                countMap.put("count", count);
            }
        }
        
        return processedResult;
    }
}
