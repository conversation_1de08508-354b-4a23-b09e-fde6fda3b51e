package com.smartPark.business.accessControl.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.accessControl.entity.AccessControl;
import com.smartPark.business.accessControl.entity.dto.AccessControlDeviceDTO;
import com.smartPark.business.accessControl.entity.dto.AccessControlVo;
import com.smartPark.business.accessControl.mapper.AccessControlMapper;
import com.smartPark.business.accessControl.service.AccessControlService;
import com.smartPark.business.manhole.entity.vo.CountByStateDTO;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.entity.device.DeviceApplicationModelRef;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.entity.device.DeviceStatusDTO;
import com.smartPark.common.exception.BusinessException;
import com.smartPark.common.utils.EventUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 门禁设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class AccessControlServiceImpl extends ServiceImpl<AccessControlMapper, AccessControl> implements AccessControlService {

    @Autowired
    private CommonService commonService;

    @Override
    public IPage<AccessControlDeviceDTO> queryListByPage(RequestModel<AccessControlVo> requestModel) {
        Page page = requestModel.getPage();
        AccessControlVo accessControlVo = requestModel.getCustomQueryParams();
        IPage<AccessControlDeviceDTO> accessControlIPage = baseMapper.queryListByPage(page, accessControlVo);
        List<AccessControlDeviceDTO> records = accessControlIPage.getRecords();
        
        if (CollectionUtil.isNotEmpty(records)) {
            // 获取设备编码列表
            List<String> codes = records.stream().map(AccessControlDeviceDTO::getDeviceCode).collect(Collectors.toList());
            
            // 设置设备属性状态
            setDevicePropertyStatus(records, codes);
            
            records.forEach(m -> {
                // 区域范围处理
                if (StringUtils.isNotBlank(m.getAreaPath())) {
                    m.setAreaPath(m.getAreaPath().replace("@", "/"));
                }
            });
        }
        
        return accessControlIPage;
    }

    @Transactional
    @Override
    public void insert(AccessControl accessControl) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        // 验证重复
        checkExist(accessControl);
        
        coreParamSj.add("门禁设备编码:" + accessControl.getDeviceCode());
        sj.add("门禁设备编码:" + accessControl.getDeviceCode());
        
        // 设置基本属性
        setBase(accessControl);
        save(accessControl);
        
        // 获取应用名，应用id
        DeviceApplicationModelRef device = getDeviceApplicationModelRef();
        device.setDeviceCode(accessControl.getDeviceCode());
        // 将设备信息关联至base的关联表中
        EventUtil.publishRefEvent(device);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void updateOne(AccessControl accessControl) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        Assert.notNull(accessControl.getId(), "id不能为空");
        
        // 验证重复（排除自身）
        checkExistForUpdate(accessControl);
        
        coreParamSj.add("门禁设备编码:" + accessControl.getDeviceCode());
        sj.add("门禁设备编码:" + accessControl.getDeviceCode());
        
        // 设置修改信息
        setModify(accessControl);
        updateById(accessControl);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Override
    public AccessControlDeviceDTO findDeviceByDeviceCode(String deviceCode, Boolean flag) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        
        AccessControlDeviceDTO deviceDTO = baseMapper.findDeviceByDeviceCode(deviceCode);
        if (deviceDTO == null && BooleanUtil.isTrue(flag)) {
            throw new BusinessException("设备不存在");
        }
        
        if (deviceDTO != null) {
            // 设置设备属性状态
            setDevicePropertyStatus(Arrays.asList(deviceDTO), Arrays.asList(deviceCode));
            
            // 区域范围处理
            if (StringUtils.isNotBlank(deviceDTO.getAreaPath())) {
                deviceDTO.setAreaPath(deviceDTO.getAreaPath().replace("@", "/"));
            }
        }
        
        return deviceDTO;
    }

    @Override
    public AccessControlDeviceDTO findById(Long id) {
        Assert.notNull(id, "id不能为空");
        
        AccessControlDeviceDTO deviceDTO = baseMapper.findDeviceById(id);
        if (deviceDTO == null) {
            throw new BusinessException("设备不存在");
        }
        
        // 设置设备属性状态
        setDevicePropertyStatus(Arrays.asList(deviceDTO), Arrays.asList(deviceDTO.getDeviceCode()));
        
        // 区域范围处理
        if (StringUtils.isNotBlank(deviceDTO.getAreaPath())) {
            deviceDTO.setAreaPath(deviceDTO.getAreaPath().replace("@", "/"));
        }
        
        return deviceDTO;
    }

    @Transactional
    @Override
    public void delBatch(Set<Long> ids) {
        Assert.notEmpty(ids, "删除的id不能为空");
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        // 查询要删除的设备信息
        List<AccessControl> accessControls = listByIds(ids);
        if (CollectionUtil.isNotEmpty(accessControls)) {
            List<String> deviceCodes = accessControls.stream()
                    .map(AccessControl::getDeviceCode)
                    .collect(Collectors.toList());
            
            coreParamSj.add("删除门禁设备编码:" + String.join(",", deviceCodes));
            sj.add("删除门禁设备编码:" + String.join(",", deviceCodes));
        }
        
        // 逻辑删除
        removeByIds(ids);
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Transactional
    @Override
    public void insertBatch(Set<String> deviceCodes) {
        Assert.notEmpty(deviceCodes, "设备编码不能为空");
        
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        
        List<AccessControl> accessControls = new ArrayList<>();
        for (String deviceCode : deviceCodes) {
            // 验证设备是否已存在
            LambdaQueryWrapper<AccessControl> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AccessControl::getDeviceCode, deviceCode);
            AccessControl existDevice = getOne(wrapper);
            if (existDevice != null) {
                continue; // 跳过已存在的设备
            }
            
            AccessControl accessControl = new AccessControl();
            accessControl.setDeviceCode(deviceCode);
            accessControl.setDeviceName("门禁设备-" + deviceCode);
            accessControl.setDeviceType(1); // 默认刷卡门禁
            accessControl.setDeviceStatus(1); // 默认正常
            accessControl.setControlStatus(1); // 默认开启
            accessControl.setCommunicationStatus(0); // 默认离线
            
            setBase(accessControl);
            accessControls.add(accessControl);
        }
        
        if (CollectionUtil.isNotEmpty(accessControls)) {
            saveBatch(accessControls);
            
            coreParamSj.add("批量新增门禁设备数量:" + accessControls.size());
            sj.add("批量新增门禁设备数量:" + accessControls.size());
        }
        
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
    }

    @Override
    public List<CountByStateDTO> countByStatus(AccessControlVo accessControlVo) {
        return baseMapper.countByStatus(accessControlVo);
    }

    @Override
    public Map<String, Map<String, Long>> countByDeviceType(AccessControlVo accessControlVo) {
        List<Map<String, Object>> result = baseMapper.countByDeviceType(accessControlVo);
        return processCountResult(result);
    }

    @Override
    public Map<String, Map<String, Long>> countByCommunicationStatus(AccessControlVo accessControlVo) {
        List<Map<String, Object>> result = baseMapper.countByCommunicationStatus(accessControlVo);
        return processCountResult(result);
    }

    @Override
    public Map<String, Map<String, Long>> countByControlStatus(AccessControlVo accessControlVo) {
        List<Map<String, Object>> result = baseMapper.countByControlStatus(accessControlVo);
        return processCountResult(result);
    }

    @Override
    public Boolean controlDevice(String deviceCode, Integer controlType, Integer controlDuration) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        Assert.notNull(controlType, "控制类型不能为空");
        
        // TODO: 实现设备控制逻辑，调用设备控制接口
        log.info("控制门禁设备: deviceCode={}, controlType={}, controlDuration={}", 
                deviceCode, controlType, controlDuration);
        
        return true;
    }

    @Override
    public Map<String, Object> remoteMonitor(String deviceCode) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        
        // TODO: 实现远程监控逻辑
        Map<String, Object> monitorInfo = new HashMap<>();
        monitorInfo.put("deviceCode", deviceCode);
        monitorInfo.put("status", "online");
        monitorInfo.put("monitorTime", new Date());
        
        return monitorInfo;
    }

    @Override
    public Boolean testCommunication(String deviceCode) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        
        // TODO: 实现通讯测试逻辑
        log.info("测试门禁设备通讯: deviceCode={}", deviceCode);
        
        return true;
    }

    @Override
    public Map<String, Object> testRecognition(String deviceCode, Integer recognitionType) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        Assert.notNull(recognitionType, "识别类型不能为空");
        
        // TODO: 实现身份识别测试逻辑
        Map<String, Object> recognitionResult = new HashMap<>();
        recognitionResult.put("deviceCode", deviceCode);
        recognitionResult.put("recognitionType", recognitionType);
        recognitionResult.put("testResult", "success");
        recognitionResult.put("testTime", new Date());
        
        return recognitionResult;
    }

    @Override
    public Long export(AccessControlVo accessControlVo, HttpServletRequest request, HttpServletResponse response) {
        // TODO: 实现导出功能
        return 0L;
    }

    @Override
    public List<AccessControlDeviceDTO> queryList4Export(AccessControlVo accessControlVo) {
        return baseMapper.queryList4Export(accessControlVo);
    }

    @Override
    public Long imports(MultipartFile file) throws IOException {
        // TODO: 实现导入功能
        return 0L;
    }

    @Override
    public Long importUpdate(MultipartFile file) throws IOException {
        // TODO: 实现导入更新功能
        return 0L;
    }

    /**
     * 验证设备是否已存在
     */
    private void checkExist(AccessControl accessControl) {
        LambdaQueryWrapper<AccessControl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccessControl::getDeviceCode, accessControl.getDeviceCode());
        AccessControl existDevice = getOne(wrapper);
        if (existDevice != null) {
            throw new BusinessException("设备编码已存在");
        }
    }

    /**
     * 验证设备是否已存在（更新时排除自身）
     */
    private void checkExistForUpdate(AccessControl accessControl) {
        LambdaQueryWrapper<AccessControl> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AccessControl::getDeviceCode, accessControl.getDeviceCode());
        wrapper.ne(AccessControl::getId, accessControl.getId());
        AccessControl existDevice = getOne(wrapper);
        if (existDevice != null) {
            throw new BusinessException("设备编码已存在");
        }
    }

    /**
     * 设置基本属性
     */
    private void setBase(AccessControl accessControl) {
        commonService.setCreateAndModifyInfo(accessControl);
    }

    /**
     * 设置修改信息
     */
    private void setModify(AccessControl accessControl) {
        commonService.setModifyInfo(accessControl);
    }

    /**
     * 获取设备应用模型引用
     */
    private DeviceApplicationModelRef getDeviceApplicationModelRef() {
        DeviceApplicationModelRef device = new DeviceApplicationModelRef();
        device.setApplicationId(1L); // TODO: 从配置中获取应用ID
        device.setModelId(DeviceModelConstant.ACCESS_CONTROL); // TODO: 定义门禁设备模型常量
        return device;
    }

    /**
     * 设置设备属性状态
     */
    private void setDevicePropertyStatus(List<AccessControlDeviceDTO> records, List<String> codes) {
        if (CollectionUtil.isEmpty(records) || CollectionUtil.isEmpty(codes)) {
            return;
        }
        
        // TODO: 实现设备属性状态查询逻辑
        DeviceStatusDTO deviceStatusDTO = new DeviceStatusDTO();
        deviceStatusDTO.setDeviceCodes(codes);
        deviceStatusDTO.setPropCodes(Arrays.asList("door_state", "battery_level", "signal_strength", "temperature"));
        
        // List<DevicePropertyStatus> list = getDevicePropertyStatus(deviceStatusDTO);
        // Map<String, List<DevicePropertyStatus>> map = list.stream().collect(Collectors.groupingBy(DevicePropertyStatus::getDeviceCode));
        
        records.forEach(m -> {
            // m.setDevicePropertyStatusList(map.get(m.getDeviceCode()));
        });
    }

    /**
     * 处理统计结果
     */
    private Map<String, Map<String, Long>> processCountResult(List<Map<String, Object>> result) {
        Map<String, Map<String, Long>> processedResult = new HashMap<>();
        
        if (CollectionUtil.isNotEmpty(result)) {
            for (Map<String, Object> item : result) {
                String key = String.valueOf(item.get("key"));
                Long count = Long.valueOf(String.valueOf(item.get("count")));
                
                Map<String, Long> countMap = processedResult.computeIfAbsent(key, k -> new HashMap<>());
                countMap.put("count", count);
            }
        }
        
        return processedResult;
    }
}
