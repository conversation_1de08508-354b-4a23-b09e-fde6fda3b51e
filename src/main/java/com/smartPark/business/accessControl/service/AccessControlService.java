package com.smartPark.business.accessControl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.accessControl.entity.AccessControl;
import com.smartPark.business.accessControl.entity.dto.AccessControlDeviceDTO;
import com.smartPark.business.accessControl.entity.dto.AccessControlVo;
import com.smartPark.business.manhole.entity.vo.CountByStateDTO;
import com.smartPark.common.base.model.RequestModel;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 门禁设备表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AccessControlService extends IService<AccessControl> {

    /**
     * 分页查询门禁设备列表
     *
     * @param requestModel 请求参数
     * @return 分页结果
     */
    IPage<AccessControlDeviceDTO> queryListByPage(RequestModel<AccessControlVo> requestModel);

    /**
     * 新增门禁设备
     *
     * @param accessControl 门禁设备信息
     */
    void insert(AccessControl accessControl);

    /**
     * 根据id编辑门禁设备
     *
     * @param accessControl 门禁设备信息
     */
    void updateOne(AccessControl accessControl);

    /**
     * 根据设备编码查询设备信息
     *
     * @param deviceCode 设备编码
     * @param flag true 验证设备码 false不验证
     * @return 设备信息
     */
    AccessControlDeviceDTO findDeviceByDeviceCode(String deviceCode, Boolean flag);

    /**
     * 根据ID查询门禁设备详情
     *
     * @param id 设备ID
     * @return 设备详情
     */
    AccessControlDeviceDTO findById(Long id);

    /**
     * 删除门禁设备（包含批量删除）
     *
     * @param ids 设备ID集合
     */
    void delBatch(Set<Long> ids);

    /**
     * 批量新增门禁设备
     *
     * @param deviceCodes 设备编码集合
     */
    void insertBatch(Set<String> deviceCodes);

    /**
     * 根据状态统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    List<CountByStateDTO> countByStatus(AccessControlVo accessControlVo);

    /**
     * 根据设备类型统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByDeviceType(AccessControlVo accessControlVo);

    /**
     * 根据通讯状态统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByCommunicationStatus(AccessControlVo accessControlVo);

    /**
     * 根据控制状态统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    Map<String, Map<String, Long>> countByControlStatus(AccessControlVo accessControlVo);

    /**
     * 门禁设备控制
     *
     * @param deviceCode 设备编码
     * @param controlType 控制类型(1:开门,2:关门,3:重启,4:恢复出厂设置)
     * @param controlDuration 控制持续时间(秒)
     * @return 控制结果
     */
    Boolean controlDevice(String deviceCode, Integer controlType, Integer controlDuration);

    /**
     * 远程监控
     *
     * @param deviceCode 设备编码
     * @return 监控信息
     */
    Map<String, Object> remoteMonitor(String deviceCode);

    /**
     * 设备通讯测试
     *
     * @param deviceCode 设备编码
     * @return 通讯状态
     */
    Boolean testCommunication(String deviceCode);

    /**
     * 身份识别功能测试
     *
     * @param deviceCode 设备编码
     * @param recognitionType 识别类型(1:刷卡,2:人脸识别,3:指纹,4:密码)
     * @return 识别结果
     */
    Map<String, Object> testRecognition(String deviceCode, Integer recognitionType);

    /**
     * 导出门禁设备数据
     *
     * @param accessControlVo 查询条件
     * @param request 请求对象
     * @param response 响应对象
     * @return 导出结果
     */
    Long export(AccessControlVo accessControlVo, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询导出excel数据
     *
     * @param accessControlVo 查询条件
     * @return 导出数据列表
     */
    List<AccessControlDeviceDTO> queryList4Export(AccessControlVo accessControlVo);

    /**
     * 门禁设备导入
     *
     * @param file 导入文件
     * @return 导入结果
     * @throws IOException IO异常
     */
    Long imports(MultipartFile file) throws IOException;

    /**
     * 门禁设备导入修改
     *
     * @param file 导入文件
     * @return 导入结果
     * @throws IOException IO异常
     */
    Long importUpdate(MultipartFile file) throws IOException;
}
