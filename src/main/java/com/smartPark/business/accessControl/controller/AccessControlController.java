package com.smartPark.business.accessControl.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.accessControl.entity.AccessControl;
import com.smartPark.business.accessControl.entity.dto.AccessControlDeviceDTO;
import com.smartPark.business.accessControl.entity.dto.AccessControlVo;
import com.smartPark.business.accessControl.service.AccessControlService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.Set;

/**
 * 门禁设备管理
 * @author: system
 * @Date: 2024/01/15 10:00
 * @Description: 门禁设备管理
 */
@RestController
@RequestMapping("accessControl")
@Api(tags = "门禁设备管理")
public class AccessControlController {

    @Autowired
    private AccessControlService accessControlService;

    /**
     * @Description: 查询设备信息
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据设备编码查询设备信息")
    @GetMapping("device/{deviceCode}")
    public RestMessage findDeviceByDeviceCode(@PathVariable("deviceCode") String deviceCode) {
        //参数验证
        Assert.hasLength(deviceCode, "设备编码不能为空");
        AccessControlDeviceDTO accessControlDeviceDTO = accessControlService.findDeviceByDeviceCode(deviceCode, true);
        return RestBuilders.successBuilder().data(accessControlDeviceDTO).build();
    }

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<AccessControlVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<AccessControlDeviceDTO> record = accessControlService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 增加门禁设备
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("增加门禁设备")
    @PostMapping
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "accessControl:device:add", desc = "新增")
    public RestMessage insert(@RequestBody AccessControl accessControl) {
        //参数验证
        Assert.hasLength(accessControl.getDeviceCode(), "设备编码不能为空");
        accessControlService.insert(accessControl);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 批量增加门禁设备
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("批量增加门禁设备")
    @PostMapping("insertBatch")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "accessControl:device:batchAdd", desc = "批量新增")
    public RestMessage insertBatch(@RequestBody Set<String> deviceCodes) {
        Assert.notEmpty(deviceCodes, "设备编码不能为空");
        accessControlService.insertBatch(deviceCodes);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 删除门禁设备（包含批量删除）
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("删除门禁设备（包含批量删除）")
    @DeleteMapping
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DELETE, menuCode = "accessControl:device:delete", desc = "删除")
    public RestMessage delBatch(@RequestBody Set<Long> ids) {
        Assert.notEmpty(ids, "删除的id不能为空");
        accessControlService.delBatch(ids);
        return RestBuilders.successBuilder().build();
    }

    @ApiOperation("编辑门禁设备")
    @PutMapping
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "accessControl:device:edit", desc = "编辑")
    public RestMessage updateById(@RequestBody AccessControl accessControl) {
        Assert.notNull(accessControl.getId(), "id不能为空");
        accessControlService.updateOne(accessControl);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 根据id查询门禁设备详情
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据id查询门禁设备详情")
    @GetMapping("{id}")
    public RestMessage findById(@PathVariable("id") Long id) {
        Assert.notNull(id, "id不能为空");
        AccessControlDeviceDTO accessControlDeviceDTO = accessControlService.findById(id);
        return RestBuilders.successBuilder().data(accessControlDeviceDTO).build();
    }

    /**
     * @Description: 门禁设备控制
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("门禁设备控制")
    @PostMapping("control")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "accessControl:device:control", desc = "设备控制")
    public RestMessage controlDevice(@RequestParam("deviceCode") String deviceCode,
                                   @RequestParam("controlType") Integer controlType,
                                   @RequestParam(value = "controlDuration", required = false, defaultValue = "5") Integer controlDuration) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        Assert.notNull(controlType, "控制类型不能为空");
        Boolean result = accessControlService.controlDevice(deviceCode, controlType, controlDuration);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 远程监控
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("远程监控")
    @GetMapping("monitor/{deviceCode}")
    public RestMessage remoteMonitor(@PathVariable("deviceCode") String deviceCode) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        Map<String, Object> monitorInfo = accessControlService.remoteMonitor(deviceCode);
        return RestBuilders.successBuilder().data(monitorInfo).build();
    }

    /**
     * @Description: 设备通讯测试
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("设备通讯测试")
    @PostMapping("testCommunication")
    public RestMessage testCommunication(@RequestParam("deviceCode") String deviceCode) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        Boolean result = accessControlService.testCommunication(deviceCode);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 身份识别功能测试
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("身份识别功能测试")
    @PostMapping("testRecognition")
    public RestMessage testRecognition(@RequestParam("deviceCode") String deviceCode,
                                     @RequestParam("recognitionType") Integer recognitionType) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        Assert.notNull(recognitionType, "识别类型不能为空");
        Map<String, Object> result = accessControlService.testRecognition(deviceCode, recognitionType);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据条件导出
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据条件导出")
    @PostMapping("export")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EXPORT, menuCode = "accessControl:device:export", desc = "导出")
    public RestMessage export(@RequestBody AccessControlVo accessControlVo, HttpServletRequest request, HttpServletResponse response) {
        Long result = accessControlService.export(accessControlVo, request, response);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 门禁设备导入
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("门禁设备导入")
    @PostMapping("import")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.IMPORT, menuCode = "accessControl:device:import", desc = "导入")
    public RestMessage imports(@RequestParam("file") MultipartFile file) throws Exception {
        Assert.notNull(file, "文件不能为空");
        Long result = accessControlService.imports(file);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 门禁设备导入修改
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("门禁设备导入修改")
    @PostMapping("importUpdate")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.IMPORT, menuCode = "accessControl:device:importUpdate", desc = "导入修改")
    public RestMessage importUpdate(@RequestParam("file") MultipartFile file) throws Exception {
        Assert.notNull(file, "文件不能为空");
        Long result = accessControlService.importUpdate(file);
        return RestBuilders.successBuilder().data(result).build();
    }
}
