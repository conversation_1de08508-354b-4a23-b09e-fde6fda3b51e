package com.smartPark.business.accessControl.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.accessControl.entity.AccessAuth;
import com.smartPark.business.accessControl.entity.dto.AccessAuthVo;
import com.smartPark.business.accessControl.service.AccessAuthService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 门禁授权管理
 * @author: system
 * @Date: 2024/01/15 10:00
 * @Description: 门禁授权管理
 */
@RestController
@RequestMapping("accessAuth")
@Api(tags = "门禁授权管理")
public class AccessAuthController {

    @Autowired
    private AccessAuthService accessAuthService;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<AccessAuthVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<AccessAuthVo> record = accessAuthService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 新增门禁授权
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("新增门禁授权")
    @PostMapping
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "accessControl:auth:add", desc = "新增授权")
    public RestMessage insert(@RequestBody AccessAuth accessAuth) {
        //参数验证
        Assert.hasLength(accessAuth.getDeviceCode(), "设备编码不能为空");
        Assert.hasLength(accessAuth.getPersonName(), "人员姓名不能为空");
        accessAuthService.insert(accessAuth);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 批量门禁授权
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("批量门禁授权")
    @PostMapping("batchAuth")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "accessControl:auth:batchAdd", desc = "批量授权")
    public RestMessage batchAuth(@RequestBody List<AccessAuth> accessAuthList) {
        Assert.notEmpty(accessAuthList, "授权信息不能为空");
        accessAuthService.batchAuth(accessAuthList);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 删除门禁授权（包含批量删除）
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("删除门禁授权（包含批量删除）")
    @DeleteMapping
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DELETE, menuCode = "accessControl:auth:delete", desc = "删除授权")
    public RestMessage delBatch(@RequestBody Set<Long> ids) {
        Assert.notEmpty(ids, "删除的id不能为空");
        accessAuthService.delBatch(ids);
        return RestBuilders.successBuilder().build();
    }

    @ApiOperation("编辑门禁授权")
    @PutMapping
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "accessControl:auth:edit", desc = "编辑授权")
    public RestMessage updateById(@RequestBody AccessAuth accessAuth) {
        Assert.notNull(accessAuth.getId(), "id不能为空");
        accessAuthService.updateOne(accessAuth);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 根据id查询门禁授权详情
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据id查询门禁授权详情")
    @GetMapping("{id}")
    public RestMessage findById(@PathVariable("id") Long id) {
        Assert.notNull(id, "id不能为空");
        AccessAuthVo accessAuthVo = accessAuthService.findById(id);
        return RestBuilders.successBuilder().data(accessAuthVo).build();
    }

    /**
     * @Description: 撤销门禁授权
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("撤销门禁授权")
    @PostMapping("revoke/{id}")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "accessControl:auth:revoke", desc = "撤销授权")
    public RestMessage revokeAuth(@PathVariable("id") Long id, @RequestParam("revokeReason") String revokeReason) {
        Assert.notNull(id, "授权ID不能为空");
        Assert.hasLength(revokeReason, "撤销原因不能为空");
        accessAuthService.revokeAuth(id, revokeReason);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 批量撤销门禁授权
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("批量撤销门禁授权")
    @PostMapping("batchRevoke")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "accessControl:auth:batchRevoke", desc = "批量撤销授权")
    public RestMessage batchRevokeAuth(@RequestBody Set<Long> ids, @RequestParam("revokeReason") String revokeReason) {
        Assert.notEmpty(ids, "授权ID不能为空");
        Assert.hasLength(revokeReason, "撤销原因不能为空");
        accessAuthService.batchRevokeAuth(ids, revokeReason);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 延期授权
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("延期授权")
    @PostMapping("extend/{id}")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "accessControl:auth:extend", desc = "延期授权")
    public RestMessage extendAuth(@PathVariable("id") Long id, @RequestParam("extendDays") Integer extendDays) {
        Assert.notNull(id, "授权ID不能为空");
        Assert.notNull(extendDays, "延期天数不能为空");
        accessAuthService.extendAuth(id, extendDays);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 批量延期授权
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("批量延期授权")
    @PostMapping("batchExtend")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "accessControl:auth:batchExtend", desc = "批量延期授权")
    public RestMessage batchExtendAuth(@RequestBody Set<Long> ids, @RequestParam("extendDays") Integer extendDays) {
        Assert.notEmpty(ids, "授权ID不能为空");
        Assert.notNull(extendDays, "延期天数不能为空");
        accessAuthService.batchExtendAuth(ids, extendDays);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 根据授权类型统计数量
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByAuthType")
    @ApiOperation("根据授权类型统计数量")
    public RestMessage countByAuthType(@RequestBody AccessAuthVo accessAuthVo) {
        Map<String, Map<String, Long>> result = accessAuthService.countByAuthType(accessAuthVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据授权状态统计数量
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByAuthStatus")
    @ApiOperation("根据授权状态统计数量")
    public RestMessage countByAuthStatus(@RequestBody AccessAuthVo accessAuthVo) {
        Map<String, Map<String, Long>> result = accessAuthService.countByAuthStatus(accessAuthVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据设备统计授权数量
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByDevice")
    @ApiOperation("根据设备统计授权数量")
    public RestMessage countByDevice(@RequestBody AccessAuthVo accessAuthVo) {
        Map<String, Map<String, Long>> result = accessAuthService.countByDevice(accessAuthVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据时间统计授权趋势
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countTrendByTime")
    @ApiOperation("根据时间统计授权趋势")
    public RestMessage countTrendByTime(@RequestBody AccessAuthVo accessAuthVo) {
        List<Map<String, Object>> result = accessAuthService.countTrendByTime(accessAuthVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 查询即将过期的授权列表
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("queryWillExpireList")
    @ApiOperation("查询即将过期的授权列表")
    public RestMessage queryWillExpireList(@RequestBody AccessAuthVo accessAuthVo) {
        List<AccessAuthVo> result = accessAuthService.queryWillExpireList(accessAuthVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据条件导出
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据条件导出")
    @PostMapping("export")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EXPORT, menuCode = "accessControl:auth:export", desc = "导出")
    public RestMessage export(@RequestBody AccessAuthVo accessAuthVo, HttpServletRequest request, HttpServletResponse response) {
        Long result = accessAuthService.export(accessAuthVo, request, response);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 门禁授权导入
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("门禁授权导入")
    @PostMapping("import")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.IMPORT, menuCode = "accessControl:auth:import", desc = "导入")
    public RestMessage imports(@RequestParam("file") MultipartFile file) throws Exception {
        Assert.notNull(file, "文件不能为空");
        Long result = accessAuthService.imports(file);
        return RestBuilders.successBuilder().data(result).build();
    }
}
