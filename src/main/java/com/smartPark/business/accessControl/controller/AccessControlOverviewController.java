package com.smartPark.business.accessControl.controller;

import com.smartPark.business.accessControl.entity.dto.AccessControlVo;
import com.smartPark.business.accessControl.service.AccessControlService;
import com.smartPark.business.manhole.entity.vo.CountByStateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;
import java.util.Map;

/**
 * 门禁设备概览
 * @Description: 门禁设备概览
 */
@RestController
@RequestMapping("accessControlOverview")
@Api(tags = "门禁设备概览")
public class AccessControlOverviewController {

    @Autowired
    private AccessControlService accessControlService;

    @PostMapping("countByStatus")
    @ApiOperation("根据在离线状态统计数量")
    public RestMessage countByStatus(@RequestBody AccessControlVo accessControlVo) {
        List<CountByStateDTO> result = accessControlService.countByStatus(accessControlVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    @PostMapping("countByDeviceType")
    @ApiOperation("根据设备类型统计数量")
    public RestMessage countByDeviceType(@RequestBody AccessControlVo accessControlVo) {
        Map<String, Map<String, Long>> result = accessControlService.countByDeviceType(accessControlVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    @PostMapping("countByCommunicationStatus")
    @ApiOperation("根据通讯状态统计数量")
    public RestMessage countByCommunicationStatus(@RequestBody AccessControlVo accessControlVo) {
        Map<String, Map<String, Long>> result = accessControlService.countByCommunicationStatus(accessControlVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    @PostMapping("countByControlStatus")
    @ApiOperation("根据控制状态统计数量")
    public RestMessage countByControlStatus(@RequestBody AccessControlVo accessControlVo) {
        Map<String, Map<String, Long>> result = accessControlService.countByControlStatus(accessControlVo);
        return RestBuilders.successBuilder().data(result).build();
    }
}
