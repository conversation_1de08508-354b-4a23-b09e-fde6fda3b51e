package com.smartPark.business.accessControl.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.accessControl.entity.AccessHistory;
import com.smartPark.business.accessControl.entity.dto.AccessHistoryVo;
import com.smartPark.business.accessControl.service.AccessHistoryService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 进出门历史管理
 * @author: system
 * @Date: 2024/01/15 10:00
 * @Description: 进出门历史管理
 */
@RestController
@RequestMapping("accessHistory")
@Api(tags = "进出门历史管理")
public class AccessHistoryController {

    @Autowired
    private AccessHistoryService accessHistoryService;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<AccessHistoryVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<AccessHistoryVo> record = accessHistoryService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 新增进出门历史记录
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("新增进出门历史记录")
    @PostMapping
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "accessControl:history:add", desc = "新增记录")
    public RestMessage insert(@RequestBody AccessHistory accessHistory) {
        //参数验证
        Assert.hasLength(accessHistory.getDeviceCode(), "设备编码不能为空");
        Assert.hasLength(accessHistory.getPersonName(), "人员姓名不能为空");
        accessHistoryService.insert(accessHistory);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 根据id查询进出门历史详情
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据id查询进出门历史详情")
    @GetMapping("{id}")
    public RestMessage findById(@PathVariable("id") Long id) {
        Assert.notNull(id, "id不能为空");
        AccessHistoryVo accessHistoryVo = accessHistoryService.findById(id);
        return RestBuilders.successBuilder().data(accessHistoryVo).build();
    }

    /**
     * @Description: 根据设备编码查询最后进出记录
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据设备编码查询最后进出记录")
    @GetMapping("lastAccess/{deviceCode}")
    public RestMessage findLastAccessByDeviceCode(@PathVariable("deviceCode") String deviceCode) {
        Assert.hasLength(deviceCode, "设备编码不能为空");
        AccessHistoryVo accessHistoryVo = accessHistoryService.findLastAccessByDeviceCode(deviceCode);
        return RestBuilders.successBuilder().data(accessHistoryVo).build();
    }

    /**
     * @Description: 查询最近进出记录
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("queryRecentList")
    @ApiOperation("查询最近进出记录")
    public RestMessage queryRecentList(@RequestBody AccessHistoryVo accessHistoryVo, 
                                     @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit) {
        List<AccessHistoryVo> result = accessHistoryService.queryRecentList(accessHistoryVo, limit);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据进出类型统计数量
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByAccessType")
    @ApiOperation("根据进出类型统计数量")
    public RestMessage countByAccessType(@RequestBody AccessHistoryVo accessHistoryVo) {
        Map<String, Map<String, Long>> result = accessHistoryService.countByAccessType(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据识别方式统计数量
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByRecognitionMethod")
    @ApiOperation("根据识别方式统计数量")
    public RestMessage countByRecognitionMethod(@RequestBody AccessHistoryVo accessHistoryVo) {
        Map<String, Map<String, Long>> result = accessHistoryService.countByRecognitionMethod(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据进出状态统计数量
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByAccessStatus")
    @ApiOperation("根据进出状态统计数量")
    public RestMessage countByAccessStatus(@RequestBody AccessHistoryVo accessHistoryVo) {
        Map<String, Map<String, Long>> result = accessHistoryService.countByAccessStatus(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据设备统计进出次数
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByDevice")
    @ApiOperation("根据设备统计进出次数")
    public RestMessage countByDevice(@RequestBody AccessHistoryVo accessHistoryVo) {
        Map<String, Map<String, Long>> result = accessHistoryService.countByDevice(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据时间统计进出趋势
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countTrendByTime")
    @ApiOperation("根据时间统计进出趋势")
    public RestMessage countTrendByTime(@RequestBody AccessHistoryVo accessHistoryVo) {
        List<Map<String, Object>> result = accessHistoryService.countTrendByTime(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据人员统计进出次数
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countByPerson")
    @ApiOperation("根据人员统计进出次数")
    public RestMessage countByPerson(@RequestBody AccessHistoryVo accessHistoryVo) {
        Map<String, Map<String, Long>> result = accessHistoryService.countByPerson(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 统计今日进入人数
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countTodayEnter")
    @ApiOperation("统计今日进入人数")
    public RestMessage countTodayEnter(@RequestBody AccessHistoryVo accessHistoryVo) {
        Long result = accessHistoryService.countTodayEnter(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 统计今日外出人数
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countTodayExit")
    @ApiOperation("统计今日外出人数")
    public RestMessage countTodayExit(@RequestBody AccessHistoryVo accessHistoryVo) {
        Long result = accessHistoryService.countTodayExit(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 统计总进出次数
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countTotal")
    @ApiOperation("统计总进出次数")
    public RestMessage countTotal(@RequestBody AccessHistoryVo accessHistoryVo) {
        Long result = accessHistoryService.countTotal(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 统计成功进出次数
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countSuccess")
    @ApiOperation("统计成功进出次数")
    public RestMessage countSuccess(@RequestBody AccessHistoryVo accessHistoryVo) {
        Long result = accessHistoryService.countSuccess(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 统计失败进出次数
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @PostMapping("countFailed")
    @ApiOperation("统计失败进出次数")
    public RestMessage countFailed(@RequestBody AccessHistoryVo accessHistoryVo) {
        Long result = accessHistoryService.countFailed(accessHistoryVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * @Description: 根据条件导出
     * <AUTHOR>
     * @date 2024/01/15 10:00
     */
    @ApiOperation("根据条件导出")
    @PostMapping("export")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EXPORT, menuCode = "accessControl:history:export", desc = "导出")
    public RestMessage export(@RequestBody AccessHistoryVo accessHistoryVo, HttpServletRequest request, HttpServletResponse response) {
        Long result = accessHistoryService.export(accessHistoryVo, request, response);
        return RestBuilders.successBuilder().data(result).build();
    }
}
