package com.smartPark.business.accessControl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.accessControl.entity.AccessControl;
import com.smartPark.business.accessControl.entity.dto.AccessControlDeviceDTO;
import com.smartPark.business.accessControl.entity.dto.AccessControlVo;
import com.smartPark.business.manhole.entity.vo.CountByStateDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 门禁设备表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AccessControlMapper extends BaseMapper<AccessControl> {

    /**
     * 分页查询门禁设备列表
     *
     * @param page 分页参数
     * @param accessControlVo 查询条件
     * @return 分页结果
     */
    IPage<AccessControlDeviceDTO> queryListByPage(Page page, @Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 根据设备编码查询设备详情
     *
     * @param deviceCode 设备编码
     * @return 设备详情
     */
    AccessControlDeviceDTO findDeviceByDeviceCode(@Param("deviceCode") String deviceCode);

    /**
     * 根据ID查询设备详情
     *
     * @param id 设备ID
     * @return 设备详情
     */
    AccessControlDeviceDTO findDeviceById(@Param("id") Long id);

    /**
     * 根据状态统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    List<CountByStateDTO> countByStatus(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 根据设备类型统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByDeviceType(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 根据通讯状态统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByCommunicationStatus(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 根据控制状态统计设备数量
     *
     * @param accessControlVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByControlStatus(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 统计设备总数
     *
     * @param accessControlVo 查询条件
     * @return 设备总数
     */
    Long countTotal(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 统计在线设备数
     *
     * @param accessControlVo 查询条件
     * @return 在线设备数
     */
    Long countOnline(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 统计离线设备数
     *
     * @param accessControlVo 查询条件
     * @return 离线设备数
     */
    Long countOffline(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 统计告警设备数
     *
     * @param accessControlVo 查询条件
     * @return 告警设备数
     */
    Long countAlarm(@Param("accessControlVo") AccessControlVo accessControlVo);

    /**
     * 查询导出数据
     *
     * @param accessControlVo 查询条件
     * @return 导出数据列表
     */
    List<AccessControlDeviceDTO> queryList4Export(@Param("accessControlVo") AccessControlVo accessControlVo);
}
