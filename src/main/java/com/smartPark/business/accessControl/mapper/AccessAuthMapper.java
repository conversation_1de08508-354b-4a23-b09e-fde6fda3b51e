package com.smartPark.business.accessControl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.accessControl.entity.AccessAuth;
import com.smartPark.business.accessControl.entity.dto.AccessAuthVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 门禁授权记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AccessAuthMapper extends BaseMapper<AccessAuth> {

    /**
     * 分页查询门禁授权记录列表
     *
     * @param page 分页参数
     * @param accessAuthVo 查询条件
     * @return 分页结果
     */
    IPage<AccessAuthVo> queryListByPage(Page page, @Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 根据授权类型统计数量
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByAuthType(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 根据授权状态统计数量
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByAuthStatus(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 统计即将过期的授权数量(7天内过期)
     *
     * @param accessAuthVo 查询条件
     * @return 即将过期的授权数量
     */
    Long countWillExpire(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 统计已过期的授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 已过期的授权数量
     */
    Long countExpired(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 统计有效授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 有效授权数量
     */
    Long countValid(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 统计总授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 总授权数量
     */
    Long countTotal(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 根据设备统计授权数量
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByDevice(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 根据时间统计授权趋势
     *
     * @param accessAuthVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countTrendByTime(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 查询即将过期的授权列表
     *
     * @param accessAuthVo 查询条件
     * @return 即将过期的授权列表
     */
    List<AccessAuthVo> queryWillExpireList(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 查询导出数据
     *
     * @param accessAuthVo 查询条件
     * @return 导出数据列表
     */
    List<AccessAuthVo> queryList4Export(@Param("accessAuthVo") AccessAuthVo accessAuthVo);

    /**
     * 批量撤销授权
     *
     * @param ids 授权ID列表
     * @param revokerId 撤销人ID
     * @param revokerName 撤销人姓名
     * @param revokeReason 撤销原因
     * @return 影响行数
     */
    int batchRevokeAuth(@Param("ids") List<Long> ids, 
                       @Param("revokerId") Long revokerId, 
                       @Param("revokerName") String revokerName, 
                       @Param("revokeReason") String revokeReason);
}
