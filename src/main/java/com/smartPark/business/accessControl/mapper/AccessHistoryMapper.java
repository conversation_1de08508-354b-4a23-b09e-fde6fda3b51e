package com.smartPark.business.accessControl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.accessControl.entity.AccessHistory;
import com.smartPark.business.accessControl.entity.dto.AccessHistoryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 进出门历史记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AccessHistoryMapper extends BaseMapper<AccessHistory> {

    /**
     * 分页查询进出门历史记录列表
     *
     * @param page 分页参数
     * @param accessHistoryVo 查询条件
     * @return 分页结果
     */
    IPage<AccessHistoryVo> queryListByPage(Page page, @Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 根据进出类型统计数量
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByAccessType(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 根据识别方式统计数量
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByRecognitionMethod(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 根据进出状态统计数量
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByAccessStatus(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 根据设备统计进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByDevice(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 根据时间统计进出趋势
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countTrendByTime(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 根据人员统计进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 统计结果
     */
    List<Map<String, Object>> countByPerson(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 统计今日进入人数
     *
     * @param accessHistoryVo 查询条件
     * @return 今日进入人数
     */
    Long countTodayEnter(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 统计今日外出人数
     *
     * @param accessHistoryVo 查询条件
     * @return 今日外出人数
     */
    Long countTodayExit(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 统计总进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 总进出次数
     */
    Long countTotal(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 统计成功进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 成功进出次数
     */
    Long countSuccess(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 统计失败进出次数
     *
     * @param accessHistoryVo 查询条件
     * @return 失败进出次数
     */
    Long countFailed(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 查询最近进出记录
     *
     * @param accessHistoryVo 查询条件
     * @param limit 限制条数
     * @return 最近进出记录
     */
    List<AccessHistoryVo> queryRecentList(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo, @Param("limit") Integer limit);

    /**
     * 查询导出数据
     *
     * @param accessHistoryVo 查询条件
     * @return 导出数据列表
     */
    List<AccessHistoryVo> queryList4Export(@Param("accessHistoryVo") AccessHistoryVo accessHistoryVo);

    /**
     * 根据设备编码查询最后进出记录
     *
     * @param deviceCode 设备编码
     * @return 最后进出记录
     */
    AccessHistoryVo findLastAccessByDeviceCode(@Param("deviceCode") String deviceCode);
}
