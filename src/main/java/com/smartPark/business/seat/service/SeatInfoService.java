package com.smartPark.business.seat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.seat.entity.SeatInfo;
import com.smartPark.business.seat.entity.dto.SeatControllerDto;
import com.smartPark.business.seat.entity.dto.SeatInfoDTO;
import com.smartPark.business.seat.entity.vo.SeatInfoVo;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.entity.device.DeviceExtendInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * SeatInfo表服务接口
 *
 * <AUTHOR>
 * @since 2023/07/18
 */
public interface SeatInfoService extends IService<SeatInfo> {

    /**
     * 新增
     *
     * @param livableSeatInfo 实体对象
     * @return 操作结果
     */
    boolean saveOne(SeatInfo livableSeatInfo);

    /**
     * 修改单条
     *
     * @param livableSeatInfo 实体对象
     * @return 修改结果
     */
    boolean updateOne(SeatInfo livableSeatInfo);

    /**
     * 查询分页
     *
     * @param page            分页对象
     * @param livableSeatInfo 分页参数对象
     * @return 查询分页结果
     */
    IPage<SeatInfo> selectPage(Page page, SeatInfo livableSeatInfo);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    SeatInfo getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param livableSeatInfo 过滤条件实体对象
     * @param request         请求
     * @param response        响应
     */
    void export(SeatInfo livableSeatInfo, HttpServletRequest request, HttpServletResponse response);

    /**
     * 通过主键查询单条数据
     *
     * @param seatInfo 条件信息
     * @return 单条数据
     */
    SeatInfoDTO selectDtoOne(SeatInfo seatInfo);

    /**
     * 分页查询所有dto数据
     *
     * @param seatInfoVo 查询分页对象
     * @return 所有数据
     */
    IPage<SeatInfoDTO> selectDtoPage(Page page, SeatInfoVo seatInfoVo);

    List<DeviceExtendInfo> getRefDevice(Set<Long> ids);

    IPage<SeatInfoVo> queryListByPage(RequestModel<SeatInfoVo> requestModel);

    /**
     * 控制
     * @param controlRecords
     */
    void control(SeatControllerDto controlRecords);
}

