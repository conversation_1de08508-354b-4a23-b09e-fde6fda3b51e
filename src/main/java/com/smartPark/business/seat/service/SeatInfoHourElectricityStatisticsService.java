package com.smartPark.business.seat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.seat.entity.SeatInfoHourElectricityStatistics;
import com.smartPark.business.seat.entity.vo.SeatInfoCountQueryDTO;
import com.smartPark.business.seat.entity.vo.SeatInfoElectricityOverviewVo;
import com.smartPark.business.seat.entity.vo.SeatInfoHourElectricityStatisticsVo;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.device.dto.FlowPushData;

import java.util.List;

/**
 * <p>
 * 座椅小时用电统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
public interface SeatInfoHourElectricityStatisticsService extends IService<SeatInfoHourElectricityStatistics> {

  IPage<SeatInfoHourElectricityStatisticsVo> queryListByPage(RequestModel<SeatInfoCountQueryDTO> requestModel);

  List<SeatInfoElectricityOverviewVo> overview(SeatInfoCountQueryDTO queryDTO);

  void parseFlow(FlowPushData flowData);
}
