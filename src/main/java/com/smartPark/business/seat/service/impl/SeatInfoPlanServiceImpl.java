package com.smartPark.business.seat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.seat.entity.SeatInfoGroup;
import com.smartPark.business.seat.entity.SeatInfoPlan;
import com.smartPark.business.seat.entity.SeatInfoPlanParam;
import com.smartPark.business.seat.entity.SeatInfoPlanRef;
import com.smartPark.business.seat.entity.dto.SeatControlConfig;
import com.smartPark.business.seat.entity.dto.SeatControllerDto;
import com.smartPark.business.seat.entity.vo.SeatInfoGroupVo;
import com.smartPark.business.seat.entity.vo.SeatInfoPlanRefVo;
import com.smartPark.business.seat.entity.vo.SeatInfoPlanVo;
import com.smartPark.business.seat.entity.vo.SeatInfoVo;
import com.smartPark.business.seat.mapper.SeatInfoGroupMapper;
import com.smartPark.business.seat.mapper.SeatInfoPlanMapper;
import com.smartPark.business.seat.mapper.SeatInfoPlanParamMapper;
import com.smartPark.business.seat.mapper.SeatInfoPlanRefMapper;
import com.smartPark.business.seat.service.SeatInfoGroupService;
import com.smartPark.business.seat.service.SeatInfoPlanService;
import com.smartPark.business.seat.service.SeatInfoService;
import com.smartPark.business.seat.util.SeatInfoDeviceUtils;
import com.smartPark.business.seat.util.SeatInfoJobUtils;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.job.constant.CronConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SeatInfo表服务实现类
 *
 * <AUTHOR>
 * @since 2023/07/18
 */
@Slf4j
@Service
public class SeatInfoPlanServiceImpl extends ServiceImpl<SeatInfoPlanMapper, SeatInfoPlan> implements SeatInfoPlanService {

    @Resource
    private BaseUserContextProducer baseUserContextProducer;

    @Resource
    private SeatInfoPlanParamMapper seatInfoPlanParamMapper;

    @Resource
    private SeatInfoPlanRefMapper seatInfoPlanRefMapper;

    @Resource
    private SeatInfoGroupMapper seatInfoGroupMapper;

    @Autowired
    private SeatInfoService seatInfoService;

    @Resource
    private SeatInfoGroupService seatInfoGroupService;

    @Override
    public IPage<SeatInfoPlanVo> queryListByPage(RequestModel<SeatInfoPlan> requestModel) {
        Page page = requestModel.getPage();
        SeatInfoPlan seatInfoPlan = requestModel.getCustomQueryParams();
        IPage<SeatInfoPlanVo> seatInfoPlanVoIPage = baseMapper.queryListByPage(page, seatInfoPlan);
        seatInfoPlanVoIPage.getRecords().forEach(r ->{
            //控制逻辑
            Map<String,Object> map = new HashMap<>(1);
            map.put("plan_id_",r.getId());
            List<SeatInfoPlanParam> planParamList = seatInfoPlanParamMapper.selectByMap(map);
            r.setParamList(planParamList);
            //设备数量
            if (Integer.valueOf(1).equals(r.getControlType())){
                //查询分组对应的设备
                List<String> ids = seatInfoPlanRefMapper.findDeviceNumByPId(r.getId());
                int size = ids.stream().filter(id -> StringUtils.isNotBlank(id)).flatMap(id -> Arrays.asList(id.split(",")).stream())
                        .filter(id -> StringUtils.isNotBlank(id)).collect(Collectors.toSet()).size();
                r.setChooseNum(size);
            }else {
                List<SeatInfoPlanRef> planRefs = seatInfoPlanRefMapper.selectByMap(map);
                r.setChooseNum(planRefs.size());
            }
        });
        return seatInfoPlanVoIPage;
    }

    @Override
    public List<SeatInfoPlanRefVo> getRefList(Long planId, Integer type) {
        List<SeatInfoPlanRefVo> list = new ArrayList<>();
        //id不为空时查询已关联的设备和分组
        //分组id
        List<Long> groupIds;
        List<Long> deviceIds;
        //设备id
        if (null != planId){
            QueryWrapper<SeatInfoPlanRef> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("plan_id_",planId);
            List<SeatInfoPlanRef> planRefList = seatInfoPlanRefMapper.selectList(queryWrapper);
            //分组id
            groupIds = planRefList.stream().filter(p -> Integer.valueOf(1).equals(p.getRefType()))
                    .map(p -> p.getRefId()).collect(Collectors.toList());
            //设备id
            deviceIds = planRefList.stream().filter(p -> Integer.valueOf(2).equals(p.getRefType()))
                    .map(p -> p.getRefId()).collect(Collectors.toList());
        } else {
            deviceIds = new ArrayList<>();
            groupIds = new ArrayList<>();
        }
        if (Integer.valueOf(1).equals(type)){
            //分组
            QueryWrapper<SeatInfoGroup> groupQueryWrapper = new QueryWrapper<>();
            List<SeatInfoGroup> seatInfoGroups = seatInfoGroupMapper.selectList(groupQueryWrapper);
            seatInfoGroups.forEach(s ->{
                SeatInfoPlanRefVo seatInfoPlanRefVo = new SeatInfoPlanRefVo();
                seatInfoPlanRefVo.setId(s.getId());
                seatInfoPlanRefVo.setName(s.getName());
                //判断是否已选择
                if (groupIds.contains(s.getId())){
                    seatInfoPlanRefVo.setChoose(true);
                }
                list.add(seatInfoPlanRefVo);
            });
        }else {
            //查询所有的路灯
            RequestModel<SeatInfoVo> requestModel = new RequestModel<>();
            requestModel.setPage(new Page(1,-1));
            SeatInfoVo seatInfoVo = new SeatInfoVo();
            requestModel.setCustomQueryParams(seatInfoVo);
            List<SeatInfoVo> records = seatInfoService.queryListByPage(requestModel).getRecords();
            records.forEach(s ->{
                SeatInfoPlanRefVo seatInfoPlanRefVo = new SeatInfoPlanRefVo();
                seatInfoPlanRefVo.setId(s.getId());
                seatInfoPlanRefVo.setName(s.getSeatName());
                if (deviceIds.contains(s.getId())){
                    seatInfoPlanRefVo.setChoose(true);
                }
                list.add(seatInfoPlanRefVo);
            });
        }
        return list;
    }

    /**
     * 增加
     * @param seatInfoPlanVo
     */
    @Override
    @Transactional
    public void insert(SeatInfoPlanVo seatInfoPlanVo) {
        /*
         * 验证重复
         */
        this.checkExist(seatInfoPlanVo);
        SeatInfoPlan seatInfoPlan = BeanUtil.toBean(seatInfoPlanVo, SeatInfoPlan.class);
        //设置基本属性
        seatInfoPlan.setPlanStatus(1);
        this.setBase(seatInfoPlan);
        this.save(seatInfoPlan);
        //保存参数
        List<SeatInfoPlanParam> paramList = seatInfoPlanVo.getParamList();
        if (CollectionUtil.isNotEmpty(paramList)){
            paramList.forEach(p ->{
                p.setPlanId(seatInfoPlan.getId());
                p.setCreateTime(seatInfoPlan.getCreateTime());
                p.setCreatorId(seatInfoPlan.getCreatorId());
                p.setModifyTime(seatInfoPlan.getModifyTime());
                p.setModifyId(seatInfoPlan.getModifyId());
                seatInfoPlanParamMapper.insert(p);
            });
        }
        //保存任务关联
        List<SeatInfoPlanRefVo> planRefList = seatInfoPlanVo.getPlanRefList();
        List<SeatInfoPlanRef> planRefs = BeanUtil.copyToList(planRefList, SeatInfoPlanRef.class);
        if (CollectionUtil.isNotEmpty(planRefs)){
            planRefs.forEach(p ->{
                p.setPlanId(seatInfoPlan.getId());
                p.setRefType(seatInfoPlan.getControlType());
                p.setCreateTime(seatInfoPlan.getCreateTime());
                p.setCreatorId(seatInfoPlan.getCreatorId());
                p.setModifyTime(seatInfoPlan.getModifyTime());
                p.setModifyId(seatInfoPlan.getModifyId());
                seatInfoPlanRefMapper.insert(p);
            });
        }
        //生成定时任务
        seatInfoPlanVo.setId(seatInfoPlan.getId());
        SeatInfoJobUtils.addJob(seatInfoPlanVo);
        LogHelper.setLogInfo("", seatInfoPlanVo.toString(), null, null,"新增座椅控制任务，任务编码："+seatInfoPlanVo.getPlanNo());
    }

    /**
     * 验证重复
     */
    private void checkExist(SeatInfoPlanVo seatInfoPlanVo) {
        QueryWrapper<SeatInfoPlan> queryWrapper = new QueryWrapper<>();
        //设置判断重复条件
        queryWrapper.and(q -> q.eq("plan_name_",seatInfoPlanVo.getPlanName())
                .or().eq("plan_no_",seatInfoPlanVo.getPlanNo()));
        //编辑的时候存在id
        Optional.ofNullable(seatInfoPlanVo.getId()).ifPresent(id -> queryWrapper.ne("id_",seatInfoPlanVo.getId()));
        Integer integer = baseMapper.selectCount(queryWrapper);
        if (integer>0){
            throw new BusinessException("该座椅控制任务已存在");
        }
    }

    @Override
    public void taskCallback(JSONObject jsonObject) throws ParseException {
        Long planId = jsonObject.getLong("planId");
        //查询任务
        SeatInfoPlanVo planVo = findById(planId);
        Integer timeType = jsonObject.getInteger("timeType");
        Integer controlType = jsonObject.getInteger("controlType");
        SeatControlConfig controlConfig = jsonObject.getObject("controlConfig", SeatControlConfig.class);
        if (controlType == null){
            return;
        }
        if (controlConfig == null){
            return;
        }
        Date date = new Date();
        //获取任务中的设备
        List<SeatInfoVo> seatInfoVos = this.getSeatInfosByPlan(planVo);
        //过滤掉没绑定物联网设备的座椅
        seatInfoVos = seatInfoVos.stream().filter(s -> StringUtils.isNotBlank(s.getDeviceCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(seatInfoVos)){
            boolean flag = false;
            if (!CronConstant.TIME_TYPE_QUARTER.equals(timeType)){
                //每天查询是否在时间段中
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String timeValues = planVo.getTimeValues();
                String[] strings = timeValues.split(",");
                for (int i = 0; i < strings.length; i++) {
                    String[] dateStr = strings[i].split("~");
                    Date startTime = simpleDateFormat.parse(DateUtil.year(date) + "-" + dateStr[0]);
                    Date endTime = DateUtil.endOfDay(simpleDateFormat.parse(DateUtil.year(date) + "-" + dateStr[1]));
                    long time = date.getTime();
                    if (time >= startTime.getTime() && time <= endTime.getTime()){
                        flag = true;
                        break;
                    }
                }
            }else {
                flag = true;
            }
            if (flag){
                seatInfoVos.forEach(s ->{
                    //记录控制记录
                    SeatControllerDto controlRecords = new SeatControllerDto();
                    controlRecords.setBusinessId(s.getId());
                    controlRecords.setDeviceCode(s.getDeviceCode());
                    controlRecords.setControlSource(3);
                    controlRecords.setControlType(controlType);
                    controlRecords.setControlConfig(controlConfig);
                    controlRecords.setType(2);
                    controlRecords.setRefId(planId);
                    controlRecords.setCreateTime(date);
                    controlRecords.setModifyTime(date);

                    try {
                        //下发指令
                        SeatInfoDeviceUtils.controlToDevice(controlRecords);
                    } catch (Exception e) {
                        log.info("下行报错："+controlRecords.getDeviceCode());
                    }
                });
            }
        }
    }

    //获取任务中的设备
    private List<SeatInfoVo> getSeatInfosByPlan(SeatInfoPlanVo planVo) {
        RequestModel<SeatInfoVo> voRequestModel = new RequestModel<>();
        voRequestModel.setPage(new Page<>(1,-1));
        SeatInfoVo seatInfoVo = new SeatInfoVo();
        List<SeatInfoPlanRefVo> refList = planVo.getPlanRefList();
        Set<Long> refIds = (null == refList?new ArrayList<SeatInfoPlanRefVo>():refList).stream().map(s -> s.getRefId()).collect(Collectors.toSet());
        if (Integer.valueOf(1).equals(planVo.getControlType()) && CollectionUtil.isNotEmpty(refIds)){
            //分组
            QueryWrapper<SeatInfoGroup> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id_",refIds);
            List<SeatInfoGroup> groups = seatInfoGroupMapper.selectList(queryWrapper);
            refIds = groups.stream().filter(g -> StringUtils.isNotBlank(g.getSeatInfoIds()))
                    .map(g -> g.getSeatInfoIds()).flatMap(g -> Arrays.asList(g.split(",")).stream())
                    .filter(g -> StringUtils.isNotBlank(g)).map(g->Long.valueOf(g))
                    .collect(Collectors.toSet());
        }
        if (CollectionUtil.isNotEmpty(refIds)){
            seatInfoVo.setIds(refIds);
        }else {
            //没有设备时设置一个查询不到数据的条件
            Set<Long> ids = new HashSet<>();
            ids.add(-1L);
            seatInfoVo.setIds(ids);
        }
        voRequestModel.setCustomQueryParams(seatInfoVo);
        IPage<SeatInfoVo> seatInfoVoIPage = seatInfoService.queryListByPage(voRequestModel);
        return seatInfoVoIPage.getRecords();
    }

    /**
     * @Description: 根据id查询路灯控制任务(编辑使用)
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @Override
    public SeatInfoPlanVo findById(Long id) {
        SeatInfoPlan seatInfoPlan = baseMapper.selectById(id);
        Map<String,Object> map = new HashMap<>();
        map.put("plan_id_",id);
        SeatInfoPlanVo seatInfoPlanVo = BeanUtil.toBean(seatInfoPlan, SeatInfoPlanVo.class);
        //参数
        List<SeatInfoPlanParam> seatInfoPlanParams = seatInfoPlanParamMapper.selectByMap(map);
        seatInfoPlanVo.setParamList(seatInfoPlanParams);
        //关联
        List<SeatInfoPlanRefVo> list = new ArrayList<>();
        QueryWrapper<SeatInfoPlanRef> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plan_id_",id);
        List<SeatInfoPlanRef> planRefList = seatInfoPlanRefMapper.selectList(queryWrapper);
        Set<Long> refIds = planRefList.stream().map(p -> p.getRefId()).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(refIds)){
            if (Integer.valueOf(1).equals(seatInfoPlan.getControlType())){
                //分组
                QueryWrapper<SeatInfoGroup> groupQueryWrapper = new QueryWrapper<>();
                groupQueryWrapper.in("id_",refIds);
                List<SeatInfoGroup> seatInfoGroups = seatInfoGroupMapper.selectList(groupQueryWrapper);
                seatInfoGroups.forEach(s ->{
                    SeatInfoPlanRefVo seatInfoPlanRefVo = new SeatInfoPlanRefVo();
                    seatInfoPlanRefVo.setRefId(s.getId());
                    seatInfoPlanRefVo.setName(s.getName());
                    list.add(seatInfoPlanRefVo);
                });
            }else {
                //查询所有的路灯
                RequestModel<SeatInfoVo> requestModel = new RequestModel<>();
                requestModel.setPage(new Page(1,-1));
                SeatInfoVo seatInfoVo = new SeatInfoVo();
                seatInfoVo.setIds(refIds);
                requestModel.setCustomQueryParams(seatInfoVo);
                List<SeatInfoVo> records = seatInfoService.queryListByPage(requestModel).getRecords();
                records.forEach(s ->{
                    SeatInfoPlanRefVo seatInfoPlanRefVo = new SeatInfoPlanRefVo();
                    seatInfoPlanRefVo.setRefId(s.getId());
                    seatInfoPlanRefVo.setName(s.getSbmc());
                    list.add(seatInfoPlanRefVo);
                });
            }
            seatInfoPlanVo.setPlanRefList(list);
        }
        return seatInfoPlanVo;
    }

    /**
     * 设置基本属性
     * @param seatInfoPlan
     */
    private void setBase(SeatInfoPlan seatInfoPlan) {
        Long userId = null;
        if(null != baseUserContextProducer.getCurrent()){
            userId = baseUserContextProducer.getCurrent().getId();
        }
        seatInfoPlan.setModifyTime(new Date());
        seatInfoPlan.setModifyId(userId);
        //没有id就是新增,有就是编辑
        if (null == seatInfoPlan.getId()){
            seatInfoPlan.setCreatorId(userId);
            seatInfoPlan.setCreateTime(new Date());
        }
    }

    @Override
    public IPage<?> querySeatInfoListByPage(RequestModel<SeatInfoPlan> requestModel) {
        Long id = requestModel.getCustomQueryParams().getId();
        if (null == id){
            throw new BusinessException("id不能为可空");
        }
        SeatInfoPlan seatInfoPlan = baseMapper.selectById(id);
        //查询关联的分组或者设备
        Map<String,Object> map = new HashMap<>();
        map.put("plan_id_",id);
        //关联
        List<SeatInfoPlanRef> seatInfoPlanRefs = seatInfoPlanRefMapper.selectByMap(map);
        Set<Long> refIds = seatInfoPlanRefs.stream().map(s -> s.getRefId()).collect(Collectors.toSet());
        refIds.add(-1L);
        if (Integer.valueOf(1).equals(seatInfoPlan.getControlType())){
            //分组
            RequestModel<SeatInfoGroup> voRequestModel = new RequestModel<>();
            SeatInfoGroup seatInfoGroup = new SeatInfoGroup();
            seatInfoGroup.setIds(refIds);
            voRequestModel.setCustomQueryParams(seatInfoGroup);
            voRequestModel.setPage(requestModel.getPage());
            IPage<SeatInfoGroupVo> groupVoIPage = seatInfoGroupService.queryListByPage(voRequestModel);
            return groupVoIPage;
        }else {
            //设备
            RequestModel<SeatInfoVo> voRequestModel = new RequestModel<>();
            SeatInfoVo seatInfoVo = new SeatInfoVo();
            seatInfoVo.setIds(refIds);
            voRequestModel.setCustomQueryParams(seatInfoVo);
            voRequestModel.setPage(requestModel.getPage());
            IPage<SeatInfoVo> seatInfoVoIPage = seatInfoService.queryListByPage(voRequestModel);
            return seatInfoVoIPage;
        }
    }

    @Override
    public void updateOne(SeatInfoPlanVo seatInfoPlanVo) {
        /**
         * 验证重复
         */
        this.checkExist(seatInfoPlanVo);
        SeatInfoPlan seatInfoPlan = BeanUtil.toBean(seatInfoPlanVo, SeatInfoPlan.class);
        //设置基本属性
        this.setBase(seatInfoPlan);
        this.updateById(seatInfoPlan);
        //保存参数
        List<SeatInfoPlanParam> paramList = seatInfoPlanVo.getParamList();
        //删除参数
        Map<String,Object> map = new HashMap<>();
        map.put("plan_id_",seatInfoPlanVo.getId());
        //删除参数
        seatInfoPlanParamMapper.deleteByMap(map);
        //删除关联
        seatInfoPlanRefMapper.deleteByMap(map);
        if (CollectionUtil.isNotEmpty(paramList)){
            paramList.forEach(p ->{
                p.setPlanId(seatInfoPlan.getId());
                p.setCreateTime(seatInfoPlan.getCreateTime());
                p.setCreatorId(seatInfoPlan.getCreatorId());
                p.setModifyTime(seatInfoPlan.getModifyTime());
                p.setModifyId(seatInfoPlan.getModifyId());
                seatInfoPlanParamMapper.insert(p);
            });
        }
        //保存任务关联
        List<SeatInfoPlanRefVo> planRefList = seatInfoPlanVo.getPlanRefList();
        List<SeatInfoPlanRef> planRefs = BeanUtil.copyToList(planRefList, SeatInfoPlanRef.class);
        if (CollectionUtil.isNotEmpty(planRefs)){
            planRefs.forEach(p ->{
                p.setRefType(seatInfoPlan.getControlType());
                p.setPlanId(seatInfoPlan.getId());
                p.setCreateTime(seatInfoPlan.getCreateTime());
                p.setCreatorId(seatInfoPlan.getCreatorId());
                p.setModifyTime(seatInfoPlan.getModifyTime());
                p.setModifyId(seatInfoPlan.getModifyId());
                seatInfoPlanRefMapper.insert(p);
            });
        }
        //删除之前的任务
        SeatInfoJobUtils.delJob(seatInfoPlanVo.getId());
        //判断是否启用
        SeatInfoPlan plan = baseMapper.selectById(seatInfoPlanVo.getId());
        if (null != plan &&Integer.valueOf(1).equals(plan.getPlanStatus())){
            //创建
            SeatInfoJobUtils.addJob(seatInfoPlanVo);
        }
        LogHelper.setLogInfo("", seatInfoPlanVo.toString(), null, null,"修改座椅控制任务，任务编码："+seatInfoPlanVo.getPlanNo());
    }

    @Override
    public void delBatch(Set<Long> ids) {
        StringJoiner sj = new StringJoiner("，");
        ids.forEach(id ->{
            SeatInfoPlan byId = baseMapper.selectById(id);
            sj.add(byId.getPlanNo());
            //删除参数
            Map<String,Object> map = new HashMap<>();
            map.put("plan_id_",id);
            seatInfoPlanParamMapper.deleteByMap(map);
            //删除关联
            seatInfoPlanRefMapper.deleteByMap(map);
            baseMapper.deleteById(id);
            //任务处理
            //删除之前的任务
            SeatInfoJobUtils.delJob(id);
        });
        LogHelper.setLogInfo("", "ids:"+ids, null, null,"删除座椅控制任务，任务编码："+sj);
    }

    @Override
    public void updateStatusById(SeatInfoPlan seatInfoPlan) {
        baseMapper.updateById(seatInfoPlan);
        //任务处理
        if (Integer.valueOf(1).equals(seatInfoPlan.getPlanStatus())){
            //创建
            SeatInfoJobUtils.addJob(this.findById(seatInfoPlan.getId()));
        }else {
            //删除之前的任务
            SeatInfoJobUtils.delJob(seatInfoPlan.getId());
        }
    }
}

