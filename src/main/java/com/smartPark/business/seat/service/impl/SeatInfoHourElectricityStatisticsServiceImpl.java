package com.smartPark.business.seat.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.seat.constant.SeatInfoControl;
import com.smartPark.business.seat.entity.SeatInfoAlarm;
import com.smartPark.business.seat.entity.SeatInfoHourElectricityStatistics;
import com.smartPark.business.seat.entity.dto.SeatInfoDTO;
import com.smartPark.business.seat.entity.vo.SeatInfoCountQueryDTO;
import com.smartPark.business.seat.entity.vo.SeatInfoElectricityOverviewVo;
import com.smartPark.business.seat.entity.vo.SeatInfoHourElectricityStatisticsVo;
import com.smartPark.business.seat.mapper.SeatInfoAlarmMapper;
import com.smartPark.business.seat.mapper.SeatInfoHourElectricityStatisticsMapper;
import com.smartPark.business.seat.mapper.SeatInfoMapper;
import com.smartPark.business.seat.service.SeatInfoHourElectricityStatisticsService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.common.utils.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 路灯小时用电统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Slf4j
@Service
public class SeatInfoHourElectricityStatisticsServiceImpl extends ServiceImpl<SeatInfoHourElectricityStatisticsMapper, SeatInfoHourElectricityStatistics> implements SeatInfoHourElectricityStatisticsService {

  @Resource
  private SeatInfoAlarmMapper seatInfoAlarmMapper;
  @Resource
  private SeatInfoMapper seatInfoMapper;

  @Override
  public IPage<SeatInfoHourElectricityStatisticsVo> queryListByPage(
      RequestModel<SeatInfoCountQueryDTO> requestModel) {
    Page page = requestModel.getPage();
    SeatInfoCountQueryDTO dto = requestModel.getCustomQueryParams();
    // 校验时间不能为空
    IPage<SeatInfoHourElectricityStatisticsVo> seatInfoControlRecordsIPage = baseMapper.queryListByPage(page, dto);
    return seatInfoControlRecordsIPage;
  }

  @Override
  public List<SeatInfoElectricityOverviewVo> overview(SeatInfoCountQueryDTO queryDTO) {
    List<SeatInfoElectricityOverviewVo> result = new ArrayList<>();
    Date currentDate = DateUtil.endOfHour(new Date());
    Date startTime = DateUtil.beginOfDay(currentDate);
    // 今天 昨天
    SeatInfoElectricityOverviewVo todayVo = new SeatInfoElectricityOverviewVo();
    Double today = baseMapper.queryElectricityByTime(startTime, currentDate, queryDTO.getDeviceCodes());
    Double yesterday = baseMapper.queryElectricityByTime(DateUtil.offsetDay(startTime, -1), DateUtil.offsetDay(currentDate, -1), queryDTO.getDeviceCodes());
    todayVo.setThisTime(today);
    todayVo.setLastTime(yesterday);
    todayVo.setRate(calculationRate(today,yesterday));
    result.add(todayVo);
    // 本周 上周
    SeatInfoElectricityOverviewVo thisWeekVo = new SeatInfoElectricityOverviewVo();
    Double thisWeek = baseMapper.queryElectricityByTime(DateUtil.beginOfWeek(currentDate), currentDate, queryDTO.getDeviceCodes());
    Double lastWeek = baseMapper.queryElectricityByTime(DateUtil.offsetWeek(DateUtil.beginOfWeek(currentDate), -1), DateUtil.offsetWeek(currentDate, -1), queryDTO.getDeviceCodes());
    thisWeekVo.setThisTime(thisWeek);
    thisWeekVo.setLastTime(lastWeek);
    thisWeekVo.setRate(calculationRate(thisWeek,lastWeek));
    result.add(thisWeekVo);
    // 本月 上月
    SeatInfoElectricityOverviewVo thisMonthVo = new SeatInfoElectricityOverviewVo();
    Double thisMonth = baseMapper.queryElectricityByTime(DateUtil.beginOfMonth(currentDate), currentDate, queryDTO.getDeviceCodes());
    Double lastMonth = baseMapper.queryElectricityByTime(DateUtil.beginOfMonth(DateUtil.offsetMonth(startTime, -1)), DateUtil.offsetMonth(currentDate, -1), queryDTO.getDeviceCodes());
    thisMonthVo.setThisTime(thisMonth);
    thisMonthVo.setLastTime(lastMonth);
    thisMonthVo.setRate(calculationRate(thisMonth,lastMonth));
    result.add(thisMonthVo);
    // 本年 上年
    SeatInfoElectricityOverviewVo thisYearVo = new SeatInfoElectricityOverviewVo();
    Double thisYear = baseMapper.queryElectricityByTime(DateUtil.beginOfYear(currentDate), currentDate, queryDTO.getDeviceCodes());
    Double lastYear = baseMapper.queryElectricityByTime(DateUtil.beginOfYear(
        com.smartPark.common.utils.DateUtil.addYear(startTime, -1)), com.smartPark.common.utils.DateUtil.addYear(currentDate, -1), queryDTO.getDeviceCodes());
    thisYearVo.setThisTime(thisYear);
    thisYearVo.setLastTime(lastYear);
    thisYearVo.setRate(calculationRate(thisYear,lastYear));
    result.add(thisYearVo);
    return result;
  }

  @Override
  public void parseFlow(FlowPushData flowPushData) {
    processElectricity(flowPushData);
    processAlarmInfo(flowPushData);
  }

  private void processAlarmInfo(FlowPushData flowPushData) {
    //FlowPushData
    log.debug("----座椅告警记录流水处理----");
    try {
      String deviceCode = flowPushData.getDevice_id();
      SeatInfoDTO seatInfoDTO = seatInfoMapper.queryByDeviceCode(deviceCode);
      //设备存在，且是启用
      if(seatInfoDTO == null){
        log.warn("触发设备不存在或者未绑定到座椅模块，deviceCode:{}",deviceCode);
        return;
      }
      JSONObject flowData = (JSONObject) flowPushData.getData();
      if(null != flowData) {
        Integer alamType = flowData.getInteger(SeatInfoControl.alarm_type);
        if (null == alamType) {
          log.error("座椅告警信息，alarm_type为空");
          return;
        }
        //告警类型不为空,保存告警记录0 - 正常
        //1 - 低电量
        //2 - 电压异常
        //3 - 灯光异常
        //4 - 制冷异常
        //5 - 加热异常
        //6 - 音响异常
        if (!Objects.equals(alamType, 0)) {
          SeatInfoAlarm seatInfoAlarm = new SeatInfoAlarm();
          seatInfoAlarm.setDeviceCode(deviceCode);
          seatInfoAlarm.setSeatCode(seatInfoDTO.getSeatCode());
          seatInfoAlarm.setAlarmType(alamType);
          seatInfoAlarm.setRecordTime(new Date());
          seatInfoAlarmMapper.insert(seatInfoAlarm);
        }
      }
    } catch (Exception e) {
      log.error("---座椅告警记录，无法解析---");
    }
  }

  private void processElectricity(FlowPushData flowPushData) {
    //FlowPushData
    log.debug("----座椅用电记录流水处理----");
    try {
      String deviceCode = flowPushData.getDevice_id();
      SeatInfoDTO seatInfoDTO = seatInfoMapper.queryByDeviceCode(deviceCode);
      //设备存在，且是启用
      if(seatInfoDTO == null){
        log.warn("触发设备不存在或者未绑定到座椅模块，deviceCode:{}",deviceCode);
        return;
      }
      JSONObject flowData = (JSONObject) flowPushData.getData();
      if(null != flowData) {
        Double energyConsumption = flowData.getDouble(SeatInfoControl.combined_active_energy);
        if (null == energyConsumption) {
          log.error("座椅用电记录，combined_active_energy为空");
          return;
        }
        // 保存用电记录
        // 查询最新的一条记录
        LambdaQueryWrapper<SeatInfoHourElectricityStatistics> qw = new LambdaQueryWrapper<>();
        qw.eq(SeatInfoHourElectricityStatistics::getDeviceCode, deviceCode);
        qw.orderByDesc(SeatInfoHourElectricityStatistics::getCreateTime);
        qw.last("limit 1");
        SeatInfoHourElectricityStatistics lastRecord = baseMapper.selectOne(qw);
        Date current = new Date();
        // 设置记录时间为当前时间整点时间 2023-05-31 12:00:00
        Date hourDate = DateUtil.beginOfHour(current);
        if (null == lastRecord) {
          lastRecord = new SeatInfoHourElectricityStatistics();
          lastRecord.setSeatCode(seatInfoDTO.getSeatCode());
          lastRecord.setRecordTime(hourDate);
          lastRecord.setDeviceCode(deviceCode);
          lastRecord.setStartReading(energyConsumption);
          lastRecord.setCreateTime(current);
          lastRecord.setModifyTime(current);
          baseMapper.insert(lastRecord);
        } else {
          // 判断是否新的一小时
          Date lastRecordTime = lastRecord.getRecordTime();
          if (DateUtil.between(DateUtil.beginOfHour(lastRecordTime), DateUtil.beginOfHour(current), DateUnit.HOUR) > 0) {
            // 新的一小时
            if (lastRecord.getEndReading() == null) {// 期末读数为空，更新为期初读数
              lastRecord.setElectricyIncrement(0.0);
              lastRecord.setEndReading(lastRecord.getStartReading());
              lastRecord.setModifyTime(new Date());
              baseMapper.updateById(lastRecord);
            }
            // 新增一条记录
            SeatInfoHourElectricityStatistics newRecord = new SeatInfoHourElectricityStatistics();
            newRecord.setRecordTime(hourDate);
            newRecord.setDeviceCode(deviceCode);
            newRecord.setStartReading(lastRecord.getEndReading());
            newRecord.setElectricyIncrement(
                NumberUtil.sub(energyConsumption, lastRecord.getEndReading(), 2));
            newRecord.setSeatCode(seatInfoDTO.getSeatCode());
            newRecord.setEndReading(energyConsumption);
            newRecord.setCreateTime(current);
            newRecord.setModifyTime(current);
            baseMapper.insert(newRecord);
          } else {
            if(null == lastRecord.getEndReading()){
              if(energyConsumption <= lastRecord.getStartReading()){
                log.error("座椅用电记录，combined_active_energy小于等于期初读数");
                return;
              }
            }else{
              if(energyConsumption <= lastRecord.getEndReading()){
                log.error("座椅用电记录，combined_active_energy小于等于期末读数");
                return;
              }
            }
            // 同一小时 更新最新的记录
            lastRecord.setEndReading(energyConsumption);
            lastRecord.setElectricyIncrement(
                NumberUtil.sub(energyConsumption, lastRecord.getStartReading(), 2));
            lastRecord.setModifyTime(current);
            baseMapper.updateById(lastRecord);
          }

        }
      }
    } catch (Exception e) {
      log.error("---座椅用电记录，无法解析---");
    }
  }

  /**
   * 计算能耗比率（ 本期/上期 - 1 ）
   * @param thisConsumption 本期
   * @param lastConsumption 上期
   * @return
   */
  private Double calculationRate(Double thisConsumption,Double lastConsumption){
    if(lastConsumption == null || lastConsumption == 0){
      return 0.0d;
    }
    Double result1 = NumberUtil.div(thisConsumption,lastConsumption,4);
    result1 = NumberUtil.sub(result1,1d,4);
    Double rate = NumberUtil.mul(result1,100d,2);
    return rate;
  }


  public static void main(String[] args) {
    String dateStr = "2023-06-08 11:45:01";
    Date date = DateUtil.parse(dateStr);
    Date currentDate = DateUtil.endOfHour(date);
    Date startTime = DateUtil.beginOfDay(currentDate);
    // 今天 昨天

    System.out.println("今天 昨天");
    printTime(startTime, currentDate);
    printTime(DateUtil.offsetDay(startTime, -1), DateUtil.offsetDay(currentDate, -1));

    // 本周 上周
    System.out.println("本周 上周");
    printTime(DateUtil.beginOfWeek(currentDate), currentDate);
    printTime(DateUtil.offsetWeek(DateUtil.beginOfWeek(currentDate), -1), DateUtil.offsetWeek(currentDate, -1));

    // 本月 上月
    System.out.println("本月 上月");
    printTime(DateUtil.beginOfMonth(currentDate), currentDate);
    printTime(DateUtil.beginOfMonth(DateUtil.offsetMonth(startTime, -1)), DateUtil.offsetMonth(currentDate, -1));

    // 本年 上年
    System.out.println("本年 上年");
    printTime(DateUtil.beginOfYear(currentDate), currentDate);
    printTime(DateUtil.beginOfYear(
        com.smartPark.common.utils.DateUtil.addYear(startTime, -1)), com.smartPark.common.utils.DateUtil.addYear(currentDate, -1));


  }
  private static void printTime(Date startTime,Date endTime){
    System.out.println("startTime:"+DateUtil.format(startTime,"yyyy-MM-dd HH:mm:ss"));
    System.out.println("endTime:"+DateUtil.format(endTime,"yyyy-MM-dd HH:mm:ss"));
  }
}
