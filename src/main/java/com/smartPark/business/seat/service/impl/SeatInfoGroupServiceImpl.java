package com.smartPark.business.seat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.seat.entity.SeatInfo;
import com.smartPark.business.seat.entity.SeatInfoGroup;
import com.smartPark.business.seat.entity.SeatInfoPlanRef;
import com.smartPark.business.seat.entity.dto.SeatControllerDto;
import com.smartPark.business.seat.entity.dto.SeatInfoDTO;
import com.smartPark.business.seat.entity.vo.SeatInfoGroupVo;
import com.smartPark.business.seat.entity.vo.SeatInfoVo;
import com.smartPark.business.seat.mapper.SeatInfoGroupMapper;
import com.smartPark.business.seat.mapper.SeatInfoPlanRefMapper;
import com.smartPark.business.seat.service.SeatInfoGroupService;
import com.smartPark.business.seat.service.SeatInfoService;
import com.smartPark.business.seat.util.SeatInfoDeviceUtils;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.entity.device.ObjInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import site.morn.framework.entity.BaseUser;

import java.util.*;
import java.util.stream.Collectors;

/**
 * SeatInfo表服务实现类
 *
 * <AUTHOR>
 * @since 2023/07/18
 */
@Slf4j
@Service
public class SeatInfoGroupServiceImpl extends ServiceImpl
        <SeatInfoGroupMapper, SeatInfoGroup> implements SeatInfoGroupService {

    @Autowired
    private BaseUserContextProducer baseUserContextProducer;

    @Autowired
    private SeatInfoService seatInfoService;

    @Autowired
    private SeatInfoPlanRefMapper seatInfoPlanRefMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public IPage<SeatInfoGroupVo> queryListByPage(RequestModel<SeatInfoGroup> requestModel) {
        Page page = requestModel.getPage();
        SeatInfoGroup streetlightGroup = requestModel.getCustomQueryParams();
        IPage<SeatInfoGroupVo> streetlightGroupIPage = baseMapper.queryListByPage(page, streetlightGroup);
        //求已选择设备数量
        streetlightGroupIPage.getRecords().forEach(r ->{
            String streetlightIds = r.getSeatInfoIds();
            if (StringUtils.isNotBlank(streetlightIds)){
                String[] ids = streetlightIds.split(",");
                List<String> idList = Arrays.stream(ids).filter(d -> StringUtils.isNotBlank(d))
                        .collect(Collectors.toList());
                r.setChooseNum(idList.size());
            }
        });
        return streetlightGroupIPage;
    }

    @Override
    public void insert(SeatInfoGroupVo seatInfoGroupVo) {
        /**
         * 验证重复
         */
        this.checkExist(seatInfoGroupVo);
        //数据处理
        SeatInfoGroup seatInfoGroup = getSeatInfoGroup(seatInfoGroupVo);
        //设置基本属性
        this.setBase(seatInfoGroup);
        this.save(seatInfoGroup);
        LogHelper.setLogInfo("", seatInfoGroupVo.toString(), null, null,"新增分组，分组名称："+seatInfoGroupVo.getName());
    }

    @Override
    public SeatInfoGroupVo findByDetailId(Long id) {
        SeatInfoGroup seatInfoGroup = baseMapper.selectById(id);
        SeatInfoGroupVo seatInfoGroupVo = BeanUtil.toBean(seatInfoGroup, SeatInfoGroupVo.class);
        //创建人
        BaseUser baseUser = (BaseUser)redisUtil.hget(RedisConstant.USER_PRE,String.valueOf(seatInfoGroupVo.getCreatorId()));
        Optional.ofNullable(baseUser).ifPresent(b -> seatInfoGroupVo.setCreatorName(b.getNickname()));
        //求已选择设备数量
        String seatInfoIds = seatInfoGroupVo.getSeatInfoIds();
        if (StringUtils.isNotBlank(seatInfoIds)){
            String[] ids = seatInfoIds.split(",");
            List<String> idList = Arrays.stream(ids).filter(d -> StringUtils.isNotBlank(d))
                    .collect(Collectors.toList());
            seatInfoGroupVo.setChooseNum(idList.size());
            seatInfoGroupVo.setSelectSeatList(seatInfoService.getBaseMapper().selectList(new QueryWrapper<SeatInfo>().in("id_",idList)));
        }
        return seatInfoGroupVo;
    }

    @Override
    public IPage<SeatInfoDTO> querySeatInfoListByPage(RequestModel<SeatInfoGroupVo> requestModel) {
//        RequestModel<SeatInfoVo> voRequestModel = new RequestModel<>();
//        voRequestModel.setPage(requestModel.getPage());
        SeatInfoVo seatInfoVo = new SeatInfoVo();
        Long id = requestModel.getCustomQueryParams().getId();
        if (null == id){
            throw new BusinessException("id不能为可空");
        }
        SeatInfoGroup seatInfoGroup = baseMapper.selectById(id);
        String seatInfoIds = seatInfoGroup.getSeatInfoIds();
        if (StringUtils.isNotBlank(seatInfoIds)){
            List<String> list = Arrays.asList(seatInfoIds.split(","));
            Set<Long> ids = list.stream().filter(l->StringUtils.isNotBlank(l)).map(l -> Long.valueOf(l)).collect(Collectors.toSet());
            seatInfoVo.setIds(ids);
        }else {
            //没有设备时设置一个查询不到数据的条件
            Set<Long> ids = new HashSet<>();
            ids.add(-1L);
            seatInfoVo.setIds(ids);
        }
//        voRequestModel.setCustomQueryParams(seatInfoVo);
        IPage<SeatInfoDTO> seatInfoVoIPage = seatInfoService.selectDtoPage(requestModel.getPage(), seatInfoVo);
        return seatInfoVoIPage;
    }

    @Override
    public void updateOne(SeatInfoGroupVo seatInfoGroupVo) {
        /**
         * 验证重复
         */
        this.checkExist(seatInfoGroupVo);
        SeatInfoGroup streetlightGroup = getSeatInfoGroup(seatInfoGroupVo);
        //设置基本属性
        this.setBase(streetlightGroup);
        this.updateById(streetlightGroup);
        LogHelper.setLogInfo("", seatInfoGroupVo.toString(), null, null,"修改分组，分组名称："+seatInfoGroupVo.getName());

    }

    @Override
    public IPage<SeatInfoDTO> selectNotBindDtoPage(Page page, SeatInfoVo customQueryParams) {
        //查询已经绑定的信息
        QueryWrapper<SeatInfoGroup> queryWrapper = new QueryWrapper<SeatInfoGroup>().eq("deleted_", 0);
        if (customQueryParams.getGroupId() != null){
            queryWrapper.ne("id_", customQueryParams.getGroupId());
        }
        List<SeatInfoGroup> existList = baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(existList)){
            existList.stream().filter(e -> StringUtils.isNotBlank(e.getSeatInfoIds())).map(e -> e.getSeatInfoIds()).map(s -> s.split(","))
                    .flatMap(Arrays::stream).filter(s -> StringUtils.isNotBlank(s))
                    .forEach(s -> customQueryParams.getNotInIds().add(Long.valueOf(s)));
        }
        IPage<SeatInfoDTO> iPage = baseMapper.selectNotBindDtoPage(page, customQueryParams);

        iPage.getRecords().forEach(dto -> {
            //区域
            ObjInfo objInfo = dto.getObjInfo();
            if (objInfo != null) {
                String areaPath = objInfo.getAreaPath();
                if (StringUtils.isNotBlank(areaPath)) {
                    objInfo.setAreaPath(areaPath.replaceAll("@", "/"));
                }
            }

        });

        return iPage;
    }

    @Override
    public void delBatch(Set<Long> ids) {
        StringJoiner sj = new StringJoiner("，");
        ids.forEach(id->{
            SeatInfoGroup seatInfoGroup = baseMapper.selectById(id);
            sj.add(seatInfoGroup.getName());
        });
        QueryWrapper<SeatInfoPlanRef> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("ref_id_",ids).eq("ref_type_",1);
        seatInfoPlanRefMapper.delete(queryWrapper);
        baseMapper.deleteBatchIds(ids);
        LogHelper.setLogInfo("", ids.toString(), null, null,"删除分组，分组名称："+sj);

    }

    @Override
    public void control(SeatControllerDto seatControllerDto) {
        Long groupId = seatControllerDto.getGroupId();
        //查询分组对应的设备
        SeatInfoGroup seatInfoGroup = baseMapper.selectById(groupId);
        if (null != seatInfoGroup && StringUtils.isNotBlank(seatInfoGroup.getSeatInfoIds())){
            List<Long> ids = Arrays.stream(seatInfoGroup.getSeatInfoIds().split(","))
                    .filter(s -> StringUtils.isNotBlank(s)).map(s -> Long.valueOf(s))
                    .collect(Collectors.toList());
            List<SeatInfo> seatInfos = seatInfoService.getBaseMapper().selectBatchIds(ids);
            if (CollectionUtil.isEmpty(seatInfos)){
                throw new BusinessException("该分组没有对应的座椅");
            }
            //获取所有设备
            Set<Long> collect = seatInfos.stream().map(s -> s.getId()).collect(Collectors.toSet());
            List<DeviceExtendInfo> deviceExtendInfos = seatInfoService.getRefDevice(collect);
            if (CollectionUtil.isEmpty(deviceExtendInfos)){
                throw new BusinessException("该分组没有对应的设备");
            }
            baseMapper.update(null, Wrappers.<SeatInfoGroup>lambdaUpdate().set(SeatInfoGroup::getLastControlConfig, JSONUtil.toJsonStr(seatControllerDto.getControlConfig())).eq(SeatInfoGroup::getId, groupId));

            deviceExtendInfos.forEach(deviceExtendInfo ->{
                SeatControllerDto copy = BeanUtil.copyProperties(seatControllerDto, SeatControllerDto.class);
                copy.setDeviceCode(deviceExtendInfo.getDeviceId());
                try {
                    //下发指令
                    SeatInfoDeviceUtils.controlToDevice(copy);
                    LogHelper.setLogInfo("", "id:"+groupId, null, null,"控制座椅分组，分组名称："+seatInfoGroup.getName());
                } catch (Exception e) {
                    log.info("下行报错："+copy.getDeviceCode());
                }
            });
        }else {
            throw new BusinessException("该分组没有对应的设备");
        }
    }

    /**
     * 设置基本属性
     * @param seatInfoGroup
     */
    private void setBase(SeatInfoGroup seatInfoGroup) {
        Long userId = null;
        if(null != baseUserContextProducer.getCurrent()){
            userId = baseUserContextProducer.getCurrent().getId();
        }
        seatInfoGroup.setModifyTime(new Date());
        seatInfoGroup.setModifyId(userId);
        //没有id就是新增,有就是编辑
        if (null == seatInfoGroup.getId()){
            seatInfoGroup.setCreatorId(userId);
            seatInfoGroup.setCreateTime(new Date());
        }
    }

    /**
     * 验证重复
     */
    private void checkExist(SeatInfoGroupVo streetlightGroupVo) {
        QueryWrapper<SeatInfoGroup> queryWrapper = new QueryWrapper<>();
        //设置判断重复条件
        queryWrapper.eq("name_",streetlightGroupVo.getName());
        //编辑的时候存在id
        Optional.ofNullable(streetlightGroupVo.getId()).ifPresent(id -> queryWrapper.ne("id_",streetlightGroupVo.getId()));
        Integer integer = baseMapper.selectCount(queryWrapper);
        if (integer>0){
            throw new BusinessException("该分组已存在");
        }
    }

    /**
     * 分组数据转换
     * @param seatInfoGroupVo
     * @return
     */
    private static SeatInfoGroup getSeatInfoGroup(SeatInfoGroupVo seatInfoGroupVo) {
        //数据处理
        if (CollectionUtil.isNotEmpty(seatInfoGroupVo.getSelectSeatList())){
            List<Long> ids = seatInfoGroupVo.getSelectSeatList().stream().map(s -> s.getId())
                    .collect(Collectors.toList());
            String idStr = StringUtils.join(ids, ",");
            //方便切割
            idStr = "," + idStr +",";
            seatInfoGroupVo.setSeatInfoIds(idStr);
        }else {
            seatInfoGroupVo.setSeatInfoIds(null);
        }
        SeatInfoGroup seatInfoGroup = BeanUtil.toBean(seatInfoGroupVo, SeatInfoGroup.class);
        return seatInfoGroup;
    }
}

