package com.smartPark.business.seat.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONObject;
import com.smartPark.business.seat.entity.SeatInfoPlanParam;
import com.smartPark.business.seat.entity.vo.SeatInfoPlanVo;
import com.smartPark.common.cronHelp.JobGroupConstant;
import com.smartPark.common.job.JobEntity;
import com.smartPark.common.job.constant.CronConstant;
import com.smartPark.common.job.entity.JobDTO;
import com.smartPark.common.job.util.JobUtils;

import java.util.List;

/**
 * @Description 路灯定时任务
 * <AUTHOR> yuan<PERSON>
 * @Date 2023/4/11 10:05
 */
public class SeatInfoJobUtils {
    /**
     * 创建任务
     */
    public static void addJob(SeatInfoPlanVo seatInfoPlanVo){
        //时间分隔
        List<SeatInfoPlanParam> paramList = seatInfoPlanVo.getParamList();
        if (CollectionUtil.isNotEmpty(paramList)){
            for (int i = 0; i < paramList.size(); i++) {
                SeatInfoPlanParam p = paramList.get(i);
                String startTime = p.getStartTime();
                //组装任务
                JobDTO jobDTO = new JobDTO();
                jobDTO.setStartTime(ListUtil.of(startTime));
                //回调
                jobDTO.setCallBack("/openapi/task/callback/taskSeatInfoControl");
                //季度才传。其他默认每天
                Integer timeType = seatInfoPlanVo.getTimeType();
                if (CronConstant.TIME_TYPE_QUARTER.equals(timeType)){
                    jobDTO.setTimeType(timeType);
                    jobDTO.setTimeValues(seatInfoPlanVo.getTimeValues());
                }
                //参数处理
                JSONObject jsonObject = new JSONObject();
                jsonObject.putOpt("planId",seatInfoPlanVo.getId());
                jsonObject.putOpt("controlType",p.getControlType());
                jsonObject.putOpt("timeType",timeType);
                jobDTO.setModelGroupNameEnum(JobGroupConstant.ModelGroupNameEnum.SEAT_CONTROLLER_PLAN);
                jobDTO.setParam(jsonObject);
                JobEntity jobEntity = JobUtils.initTaskJobEntity(jobDTO);
                //重新组装名称
                jobEntity.setJobGroup(jobEntity.getJobGroup()+"_"+seatInfoPlanVo.getId());
                jobEntity.setJobName(jobEntity.getJobName()+"_"+seatInfoPlanVo.getPlanNo()+"_"+startTime+"_"+i);
                JobUtils.requestTaskApi(JobGroupConstant.JobInterfaceEnum.ADD_JOB,jobEntity,null);
            }
        }
    }

    /**
     * 删除任务
     */
    public static void delJob(Long planId){
        //组装任务
        JobDTO jobDTO = new JobDTO();
        jobDTO.setModelGroupNameEnum(JobGroupConstant.ModelGroupNameEnum.SEAT_CONTROLLER_PLAN);
        JobEntity jobEntity = JobUtils.initTaskJobEntity(jobDTO);
        //重新组装名称
        jobEntity.setJobGroup(jobEntity.getJobGroup()+"_"+planId);
        JobUtils.requestTaskApi(JobGroupConstant.JobInterfaceEnum.DEL_JOB,jobEntity,null);
    }
}
