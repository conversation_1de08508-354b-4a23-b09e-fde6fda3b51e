package com.smartPark.business.seat.controller;

import com.smartPark.business.seat.entity.vo.SeatAlarmCountVo;
import com.smartPark.business.seat.entity.vo.SeatInfoCountQueryDTO;
import com.smartPark.business.seat.entity.vo.SeatInfoCountVo;
import com.smartPark.business.seat.service.SeatInfoCountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 智慧座椅/座椅概况
 * @Description 智慧座椅/座椅概况
 * <AUTHOR> yuanfeng
 * @Date 2023/4/8 13:31
 */
@RestController
@RequestMapping("seatInfoCount")
@Api(tags = "智慧座椅/座椅概况")
public class SeatInfoCountController {

    @Autowired
    private SeatInfoCountService seatInfoCountService;

    /**
     * @Description: 路灯数量统计
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @GetMapping("num")
    @ApiOperation("路灯数量统计")
    public RestMessage countNum() {
        SeatInfoCountVo streetlightCountVo = seatInfoCountService.countNum();
        return RestBuilders.successBuilder().data(streetlightCountVo).build();
    }

    /**
     * @Description: 近两周能耗统计
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PostMapping("count2Week")
    @ApiOperation("近两周能耗统计")
    public RestMessage count2Week(@RequestBody SeatInfoCountQueryDTO seatInfoCountQueryDTO) {
        List<SeatInfoCountVo> streetlightCountVoList = seatInfoCountService.count2Week(seatInfoCountQueryDTO);
        return RestBuilders.successBuilder().data(streetlightCountVoList).build();
    }

    /**
     * @Description: 统计告警数据
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @PostMapping("countAlarm")
    @ApiOperation("统计告警数据")
    public RestMessage countAlarm(@RequestBody SeatInfoCountQueryDTO seatInfoCountQueryDTO) {
        List<SeatAlarmCountVo> seatInfoCountVoList = seatInfoCountService.countAlarm(seatInfoCountQueryDTO);
        Map<String, List<SeatAlarmCountVo>> collect = seatInfoCountVoList.stream().collect(Collectors.groupingBy(SeatAlarmCountVo::getTime));
        return RestBuilders.successBuilder().data(collect).build();
    }
//
//    /**
//     * @Description: 近两周能耗统计跟上个月对比
//     * <AUTHOR> yuanfeng
//     * @date 2020/11/04 11:42
//     */
//    @PostMapping("count2WeekCompare")
//    @ApiOperation("近两周能耗统计跟上个月对比")
//    public RestMessage count2WeekCompare(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
//        List<StreetlightCountVo> streetlightCountVoList = streetlightCountService.count2WeekCompare(streetlightCountQueryDTO);
//        return RestBuilders.successBuilder().data(streetlightCountVoList).build();
//    }
//
//    /**
//     * 路灯报警数量统计
//     * @param streetlightCountQueryDTO
//     * @return
//     */
//    @PostMapping("countAlarmNum")
//    @ApiOperation("路灯报警数量统计")
//    public RestMessage countAlarmNum(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
//        List<StreetlightCountVo> streetlightCountVoList = streetlightCountService.countAlarmNum(streetlightCountQueryDTO);
//        return RestBuilders.successBuilder().data(streetlightCountVoList).build();
//    }
}
