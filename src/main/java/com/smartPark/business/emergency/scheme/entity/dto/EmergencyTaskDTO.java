package com.smartPark.business.emergency.scheme.entity.dto;

import com.smartPark.business.emergency.scheme.entity.EmergencyTask;
import com.smartPark.business.emergency.scheme.entity.EmergencyTaskExecutor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * EmergencyTask实体类DTO
 *
 * <AUTHOR>
 * @date 2023/09/19
 */

@Data
@Accessors(chain = true)
public class EmergencyTaskDTO extends EmergencyTask {
    /**
     * 任务执行人list
     */
    private List<EmergencyTaskExecutor> executorList;
}
