package com.smartPark.business.emergency.scheme.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.scheme.entity.EmergencyTaskExecutor;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * EmergencyTaskExecutor表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/09/19
 */
public interface EmergencyTaskExecutorMapper extends BaseMapper<EmergencyTaskExecutor> {


    /**
     * 查询分页
     *
     * @param page        分页参数对象
     * @param safeEmergencyTaskExecutor 过滤参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyTaskExecutor> selectPage(Page page, @Param("safeEmergencyTaskExecutor") EmergencyTaskExecutor safeEmergencyTaskExecutor);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyTaskExecutor getOneById(@Param("id") Serializable id);

    /**
     * 根据任务id查询执行人列表
     * @param taskId 任务id
     * @return 执行人列表
     */
    List<EmergencyTaskExecutor> selectByTaskId(@Param("taskId") Serializable taskId);
}

