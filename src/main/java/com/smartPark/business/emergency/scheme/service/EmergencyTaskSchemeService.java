package com.smartPark.business.emergency.scheme.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.scheme.entity.EmergencyTaskScheme;
import com.smartPark.business.emergency.scheme.entity.dto.EmergencyTaskSchemeDTO;
import com.smartPark.business.emergency.scheme.entity.vo.EmergencyTaskSchemeVo;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencyTaskScheme表服务接口
 *
 * <AUTHOR>
 * @date 2023/09/19
 */
public interface EmergencyTaskSchemeService extends IService<EmergencyTaskScheme> {

    /**
     * 新增
     *
     * @param safeEmergencyTaskScheme 实体对象
     * @return 操作结果
     */
    boolean saveOne(EmergencyTaskScheme safeEmergencyTaskScheme);

    /**
     * 修改单条
     *
     * @param safeEmergencyTaskScheme 实体对象
     * @return 修改结果
     */
    boolean updateOne(EmergencyTaskScheme safeEmergencyTaskScheme);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param safeEmergencyTaskScheme 分页参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyTaskScheme> selectPage(Page page, EmergencyTaskScheme safeEmergencyTaskScheme);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyTaskScheme getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param safeEmergencyTaskScheme 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(EmergencyTaskScheme safeEmergencyTaskScheme, HttpServletRequest request, HttpServletResponse response);

    /**
     * 新增应急任务方案
     * @param taskSchemeVo 应急任务方案
     * @return 操作结果
     */
    EmergencyTaskSchemeVo addTaskScheme(EmergencyTaskSchemeVo taskSchemeVo);

    /**
     * 应急任务方案详情
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyTaskSchemeVo taskSchemeDetail(Serializable id);

    /**
     * 修改应急任务方案
     * @param taskSchemeVo 应急任务方案
     * @return 操作结果
     */
    EmergencyTaskSchemeVo updateTaskScheme(EmergencyTaskSchemeVo taskSchemeVo);

    /**
     * 批量删除
     * @param idList 主键列表
     * @return 操作结果
     */
    RestMessage batchDelete(List<Long> idList);

    /**
     * 分页查询所有数据
     * @param page 查询分页对象
     * @param customQueryParams 查询参数
     * @return 所有数据
     */
    IPage<EmergencyTaskSchemeDTO> selectSchemePage(Page page, EmergencyTaskSchemeVo customQueryParams);

    /**
     * 方案执行应急队伍与人员
     * @return 应急队伍与人员
     */
    RestMessage taskExecutorTeamAndPerson();
}

