package com.smartPark.business.emergency.event.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.security.context.BaseUserContextProducer;

import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;


import com.smartPark.business.emergency.event.mapper.EmergencyEventExecutorMapper;
import com.smartPark.business.emergency.event.entity.EmergencyEventExecutor;
import com.smartPark.business.emergency.event.entity.dto.EmergencyEventExecutorDTO;
import com.smartPark.business.emergency.event.service.EmergencyEventExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * EmergencyEventExecutor表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/21
 */
@Slf4j
@Service("safeEmergencyEventExecutorService")
public class EmergencyEventExecutorServiceImpl extends ServiceImpl
        <EmergencyEventExecutorMapper, EmergencyEventExecutor> implements EmergencyEventExecutorService {
    @Resource
    private CommonService commonService;

    @Override
    public boolean removeById(Serializable id) {
    return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(EmergencyEventExecutor::getId, idList).update();
        return true;
    }


    @Override
    public boolean saveOne(EmergencyEventExecutor safeEmergencyEventExecutor) {
        commonService.setCreateAndModifyInfo(safeEmergencyEventExecutor);

        validParamRequired(safeEmergencyEventExecutor);
        validRepeat(safeEmergencyEventExecutor);
        validParamFormat(safeEmergencyEventExecutor);
        return save(safeEmergencyEventExecutor);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(EmergencyEventExecutor safeEmergencyEventExecutor) {
        Assert.notNull(safeEmergencyEventExecutor.getId(), "id不能为空");
        commonService.setModifyInfo(safeEmergencyEventExecutor);

        validRepeat(safeEmergencyEventExecutor);
        validParamFormat(safeEmergencyEventExecutor);
        return updateById(safeEmergencyEventExecutor);
    }

    @Override
    public IPage<EmergencyEventExecutor> selectPage(Page page, EmergencyEventExecutor safeEmergencyEventExecutor) {
        return baseMapper.selectPage(page, safeEmergencyEventExecutor);
    }

    @Override
    public void export(EmergencyEventExecutor safeEmergencyEventExecutor, HttpServletRequest request, HttpServletResponse
            response) {

    }

    /**
     * 根据 人员id和 队伍id 查询事件
     * @param personIds
     * @param teamId
     * @return
     */
    @Override
    public List<Long> findEventIdByPersonIdAndTeamId(List<Integer> personIds, Long teamId) {
        List<Long> eventIds = baseMapper.findEventIdByPersonIdAndTeamId(personIds,teamId);
        return eventIds;
    }

    @Override
    public EmergencyEventExecutor getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(EmergencyEventExecutor safeEmergencyEventExecutor) {

        /* List<EmergencyEventExecutor> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(EmergencyEventExecutor::getDeviceCode, safeEmergencyEventExecutor.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(safeEmergencyEventExecutor.getId()) || !safeEmergencyEventExecutor.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("名称重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(EmergencyEventExecutor safeEmergencyEventExecutor) {
        //Assert.notNull(safeEmergencyEventExecutor, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(safeEmergencyEventExecutor.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(EmergencyEventExecutor safeEmergencyEventExecutor) {
        //Assert.isTrue(safeEmergencyEventExecutor.getName() == null || safeEmergencyEventExecutor.getName().length() <= 50,
        //        "名称超长");
    }
}

