package com.smartPark.business.emergency.event.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应急事件预案任务执行人
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_emergency_event_executor")
public class EmergencyEventExecutor extends BaseEntity<EmergencyEventExecutor> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 执行人id
     */
    @TableField("executor_id_")
    private Long executorId;

    /**
     * 执行人名称,冗余
     */
    @TableField("executor_name_")
    private String executorName;

    /**
     * 应急执行人类型,1应急人,2应急队伍
     */
    @TableField("emergency_people_type_")
    private Integer emergencyPeopleType;

    /**
     * 应急队伍id,应急队伍时非空
     */
    @TableField("executor_team_id_")
    private Long executorTeamId;

    /**
     * 应急事件任务id
     */
    @TableField("event_task_id_")
    private Long eventTaskId;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除,0否,非0删除
     */
    @TableField("deleted_")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
