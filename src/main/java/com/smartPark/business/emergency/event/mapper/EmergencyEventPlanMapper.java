package com.smartPark.business.emergency.event.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.event.entity.EmergencyEventPlan;
import com.smartPark.business.emergency.event.entity.dto.EmergencyEventPlanDTO;
import com.smartPark.business.emergency.event.entity.vo.EmergencyEventPlanVo;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * EmergencyEventPlan表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
public interface EmergencyEventPlanMapper extends BaseMapper<EmergencyEventPlan> {


    /**
     * 查询分页
     *
     * @param page                   分页参数对象
     * @param safeEmergencyEventPlan 过滤参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyEventPlan> selectPage(Page page, @Param("safeEmergencyEventPlan") EmergencyEventPlan safeEmergencyEventPlan);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyEventPlan getOneById(@Param("id") Serializable id);

    /**
     * 根据条件查询事件预案dtoList
     *
     * @param page        分页对象
     * @param eventPlanVo 过滤参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyEventPlanDTO> selectEventPlanDtoPage(Page page, @Param("eventPlanVo") EmergencyEventPlanVo eventPlanVo);

    /**
     * 根据id查询事件预案详情
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyEventPlanDTO getEventPlanDetail(@Param("id") Long id);
}

