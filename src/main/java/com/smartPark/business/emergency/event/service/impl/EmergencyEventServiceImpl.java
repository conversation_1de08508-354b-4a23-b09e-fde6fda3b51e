package com.smartPark.business.emergency.event.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.emergency.event.entity.*;
import com.smartPark.business.emergency.event.entity.dto.EmergencyEventDTO;
import com.smartPark.business.emergency.event.entity.dto.EmergencyEventTaskDTO;
import com.smartPark.business.emergency.event.entity.vo.EmergencyEventVo;
import com.smartPark.business.emergency.event.excel.handler.EmergencyEventListExportHandler;
import com.smartPark.business.emergency.event.mapper.EmergencyEventMapper;
import com.smartPark.business.emergency.event.mapper.EmergencyEventTaskMapper;
import com.smartPark.business.emergency.event.service.*;
import com.smartPark.business.emergency.event.util.EventTaskTreeUtil;
import com.smartPark.business.emergency.event.util.MoreParentTreeNode;
import com.smartPark.business.emergency.event.util.ProcessGraph;
import com.smartPark.business.emergency.plan.entity.EmergencyPlan;
import com.smartPark.business.emergency.plan.entity.EmergencyPlanExecutor;
import com.smartPark.business.emergency.plan.entity.dto.PlanEventTypeDTO;
import com.smartPark.business.emergency.plan.entity.vo.EmergencyPlanTaskVo;
import com.smartPark.business.emergency.plan.entity.vo.EmergencyPlanVo;
import com.smartPark.business.emergency.plan.mapper.EmergencyPlanMapper;
import com.smartPark.business.emergency.plan.service.EmergencyPlanService;
import com.smartPark.business.emergency.team.entity.EmergencyPersonRefTeam;
import com.smartPark.business.emergency.team.entity.EmergencyTeam;
import com.smartPark.business.emergency.team.mapper.EmergencyPersonRefTeamMapper;
import com.smartPark.business.emergency.team.mapper.EmergencyTeamMapper;
import com.smartPark.common.alarm.entity.Alarm;
import com.smartPark.common.alarm.mapper.AlarmMapper;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.utils.BusinessSerialNoUtil;
import com.smartPark.common.utils.PrivilegeUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * EmergencyEvent表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Slf4j
@Service
public class EmergencyEventServiceImpl extends ServiceImpl<EmergencyEventMapper, EmergencyEvent>
        implements EmergencyEventService {
    @Resource
    private CommonService commonService;

    @Resource
    private AlarmMapper alarmMapper;

    @Resource
    private EmergencyPlanService planService;

    @Resource
    private EmergencyEventPlanService eventPlanService;

    @Resource
    private EmergencyEventTaskService eventPlanTaskService;

    @Resource
    private EmergencyEventExecutorService eventTaskExecutorService;

    @Resource
    private EmergencyEventOperateService eventOperateService;

    @Resource
    private BaseUserContextProducer baseUserContextProducer;
    @Resource
    private ExcelService excelService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private EmergencyPlanMapper planMapper;
    @Resource
    private EmergencyEventTaskMapper taskMapper;
    @Resource
    private EmergencyPersonRefTeamMapper personRefTeamMapper;
    @Resource
    private EmergencyTeamMapper teamMapper;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(EmergencyEvent::getId, idList)
                .update();
        return true;
    }

    @Override
    public boolean saveOne(EmergencyEvent safeEmergencyEvent) {
        commonService.setCreateAndModifyInfo(safeEmergencyEvent);

        validParamRequired(safeEmergencyEvent);
        validRepeat(safeEmergencyEvent);
        validParamFormat(safeEmergencyEvent);
        return save(safeEmergencyEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(EmergencyEvent safeEmergencyEvent) {
        Assert.notNull(safeEmergencyEvent.getId(), "id不能为空");
        commonService.setModifyInfo(safeEmergencyEvent);

        validRepeat(safeEmergencyEvent);
        validParamFormat(safeEmergencyEvent);
        return updateById(safeEmergencyEvent);
    }

    @Override
    public IPage<EmergencyEvent> selectPage(Page page, EmergencyEvent safeEmergencyEvent) {
        return baseMapper.selectPage(page, safeEmergencyEvent);
    }

    @Override
    public void export(EmergencyEvent safeEmergencyEvent, HttpServletRequest request, HttpServletResponse response) {

    }

    @Override
    public RestMessage alarmTriggerEventPlan(List<Long> alarmIdList) {
        Assert.notNull(alarmIdList, "参数为空");
        Assert.isTrue(CollectionUtil.isNotEmpty(alarmIdList), "参数为空");
        List<Alarm> alarmList = alarmMapper.selectBatchIds(alarmIdList);
        if (CollectionUtil.isNotEmpty(alarmIdList)) {
            alarmList.forEach(alarm -> {
                // 1.根据告警信息获取告警事件编码、等级
                String alarmType = alarm.getAlarmType();
                Integer alarmLevel = alarm.getLevel();
                String alarmCode = alarm.getCode();
                // 2.根据告警事件编码、等级查询应急预案
                List<EmergencyPlan> emergencyPlanList = queryHighLevelEmergencyPlan(alarm);
                // 3.根据应急预案获取预案任务、执行人等
                if (CollectionUtil.isNotEmpty(emergencyPlanList)) {
                    List<EmergencyPlanVo> planVoList = emergencyPlanList.stream()
                            .map(plan -> planService.planDetail(plan.getId())).collect(Collectors.toList());
                    addEvent(planVoList, alarm);
                }
            });
        }
        return RestBuilders.successBuilder().build();
    }

    @Override
    public EmergencyEventVo eventDetail(Serializable id) {
        // 查询事件
        EmergencyEvent event = getOneById(id);
        Assert.notNull(event, "事件不存在");

        // 查询事件预案
        QueryWrapper<EmergencyEventPlan> eventPlanQw = new QueryWrapper<>();
        eventPlanQw.eq("event_id_", event.getId());
        List<EmergencyEventPlan> eventPlanList = eventPlanService.list(eventPlanQw);

        EmergencyEventVo eventVo = new EmergencyEventVo();
        eventVo.setEvent(event);
        eventVo.setEventPlanList(eventPlanList);

        return eventVo;
    }

    @Override
    public boolean updateEventStatus(EmergencyEvent safeEmergencyEvent) {
        Assert.notNull(safeEmergencyEvent.getId(), "id不能为空");
        Assert.notNull(safeEmergencyEvent.getDisposeStatus(), "处理状态不能为空");

        // 查询事件
        EmergencyEvent dbEvent = getOneById(safeEmergencyEvent.getId());
        Assert.notNull(dbEvent, "事件不存在");

        Integer disposeStatus = safeEmergencyEvent.getDisposeStatus();

        // 处理状态,1处理中,2暂停中,3已完成,4已终止
        Integer dbEventDisposeStatus = dbEvent.getDisposeStatus();
        // 处理中的可以暂停、终止
        if (disposeStatus == 4) {
            // 终止
            Assert.isTrue(dbEventDisposeStatus == 1, "非处理中的事件不能终止");
        }

        // 暂停中的可以恢复
        if (disposeStatus == 1 || disposeStatus == 2) {
            // 恢复
            Assert.isTrue(!(dbEventDisposeStatus == 3 || dbEventDisposeStatus == 4), "已完成、终止的事件不能暂停/恢复");
        }

        EmergencyEvent upEvent = new EmergencyEvent();
        upEvent.setId(safeEmergencyEvent.getId());
        upEvent.setDisposeStatus(disposeStatus);
        commonService.setModifyInfo(upEvent);

        return updateById(upEvent);

    }

    @Override
    public boolean eventBatchDelete(List<Long> idList) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        String eventCodeStr = "";

        Assert.notNull(idList, "参数为空");
        Assert.isTrue(CollectionUtil.isNotEmpty(idList), "参数为空");
        // 事件详情
        QueryWrapper<EmergencyEvent> eventQw = new QueryWrapper<>();
        eventQw.in("id_", idList);
        List<EmergencyEvent> eventList = list(eventQw);
        if (CollectionUtil.isNotEmpty(eventList)) {
            List<Long> eventIds = eventList.stream().map(event -> event.getId()).collect(Collectors.toList());
            // 删除事件预案
            QueryWrapper<EmergencyEventPlan> eventPlanQw = new QueryWrapper<>();
            eventPlanQw.in("event_id_", eventIds);
            eventPlanService.remove(eventPlanQw);
            // 删除事件任务
            QueryWrapper<EmergencyEventTask> eventTaskQw = new QueryWrapper<>();
            eventTaskQw.in("event_id_", eventIds);
            List<EmergencyEventTask> eventExecutorList = eventPlanTaskService.list(eventTaskQw);
            if (CollectionUtil.isNotEmpty(eventExecutorList)) {
                List<Long> eventTaskIds = eventExecutorList.stream().map(task -> task.getId())
                        .collect(Collectors.toList());
                // 删除事件执行人
                QueryWrapper<EmergencyEventExecutor> eventExecutorQw = new QueryWrapper<>();
                eventExecutorQw.in("event_task_id_", eventTaskIds);
                eventTaskExecutorService.remove(eventExecutorQw);
                // 删除处理记录
                QueryWrapper<EmergencyEventOperate> operateQw = new QueryWrapper<>();
                operateQw.in("event_task_id_", eventTaskIds);
                eventOperateService.remove(operateQw);
            }
            eventPlanTaskService.remove(eventTaskQw);

            // eventList的EventCode以，隔开，组织成字符串
            eventCodeStr = eventList.stream().map(event -> event.getEventCode()).collect(Collectors.joining(","));
        }
        // 删除事件
        removeByIds(idList);

        // 日志
        sj.add("删除应急事件,事件编码：[" + eventCodeStr + "]");
        coreParamSj.add("删除应急事件,事件编码：[" + eventCodeStr + "]");
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return true;
    }

    @Override
    public IPage<EmergencyEventDTO> selectEventDtoPage(Page page, EmergencyEventVo eventVo, String privilegeCode) {
        // 获取当前用户信息,查询菜单权限管理员(等张磊公共接口)
        // String privilegeCode = "emergencyResponse:emergencyMonitoring:event";
        boolean isIsolation = PrivilegeUtil.isDataIsolation(privilegeCode);
        IPage<EmergencyEventDTO> eventDtoPage = new Page<>();
        if (!isIsolation) {
            // 应急队伍查询
            if (eventVo.getTeamId() != null) {
                // 查询对应的应急人员
                List<Integer> personIds = personRefTeamMapper
                        .selectList(new LambdaQueryWrapper<EmergencyPersonRefTeam>()
                                .select(EmergencyPersonRefTeam::getPersonId)
                                .eq(EmergencyPersonRefTeam::getTeamId, eventVo.getTeamId()))
                        .stream().map(EmergencyPersonRefTeam::getPersonId).collect(Collectors.toList());
                eventVo.setExecutorIds(personIds);
                // //根据 人员id和 队伍id 查询事件
                // List<Long> eventIds =
                // eventTaskExecutorService.findEventIdByPersonIdAndTeamId(personIds,
                // eventVo.getTeamId());
                // eventVo.setEventIds(eventIds);
            }
            eventDtoPage = baseMapper.selectEventDtoPage(page, eventVo, !isIsolation);

            // 查询应急队伍
            eventDtoPage.getRecords().forEach(e -> {
                // 区域范围
                if (StringUtils.isNotBlank(e.getAreaPath())) {
                    e.setAreaPath(e.getAreaPath().replace("@", "/"));
                }
                if (eventVo.getIsNeedTeam() != null && eventVo.getIsNeedTeam()) {
                    // 查询执行任务
                    List<EmergencyEventTask> eventTasks = taskMapper
                            .selectList(new LambdaQueryWrapper<EmergencyEventTask>()
                                    .eq(EmergencyEventTask::getEventId, e.getId()));
                    if (CollectionUtil.isNotEmpty(eventTasks)) {
                        List<Long> taskIds = eventTasks.stream().map(EmergencyEventTask::getId)
                                .collect(Collectors.toList());
                        // 任务执行人
                        List<EmergencyEventExecutor> executorList = eventTaskExecutorService
                                .list(new LambdaQueryWrapper<EmergencyEventExecutor>()
                                        .in(EmergencyEventExecutor::getEventTaskId, taskIds));
                        // 收集执行队伍id
                        List<Long> teamIds = executorList.stream().map(EmergencyEventExecutor::getExecutorTeamId)
                                .filter(Objects::nonNull).collect(Collectors.toList());
                        // 收集执行人id
                        List<Long> executorIds = executorList.stream().map(EmergencyEventExecutor::getExecutorId)
                                .filter(Objects::nonNull).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(executorIds)) {
                            // 查询执行队伍
                            List<EmergencyPersonRefTeam> refTeams = personRefTeamMapper
                                    .selectList(new LambdaQueryWrapper<EmergencyPersonRefTeam>()
                                            .in(EmergencyPersonRefTeam::getPersonId, executorIds));
                            // 收集执行队伍id
                            List<Long> refTeamIds = refTeams.stream().filter(re -> null != re.getTeamId())
                                    .map(re -> Long.valueOf(re.getTeamId())).collect(Collectors.toList());
                            teamIds.addAll(refTeamIds);
                        }
                        // 查询队伍
                        if (CollectionUtil.isNotEmpty(teamIds)) {
                            List<EmergencyTeam> teams = teamMapper.selectList(new LambdaQueryWrapper<EmergencyTeam>()
                                    .in(EmergencyTeam::getId, teamIds));
                            // 队伍名称去重，逗号拼接
                            List<String> teamNames = teams.stream().map(EmergencyTeam::getTeamName).distinct()
                                    .collect(Collectors.toList());
                            e.setTeamNames(String.join(",", teamNames));
                        }
                    }
                }
            });
        }

        return eventDtoPage;
    }

    @Override
    public Long selectEventDtoPageExport(EmergencyEventVo eventVo, HttpServletRequest request,
                                         HttpServletResponse response) {
        Long userId = baseUserContextProducer.getCurrent().getId();
        String privilegeCode = request.getHeader("Privilege-Code");
        eventVo.setPrivilegeCode(privilegeCode);
        DataExportParam<EmergencyEventVo> dataExportParam = new DataExportParam<>();
        dataExportParam.setParam(eventVo);
        dataExportParam.setExportFileName("应急事件列表导出");
        dataExportParam.setTenantCode("safe");
        dataExportParam.setBusinessCode("emergencyEventList");
        dataExportParam.setCreateUserCode(userId.toString());
        return excelService.doExport(dataExportParam, EmergencyEventListExportHandler.class);
    }

    @Override
    public RestMessage addSimulationExerciseEvent(EmergencyEventVo eventVo) {
        Assert.notNull(eventVo, "参数为空");
        Assert.notNull(eventVo.getEvent(), "事件为空");
        EmergencyEvent emergencyEvent = eventVo.getEvent();
        // 事件来源,1物联监测,2人为上报,3模拟演练
        Integer eventSource = emergencyEvent.getEventSource();
        Assert.isTrue(eventSource == 3, "事件类型不是模拟演练");
        // 事件类型
        String eventType = emergencyEvent.getEventType();
        Assert.isTrue(StringUtils.isNotBlank(eventType), "事件类型不能为空");
        // 事件等级
        Integer eventLevel = emergencyEvent.getEventLevel();
        // Assert.notNull(eventLevel ,"事件等级不能为空");
        // 根据事件类型、等级查询预案
        // List<EmergencyPlan> emergencyPlanList = planMapper.queryPlanListBy(eventType,
        // eventLevel);
        // 与原逻辑一致
        Alarm alarm = new Alarm();
        alarm.setAlarmType(eventType);
        alarm.setLevel(eventLevel);
        List<EmergencyPlan> emergencyPlanList = queryHighLevelEmergencyPlan(alarm);
        Assert.isTrue(CollectionUtil.isNotEmpty(emergencyPlanList), "没有相应的预案");
        List<EmergencyEventPlan> eventPlanList = eventVo.getEventPlanList();
        if (CollectionUtil.isNotEmpty(eventPlanList)) {
            // emergencyPlanList过滤，只保留eventPlanList中的预案
            emergencyPlanList = emergencyPlanList.stream().filter(
                            plan -> eventPlanList.stream().anyMatch(eventPlan -> eventPlan.getId().equals(plan.getId())))
                    .collect(Collectors.toList());
        }

        AtomicReference<List<EmergencyPlanVo>> planVoList = new AtomicReference<>(new ArrayList<>());
        List<EmergencyPlan> finalEmergencyPlanList = emergencyPlanList;
        if (CollectionUtil.isNotEmpty(finalEmergencyPlanList)) {
            finalEmergencyPlanList.forEach(emergencyPlan -> {
                planVoList.set(finalEmergencyPlanList.stream().map(plan -> planService.planDetail(plan.getId()))
                        .collect(Collectors.toList()));
            });
        }

        // 所有预案提出来只创建一个事件
        addEvent(planVoList.get(), emergencyEvent);

        return RestBuilders.successBuilder().build();
    }

    /**
     * 新增事件
     *
     * @param planVoList     预案list
     * @param emergencyEvent 事件
     */
    private void addEvent(List<EmergencyPlanVo> planVoList, EmergencyEvent emergencyEvent) {
        // 预案总数
        Integer planNum = planVoList.size();
        // 预案任务总数
        Integer planTaskNum = planVoList.stream().filter(plan -> CollectionUtil.isNotEmpty(plan.getPlanTaskList()))
                .map(plan -> plan.getPlanTaskList().size()).reduce(0, Integer::sum);

        EmergencyEvent event = new EmergencyEvent();
        event.setEventSource(emergencyEvent.getEventSource());
        event.setTaskEnd(0);
        event.setTaskTotal(planTaskNum);
        event.setExecuteEnd(0);
        event.setExecuteTotal(planNum);
        event.setPlanStartTime(DateUtil.date());
        // 预计完成时间计算
        // 1处理中,2,暂停中,3已完成,4已终止
        event.setDisposeStatus(1);
        // 模拟演练事件核心属性
        String eventCode = BusinessSerialNoUtil.genSeqCode(redisUtil, "MNYL", "", 2);
        event.setEventCode(eventCode);
        event.setEventType(emergencyEvent.getEventType());
        event.setEventLevel(emergencyEvent.getEventLevel());
        event.setElementInfo(emergencyEvent.getElementInfo());
        event.setEnvironmentData(emergencyEvent.getEnvironmentData());

        commonService.setCreateAndModifyInfo(event);
        // 保存事件
        save(event);

        // 保存事件预案
        planVoList.forEach(planVo -> {
            addEventPlanTask(planVo, null, event);
        });

        // 计算预案计划完成时间
        Date eventPlanEndTime = calPlanEndTime(event);
        event.setPlanEndTime(eventPlanEndTime);
        commonService.setModifyInfo(event);

        // 更新事件
        updateById(event);
    }

    /**
     * 新增告警事件
     *
     * @param emergencyPlanList 预案list
     * @param alarm             告警信息
     */
    private void addEvent(List<EmergencyPlanVo> emergencyPlanList, Alarm alarm) {
        // 预案总数
        Integer planNum = emergencyPlanList.size();
        // 预案任务总数
        Integer planTaskNum = emergencyPlanList.stream()
                .filter(plan -> CollectionUtil.isNotEmpty(plan.getPlanTaskList()))
                .map(plan -> plan.getPlanTaskList().size()).reduce(0, Integer::sum);

        EmergencyEvent event = new EmergencyEvent();
        event.setEventCode(alarm.getCode());
        event.setEventType(alarm.getAlarmType());
        event.setEventSource(1);
        event.setEventLevel(alarm.getLevel());
        event.setTaskEnd(0);
        event.setTaskTotal(planTaskNum);
        event.setExecuteEnd(0);
        event.setExecuteTotal(planNum);
        // event.setActualEndTime(DateUtil.date());
        event.setPlanStartTime(DateUtil.date());
        // 预计完成时间计算
        // event.setPlanEndTime(planEndTime);
        // 1处理中,2,暂停中,3已完成,4已终止
        event.setDisposeStatus(1);
        commonService.setCreateAndModifyInfo(event);
        // 保存事件
        save(event);

        // 保存事件预案
        emergencyPlanList.forEach(planVo -> {
            addEventPlanTask(planVo, alarm, event);
        });

        // 计算预案计划完成时间
        Date eventPlanEndTime = calPlanEndTime(event);
        event.setPlanEndTime(eventPlanEndTime);
        commonService.setModifyInfo(event);

        // 更新事件
        updateById(event);

    }

    /**
     * 保存事件预案
     *
     * @param planVo 预案vo
     * @param alarm  告警信息
     * @param event  事件
     */
    private void addEventPlanTask(EmergencyPlanVo planVo, Alarm alarm, EmergencyEvent event) {
        EmergencyPlan plan = planVo.getPlan();
        List<PlanEventTypeDTO> planEventTypeList = planVo.getPlanEventTypeList();
        List<EmergencyPlanTaskVo> planTaskList = planVo.getPlanTaskList();
        EmergencyEventPlan eventPlan = new EmergencyEventPlan();
        BeanUtil.copyProperties(plan, eventPlan, "id");
        eventPlan.setEventId(event.getId());
        commonService.setCreateAndModifyInfo(eventPlan);

        // 保存事件预案
        eventPlanService.save(eventPlan);

        // 事件预案任务转map,key未名称,value为任务
        Map<String, EmergencyPlanTaskVo> planTaskMap = planTaskList.stream()
                .collect(Collectors.toMap(EmergencyPlanTaskVo::getTaskName, planTask -> planTask));
        // 保存事件预案任务
        planTaskList.forEach(planTask -> {
            EmergencyEventTask eventPlanTask = new EmergencyEventTask();
            BeanUtil.copyProperties(planTask, eventPlanTask, "id");
            commonService.setCreateAndModifyInfo(eventPlanTask);
            eventPlanTask.setEventId(event.getId());
            eventPlanTask.setEventPlanId(eventPlan.getId());
            // 生成事件任务code
            String eventTaskNo = BusinessSerialNoUtil.genSeqCode(redisUtil, "SJRW", "", 4);
            eventPlanTask.setTaskNo(eventTaskNo);

            // 保存事件任务
            eventPlanTaskService.save(eventPlanTask);

            // 更新事件预案任务状态、前置任务信息
            updateTaskBeforeInfo(eventPlan.getId(), planTaskMap);

            // 保存事件任务执行人
            List<EmergencyPlanExecutor> eventPlanExecutorList = planTask.getPlanTaskExecutorList();
            if (CollectionUtil.isNotEmpty(eventPlanExecutorList)) {
                eventPlanExecutorList.forEach(planExecutor -> {
                    EmergencyEventExecutor eventTaskExecutor = new EmergencyEventExecutor();
                    BeanUtil.copyProperties(planExecutor, eventTaskExecutor, "id");
                    commonService.setCreateAndModifyInfo(eventTaskExecutor);
                    eventTaskExecutor.setEventTaskId(eventPlanTask.getId());
                    // 保存事件任务执行人
                    eventTaskExecutorService.save(eventTaskExecutor);
                });
            }

        });

    }

    /**
     * 更新事件预案任务状态、前置任务信息
     *
     * @param eventPlanId 事件预案id
     * @param taskVoMap   事件预案任务map
     */
    private void updateTaskBeforeInfo(Long eventPlanId, Map<String, EmergencyPlanTaskVo> taskVoMap) {
        // 查询事件预案任务
        QueryWrapper<EmergencyEventTask> taskQw = new QueryWrapper<>();
        taskQw.eq("event_plan_id_", eventPlanId);
        List<EmergencyEventTask> newTaskList = eventPlanTaskService.list(taskQw);

        // 根据入参数,处理前置任务信息
        newTaskList.forEach(task -> {
            EmergencyPlanTaskVo taskVo = taskVoMap.get(task.getTaskName());
            String beforeTaskNames = taskVo.getBeforeTaskNames();
            List<String> preTaskNameList = StringUtils.isNotBlank(beforeTaskNames)
                    ? CollectionUtil.newArrayList(beforeTaskNames.split(","))
                    : null;
            if (CollectionUtil.isNotEmpty(preTaskNameList)) {
                List<EmergencyEventTask> preTaskList = newTaskList.stream()
                        .filter(t -> preTaskNameList.contains(t.getTaskName())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(preTaskList)) {
                    String preTaskIds = preTaskList.stream().map(t -> String.valueOf(t.getId()))
                            .collect(Collectors.joining(","));
                    task.setBeforeTaskIds(preTaskIds);
                }
            } else {
                // 没有前置任务,则设置为节点激活
                task.setActivateStatus(1);
                task.setActivateTime(DateUtil.date());
            }
            commonService.setModifyInfo(task);
            eventPlanTaskService.updateById(task);
        });
    }

    /**
     * 计算预计完成时间
     *
     * @param event 事件
     * @return 预计完成时间
     */
    private Date calPlanEndTime(EmergencyEvent event) {
        // 其实可以不用再查一遍的,这里直接再查一遍,1是验证数据,2是为了方便计算
        Date planStartTime = event.getPlanStartTime();
        Date finalNow = planStartTime;
        // 先查询预案任务
        QueryWrapper<EmergencyEventPlan> eventPlanQw = new QueryWrapper<>();
        eventPlanQw.eq("event_id_", event.getId());
        List<EmergencyEventPlan> eventPlanList = eventPlanService.list(eventPlanQw);
        if (CollectionUtil.isNotEmpty(eventPlanList)) {
            // 最终预计完成时间
            // 再查询预案任务
            for (int i = 0; i < eventPlanList.size(); i++) {
                QueryWrapper<EmergencyEventTask> eventTaskQw = new QueryWrapper<>();
                eventTaskQw.eq("event_id_", event.getId());
                eventTaskQw.eq("event_plan_id_", eventPlanList.get(i).getId());
                List<EmergencyEventTask> eventTaskList = eventPlanTaskService.list(eventTaskQw);
                if (CollectionUtil.isNotEmpty(eventTaskList)) {

                    // 再组织流程树
                    MoreParentTreeNode<?> processTree = EventTaskTreeUtil.convertTree(eventTaskList);

                    // 最后计算时间
                    Date planTaskEndTime = calEndTime(processTree, planStartTime, eventTaskList);
                    if (planTaskEndTime.after(finalNow)) {
                        finalNow = planTaskEndTime;
                    }
                }

            }

        }

        return finalNow;
    }

    /**
     * 计算预计完成时间
     *
     * @param processTree   流程树
     * @param planStartTime 预计开始时间
     * @param eventTaskList 事件任务列表
     * @return 预计完成时间
     */
    private Date calEndTime(MoreParentTreeNode<?> processTree, Date planStartTime,
                            List<EmergencyEventTask> eventTaskList) {
        int taskNum = eventTaskList.size() + 1;
        Double[] exeHourArr = new Double[taskNum];
        Arrays.fill(exeHourArr, 0d);
        // 计算最长执行时间
        ProcessGraph graph = new ProcessGraph(taskNum, exeHourArr);
        itTransTreeRefToGraph(processTree, graph, exeHourArr);

        // 重新设置执行时间
        graph.setExecutionTimes(exeHourArr);

        Double exeHour = graph.findMaxExecutionTime();
        log.info("-----------------exeHour:{}", exeHour);
        return DateUtil.offsetHour(planStartTime, exeHour.intValue());
    }

    /**
     * 递归转换树为图的边,并获取执行时间
     *
     * @param processNode 树节点
     * @param graph       图
     * @param exeHour     执行时间
     */
    private void itTransTreeRefToGraph(MoreParentTreeNode<?> processNode, ProcessGraph graph, Double[] exeHour) {
        Integer src = processNode.getIndex();
        Object extendInfo = processNode.getExtendInfo();
        if (extendInfo instanceof EmergencyEventTask) {
            EmergencyEventTask eventTask = (EmergencyEventTask) extendInfo;
            Double hour = eventTask.getTaskDuration();
            exeHour[src] = hour;
        }
        if (extendInfo instanceof EmergencyEventTaskDTO) {
            EmergencyEventTaskDTO eventTaskDTO = (EmergencyEventTaskDTO) extendInfo;
            Double hour = eventTaskDTO.getTaskDuration();
            exeHour[src] = hour;
        }
        List<?> children = processNode.getChildren();
        if (CollectionUtil.isNotEmpty(children)) {
            for (int i = 0; i < children.size(); i++) {
                MoreParentTreeNode<?> child = (MoreParentTreeNode<?>) children.get(i);
                Integer dest = child.getIndex();
                graph.addEdge(src, dest);
                itTransTreeRefToGraph(child, graph, exeHour);
            }
        }
    }

    /**
     * 根据告警信息查询应急预案 (告警高于预案等级)
     *
     * @param alarm 告警信息
     * @return 应急预案
     */
    @Override
    public List<EmergencyPlan> queryHighLevelEmergencyPlan(Alarm alarm) {
        String alarmType = alarm.getAlarmType();
        Integer alarmLevel = alarm.getLevel();
        String alarmCode = alarm.getCode();
        return planMapper.queryHighLevelEmergencyPlan(alarmType, alarmLevel);
    }

    @Override
    public EmergencyEvent getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(EmergencyEvent safeEmergencyEvent) {

        /*
         * List<EmergencyEvent> list = new LambdaQueryChainWrapper<>(baseMapper)
         * .eq(EmergencyEvent::getDeviceCode, safeEmergencyEvent.getDeviceCode())
         * .list();
         * if (list.size() > 0 && (list.size() > 1 ||
         * ObjectUtils.isEmpty(safeEmergencyEvent.getId()) ||
         * !safeEmergencyEvent.getId().equals(list.get(0).getId()))) {
         * throw new BusinessException("名称重复");
         * }
         */

    }

    /**
     * 校验参数必填
     */
    private void validParamRequired(EmergencyEvent safeEmergencyEvent) {
        // Assert.notNull(safeEmergencyEvent, "参数为空");
        // Assert.isTrue(StringUtils.isNotBlank(safeEmergencyEvent.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(EmergencyEvent safeEmergencyEvent) {
        // Assert.isTrue(safeEmergencyEvent.getName() == null ||
        // safeEmergencyEvent.getName().length() <= 50,
        // "名称超长");
    }
}
