package com.smartPark.business.emergency.department.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.emergency.department.entity.Department;
import com.smartPark.business.emergency.department.entity.DepartmentWorkerRef;
import com.smartPark.business.emergency.department.entity.vo.DepartmentVo;
import com.smartPark.business.emergency.department.mapper.DepartmentMapper;
import com.smartPark.business.emergency.department.mapper.DepartmentResourcesRefMapper;
import com.smartPark.business.emergency.department.mapper.DepartmentWorkerRefMapper;
import com.smartPark.business.emergency.department.service.DepartmentResourcesRefService;
import com.smartPark.business.emergency.department.service.DepartmentService;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.security.entity.Worker;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencyEvent表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Slf4j
@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    @Resource
    private CommonService commonService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private DepartmentWorkerRefMapper departmentWorkerRefMapper;

    @Resource
    private DepartmentResourcesRefService departmentResourcesRefService;

    @Resource
    private DepartmentResourcesRefMapper departmentResourcesRefMapper;


    @Override
    public boolean saveOne(DepartmentVo departmentVo) {
        StringBuilder sj = new StringBuilder();
        sj.append("新增部门,部门名称:");
        sj.append(departmentVo.getName());
        commonService.setCreateAndModifyInfo(departmentVo);
        validParamRequired(departmentVo);
        validRepeat(departmentVo);
        validParamFormat(departmentVo);
        departmentVo.setNo(generateCheckNo());
        Department department = new Department();
        BeanUtils.copyProperties(departmentVo, department);
        save(department);
        //处理关联人员
        if (CollectionUtil.isNotEmpty(departmentVo.getRefList())) {
            departmentVo.getRefList().forEach(item -> {
                item.setDepartmentId(department.getId());
                departmentWorkerRefMapper.insert(item);
            });
        }
        // 处理关联资源
        if (CollectionUtil.isNotEmpty(departmentVo.getResourcesList())) {
            departmentVo.getResourcesList().forEach(item -> {
                item.setDepartmentId(department.getId());
                departmentResourcesRefService.saveOne(item);
            });
        }
        return true;
    }

    private void validRepeat(DepartmentVo departmentVo) {
        //名称不能重复
        List<Department> list = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(Department::getName, departmentVo.getName())
                .list();
        if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(departmentVo.getId()) || !departmentVo.getId().equals(list.get(0).getId()))) {
            throw new BusinessException("名称重复");
        }
    }

    private String generateCheckNo() {
        //获取当前年月日
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
//        String format = simpleDateFormat.format(new Date());
//        long result = redisUtil.incr("YJBM"+format, 1);
        long result = redisUtil.incr("YJBM", 1);
        //如果不足4位，则用0补齐
        String str = String.format("%8d", result).replace(" ", "0");
//        redisUtil.expire("YJBM"+format,24*60*60);
        //GW+YYYYMMDD+4位顺序码
//        return "YJBM"+format+str;
        return "YJBM" + str;
    }

    private void validParamFormat(DepartmentVo departmentVo) {
        Assert.isTrue(departmentVo.getId() == null || null != baseMapper.selectById(departmentVo.getId()), "部门不存在");
    }

    private void validParamRequired(DepartmentVo departmentVo) {
        Assert.notNull(departmentVo, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(drainageHouseholdArchives.getArchivesObjId()), "部件码为空");
        Assert.notNull(departmentVo.getName(), "部门名称为空");
    }

    @Override
    public boolean updateOne(DepartmentVo departmentVo) {
        StringBuilder sj = new StringBuilder();
        sj.append("修改部门,部门id:");
        sj.append(departmentVo.getId());
        commonService.setModifyInfo(departmentVo);
        validParamRequired(departmentVo);
//        validRepeat(drainageHouseholdArchives);
        validParamFormat(departmentVo);
//        departmentVo.setNo(generateCheckNo());
        Department department = new Department();
        BeanUtils.copyProperties(departmentVo, department);
        updateById(department);
        //处理关联人员
        departmentWorkerRefMapper.delete(new QueryWrapper<DepartmentWorkerRef>().eq("department_id_", department.getId()));
        if (CollectionUtil.isNotEmpty(departmentVo.getRefList())) {
            departmentVo.getRefList().forEach(item -> {
                item.setDepartmentId(department.getId());
                departmentWorkerRefMapper.insert(item);
            });
        }
        // 处理关联资源
        if (CollectionUtil.isNotEmpty(departmentVo.getResourcesList())) {
            departmentVo.getResourcesList().forEach(item -> {
                item.setDepartmentId(department.getId());
                if (item.getId() == null) {
                    departmentResourcesRefService.saveOne(item);
                } else {
                    departmentResourcesRefService.updateOne(item);
                    if (item.getQuantity() == 0) {
                        departmentResourcesRefService.removeById(item.getId());
                    }
                }
            });
        }
        return true;
    }

    @Override
    public IPage<DepartmentVo> selectPage(Page page, DepartmentVo department) {
        IPage<DepartmentVo> emergencyEventIPage = baseMapper.selectPage(page, department);
        emergencyEventIPage.getRecords().forEach(item -> item.setResourcesList(
                departmentResourcesRefService.selectListByDepartmentId(item.getId()))
        );
        return emergencyEventIPage;
    }

    @Override
    public DepartmentVo getOneById(Serializable id) {
        DepartmentVo departmentVo = baseMapper.getDetailById(id);
        departmentVo.setResourcesList(departmentResourcesRefService.selectListByDepartmentId(departmentVo.getId()));
        return departmentVo;
    }

    @Override
    public boolean deleteByIds(List<Long> idList) {
//        StringJoiner sj = new StringJoiner("-");
//        StringJoiner coreParamSj = new StringJoiner("-");
//        StringJoiner nameSj = new StringJoiner(",");
//
//        //查询应急法规
//        List<Department> departmentList = baseMapper.selectList(new QueryWrapper<Department>().in("id_", idList));
//        if (CollectionUtil.isNotEmpty(departmentList)) {
//            for (Department department : departmentList) {
//                nameSj.add(department.getName());
//            }
//        }

//        // 将删除状态改为主键值
//        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(EmergencyRegula::getId, idList).update();
//
//        sj.add("删除应急法规，法规名称：[" + nameSj.toString() + "]");
//        coreParamSj.add("删除应急法规，法规名称：[" + nameSj.toString() + "]");
//        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        departmentWorkerRefMapper.delete(new QueryWrapper<DepartmentWorkerRef>().in("department_id_", idList));
        idList.forEach(id -> departmentResourcesRefService.removeByDepartmentId(id));
        baseMapper.deleteBatchIds(idList);
        return true;
    }

    @Override
    public List<Worker> getNotRefWorkerList(Long id) {
        return departmentWorkerRefMapper.getNotRefWorkerList(id);
    }
}

