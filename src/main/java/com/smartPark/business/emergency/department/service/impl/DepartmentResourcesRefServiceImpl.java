package com.smartPark.business.emergency.department.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.emergency.department.entity.DepartmentResourcesRef;
import com.smartPark.business.emergency.department.entity.vo.DepartmentResourcesRefVo;
import com.smartPark.business.emergency.department.mapper.DepartmentResourcesRefMapper;
import com.smartPark.business.emergency.department.service.DepartmentResourcesRefService;
import com.smartPark.business.emergency.supply.entity.EmergencySupply;
import com.smartPark.business.emergency.supply.service.EmergencySupplyService;
import com.smartPark.common.base.model.RequestModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * EmergencyEvent表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Slf4j
@Service
public class DepartmentResourcesRefServiceImpl extends ServiceImpl<DepartmentResourcesRefMapper, DepartmentResourcesRef>
        implements DepartmentResourcesRefService {

    @Resource
    private DepartmentResourcesRefMapper departmentResourcesRefMapper;

    @Resource
    private EmergencySupplyService safeEmergencySupplyService;

    @Override
    public void saveOne(DepartmentResourcesRef departmentResourcesRef) {
        departmentResourcesRefMapper.insert(departmentResourcesRef);
        if (departmentResourcesRef.getType() == 2) {
            // 获取被关联部门的资源数据
            DepartmentResourcesRef refInfo = departmentResourcesRefMapper.selectOne(
                    new QueryWrapper<DepartmentResourcesRef>()
                            .eq("department_id_", departmentResourcesRef.getRefDepartmentId())
                            .eq("ref_supply_id_", departmentResourcesRef.getRefSupplyId()));
            if (refInfo != null) {
                // 更新被关联部门的资源数量
                refInfo.setQuantity(refInfo.getQuantity() - departmentResourcesRef.getQuantity());
                departmentResourcesRefMapper.updateById(refInfo);
            }
        }
    }

    @Override
    public void updateOne(DepartmentResourcesRef departmentResourcesRef) {
        DepartmentResourcesRef refOld = departmentResourcesRefMapper.selectOne(
                new QueryWrapper<DepartmentResourcesRef>()
                        .eq("id_", departmentResourcesRef.getId()));
        departmentResourcesRefMapper.updateById(departmentResourcesRef);
        if (refOld != null) {
            // 处理关联部门原有资源数量
            if (departmentResourcesRef.getType() == 2) {
                // 获取被关联部门的资源数据
                DepartmentResourcesRef refInfo = departmentResourcesRefMapper.selectOne(
                        new QueryWrapper<DepartmentResourcesRef>()
                                .eq("department_id_", departmentResourcesRef.getRefDepartmentId())
                                .eq("ref_supply_id_", departmentResourcesRef.getRefSupplyId()));
                if (refInfo != null) {
                    // 更新被关联部门的资源数量
                    refInfo.setQuantity(
                            refInfo.getQuantity() + refOld.getQuantity() - departmentResourcesRef.getQuantity());
                    departmentResourcesRefMapper.updateById(refInfo);
                }
            }
        }
    }

    @Override
    public List<DepartmentResourcesRef> selectListByDepartmentId(Long departmentId) {
        if (departmentId != null) {
            List<DepartmentResourcesRef> list = departmentResourcesRefMapper.selectList(
                    new QueryWrapper<DepartmentResourcesRef>()
                            .eq("department_id_", departmentId));
            return list;
        }
        return null;
    }

    @Override
    public IPage<DepartmentResourcesRefVo> queryListByPage(RequestModel<DepartmentResourcesRefVo> requestModel) {
        Page page = requestModel.getPage();
        DepartmentResourcesRefVo departmentResourcesRefVo = requestModel.getCustomQueryParams();
        IPage<DepartmentResourcesRefVo> list = departmentResourcesRefMapper.selectPage(page, departmentResourcesRefVo);
        list.getRecords().forEach(item -> {
            EmergencySupply emergencySupply = safeEmergencySupplyService.getOneById(item.getRefSupplyId());
            if (emergencySupply != null) {
                item.setSupplyName(emergencySupply.getName());
                item.setSupplyCode(emergencySupply.getSupplyCode());
            }
        });
        return list;
    }

    public DepartmentResourcesRef selectOne(Long id) {
        return departmentResourcesRefMapper.selectOne(new QueryWrapper<DepartmentResourcesRef>().eq("id_", id));
    }

    @Override
    public void removeByDepartmentId(Long departmentId) {
        List<DepartmentResourcesRef> ref = selectListByDepartmentId(departmentId);
        if (ref != null) {
            ref.forEach(item -> {
                if (item.getType() == 2) {
                    // 获取被关联部门的资源数据
                    DepartmentResourcesRef refInfo = departmentResourcesRefMapper.selectOne(
                            new QueryWrapper<DepartmentResourcesRef>()
                                    .eq("department_id_", item.getRefDepartmentId())
                                    .eq("ref_supply_id_", item.getRefSupplyId()));

                    if (refInfo != null) {
                        refInfo.setQuantity(refInfo.getQuantity() + item.getQuantity());
                        departmentResourcesRefMapper.updateById(refInfo);
                    }
                }
                departmentResourcesRefMapper.deleteById(item.getId());
            });
        }
    }
}
