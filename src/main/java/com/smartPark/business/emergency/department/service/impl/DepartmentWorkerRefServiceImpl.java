package com.smartPark.business.emergency.department.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.emergency.department.entity.DepartmentWorkerRef;
import com.smartPark.business.emergency.department.entity.vo.DepartmentWorkerRefVo;
import com.smartPark.business.emergency.department.mapper.DepartmentWorkerRefMapper;
import com.smartPark.business.emergency.department.service.DepartmentWorkerRefService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * EmergencyEvent表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/20
 */
@Slf4j
@Service
public class DepartmentWorkerRefServiceImpl extends ServiceImpl<DepartmentWorkerRefMapper, DepartmentWorkerRef> implements DepartmentWorkerRefService {


    @Override
    public IPage<DepartmentWorkerRefVo> selectPage(Page page, DepartmentWorkerRefVo department) {
        return baseMapper.selectPage(page,department);
    }

}

