package com.smartPark.business.emergency.department.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.department.entity.DepartmentResourcesRef;
import com.smartPark.business.emergency.department.entity.vo.DepartmentResourcesRefVo;
import org.apache.ibatis.annotations.Param;

/**
 * 部门资源关联表
 *
 * <AUTHOR> ruilin
 * @date 2025/05/23
 */
public interface DepartmentResourcesRefMapper extends BaseMapper<DepartmentResourcesRef> {

    /**
     * 分页查询部门资源关联列表
     *
     * @param page   分页参数
     * @param departmentResourcesRefVo 查询参数
     * @return 分页结果
     */
    IPage<DepartmentResourcesRefVo> selectPage(@Param("page") Page page,
            @Param("departmentResourcesRefVo") DepartmentResourcesRefVo departmentResourcesRefVo);
}
