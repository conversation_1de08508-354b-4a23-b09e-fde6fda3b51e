package com.smartPark.business.emergency.information.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.information.entity.EmergencyInformationPublish;
import com.smartPark.business.emergency.information.entity.dto.EmergencyInformationPublishDTO;
import com.smartPark.business.emergency.information.entity.vo.EmergencyInformationPublishVo;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;

/**
 * EmergencyInformationPublish表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/10/30
 */
public interface EmergencyInformationPublishMapper extends BaseMapper<EmergencyInformationPublish> {


    /**
     * 查询分页
     *
     * @param page        分页参数对象
     * @param safeEmergencyInformationPublish 过滤参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyInformationPublish> selectPage(Page page, @Param("safeEmergencyInformationPublish") EmergencyInformationPublish safeEmergencyInformationPublish);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyInformationPublish getOneById(@Param("id") Serializable id);

    /**
     * 查询分页dto
     * @param page 分页参数对象
     * @param informationPublishVo 过滤参数对象
     * @return 查询分页结果dto
     */
    IPage<EmergencyInformationPublishDTO> selectDtoPage(Page page, @Param("informationPublishVo") EmergencyInformationPublishVo informationPublishVo);
}

