package com.smartPark.business.emergency.information.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 信息发布
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_emergency_information_publish")
public class EmergencyInformationPublish extends BaseEntity<EmergencyInformationPublish> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 信息标题
     */
    @TableField("title_")
    private String title;

    /**
     * 信息内容
     */
    @TableField("content_")
    private String content;

    /**
     * 发布渠道，1诱导屏，2广播系统，3公众号，4站内消息,多个，逗号隔开
     */
    @TableField("publish_way_")
    //@Trans(type = TransType.DICTIONARY, key = "emergency_information_type")
    private String publishWay;

    /**
     * 信息类型，1报警信息，2预警信息，3提示信息
     */
    @TableField("information_type_")
    @Trans(type = TransType.DICTIONARY, key = "emergency_information_type")
    private Integer informationType;

    /**
     * 信息发布状态，1未发布，2已发布
     */
    @TableField("publish_state_")
    @Trans(type = TransType.DICTIONARY, key = "emergency_information_publish_state")
    private Integer publishState;

    /**
     * 发布人id
     */
    @TableField("publisher_id_")
    private Long publisherId;
    /**
     * 事件编号
     */
    @TableField(value = "event_no_",updateStrategy = FieldStrategy.IGNORED)
    private String eventNo;

    /**
     * 事件类型，冗余
     */
    @TableField("event_type_")
    @Trans(type = TransType.DICTIONARY, key = "allEventType")
    private String eventType;

    /**
     * 发布时间
     */
    @TableField("publish_time_")
    private Date publishTime;

    /**
     * 信息文件地址，多个逗号隔开
     */
    @TableField("file_urls_")
    private String fileUrls;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，0否，1是
     */
    @TableField("deleted_")
    @TableLogic(value = "0", delval = "1")
    private Long deleted;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
