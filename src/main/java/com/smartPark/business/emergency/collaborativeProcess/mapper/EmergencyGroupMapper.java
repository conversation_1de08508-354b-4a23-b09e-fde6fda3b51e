package com.smartPark.business.emergency.collaborativeProcess.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.collaborativeProcess.entity.EmergencyGroup;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smartPark.business.emergency.collaborativeProcess.entity.dto.EmergencyGroupDTO;
import com.smartPark.business.emergency.collaborativeProcess.entity.vp.EmergencyGroupQueryVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 应急小组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface EmergencyGroupMapper extends BaseMapper<EmergencyGroup> {

  IPage<EmergencyGroupDTO> queryListByPage(Page page, @Param("queryVO") EmergencyGroupQueryVO queryVO);
}
