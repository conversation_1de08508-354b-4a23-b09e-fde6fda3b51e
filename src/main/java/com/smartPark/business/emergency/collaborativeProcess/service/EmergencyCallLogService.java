package com.smartPark.business.emergency.collaborativeProcess.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.emergency.collaborativeProcess.entity.EmergencyCallLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.collaborativeProcess.entity.EmergencyCallLogParticipants;
import com.smartPark.business.emergency.collaborativeProcess.entity.dto.EmergencyCallLogDTO;
import com.smartPark.common.base.model.RequestModel;

/**
 * <p>
 * 通话记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface EmergencyCallLogService extends IService<EmergencyCallLog> {

  EmergencyCallLog addCallLog(EmergencyCallLog emergencyCallLog);

  IPage<EmergencyCallLogDTO> queryListCallLogByPage(RequestModel<EmergencyCallLog> requestModel);

  void overCallLog(EmergencyCallLog emergencyCallLog);

  void addOrOutCallLog(EmergencyCallLogParticipants callLogParticipants);
}
