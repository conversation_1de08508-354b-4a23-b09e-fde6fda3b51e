package com.smartPark.business.emergency.collaborativeProcess.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.emergency.collaborativeProcess.entity.EmergencyCallLog;
import com.smartPark.business.emergency.collaborativeProcess.entity.EmergencyCallLogParticipants;
import com.smartPark.business.emergency.collaborativeProcess.entity.EmergencyGroup;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.collaborativeProcess.entity.dto.EmergencyCallLogDTO;
import com.smartPark.business.emergency.collaborativeProcess.entity.dto.EmergencyGroupDTO;
import com.smartPark.business.emergency.collaborativeProcess.entity.dto.EmergencyGroupRefPersonDTO;
import com.smartPark.business.emergency.collaborativeProcess.entity.vp.EmergencyGroupQueryVO;
import com.smartPark.common.base.model.RequestModel;
import java.util.List;

/**
 * <p>
 * 应急小组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface EmergencyGroupService extends IService<EmergencyGroup> {

  void insertOne(EmergencyGroup emergencyGroup);

  void updateOne(EmergencyGroup emergencyGroup);

  EmergencyGroupDTO findById(Long id);

  void delBatch(List<Integer> ids);

  IPage<EmergencyGroupDTO> queryListByPage(RequestModel<EmergencyGroupQueryVO> requestModel);

}
