package com.smartPark.business.emergency.analyze.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.analyze.entity.EmergencyEventStatistics;
import com.smartPark.business.emergency.analyze.entity.dto.EmergencyEventStatisticsDTO;
import com.smartPark.business.emergency.analyze.entity.dto.EventOverviewDTO;
import com.smartPark.business.emergency.analyze.entity.vo.EventOverviewVo;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * EmergencyEventStatistics表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/10/26
 */
public interface EmergencyEventStatisticsMapper extends BaseMapper<EmergencyEventStatistics> {


    /**
     * 查询分页
     *
     * @param page                         分页参数对象
     * @param safeEmergencyEventStatistics 过滤参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyEventStatistics> selectPage(Page page, @Param("safeEmergencyEventStatistics") EmergencyEventStatistics safeEmergencyEventStatistics);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyEventStatistics getOneById(@Param("id") Serializable id);

    /**
     * 统计数据
     *
     * @param eventOverviewVo 查询条件
     * @return 统计结果
     */
    List<EmergencyEventStatisticsDTO> countNumByDisposeStatusKx(@Param("eventOverviewVo") EventOverviewVo eventOverviewVo);

    /**
     * 统计数量根据事件类型分组
     *
     * @param eventOverviewVo 查询条件
     * @return 统计结果
     */
    List<EmergencyEventStatisticsDTO> countNumGroupByEventType(@Param("eventOverviewVo") EventOverviewVo eventOverviewVo);

    /**
     * 统计事件平均用时，按日期分组
     *
     * @param eventOverviewVo 查询条件
     * @return 统计结果
     */
    List<EmergencyEventStatisticsDTO> countAvgDealTimeGroupByDate(@Param("eventOverviewVo") EventOverviewVo eventOverviewVo);

    /**
     * 统计事件平均用时，按事件类型分组
     *
     * @param eventOverviewVo 查询条件
     * @return 统计结果
     */
    List<EmergencyEventStatisticsDTO> countAvgDealTimeGroupByEventType(@Param("eventOverviewVo") EventOverviewVo eventOverviewVo);
}

