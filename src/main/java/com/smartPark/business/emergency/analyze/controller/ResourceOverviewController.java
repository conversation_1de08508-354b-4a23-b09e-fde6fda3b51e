package com.smartPark.business.emergency.analyze.controller;

import com.smartPark.business.emergency.analyze.entity.vo.ResourceOverviewVo;
import com.smartPark.business.emergency.analyze.service.ResourceOverviewService;
import com.smartPark.common.base.model.RequestModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;

/**
 * 应急资源管理/应急资源概况
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/emergencyResourceOverview")
public class ResourceOverviewController {
    @Resource
    private ResourceOverviewService resourceOverviewService;

    /**
     * 应急资源概况统计
     *
     * @param resourceOverviewVo 应急资源概况vo
     * @return 统计数据
     */
    @PostMapping("/topCount")
    public RestMessage topCount(@RequestBody ResourceOverviewVo resourceOverviewVo) {
        return resourceOverviewService.topCount(resourceOverviewVo);
    }

    /**
     * 应急资源曲线
     *
     * @param resourceOverviewVo 应急资源概况vo
     * @return 统计数据
     */
    @PostMapping("/trendData")
    public RestMessage trendData(@RequestBody ResourceOverviewVo resourceOverviewVo) {
        return resourceOverviewService.trendData(resourceOverviewVo);
    }

    /**
     * 应急资源明细
     * @param requestModel 应急资源概况requestModel
     * @return 统计数据
     */
    @PostMapping("/trendDetailData")
    public RestMessage trendDetailData(@RequestBody RequestModel<ResourceOverviewVo> requestModel) {
        Assert.notNull(requestModel, "参数不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        return resourceOverviewService.trendDetailData(requestModel);
    }

    /**
     * 应急资源明细导出
     * @param resourceOverviewVo 应急资源概况vo
     * @return 统计数据
     */
    @PostMapping("/trendDetailDataExport")
    public RestMessage trendDetailDataExport(@RequestBody ResourceOverviewVo resourceOverviewVo) {
        Assert.notNull(resourceOverviewVo, "参数不能为空");
        Assert.notNull(resourceOverviewVo.getStartTime(), "开始时间不能为空");
        Assert.notNull(resourceOverviewVo.getEndTime(), "结束时间不能为空");
        Assert.isTrue(StringUtils.isNotBlank(resourceOverviewVo.getQueryType()), "类型不能为空");
        Long taskId = resourceOverviewService.trendDetailDataExport(resourceOverviewVo);
        return RestBuilders.successBuilder().data(taskId).build();
    }

}
