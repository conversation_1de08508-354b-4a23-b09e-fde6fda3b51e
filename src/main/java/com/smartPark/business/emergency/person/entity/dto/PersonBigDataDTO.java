package com.smartPark.business.emergency.person.entity.dto;

import com.smartPark.business.emergency.person.entity.PersonBigData;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * PersonBigData实体类DTO
 *
 * <AUTHOR>
 * @date 2023/04/19
 */

@Data
@Accessors(chain = true)
public class PersonBigDataDTO extends PersonBigData {


    public PersonBigDataDTO(PersonBigData safePersonBigData) {
        //this.setName(safePersonBigData.getName());
    }
}
