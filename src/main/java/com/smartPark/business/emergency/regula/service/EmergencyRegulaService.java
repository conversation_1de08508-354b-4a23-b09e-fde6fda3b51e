package com.smartPark.business.emergency.regula.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.regula.entity.EmergencyRegula;
import com.smartPark.business.emergency.regula.entity.dto.EmergencyRegulaDTO;
import com.smartPark.business.emergency.regula.entity.vo.EmergencyRegulaVo;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencyRegula表服务接口
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
public interface EmergencyRegulaService extends IService<EmergencyRegula> {

    /**
     * 新增
     *
     * @param safeEmergencyRegula 实体对象
     * @return 操作结果
     */
    boolean saveOne(EmergencyRegula safeEmergencyRegula);

    /**
     * 修改单条
     *
     * @param safeEmergencyRegula 实体对象
     * @return 修改结果
     */
    boolean updateOne(EmergencyRegula safeEmergencyRegula);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param safeEmergencyRegula 分页参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyRegula> selectPage(Page page, EmergencyRegula safeEmergencyRegula);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyRegula getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param safeEmergencyRegula 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    Long export(EmergencyRegula safeEmergencyRegula, HttpServletRequest request, HttpServletResponse response);

    /**
     * 新增法规
     *
     * @param emergencyRegulaVo 实体对象
     * @return 新增结果
     */
    RestMessage addRegula(EmergencyRegulaVo emergencyRegulaVo);

    /**
     * 修改法规
     *
     * @param emergencyRegulaVo 实体对象vo
     * @return 修改结果
     */
    RestMessage updateRegula(EmergencyRegulaVo emergencyRegulaVo);

    /**
     * 分页查询所有数据Dto
     * @param page 分页对象
     * @param emergencyRegulaVo 查询条件vo
     * @return 所有数据
     */
    IPage<EmergencyRegulaDTO> selectDtoPage(Page page, EmergencyRegulaVo emergencyRegulaVo);

    /**
     * 详情dto
     * @param id 主键
     * @return 单条数据
     */
    EmergencyRegulaDTO selectDtoById(Serializable id);

    /**
     * @Description: 导入
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    Long imports(MultipartFile file) throws IOException;
}

