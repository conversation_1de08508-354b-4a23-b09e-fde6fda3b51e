package com.smartPark.business.emergency.regula.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.importer.DataImportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.emergency.regula.entity.vo.EmergencyRegulaVo;
import com.smartPark.business.emergency.regula.excel.handler.EmergencyRegulaExportHandler;
import com.smartPark.business.emergency.regula.excel.handler.EmergencyRegulaImportHandler;
import com.smartPark.business.emergency.regula.excel.model.EmergencyRegulaImportModel;
import com.smartPark.business.manhole.excel.model.ManholeImportModel;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.security.context.BaseUserContextProducer;

import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;


import com.smartPark.business.emergency.regula.mapper.EmergencyRegulaMapper;
import com.smartPark.business.emergency.regula.entity.EmergencyRegula;
import com.smartPark.business.emergency.regula.entity.dto.EmergencyRegulaDTO;
import com.smartPark.business.emergency.regula.service.EmergencyRegulaService;
import com.smartPark.common.utils.BusinessSerialNoUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * EmergencyRegula表服务实现类
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
@Slf4j
@Service("safeEmergencyRegulaService")
public class EmergencyRegulaServiceImpl extends ServiceImpl
        <EmergencyRegulaMapper, EmergencyRegula> implements EmergencyRegulaService {
    @Resource
    private BaseUserContextProducer baseUserContextProducer;
    @Resource
    private ExcelService excelService;
    @Resource
    private CommonService commonService;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        StringJoiner nameSj = new StringJoiner(",");

        //查询应急法规
        List<EmergencyRegula> emergencyRegulaList = baseMapper.selectList(new QueryWrapper<EmergencyRegula>().in("id_", idList));
        if (CollectionUtil.isNotEmpty(emergencyRegulaList)) {
            for (EmergencyRegula emergencyRegula : emergencyRegulaList) {
                nameSj.add(emergencyRegula.getRegulaName());
            }
        }

        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(EmergencyRegula::getId, idList).update();

        sj.add("删除应急法规，法规名称：[" + nameSj.toString() + "]");
        coreParamSj.add("删除应急法规，法规名称：[" + nameSj.toString() + "]");
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return true;
    }


    @Override
    public boolean saveOne(EmergencyRegula safeEmergencyRegula) {
        commonService.setCreateAndModifyInfo(safeEmergencyRegula);

        //validParamRequired(safeEmergencyRegula);
        //validRepeat(safeEmergencyRegula);
        //validParamFormat(safeEmergencyRegula);
        return save(safeEmergencyRegula);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(EmergencyRegula safeEmergencyRegula) {
        Assert.notNull(safeEmergencyRegula.getId(), "id不能为空");
        commonService.setModifyInfo(safeEmergencyRegula);

        // validRepeat(safeEmergencyRegula);
        //validParamFormat(safeEmergencyRegula);
        return updateById(safeEmergencyRegula);
    }

    @Override
    public IPage<EmergencyRegula> selectPage(Page page, EmergencyRegula safeEmergencyRegula) {
        return baseMapper.selectPage(page, safeEmergencyRegula);
    }

    @Override
    public Long export(EmergencyRegula safeEmergencyRegula, HttpServletRequest request, HttpServletResponse
            response) {
        Long userId = baseUserContextProducer.getCurrent().getId();
        DataExportParam dataExportParam = new DataExportParam();
        dataExportParam.setParam(safeEmergencyRegula);
        dataExportParam.setExportFileName("应急法规列表");
        dataExportParam.setTenantCode("safe");
        dataExportParam.setBusinessCode("emergencyRegula");
        dataExportParam.setCreateUserCode(userId.toString());
        Long taskId = excelService.doExport(dataExportParam, EmergencyRegulaExportHandler.class);
        return taskId;
    }

    @Override
    public RestMessage addRegula(EmergencyRegulaVo emergencyRegulaVo) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");

        //参数校验
        validParamRequired(emergencyRegulaVo);

        //校验唯一
        validRepeat(emergencyRegulaVo);

        //转换
        EmergencyRegula emergencyRegula = new EmergencyRegula();
        BeanUtil.copyProperties(emergencyRegulaVo, emergencyRegula, CopyOptions.create().ignoreNullValue());

        //设置编码
        //genSeqCode(RedisUtil redis, String prefix, String splitStr, int dayNoSize, String redisKey, String comStr, String comDir)
        String code = BusinessSerialNoUtil.genSeqCode(redisUtil, "YJFG", "", 8, "emergencyRegula:YJFG", "0", "before");
        emergencyRegula.setRegulaCode(code);

        //设置创建人
        commonService.setCreateAndModifyInfo(emergencyRegula);
        baseMapper.insert(emergencyRegula);
        BeanUtil.copyProperties(emergencyRegula, emergencyRegulaVo, CopyOptions.create().ignoreNullValue());

        sj.add("新增应急法规，法规名称：" + emergencyRegulaVo.getRegulaName());
        coreParamSj.add("新增应急法规，法规名称：" + emergencyRegulaVo.getRegulaName());
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return RestBuilders.successBuilder().data(emergencyRegulaVo).build();
    }

    @Override
    public RestMessage updateRegula(EmergencyRegulaVo emergencyRegulaVo) {
        //参数校验
        Assert.notNull(emergencyRegulaVo.getId(), "id不能为空");

        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");

        validParamRequired(emergencyRegulaVo);

        //校验唯一
        validRepeat(emergencyRegulaVo);

        //转换
        EmergencyRegula emergencyRegula = new EmergencyRegula();
        BeanUtil.copyProperties(emergencyRegulaVo, emergencyRegula, CopyOptions.create().ignoreNullValue());

        //修改
        commonService.setModifyInfo(emergencyRegula);
        baseMapper.updateById(emergencyRegula);

        BeanUtil.copyProperties(emergencyRegula, emergencyRegulaVo, CopyOptions.create().ignoreNullValue());

        sj.add("修改应急法规，法规名称：" + emergencyRegulaVo.getRegulaName());
        coreParamSj.add("修改应急法规，法规名称：" + emergencyRegulaVo.getRegulaName());
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return RestBuilders.successBuilder().data(emergencyRegulaVo).build();
    }

    @Override
    public IPage<EmergencyRegulaDTO> selectDtoPage(Page page, EmergencyRegulaVo emergencyRegulaVo) {
        if (emergencyRegulaVo.getEffectState() != null) {
            emergencyRegulaVo.setNowTime(DateUtil.date());
        }
        IPage<EmergencyRegulaDTO> iPage = baseMapper.selectDtoPage(page, emergencyRegulaVo);
        List<EmergencyRegulaDTO> dtoList = iPage.getRecords();
        if (CollectionUtil.isNotEmpty(dtoList)) {
            //处理有效状态
            Date nowTime = DateUtil.date();
            for (EmergencyRegulaDTO dto : dtoList) {
                Date effectStartTime = dto.getEffectStartTime();
                Date effectEndTime = dto.getEffectEndTime();

                if (effectEndTime == null) {
                    if (effectStartTime.after(nowTime)) {
                        dto.setEffectState(2);
                    }else {
                        dto.setEffectState(1);
                    }
                } else {
                    if (DateUtil.isIn(nowTime, effectStartTime, effectEndTime)) {
                        dto.setEffectState(1);
                    } else {
                        if (effectStartTime.after(nowTime)) {
                            dto.setEffectState(2);
                        }
                        if (effectEndTime.before(nowTime)) {
                            dto.setEffectState(3);
                        }
                    }
                }
            }
        }
        return iPage;
    }

    @Override
    public EmergencyRegulaDTO selectDtoById(Serializable id) {
        EmergencyRegula emergencyRegula = baseMapper.getOneById(id);
        Assert.isTrue(emergencyRegula != null, "数据不存在");
        EmergencyRegulaDTO dto = new EmergencyRegulaDTO();
        BeanUtil.copyProperties(emergencyRegula, dto, CopyOptions.create().ignoreNullValue());
        //处理有效状态
        Date nowTime = DateUtil.date();
        Date effectStartTime = dto.getEffectStartTime();
        Date effectEndTime = dto.getEffectEndTime();
        if (effectEndTime == null){
            if (effectStartTime.after(nowTime)) {
                dto.setEffectState(2);
            }else {
                dto.setEffectState(1);
            }
        }else {
            if (DateUtil.isIn(nowTime, effectStartTime, effectEndTime)) {
                dto.setEffectState(1);
            } else {
                if (effectStartTime.after(nowTime)) {
                    dto.setEffectState(2);
                }
                if (effectEndTime.before(nowTime)) {
                    dto.setEffectState(3);
                }
            }

        }
        return dto;
    }

    /**
     * @Description: 导入
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @Override
    public Long imports(MultipartFile file) throws IOException {
        Long userId = baseUserContextProducer.getCurrent().getId();
        DataImportParam dataImportParam = new DataImportParam()
                .setStream(file.getInputStream())
                .setModel(EmergencyRegulaImportModel.class)
                .setBatchSize(100)
                .setFilename("应急法规导入");
        dataImportParam.setTenantCode("safe");
        dataImportParam.setBusinessCode("emergencyRegula");
        dataImportParam.setCreateUserCode(userId.toString());
        Long taskId = excelService.doImport(EmergencyRegulaImportHandler.class, dataImportParam);
        return taskId;
    }

    @Override
    public EmergencyRegula getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(EmergencyRegulaVo emergencyRegulaVo) {
        LambdaQueryChainWrapper<EmergencyRegula> qw = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(EmergencyRegula::getRegulaName, emergencyRegulaVo.getRegulaName());
        if (emergencyRegulaVo.getId() != null) {
            qw.ne(EmergencyRegula::getId, emergencyRegulaVo.getId());
        }

        List<EmergencyRegula> list = qw.list();
        if (emergencyRegulaVo.getId() == null) {
            if (CollectionUtil.isNotEmpty(list)) {
                throw new BusinessException("名称重复");
            }
        }
        if (CollectionUtil.isNotEmpty(list) && !emergencyRegulaVo.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称重复");
        }

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(EmergencyRegulaVo emergencyRegulaVo) {
        Assert.notNull(emergencyRegulaVo, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(emergencyRegulaVo.getRegulaName()), "名称为空");
        //Assert.isTrue(StringUtils.isNotBlank(emergencyRegulaVo.getThemeWords()), "主题词为空");
        Assert.notNull(emergencyRegulaVo.getType(), "类别为空");
        Assert.notNull(emergencyRegulaVo.getLevel(), "级别为空");
        Assert.notNull(emergencyRegulaVo.getEffectStartTime(), "生效时间为空");
        //Assert.notNull(emergencyRegulaVo.getEffectEndTime(), "失效时间为空");
        if (emergencyRegulaVo.getEffectStartTime() != null && emergencyRegulaVo.getEffectEndTime() != null) {
            Assert.isTrue(emergencyRegulaVo.getEffectStartTime().before(emergencyRegulaVo.getEffectEndTime()), "生效时间不能晚于失效时间");
        }
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(EmergencyRegulaVo emergencyRegulaVo) {
        //Assert.isTrue(safeEmergencyRegula.getName() == null || safeEmergencyRegula.getName().length() <= 50,
        //        "名称超长");
    }
}

