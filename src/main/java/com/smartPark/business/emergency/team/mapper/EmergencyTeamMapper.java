package com.smartPark.business.emergency.team.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.analyze.entity.vo.CountItemVo;
import com.smartPark.business.emergency.analyze.entity.vo.ResourceOverviewVo;
import com.smartPark.business.emergency.person.entity.PersonBigData;
import com.smartPark.business.emergency.team.entity.EmergencyTeam;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * EmergencyTeam表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/04/20
 */
public interface EmergencyTeamMapper extends BaseMapper<EmergencyTeam> {


    /**
     * 查询分页
     *
     * @param page              分页参数对象
     * @param safeEmergencyTeam 过滤参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyTeam> selectPage(Page page, @Param("safeEmergencyTeam") EmergencyTeam safeEmergencyTeam);

    /**
     * 查询单条
     *
     * @param id                主键id
     * @param needPersonsDetail 是否需要人员详情
     * @return 查询结果
     */
    EmergencyTeam getOneById(@Param("id") Serializable id, @Param("needPersonsDetail") Boolean needPersonsDetail);

    /**
     * 获取人员分页
     *
     * @param page          分页参数
     * @param personBigData 过滤参数
     * @param teamId        团队id
     * @return
     */
    IPage<PersonBigData> getPersonPage(Page page, @Param("personBigData") PersonBigData personBigData, @Param("teamId") Integer teamId);

    /**
     * 获取人员列表
     * @return 人员列表
     */
    List<EmergencyTeam> queryTeamAndPerson();

    /**
     * 根据条件查询队伍统计按时间分组
     * @param resourceOverviewVo 条件vo
     * @return 统计数据list
     */
    List<CountItemVo> countNumGroupByDate(@Param("resourceOverviewVo") ResourceOverviewVo resourceOverviewVo);
}

