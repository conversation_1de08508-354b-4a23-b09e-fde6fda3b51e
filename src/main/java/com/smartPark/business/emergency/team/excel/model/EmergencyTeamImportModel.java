package com.smartPark.business.emergency.team.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.asyncexcel.core.ImportRow;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EmergencyTeamImportModel extends ImportRow {
    //队伍名称、主管部门、队伍类型、负责人、成立时间、地址、装备描述、专长描述、备注
//  借鉴 EmergencyTeam 对应的属性生成 字段都去一样的
    @ExcelProperty("队伍名称")
    private String teamName;

    @ExcelProperty("主管部门")
    private String department;

    @ExcelProperty("队伍类型")
    private String teamTypeStr;

    @ExcelProperty("负责人")
    private String leader;

    @ExcelProperty("成立时间")
    private String establishTimeStr;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("装备描述")
    private String equipmentDescription;

    @ExcelProperty("专长描述")
    private String specialtyDescription;

    @ExcelProperty("备注")
    private String remark;
}
