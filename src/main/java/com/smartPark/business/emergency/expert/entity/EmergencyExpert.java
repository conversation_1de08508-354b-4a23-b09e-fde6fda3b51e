package com.smartPark.business.emergency.expert.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应急专家
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_emergency_expert")
@ApiModel(value = "EmergencyExpert对象", description = "应急专家")
public class EmergencyExpert implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id_", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "专家编号 YJZJ+8位序号")
    @TableField(value = "expert_code_")
    private String expertCode;

    @ApiModelProperty(value = "专家姓名")
    @TableField(value = "name_")
    private String name;

    @ApiModelProperty(value = "专家类别 1-事故灾难类专家 2-公共卫生类专家 3-社会安全类专家 4-综合类专家 5-其它专家")
    @TableField(value = "category_")
    private String category;

    @ApiModelProperty(value = "性别 0-女 1-男")
    @TableField(value = "gender_", updateStrategy = FieldStrategy.IGNORED)
    private Integer gender;

    @ApiModelProperty(value = "联系电话")
    @TableField(value = "phone_", updateStrategy = FieldStrategy.IGNORED)
    private String phone;

    @ApiModelProperty(value = "职称")
    @TableField(value = "job_title_", updateStrategy = FieldStrategy.IGNORED)
    private String jobTitle;

    @ApiModelProperty(value = "健康状况 1-健康、2-良好、3-一般、4-较差")
    @TableField(value = "health_", updateStrategy = FieldStrategy.IGNORED)
    private Integer health;

    @ApiModelProperty(value = "出生日期")
    @TableField(value = "birthday_", updateStrategy = FieldStrategy.IGNORED)
    private Date birthday;

    @ApiModelProperty(value = "身份证号码")
    @TableField(value = "id_card_", updateStrategy = FieldStrategy.IGNORED)
    private String idCard;

    @ApiModelProperty(value = "家庭住址")
    @TableField(value = "address_", updateStrategy = FieldStrategy.IGNORED)
    private String address;

    @ApiModelProperty(value = "工作单位")
    @TableField(value = "company_", updateStrategy = FieldStrategy.IGNORED)
    private String company;

    @ApiModelProperty(value = "参加工作时间")
    @TableField(value = "work_time_", updateStrategy = FieldStrategy.IGNORED)
    private Date workTime;

    @ApiModelProperty(value = "专家工作单位的上级主管部门")
    @TableField(value = "superior_department_", updateStrategy = FieldStrategy.IGNORED)
    private String superiorDepartment;

    @ApiModelProperty(value = "最高学历 1-博士、2-硕士、3-本科、4-大专、5-中专")
    @TableField(value = "highest_degree_", updateStrategy = FieldStrategy.IGNORED)
    private String highestDegree;

    @ApiModelProperty(value = "毕业院校")
    @TableField(value = "graduate_school_", updateStrategy = FieldStrategy.IGNORED)
    private String graduateSchool;

    @ApiModelProperty(value = "专业类别")
    @TableField(value = "major_category_", updateStrategy = FieldStrategy.IGNORED)
    private String majorCategory;

    @ApiModelProperty(value = "所属专家组,多个用逗号隔开")
    @TableField(value = "expert_group_", updateStrategy = FieldStrategy.IGNORED)
    private String expertGroup;

    @ApiModelProperty(value = "专长描述")
    @TableField(value = "specialty_", updateStrategy = FieldStrategy.IGNORED)
    private String specialty;

    @ApiModelProperty(value = "工作经历简述")
    @TableField(value = "work_experience_", updateStrategy = FieldStrategy.IGNORED)
    private String workExperience;

    @ApiModelProperty(value = "救援方向")
    @TableField(value = "rescue_direction_", updateStrategy = FieldStrategy.IGNORED)
    private String rescueDirection;

    @ApiModelProperty(value = "备注")
    @TableField(value = "remark_", updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "creator_id_")
    private Long creatorId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time_")
    private Date createTime;

    @ApiModelProperty(value = "修改人id")
    @TableField(value = "modify_id_")
    private Long modifyId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "modify_time_")
    private Date modifyTime;

    @ApiModelProperty(value = "是否删除 0存在,非0-删除")
    @TableLogic(value = "0")
    @TableField(value = "deleted_")
    private Integer deleted;


}
