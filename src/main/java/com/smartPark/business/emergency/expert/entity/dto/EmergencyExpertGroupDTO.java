package com.smartPark.business.emergency.expert.entity.dto;

import com.smartPark.business.emergency.expert.entity.EmergencyExpertGroup;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * EmergencyExpertGroup实体类DTO
 *
 * <AUTHOR>
 * @date 2023/04/23
 */

@Data
@Accessors(chain = true)
public class EmergencyExpertGroupDTO extends EmergencyExpertGroup {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public EmergencyExpertGroupDTO(EmergencyExpertGroup safeEmergencyExpertGroup) {
        //this.setName(safeEmergencyExpertGroup.getName());
    }
}
