package com.smartPark.business.emergency.expert.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.emergency.expert.entity.EmergencyExpert;
import com.smartPark.business.emergency.expert.entity.EmergencyExpertGroup;
import com.smartPark.business.emergency.expert.entity.EmergencyExpertRefExpertGroup;
import com.smartPark.business.emergency.expert.mapper.EmergencyExpertGroupMapper;
import com.smartPark.business.emergency.expert.service.EmergencyExpertGroupService;
import com.smartPark.business.emergency.expert.service.EmergencyExpertRefExpertGroupService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * EmergencyExpertGroup表服务实现类
 *
 * <AUTHOR>
 * @date 2023/04/23
 */
@Slf4j
@Service("safeEmergencyExpertGroupService")
public class EmergencyExpertGroupServiceImpl extends ServiceImpl
        <EmergencyExpertGroupMapper, EmergencyExpertGroup> implements EmergencyExpertGroupService {
    @Resource
    private CommonService commonService;
    @Resource
    private EmergencyExpertRefExpertGroupService emergencyExpertRefExpertGroupService;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");

        //查询应急专家组
        List<EmergencyExpertGroup> emergencyExpertGroups = new LambdaQueryChainWrapper<>(getBaseMapper()).in(EmergencyExpertGroup::getId, idList).list();
        StringJoiner nameSj = new StringJoiner(",");
        if (CollectionUtil.isNotEmpty(emergencyExpertGroups)) {
            for (EmergencyExpertGroup emergencyExpertGroup : emergencyExpertGroups) {
                nameSj.add(emergencyExpertGroup.getGroupName());
            }
        }

        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(EmergencyExpertGroup::getId, idList).update();
        //删除队伍与人员关联表
        emergencyExpertRefExpertGroupService.remove(new QueryWrapper<EmergencyExpertRefExpertGroup>().in("expert_group_id_", idList));

        sj.add("删除应急专家组，专家组名称：" + nameSj.toString());
        coreParamSj.add("删除应急专家组，专家组名称：" + nameSj.toString());

        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOne(EmergencyExpertGroup safeEmergencyExpertGroup) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");

        commonService.setCreateAndModifyInfo(safeEmergencyExpertGroup);
        validParamRequired(safeEmergencyExpertGroup);
        validRepeat(safeEmergencyExpertGroup);
        validParamFormat(safeEmergencyExpertGroup);
        safeEmergencyExpertGroup.setMemberCount(0).setGroupCode(" ");
        save(safeEmergencyExpertGroup);
        LambdaUpdateChainWrapper<EmergencyExpertGroup> updateChainWrapper = new LambdaUpdateChainWrapper<>(baseMapper).eq(EmergencyExpertGroup::getId, safeEmergencyExpertGroup.getId()).set(EmergencyExpertGroup::getGroupCode, generateTeamCode(safeEmergencyExpertGroup.getId()));

        if (!ObjectUtils.isEmpty(safeEmergencyExpertGroup.getEmergencyExpertList())) {
            // 去重
            safeEmergencyExpertGroup.setEmergencyExpertList(new ArrayList<>(new LinkedHashSet<>(safeEmergencyExpertGroup.getEmergencyExpertList())));
            //更新人员数量
            updateChainWrapper.set(EmergencyExpertGroup::getMemberCount, safeEmergencyExpertGroup.getEmergencyExpertList().size());
            //插入到队伍与人员关联表
            saveRefMembers(safeEmergencyExpertGroup);
        }
        updateChainWrapper.update();

        //更新专家表safe_emergency_expert 的所属专家组字段expert_group_，逗号分割
        if (!ObjectUtils.isEmpty(safeEmergencyExpertGroup.getEmergencyExpertList())) {
            Set<Integer> set = safeEmergencyExpertGroup.getEmergencyExpertList().stream().map(EmergencyExpert::getId).collect(Collectors.toSet());
            baseMapper.updateExpertsGroupByIds(set);
        }

        sj.add("新增应急专家组，专家组名称：" + safeEmergencyExpertGroup.getGroupName());
        coreParamSj.add("新增应急专家组，专家组名称：" + safeEmergencyExpertGroup.getGroupName());

        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return true;
    }


    private void saveRefMembers(EmergencyExpertGroup emergencyExpertGroup) {
        if (!ObjectUtils.isEmpty(emergencyExpertGroup.getEmergencyExpertList())) {
            Collection<EmergencyExpertRefExpertGroup> list = new ArrayList<>();
            for (EmergencyExpert emergencyExpert : emergencyExpertGroup.getEmergencyExpertList()) {
                EmergencyExpertRefExpertGroup emergencyExpertRefExpertGroup = new EmergencyExpertRefExpertGroup();
                emergencyExpertRefExpertGroup.setExpertGroupId(emergencyExpertGroup.getId());
                emergencyExpertRefExpertGroup.setExpertId(emergencyExpert.getId());
                list.add(emergencyExpertRefExpertGroup);
            }
            emergencyExpertRefExpertGroupService.saveBatch(list);
        }
    }

    private String generateTeamCode(Integer id) {
        StringBuilder code = new StringBuilder(id.toString());
        //如果不足8位，前面补0
        if (code.length() < 8) {
            int length = 8 - code.length();
            for (int i = 0; i < length; i++) {
                code.insert(0, "0");
            }
        }
        return "YJZJZ" + code;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(EmergencyExpertGroup safeEmergencyExpertGroup) {
        Assert.notNull(safeEmergencyExpertGroup.getId(), "id不能为空");

        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        commonService.setModifyInfo(safeEmergencyExpertGroup);

        validRepeat(safeEmergencyExpertGroup);
        validParamFormat(safeEmergencyExpertGroup);
        if (ObjectUtils.isEmpty(safeEmergencyExpertGroup.getEmergencyExpertList())) {
            safeEmergencyExpertGroup.setMemberCount(0);
        } else {
            // 去重
            safeEmergencyExpertGroup.setEmergencyExpertList(new ArrayList<>(new LinkedHashSet<>(safeEmergencyExpertGroup.getEmergencyExpertList())));
            safeEmergencyExpertGroup.setMemberCount(safeEmergencyExpertGroup.getEmergencyExpertList().size());
        }
        updateById(safeEmergencyExpertGroup);
        //删除原来的关联关系
        QueryWrapper<EmergencyExpertRefExpertGroup> qw = new QueryWrapper<EmergencyExpertRefExpertGroup>().eq("expert_group_id_", safeEmergencyExpertGroup.getId());
        qw.select("id_", "expert_id_");
        List<EmergencyExpertRefExpertGroup> refs = emergencyExpertRefExpertGroupService.list(qw);
        if (!refs.isEmpty()) {
            emergencyExpertRefExpertGroupService.removeByIds(refs.stream().map(EmergencyExpertRefExpertGroup::getId).collect(Collectors.toList()));
        }
        //插入到队伍与人员关联表
        saveRefMembers(safeEmergencyExpertGroup);

        // 查询受影响的专家id
        Set<Integer> expertIds = refs.stream().map(EmergencyExpertRefExpertGroup::getExpertId).collect(Collectors.toSet());
        if (safeEmergencyExpertGroup.getEmergencyExpertList() != null) {
            expertIds.addAll(safeEmergencyExpertGroup.getEmergencyExpertList().stream().map(EmergencyExpert::getId).collect(Collectors.toSet()));
        }
        if (!expertIds.isEmpty()) {
            baseMapper.updateExpertsGroupByIds(expertIds);
        }

        sj.add("修改应急专家组，专家组名称：" + safeEmergencyExpertGroup.getGroupName());
        coreParamSj.add("修改应急专家组，专家组名称：" + safeEmergencyExpertGroup.getGroupName());
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return true;
    }

    @Override
    public IPage<EmergencyExpertGroup> selectPage(Page page, EmergencyExpertGroup safeEmergencyExpertGroup) {
        return baseMapper.selectPage(page, safeEmergencyExpertGroup);
    }

    @Override
    public void export(EmergencyExpertGroup safeEmergencyExpertGroup, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public void updatePersonCount(List<Integer> teamIds) {
        // 队伍id 更新队伍人数
        for (Integer teamId : teamIds) {
            updateById(new EmergencyExpertGroup().setId(teamId).setMemberCount(emergencyExpertRefExpertGroupService.count(new QueryWrapper<EmergencyExpertRefExpertGroup>().eq("expert_group_id_", teamId))));
        }
    }

    @Override
    public EmergencyExpertGroup getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(EmergencyExpertGroup safeEmergencyExpertGroup) {
        List<EmergencyExpertGroup> list = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(EmergencyExpertGroup::getGroupName, safeEmergencyExpertGroup.getGroupName())
                .list();
        if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(safeEmergencyExpertGroup.getId()) || !safeEmergencyExpertGroup.getId().equals(list.get(0).getId()))) {
            throw new BusinessException("名称重复");
        }
    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(EmergencyExpertGroup safeEmergencyExpertGroup) {
        //Assert.notNull(safeEmergencyExpertGroup, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(safeEmergencyExpertGroup.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(EmergencyExpertGroup safeEmergencyExpertGroup) {
        //Assert.isTrue(safeEmergencyExpertGroup.getName() == null || safeEmergencyExpertGroup.getName().length() <= 50,
        //        "名称超长");
    }

    @Override
    public IPage<EmergencyExpert> getMemberPage(Page page, EmergencyExpertGroup emergencyExpertGroup) {
        return baseMapper.getMemberPage(page, emergencyExpertGroup);
    }
}

