package com.smartPark.business.emergency.expert.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.smartPark.business.emergency.person.entity.EmergencyPerson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应急专家组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_emergency_expert_group")
@ApiModel(value = "EmergencyExpertGroup对象", description = "应急专家组")
public class EmergencyExpertGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id_", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "专家组编号 YJZJ+8位序号")
    @TableField(value = "group_code_")
    private String groupCode;

    @ApiModelProperty(value = "专家组名称")
    @TableField(value = "group_name_")
    private String groupName;

    @ApiModelProperty(value = "组长")
    @TableField(value = "leader_", updateStrategy = FieldStrategy.IGNORED)
    private String leader;

    @ApiModelProperty(value = "第一副组长")
    @TableField(value = "deputy_leader_", updateStrategy = FieldStrategy.IGNORED)
    private String deputyLeader;

    @ApiModelProperty(value = "第二副组长")
    @TableField(value = "deputy_second_leader_", updateStrategy = FieldStrategy.IGNORED)

    private String deputySecondLeader;

    @ApiModelProperty(value = "工作内容")
    @TableField(value = "work_content_", updateStrategy = FieldStrategy.IGNORED)
    private String workContent;

    @ApiModelProperty(value = "成员数量")
    @TableField(value = "member_count_")
    private Integer memberCount;

    @ApiModelProperty(value = "备注")
    @TableField(value = "remark_", updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "creator_id_")
    private Long creatorId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time_")
    private Date createTime;

    @ApiModelProperty(value = "修改人id")
    @TableField(value = "modify_id_")
    private Long modifyId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "modify_time_")
    private Date modifyTime;

    @ApiModelProperty(value = "是否删除 0存在,非0-删除")
    @TableLogic(value = "0")
    @TableField(value = "deleted_")
    private Integer deleted;

    /**
     * 应急人员列表
     */
    @TableField(exist = false)
    private List<EmergencyExpert> emergencyExpertList;


}
