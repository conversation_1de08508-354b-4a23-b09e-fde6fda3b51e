package com.smartPark.business.emergency.supply.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 应急物资
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_emergency_supply")
@ApiModel(value = "EmergencySupply对象", description = "应急物资")
public class EmergencySupply implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id_", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "物资编号 YJWZ+8位序号")
    @TableField(value = "supply_code_")
    private String supplyCode;

    @ApiModelProperty(value = "物资名称")
    @TableField(value = "name_")
    private String name;

    @ApiModelProperty(value = "类别,取数据字典")
    @TableField(value = "category_")
    private String category;

    @ApiModelProperty(value = "规格型号")
    @TableField(value = "specification_", updateStrategy = FieldStrategy.IGNORED)
    private String specification;

    @ApiModelProperty(value = "计量单位")
    @TableField(value = "unit_", updateStrategy = FieldStrategy.IGNORED)
    private String unit;

    @ApiModelProperty(value = "物资描述")
    @TableField(value = "description_", updateStrategy = FieldStrategy.IGNORED)
    private String description;

    @ApiModelProperty(value = "物资数量")
    @TableField(value = "quantity_")
    private Integer quantity;


    @ApiModelProperty(value = "备注")
    @TableField(value = "remark_", updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    @ApiModelProperty(value = "创建人id")
    @TableField(value = "creator_id_")
    private Long creatorId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time_")
    private Date createTime;

    @ApiModelProperty(value = "修改人id")
    @TableField(value = "modify_id_")
    private Long modifyId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "modify_time_")
    private Date modifyTime;

    @ApiModelProperty(value = "是否删除 0存在,非0-删除")
    @TableLogic(value = "0")
    @TableField(value = "deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private Integer libraryId;

    /**
     * 所在应急库信息
     */
    @TableField(exist = false)
    private List<EmergencySupplyLibrary> emergencySupplyLibraryList;


}
