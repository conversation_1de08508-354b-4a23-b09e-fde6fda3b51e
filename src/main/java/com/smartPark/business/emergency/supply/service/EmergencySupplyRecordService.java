package com.smartPark.business.emergency.supply.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.supply.entity.EmergencySupplyRecord;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencySupplyRecord表服务接口
 *
 * <AUTHOR>
 * @date 2023/04/25
 */
public interface EmergencySupplyRecordService extends IService<EmergencySupplyRecord> {

    /**
     * 新增
     *
     * @param safeEmergencySupplyRecord 实体对象
     * @return 操作结果
     */
    boolean saveOne(EmergencySupplyRecord safeEmergencySupplyRecord);

    /**
     * 修改单条
     *
     * @param safeEmergencySupplyRecord 实体对象
     * @return 修改结果
     */
    boolean updateOne(EmergencySupplyRecord safeEmergencySupplyRecord);

    /**
     * 查询分页
     *
     * @param page                      分页对象
     * @param safeEmergencySupplyRecord 分页参数对象
     * @return 查询分页结果
     */
    IPage<EmergencySupplyRecord> selectPage(Page page, EmergencySupplyRecord safeEmergencySupplyRecord);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencySupplyRecord getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param safeEmergencySupplyRecord 过滤条件实体对象
     * @param request                   请求
     * @param response                  响应
     */
    void export(EmergencySupplyRecord safeEmergencySupplyRecord, HttpServletRequest request, HttpServletResponse response);

}

