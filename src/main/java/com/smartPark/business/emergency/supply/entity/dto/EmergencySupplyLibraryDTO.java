package com.smartPark.business.emergency.supply.entity.dto;

import com.smartPark.business.emergency.supply.entity.EmergencySupplyLibrary;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * EmergencySupplyLibrary实体类DTO
 *
 * <AUTHOR>
 * @date 2023/04/23
 */

@Data
@Accessors(chain = true)
public class EmergencySupplyLibraryDTO extends EmergencySupplyLibrary {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public EmergencySupplyLibraryDTO(EmergencySupplyLibrary safeEmergencySupplyLibrary) {
        //this.setName(safeEmergencySupplyLibrary.getName());
    }
}
