package com.smartPark.business.emergency.supply.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.smartPark.common.entity.device.ObjInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应急物资储备库大数据平台
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_supply_library_big_data")
@ApiModel(value = "SupplyLibraryBigData对象", description = "应急物资储备库大数据平台")
public class SupplyLibraryBigData implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id_", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "部件信息id")
    @TableField(value = "obj_id_")
    private String objId;

    @TableField(exist = false)
    private ObjInfo objInfo;


    @ApiModelProperty(value = "创建人id")
    @TableField(value = "creator_id_")
    private Long creatorId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time_")
    private Date createTime;

    @ApiModelProperty(value = "修改人id")
    @TableField(value = "modify_id_")
    private Long modifyId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "modify_time_")
    private Date modifyTime;

    @ApiModelProperty(value = "是否删除 0存在,非0-删除")
    @TableLogic(value = "0")
    @TableField(value = "deleted_")
    private Integer deleted;


}
