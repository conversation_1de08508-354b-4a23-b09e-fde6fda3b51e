package com.smartPark.business.emergency.supply.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.supply.entity.EmergencySupplyRefLibrary;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencySupplyRefLibrary表服务接口
 *
 * <AUTHOR>
 * @date 2023/04/24
 */
public interface EmergencySupplyRefLibraryService extends IService<EmergencySupplyRefLibrary> {

    /**
     * 新增
     *
     * @param safeEmergencySupplyRefLibrary 实体对象
     * @return 操作结果
     */
    boolean saveOne(EmergencySupplyRefLibrary safeEmergencySupplyRefLibrary);

    /**
     * 修改单条
     *
     * @param safeEmergencySupplyRefLibrary 实体对象
     * @return 修改结果
     */
    boolean updateOne(EmergencySupplyRefLibrary safeEmergencySupplyRefLibrary);

    /**
     * 查询分页
     *
     * @param page                          分页对象
     * @param safeEmergencySupplyRefLibrary 分页参数对象
     * @return 查询分页结果
     */
    IPage<EmergencySupplyRefLibrary> selectPage(Page page, EmergencySupplyRefLibrary safeEmergencySupplyRefLibrary);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencySupplyRefLibrary getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param safeEmergencySupplyRefLibrary 过滤条件实体对象
     * @param request                       请求
     * @param response                      响应
     */
    void export(EmergencySupplyRefLibrary safeEmergencySupplyRefLibrary, HttpServletRequest request, HttpServletResponse response);

}

