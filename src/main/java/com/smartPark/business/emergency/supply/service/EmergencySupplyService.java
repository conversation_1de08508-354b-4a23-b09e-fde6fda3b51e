package com.smartPark.business.emergency.supply.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.supply.entity.EmergencySupply;
import com.smartPark.business.emergency.supply.entity.EmergencySupplyRecord;
import com.smartPark.business.emergency.supply.entity.EmergencySupplyRefLibrary;
import com.smartPark.common.base.model.RequestModel;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencySupply表服务接口
 *
 * <AUTHOR>
 * @date 2023/04/24
 */
public interface EmergencySupplyService extends IService<EmergencySupply> {

    /**
     * 新增
     *
     * @param safeEmergencySupply 实体对象
     * @return 操作结果
     */
    boolean saveOne(EmergencySupply safeEmergencySupply);

    /**
     * 修改单条
     *
     * @param safeEmergencySupply 实体对象
     * @return 修改结果
     */
    boolean updateOne(EmergencySupply safeEmergencySupply);

    /**
     * 查询分页
     *
     * @param page                分页对象
     * @param safeEmergencySupply 分页参数对象
     * @return 查询分页结果
     */
    IPage<EmergencySupply> selectPage(Page page, EmergencySupply safeEmergencySupply);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencySupply getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param safeEmergencySupply 过滤条件实体对象
     * @param request             请求
     * @param response            响应
     */
    void export(EmergencySupply safeEmergencySupply, HttpServletRequest request, HttpServletResponse response);

    /**
     * 新增出入库记录
     *
     * @param emergencySupplyRecord 出入库记录
     * @return
     */
    boolean insertInOutRecord(EmergencySupplyRecord emergencySupplyRecord);

    /**
     * 分页查询出入库记录
     *
     * @param emergencySupplyRecord
     * @return
     */
    IPage<EmergencySupplyRecord> selectInOutRecordPage(RequestModel<EmergencySupplyRecord> emergencySupplyRecord);

    /**
     * 分页物资分布
     *
     * @param requestModel 分页参数
     * @return
     */
    IPage<EmergencySupplyRefLibrary> selectDistributionPage(RequestModel<EmergencySupply> requestModel);

    /**
     * 根据id批量删除出入库记录
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    void deleteInOutRecord(List<Integer> idList);
}

