package com.smartPark.business.emergency.plan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.plan.entity.EmergencyPlanExecutor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * EmergencyPlanExecutor表服务接口
 *
 * <AUTHOR>
 * @date 2023/09/19
 */
public interface EmergencyPlanExecutorService extends IService<EmergencyPlanExecutor> {

    /**
     * 新增
     *
     * @param safeEmergencyPlanExecutor 实体对象
     * @return 操作结果
     */
    boolean saveOne(EmergencyPlanExecutor safeEmergencyPlanExecutor);

    /**
     * 修改单条
     *
     * @param safeEmergencyPlanExecutor 实体对象
     * @return 修改结果
     */
    boolean updateOne(EmergencyPlanExecutor safeEmergencyPlanExecutor);

    /**
     * 查询分页
     *
     * @param page        分页对象
     * @param safeEmergencyPlanExecutor 分页参数对象
     * @return 查询分页结果
     */
    IPage<EmergencyPlanExecutor> selectPage(Page page, EmergencyPlanExecutor safeEmergencyPlanExecutor);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    EmergencyPlanExecutor getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param safeEmergencyPlanExecutor 过滤条件实体对象
     * @param request     请求
     * @param response    响应
     */
    void export(EmergencyPlanExecutor safeEmergencyPlanExecutor, HttpServletRequest request, HttpServletResponse response);

}

