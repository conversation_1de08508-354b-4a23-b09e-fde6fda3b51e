package com.smartPark.business.emergency.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.security.context.BaseUserContextProducer;

import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;


import com.smartPark.business.emergency.plan.mapper.EmergencyPlanEventRefMapper;
import com.smartPark.business.emergency.plan.entity.EmergencyPlanEventRef;
import com.smartPark.business.emergency.plan.entity.dto.EmergencyPlanEventRefDTO;
import com.smartPark.business.emergency.plan.service.EmergencyPlanEventRefService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * EmergencyPlanEventRef表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/19
 */
@Slf4j
@Service
public class EmergencyPlanEventRefServiceImpl extends ServiceImpl
        <EmergencyPlanEventRefMapper, EmergencyPlanEventRef> implements EmergencyPlanEventRefService {
    @Resource
    private CommonService commonService;

    @Override
    public boolean removeById(Serializable id) {
    return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(EmergencyPlanEventRef::getId, idList).update();
        return true;
    }


    @Override
    public boolean saveOne(EmergencyPlanEventRef safeEmergencyPlanEventRef) {
        commonService.setCreateAndModifyInfo(safeEmergencyPlanEventRef);

        validParamRequired(safeEmergencyPlanEventRef);
        validRepeat(safeEmergencyPlanEventRef);
        validParamFormat(safeEmergencyPlanEventRef);
        return save(safeEmergencyPlanEventRef);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(EmergencyPlanEventRef safeEmergencyPlanEventRef) {
        Assert.notNull(safeEmergencyPlanEventRef.getId(), "id不能为空");
        commonService.setModifyInfo(safeEmergencyPlanEventRef);

        validRepeat(safeEmergencyPlanEventRef);
        validParamFormat(safeEmergencyPlanEventRef);
        return updateById(safeEmergencyPlanEventRef);
    }

    @Override
    public IPage<EmergencyPlanEventRef> selectPage(Page page, EmergencyPlanEventRef safeEmergencyPlanEventRef) {
        return baseMapper.selectPage(page, safeEmergencyPlanEventRef);
    }

    @Override
    public void export(EmergencyPlanEventRef safeEmergencyPlanEventRef, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public EmergencyPlanEventRef getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(EmergencyPlanEventRef safeEmergencyPlanEventRef) {

        /* List<EmergencyPlanEventRef> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(EmergencyPlanEventRef::getDeviceCode, safeEmergencyPlanEventRef.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(safeEmergencyPlanEventRef.getId()) || !safeEmergencyPlanEventRef.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("名称重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(EmergencyPlanEventRef safeEmergencyPlanEventRef) {
        //Assert.notNull(safeEmergencyPlanEventRef, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(safeEmergencyPlanEventRef.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(EmergencyPlanEventRef safeEmergencyPlanEventRef) {
        //Assert.isTrue(safeEmergencyPlanEventRef.getName() == null || safeEmergencyPlanEventRef.getName().length() <= 50,
        //        "名称超长");
    }
}

