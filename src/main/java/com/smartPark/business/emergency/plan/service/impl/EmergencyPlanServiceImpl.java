package com.smartPark.business.emergency.plan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.emergency.event.entity.EmergencyEvent;
import com.smartPark.business.emergency.event.entity.vo.EmergencyEventVo;
import com.smartPark.business.emergency.event.service.EmergencyEventService;
import com.smartPark.business.emergency.plan.entity.EmergencyPlan;
import com.smartPark.business.emergency.plan.entity.EmergencyPlanEventRef;
import com.smartPark.business.emergency.plan.entity.EmergencyPlanExecutor;
import com.smartPark.business.emergency.plan.entity.EmergencyPlanTask;
import com.smartPark.business.emergency.plan.entity.dto.EmergencyPlanDTO;
import com.smartPark.business.emergency.plan.entity.dto.EmergencyPlanTaskDTO;
import com.smartPark.business.emergency.plan.entity.dto.PlanEventTypeDTO;
import com.smartPark.business.emergency.plan.entity.vo.EmergencyPlanTaskVo;
import com.smartPark.business.emergency.plan.entity.vo.EmergencyPlanVo;
import com.smartPark.business.emergency.plan.mapper.EmergencyPlanEventRefMapper;
import com.smartPark.business.emergency.plan.mapper.EmergencyPlanMapper;
import com.smartPark.business.emergency.plan.service.EmergencyPlanExecutorService;
import com.smartPark.business.emergency.plan.service.EmergencyPlanService;
import com.smartPark.business.emergency.plan.service.EmergencyPlanTaskService;
import com.smartPark.common.alarm.entity.Alarm;
import com.smartPark.common.alarm.entity.EventType;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.CommonConstant;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.utils.BusinessSerialNoUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * EmergencyPlan表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/19
 */
@Slf4j
@Service
public class EmergencyPlanServiceImpl extends ServiceImpl
        <EmergencyPlanMapper, EmergencyPlan> implements EmergencyPlanService {
    @Resource
    private CommonService commonService;

    @Resource
    private EmergencyPlanTaskService planTaskService;

    @Resource
    private EmergencyPlanEventRefMapper planEventRefMapper;

    @Resource
    private EmergencyPlanExecutorService planExecutorService;

    @Resource
    private EmergencyEventService emergencyEventService;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");
        String nameStr = null;

        //查询预案
        List<EmergencyPlan> planList = baseMapper.selectBatchIds(idList);
        if (CollectionUtil.isNotEmpty(planList)) {
            nameStr = planList.stream().map(EmergencyPlan::getPlanName).collect(Collectors.joining(","));
        }


        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(EmergencyPlan::getId, idList).update();

        //先删除原先的关联事件
        QueryWrapper<EmergencyPlanEventRef> planEventRefQw = new QueryWrapper<>();
        planEventRefQw.in("plan_id_", idList);
        planEventRefMapper.delete(planEventRefQw);

        //删除原先的预案任务/执行人
        QueryWrapper<EmergencyPlanTask> planTaskQw = new QueryWrapper<>();
        planTaskQw.in("plan_id_", idList);
        List<EmergencyPlanTask> planTaskList = planTaskService.list(planTaskQw);
        if (CollectionUtil.isNotEmpty(planTaskList)) {
            List<Long> planTaskIdList = planTaskList.stream().map(EmergencyPlanTask::getId).collect(Collectors.toList());
            planTaskService.removeByIds(planTaskIdList);
            QueryWrapper<EmergencyPlanExecutor> planExecutorQw = new QueryWrapper<>();
            planExecutorQw.in("plan_task_id_", planTaskIdList);
            planExecutorService.remove(planExecutorQw);
        }

        sj.add("删除预案，预案名称：[" + nameStr + "]");
        coreParamSj.add("删除预案，预案名称：[" + nameStr + "]");
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return true;
    }


    @Override
    public boolean saveOne(EmergencyPlan safeEmergencyPlan) {
        commonService.setCreateAndModifyInfo(safeEmergencyPlan);

        validParamRequired(safeEmergencyPlan);
        validRepeat(safeEmergencyPlan);
        validParamFormat(safeEmergencyPlan);
        return save(safeEmergencyPlan);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(EmergencyPlan safeEmergencyPlan) {
        Assert.notNull(safeEmergencyPlan.getId(), "id不能为空");
        commonService.setModifyInfo(safeEmergencyPlan);

        validRepeat(safeEmergencyPlan);
        validParamFormat(safeEmergencyPlan);
        return updateById(safeEmergencyPlan);
    }

    @Override
    public IPage<EmergencyPlan> selectPage(Page page, EmergencyPlan safeEmergencyPlan) {
        return baseMapper.selectPage(page, safeEmergencyPlan);
    }

    @Override
    public void export(EmergencyPlan safeEmergencyPlan, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public EmergencyPlanVo addPlan(EmergencyPlanVo planVo) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");

        EmergencyPlan plan = planVo.getPlan();
        List<PlanEventTypeDTO> eventTypeList = planVo.getPlanEventTypeList();
        List<EmergencyPlanTaskVo> planTaskVoList = planVo.getPlanTaskList();

        //校验必填
        validParamVoRequired(planVo);
        //校验重复
        //validRepeat(plan);

        //校验预案任务必填
        planTaskService.validPlanTaskRequired(planTaskVoList);

        //校验预案任务重复
        planTaskService.validPlanTaskRepeat(planTaskVoList);

        //应急预案的任务按名称转为map
        Map<String, EmergencyPlanTaskVo> taskVoMap = planTaskVoList.stream().collect(Collectors.toMap(EmergencyPlanTaskVo::getTaskName, Function.identity()));

        //初始化
        commonService.setCreateAndModifyInfo(plan);

        //生成code
        String code = BusinessSerialNoUtil.genSeqCode(redisUtil, "YJYA", "", 8, RedisConstant.APP_PRE + "YJYA", "0", CommonConstant.COMPLET_BEFORE);
        plan.setPlanCode(code);

        //保存预案
        save(plan);

        //保存预案关联事件
        eventTypeList.forEach(eventType -> {
            EmergencyPlanEventRef planEventRef = new EmergencyPlanEventRef();
            planEventRef.setEventLevel(eventType.getDefaultAlarmLevel());
            planEventRef.setEventTypeCode(eventType.getCode());
            planEventRef.setPlanId(plan.getId());
            commonService.setCreateAndModifyInfo(planEventRef);
            planEventRefMapper.insert(planEventRef);
        });

        //保存预案任务
        planTaskVoList.forEach(planTaskVo -> {
            EmergencyPlanTask planTask = new EmergencyPlanTask();
            BeanUtil.copyProperties(planTaskVo, planTask);
            planTask.setPlanId(plan.getId());
            commonService.setCreateAndModifyInfo(planTask);
            planTaskService.save(planTask);
            //保存预案任务执行人
            planTaskVo.getPlanTaskExecutorList().forEach(planTaskExecutor -> {
                planTaskExecutor.setPlanTaskId(planTask.getId());
                commonService.setCreateAndModifyInfo(planTaskExecutor);
                planExecutorService.save(planTaskExecutor);
            });
        });

        //更新预案任务前置任务信息
        updateTaskBeforeInfo(plan.getId(), taskVoMap);

        sj.add("新增预案，预案名称：" + plan.getPlanName());
        coreParamSj.add("新增预案，预案名称：" + plan.getPlanName());
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return planDetail(plan.getId());
    }

    /**
     * 更新预案任务前置任务信息
     *
     * @param planId    预案id
     * @param taskVoMap 预案任务map
     */
    private void updateTaskBeforeInfo(Long planId, Map<String, EmergencyPlanTaskVo> taskVoMap) {
        //查询应急方案任务
        QueryWrapper<EmergencyPlanTask> taskQw = new QueryWrapper<>();
        taskQw.eq("plan_id_", planId);
        List<EmergencyPlanTask> newTaskList = planTaskService.list(taskQw);

        //根据入参数,处理前置任务信息
        newTaskList.forEach(task -> {
            EmergencyPlanTaskVo taskVo = taskVoMap.get(task.getTaskName());
            String beforeTaskNames = taskVo.getBeforeTaskNames();
            List<String> preTaskNameList = StringUtils.isNotBlank(beforeTaskNames) ? CollectionUtil.newArrayList(beforeTaskNames.split(",")) : null;
            if (CollectionUtil.isNotEmpty(preTaskNameList)) {
                List<EmergencyPlanTask> preTaskList = newTaskList.stream().filter(t -> preTaskNameList.contains(t.getTaskName())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(preTaskList)) {
                    String preTaskIds = preTaskList.stream().map(t -> String.valueOf(t.getId())).collect(Collectors.joining(","));
                    task.setBeforeTaskIds(preTaskIds);
                    commonService.setModifyInfo(task);
                    planTaskService.updateById(task);
                }
            }
        });
    }

    @Override
    public EmergencyPlanVo planDetail(Serializable id) {
        Assert.notNull(id, "预案id不能为空");
        //获取预案
        EmergencyPlan plan = getById(id);

        //获取预案关联事件
        List<PlanEventTypeDTO> eventTypeList = planEventRefMapper.getEventTypeListByPlanId(id);

        //获取预案任务
        List<EmergencyPlanTaskVo> planTaskVoList = new ArrayList<>();
        List<EmergencyPlanTaskDTO> planTaskDtoList = planTaskService.getTaskDtoListByPlanId(id);
        if (CollectionUtil.isNotEmpty(planTaskDtoList)) {
            //获取预案任务执行人,根据执行人名称去重复
            planTaskDtoList.stream().forEach(planTaskDto -> {
                List<EmergencyPlanExecutor> planTaskExecutorList = planTaskDto.getPlanTaskExecutorList();
                if (CollectionUtil.isNotEmpty(planTaskExecutorList)) {
                    List<EmergencyPlanExecutor> newPlanTaskExecutorList = planTaskExecutorList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new java.util.TreeSet<>(Comparator.comparing(EmergencyPlanExecutor::getExecutorName))), ArrayList::new));
                    planTaskDto.setPlanTaskExecutorList(newPlanTaskExecutorList);
                }
            });

            planTaskVoList = BeanUtil.copyToList(planTaskDtoList, EmergencyPlanTaskVo.class);
        }

        //组装vo
        EmergencyPlanVo vo = new EmergencyPlanVo();
        vo.setPlan(plan);
        vo.setPlanEventTypeList(eventTypeList);
        vo.setPlanTaskList(planTaskVoList);

        return vo;
    }

    @Override
    public EmergencyPlanVo updatePlan(EmergencyPlanVo planVo) {
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");

        EmergencyPlan plan = planVo.getPlan();
        Assert.notNull(plan, "预案信息为空");
        Assert.notNull(plan.getId(), "预案id不能为空");
        List<PlanEventTypeDTO> eventTypeList = planVo.getPlanEventTypeList();
        List<EmergencyPlanTaskVo> planTaskVoList = planVo.getPlanTaskList();

        //校验必填
        validParamVoRequired(planVo);

        //校验预案任务必填
        planTaskService.validPlanTaskRequired(planTaskVoList);

        //校验预案任务重复
        planTaskService.validPlanTaskRepeat(planTaskVoList);

        //应急预案的任务按名称转为map
        Map<String, EmergencyPlanTaskVo> taskVoMap = planTaskVoList.stream().collect(Collectors.toMap(EmergencyPlanTaskVo::getTaskName, Function.identity()));

        //查询
        EmergencyPlan oldPlan = getById(plan.getId());
        Assert.notNull(oldPlan, "预案不存在");

        //初始化
        commonService.setModifyInfo(plan);

        //先删除原先的关联事件
        QueryWrapper<EmergencyPlanEventRef> planEventRefQw = new QueryWrapper<>();
        planEventRefQw.eq("plan_id_", plan.getId());
        planEventRefMapper.delete(planEventRefQw);

        //删除原先的预案任务/执行人
        QueryWrapper<EmergencyPlanTask> planTaskQw = new QueryWrapper<>();
        planTaskQw.eq("plan_id_", plan.getId());
        List<EmergencyPlanTaskDTO> planTaskDTOList = planTaskService.getTaskDtoListByPlanId(plan.getId());
        if (CollectionUtil.isNotEmpty(planTaskDTOList)) {
            List<Long> planTaskIdList = planTaskDTOList.stream().map(EmergencyPlanTaskDTO::getId).collect(Collectors.toList());
            planTaskService.removeByIds(planTaskIdList);
            QueryWrapper<EmergencyPlanExecutor> planExecutorQw = new QueryWrapper<>();
            planExecutorQw.in("plan_task_id_", planTaskIdList);
            planExecutorService.remove(planExecutorQw);
        }

        //更新预案
        baseMapper.updateById(plan);

        //保存预案关联事件
        eventTypeList.forEach(eventType -> {
            EmergencyPlanEventRef planEventRef = new EmergencyPlanEventRef();
            planEventRef.setEventLevel(eventType.getDefaultAlarmLevel());
            planEventRef.setEventTypeCode(eventType.getCode());
            planEventRef.setPlanId(plan.getId());
            commonService.setCreateAndModifyInfo(planEventRef);
            planEventRefMapper.insert(planEventRef);
        });

        //保存预案任务
        planTaskVoList.forEach(planTaskVo -> {
            EmergencyPlanTask planTask = new EmergencyPlanTask();
            BeanUtil.copyProperties(planTaskVo, planTask);
            planTask.setPlanId(plan.getId());
            commonService.setCreateAndModifyInfo(planTask);
            planTaskService.save(planTask);
            //保存预案任务执行人
            planTaskVo.getPlanTaskExecutorList().forEach(planTaskExecutor -> {
                planTaskExecutor.setPlanTaskId(planTask.getId());
                commonService.setCreateAndModifyInfo(planTaskExecutor);
                planExecutorService.save(planTaskExecutor);
            });
        });

        //更新预案任务前置任务信息
        updateTaskBeforeInfo(plan.getId(), taskVoMap);

        sj.add("修改预案，预案名称：" + plan.getPlanName());
        coreParamSj.add("修改预案，预案名称：" + plan.getPlanName());
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        return planDetail(plan.getId());
    }

    @Override
    public List<EmergencyPlanTaskVo> planTaskFlowChart(Serializable id) {
        Assert.notNull(id, "预案id不能为空");
        List<EmergencyPlanTaskDTO> planTaskDtoList = planTaskService.getTaskDtoListByPlanId(id);
        List<EmergencyPlanTaskVo> planTaskVoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(planTaskDtoList)) {
            planTaskVoList = BeanUtil.copyToList(planTaskDtoList, EmergencyPlanTaskVo.class);
        }
        return planTaskVoList;
    }

    @Override
    public IPage<EmergencyPlanDTO> selectDtoPage(Page page, EmergencyPlanVo planVo) {
        IPage<EmergencyPlanDTO> dtoPage = baseMapper.selectDtoPage(page, planVo);

        return dtoPage;
    }

    @Override
    public RestMessage getPlanByEventTypeLevel(EmergencyEventVo eventVo) {
        Assert.notNull(eventVo, "参数不能为空");
        //Assert.notNull(eventVo.getEvent(), "事件信息不能为空");

        String eventType = null;
        Integer eventLevel = null;
        EmergencyEvent emergencyEvent = eventVo.getEvent();
        if (emergencyEvent != null){
            eventType = emergencyEvent.getEventType();
            eventLevel = emergencyEvent.getEventLevel();
        }

        //List<EmergencyPlan> planList = baseMapper.queryPlanListBy(eventType, eventLevel);
        //根据告警类型,告警级别查询应急预案,包含下级等级，与原逻辑一致
        Alarm alarm = new Alarm();
        alarm.setAlarmType(eventType);
        alarm.setLevel(eventLevel);

        List<EmergencyPlan> planList = emergencyEventService.queryHighLevelEmergencyPlan(alarm);

        return RestBuilders.successBuilder().data(planList).build();
    }

    @Override
    public EmergencyPlan getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(EmergencyPlan safeEmergencyPlan) {

        /* List<EmergencyPlan> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(EmergencyPlan::getDeviceCode, safeEmergencyPlan.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(safeEmergencyPlan.getId()) || !safeEmergencyPlan.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("名称重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(EmergencyPlan plan) {
        //Assert.notNull(plan, "预案信息不能为空");
        //Assert.isTrue(StringUtils.isNotBlank(plan.getPlanType()), "预案分类为空");
    }

    /**
     * 校验参数必填
     */
    private void validParamVoRequired(EmergencyPlanVo planVo) {
        EmergencyPlan plan = planVo.getPlan();
        List<PlanEventTypeDTO> eventTypeList = planVo.getPlanEventTypeList();
        List<EmergencyPlanTaskVo> planTaskVoList = planVo.getPlanTaskList();

        Assert.notNull(plan, "预案信息不能为空");
        Assert.isTrue(StringUtils.isNotBlank(plan.getPlanName()), "预案名称为空");
        Assert.isTrue(StringUtils.isNotBlank(plan.getPlanType()), "预案分类为空");
        Assert.isTrue(CollectionUtil.isNotEmpty(eventTypeList), "预案关联事件为空");
        Assert.isTrue(CollectionUtil.isNotEmpty(planTaskVoList), "预案任务为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(EmergencyPlan safeEmergencyPlan) {
        //Assert.isTrue(safeEmergencyPlan.getName() == null || safeEmergencyPlan.getName().length() <= 50,
        //        "名称超长");
    }
}

