package com.smartPark.business.emergency.plan.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.emergency.plan.entity.EmergencyTaskType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.emergency.team.entity.EmergencyTeam;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 应急预案任务类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
public interface EmergencyTaskTypeService extends IService<EmergencyTaskType> {

  IPage<EmergencyTaskType> selectPage(Page page, EmergencyTaskType customQueryParams);

  EmergencyTaskType getOneById(Serializable id);

  boolean saveOne(EmergencyTaskType taskType);

  boolean updateOne(EmergencyTaskType taskType);

  boolean deleteByIds(List<Long> idList);
}
