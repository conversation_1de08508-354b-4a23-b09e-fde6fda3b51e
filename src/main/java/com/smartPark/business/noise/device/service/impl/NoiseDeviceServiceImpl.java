package com.smartPark.business.noise.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.noise.device.entity.NoiseDevice;
import com.smartPark.business.noise.device.entity.vo.NoiseDeviceDTO;
import com.smartPark.business.noise.device.entity.vo.NoiseDeviceVo;
import com.smartPark.business.noise.device.mapper.NoiseDeviceMapper;
import com.smartPark.business.noise.device.service.NoiseDeviceService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.mapper.DeviceExtendInfoMapper;
import com.smartPark.common.device.mapper.DeviceMapper;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.device.util.DeviceUtils;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.*;
import com.smartPark.common.entity.device.dto.DeviceObjInfoDevicePropertyStatusListInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.monitor.service.MonitorService;
import com.smartPark.common.monitor.vo.MonitorQueryVO;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.rpc.RpcEnum;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 噪声环境设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
@Slf4j
public class NoiseDeviceServiceImpl extends ServiceImpl<NoiseDeviceMapper, NoiseDevice> implements NoiseDeviceService {
  private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(NoiseDeviceServiceImpl.class);

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private DeviceExtendInfoMapper deviceExtendInfoMapper;
  @Autowired
  private ObjInfoMapper objInfoMapper;
  @Autowired
  private DeviceMapper deviceMapper;
  @Autowired
  private RedisUtil redisUtil;
  @Resource
  private DeviceStatusMapper deviceStatusMapper;
  @Resource
  private MonitorService monitorService;

  /**
   * 增加
   *
   * @param devices
   */
  @Override
  @Transactional
  public void insert(List<NoiseDevice> devices) {
    StringJoiner sj = new StringJoiner("，");
    devices.forEach((device) -> {
      insert(device);
      sj.add(device.getDeviceCode());
    });
    LogHelper.setLogInfo("", devices.toString(), null, null,"添加设备，设备编码："+sj);
  }

    /**
     * 增加
     * @param noiseDevice
     */
    @Transactional
    public void insert(NoiseDevice noiseDevice) {
        Assert.hasLength(noiseDevice.getDeviceCode(), "设备id不能为空");
        /**
         * 验证重复
         */
        this.checkExist(noiseDevice);
        //验证设备是否符合
        this.getDeviceExtendInfos(noiseDevice.getDeviceCode(), true);
        //设置基本属性
        this.setBase(noiseDevice);
        this.save(noiseDevice);
        // todo  绑定设备 待检查

      try {
        //远程保存
        DeviceApplicationModelRef deviceApplicationModelRef = getDeviceApplicationModelRef();
        deviceApplicationModelRef.setDeviceCode(noiseDevice.getDeviceCode());
        deviceApplicationModelRef.setType(0);
        deviceApplicationModelRef.setApplicationId(3L);
        EventUtil.publishRefEvent(deviceApplicationModelRef);
      } catch (Exception e) {
        e.printStackTrace();
      }
//    JSONObject jsonObject = RpcEnum.BASE.postForObject("/sanzhiapi/deviceApplication/add", device, JSONObject.class);
//    if (!RestBuilder.properties().getSuccessCode().equals(jsonObject.getString("code"))){
//      LOGGER.info("新增失败===="+jsonObject.getString("message"));
//      throw new BusinessException("新增失败");
//    }

  }

  /**
   * 或者保存设备关联的实体
   * @return
   */
  private DeviceApplicationModelRef getDeviceApplicationModelRef() {
    BaseApplication baseApplication = (BaseApplication)redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);
    DeviceApplicationModelRef device = DeviceApplicationModelRef.getDevice(baseApplication);
    device.setModelId(DeviceModelConstant.NOISE_ENVIRONMENT);
    return device;
  }

  /**
   * 根据id编辑
   * @param device
   */
  @Override
  public void updateOne(NoiseDevice device) {
    /**
     * 验证重复
     */
    this.checkExist(device);
    //设置基本属性
    this.setBase(device);
    this.updateById(device);
    NoiseDevice byId = baseMapper.selectById(device.getId());
    LogHelper.setLogInfo("", device.toString(), null, null,"编辑设备，设备编码："+byId.getDeviceCode());
  }

  /**
   * @Description: 查询设备信息
   * <AUTHOR>
   * @param flag true 验证设备码 false不验证
   * @date 2020/11/04 11:42
   */
  @Override
  public NoiseDeviceDTO findDeviceByDeviceId(String deviceCode, Boolean flag) {
    //校验重复
    if (flag){
      NoiseDevice NoiseDevice = new NoiseDevice();
      NoiseDevice.setDeviceCode(deviceCode);
      this.checkExist(NoiseDevice);
    }
    NoiseDeviceDTO NoiseDeviceDTO = new NoiseDeviceDTO();
    DeviceExtendInfo deviceExtendInfo = getDeviceExtendInfos(deviceCode,flag);
    NoiseDeviceDTO.setExtendInfo(deviceExtendInfo);
    if (null != deviceExtendInfo && StringUtils.isNotBlank(deviceExtendInfo.getDwbsm())){
        ObjInfo objInfo = objInfoMapper.findByMonitorPointBsm(deviceExtendInfo.getDwbsm());
        NoiseDeviceDTO.setObjInfo(objInfo);
    }
    return NoiseDeviceDTO;
  }

  /**
   * @Description: 删除噪声环境设备（包含批量删除）
   */
  @Override
  @Transactional
  public void delBatch(Set<Long> ids) {
    //获取设备id集合
    List<NoiseDevice> NoiseDevices = baseMapper.selectBatchIds(ids);
    List<String> deviceIds = NoiseDevices.stream().map(m -> m.getDeviceCode()).collect(Collectors.toList());
    StringJoiner sj = new StringJoiner("，");
    deviceIds.forEach(sj::add);
    this.removeByIds(ids);
    //从base中删除
    //获取应用名，应用id
    DeviceApplicationModelRef device = getDeviceApplicationModelRef();
    device.setDeviceCodes(deviceIds);
    //远程保存
    JSONObject jsonObject = RpcEnum.BASE.postForObject("/sanzhiapi/deviceApplication/del", device, JSONObject.class);
    if (!RestBuilder.properties().getSuccessCode().equals(jsonObject.getString("code"))){
      LOGGER.info("删除错误===="+jsonObject.getString("message"));
      throw new BusinessException("删除失败");
    }
    LogHelper.setLogInfo("", ids.toString(), null, null,"删除设备，设备编码："+sj);
  }

  /**
   * @Description: 根据id查询噪声环境设备详情
   * <AUTHOR>
   * @date 2020/11/04 11:42
   */
  @Override
  public NoiseDeviceDTO findById(Long id) {
//    deviceMapper.selectAllByDeviceUnitId(1L);

    //设备详情
    NoiseDevice NoiseDevice = baseMapper.selectById(id);
    if(NoiseDevice==null){
      throw new BusinessException("该设备已删除，请刷新页面再进行操作");
    }
    NoiseDeviceDTO deviceDTO = BeanUtil.toBean(NoiseDevice, NoiseDeviceDTO.class);
    //查询设备的属性
    QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("code",NoiseDevice.getDeviceCode());
    List<Device> devices = deviceMapper.selectList(queryWrapper);
    if (CollectionUtil.isNotEmpty(devices)){
      deviceDTO.setStatus(devices.get(0).getStatus());
      deviceDTO.setAlarmState(devices.get(0).getAlarmState());
      deviceDTO.setLastPushTime(devices.get(0).getLastPushTime());
    }
//    NoiseDeviceDTO NoiseDeviceDTO = findDeviceByDeviceId(NoiseDevice.getDeviceCode(),false);
    if (null != deviceDTO){
      DeviceUtils.setDeviceDetail(deviceDTO);
//      deviceDTO.setObjInfo(NoiseDeviceDTO.getObjInfo());
//      deviceDTO.setExtendInfo(NoiseDeviceDTO.getExtendInfo());
//      //区域范围
//      if (StringUtils.isNotBlank(deviceDTO.getExtendInfo().getAreaPath())){
//        deviceDTO.getExtendInfo().setAreaPath(deviceDTO.getExtendInfo().getAreaPath().replace("@","/"));
//      }
    }
    // 设置物模型
    deviceDTO.setPhysicModel(monitorService.getPhysicModelPropAndFilter(new MonitorQueryVO().setDeviceCode(deviceDTO.getDeviceCode())));
    //设置设备属性
    deviceDTO.setDevicePropertyStatusList(getDevicePropertyStatus(deviceDTO.getDeviceCode()));
    return deviceDTO;
  }

  /**
   * @Description: 根据id查询噪声环境设备详情
   * <AUTHOR>
   * @date 2020/11/04 11:42
   */
  @Override
  public DeviceObjInfoDevicePropertyStatusListInfo getDeviceObjInfoDevicePropertyStatusListInfo(String deviceCode) {
    //设备详情
    DeviceObjInfoDevicePropertyStatusListInfo deviceDTO = new DeviceObjInfoDevicePropertyStatusListInfo();
    //查询设备的属性
    QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("code", deviceCode);
    List<Device> devices = deviceMapper.selectList(queryWrapper);
    if (CollectionUtil.isNotEmpty(devices)) {
      deviceDTO.setDevice(devices.get(0));
    }
    DeviceExtendInfo deviceExtendInfo = getDeviceExtendInfos(deviceCode, false);
    ObjInfo objInfo = objInfoMapper.findByMonitorPointBsm(deviceExtendInfo.getDwbsm());
    deviceDTO.setObjInfo(objInfo);
    deviceDTO.setExtendInfo(deviceExtendInfo);
    //区域范围
    if (StringUtils.isNotBlank(deviceDTO.getExtendInfo().getAreaPath())) {
      deviceDTO.getExtendInfo().setAreaPath(deviceDTO.getExtendInfo().getAreaPath().replace("@", "/"));
    }
    // 设置物模型
    deviceDTO.setPhysicModel(monitorService.getPhysicModelPropAndFilter(new MonitorQueryVO().setDeviceCode(deviceCode)));
    //设置设备属性
    deviceDTO.setDevicePropertyStatusList(getDevicePropertyStatus(deviceCode));
    return deviceDTO;
  }

  /**
   * 获取设备属性
   * @param deviceDTO
   */
  private List<DevicePropertyStatus> getDevicePropertyStatus(String deviceCode) {
    List<DevicePropertyStatus> devicePropertyStatusList = new ArrayList<>();
    //TODO 暂全部返回，未实现个性化配置
    QueryWrapper<DeviceStatus> deviceStatusQw = new QueryWrapper<>();
    deviceStatusQw.eq("device_code_", deviceCode);
    List<DeviceStatus> deviceStatusList = deviceStatusMapper.selectList(deviceStatusQw);
    if (CollectionUtil.isNotEmpty(deviceStatusList)) {
      deviceStatusList.forEach(m -> {
        DevicePropertyStatus devicePropertyStatus = new DevicePropertyStatus();
        devicePropertyStatus.setProp(m.getProp());
        devicePropertyStatus.setValue(m.getValue());
        devicePropertyStatusList.add(devicePropertyStatus);
      });
    }
    return devicePropertyStatusList;
  }

  /**
   * 根据设备id获取设备部件码信息
   * @param deviceCode
   * @return
   */
  @Override
  public DeviceExtendInfo getDeviceExtendInfos(String deviceCode, Boolean flag) {
    //根据id查询设备部件码
    QueryWrapper<DeviceExtendInfo> example = new QueryWrapper<>();
    example.eq("device_id", deviceCode);
    List<DeviceExtendInfo> extendInfoList = deviceExtendInfoMapper.selectList(example);
    if (CollectionUtil.isNotEmpty(extendInfoList)){
      DeviceExtendInfo extendInfo = extendInfoList.get(0);
      if (flag){
        checkBsm(extendInfo.getBsm());
      }
      return extendInfo;
    }else if (flag){
      log.error("设备编码错误,校验不通过");
      throw new BusinessException("设备编码错误,校验不通过");
    }
    return null;
  }

  @Override
  public IPage<NoiseDeviceVo> queryListByPage(RequestModel<NoiseDeviceVo> requestModel) {
    Page page = requestModel.getPage();
    NoiseDeviceVo noiseDeviceVo = requestModel.getCustomQueryParams();
    IPage<NoiseDeviceVo> noiseDeviceVoIPage = baseMapper.queryListByPage(page, noiseDeviceVo);
    noiseDeviceVoIPage.getRecords().forEach(m -> {
      //区域范围
      if (StringUtils.isNotBlank(m.getAreaPath())) {
        m.setAreaPath(m.getAreaPath().replace("@", "/"));
      }
    });
    return noiseDeviceVoIPage;
  }

  @Override
  public List<NoiseDeviceVo> queryList(NoiseDeviceVo requestModel) {
    List<NoiseDeviceVo> noiseDeviceVos = baseMapper.queryListByPage(requestModel);
    return noiseDeviceVos;
  }

  /**
   * 验证重复
   */
  private void checkExist(NoiseDevice noiseDevice) {
    QueryWrapper<NoiseDevice> queryWrapper = new QueryWrapper<>();
    //设置判断重复条件
    queryWrapper.eq("device_code_",noiseDevice.getDeviceCode())
            .eq("deleted_",0);
    //编辑的时候存在id
    Optional.ofNullable(noiseDevice.getId()).ifPresent(id -> queryWrapper.ne("id_",noiseDevice.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该噪声环境设备已存在");
    }
  }

  /**
   * 设置基本属性
   * @param noiseDevice
   */
  private void setBase(NoiseDevice noiseDevice) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    noiseDevice.setModifyTime(new Date());
    noiseDevice.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == noiseDevice.getId()){
      noiseDevice.setCreatorId(userId);
      noiseDevice.setCreateTime(new Date());
    }
  }

  /**
   * 验证设备是否符合
   * @param bsm
   */
  private void checkBsm(String bsm) {
    //验证设备标识码是不是20位
//    if (bsm.length() != 20){
//      throw new BusinessException("设备编码错误,校验不通过");
//    }
//    String code = bsm.substring(12, 16);
//    if (!"0308".equals(code)){
//      throw new BusinessException("设备编码错误,校验不通过");
//    }
  }
}
