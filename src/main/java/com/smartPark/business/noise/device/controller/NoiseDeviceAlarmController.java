package com.smartPark.business.noise.device.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.noise.device.entity.vo.NoiseDeviceAlarmVo;
import com.smartPark.business.noise.device.service.NoiseDeviceAlarmService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 噪声环境/噪声环境设备告警
 * @Description 噪声环境设备/噪声环境设备告警
 */
@RestController
@RequestMapping("noiseDeviceAlarm")
@Api(tags = "噪声环境设备告警")
public class NoiseDeviceAlarmController {

    @Autowired
    private NoiseDeviceAlarmService noiseDeviceAlarmService;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<NoiseDeviceAlarmVo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<NoiseDeviceAlarmVo> record =  noiseDeviceAlarmService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 根据id查询单条设备告警详情
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @GetMapping("{id}")
    @ApiOperation("根据id查询单条设备告警详情")
    public RestMessage findById(@PathVariable("id") Long id){
        Assert.notNull(id, "id 不能为空");
        NoiseDeviceAlarmVo noiseDeviceAlarmVo =  noiseDeviceAlarmService.findById(id);
        return RestBuilders.successBuilder().data(noiseDeviceAlarmVo).build();
    }

}
