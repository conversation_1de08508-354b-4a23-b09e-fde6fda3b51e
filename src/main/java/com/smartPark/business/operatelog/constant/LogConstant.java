package com.smartPark.business.operatelog.constant;

/**
 * <p>
 * 告警管理常量
 * </p>
 *
 * <AUTHOR>
 * @since 2020/5/8 15:01
 */
public interface LogConstant {

    /**
     * 日志模块枚举
     */
    enum LogModule {

        /**
         * 设备
         */
        DEVICE("设备"),
        /**
         * 配电房
         */
        DISTRIBUTION_ROOM("配电房"),
        /**
         * 资产
         */
        ASSET("资产"),
        USER("用户"),
        ALARM_PERSON_CONTACT("告警联系人"),
        ALARM_PROCESS("告警处理"),
        ALARM_TEMPLATE("告警模板"),
        DATA_SOURCE("数据源"),
        APPLICATION("应用"),
        CALCULATE_CONFIG("计算配置"),
        CALCULATE_RULE("计算规则"),
        DASHBOARD("大屏"),
        DEVICE_ATTRIBUTE("设备属性"),
        DEVICE_ATTRIBUTE_TENANT_CONFIG("设备属性租户个性化配置"),
        DEVICE_SERVICE_TENANT_CONFIG("设备服务租户个性化配置"),
        BANK_ALARM_RULE("告警规则"),
        FUNCTION("功能"),
        DATA_PUSH("数据推送"),
        OPEN_API("开放接口"),
        DISTRIBUTION_CABINET("配电柜"),
        SPACE_OPEN_API("空间开放接口"),
        SYNCHRONIZATION("数据同步"),
        OPERATE_LOG("操作日志"),
        PERSONALITY("个性化"),
        DISTRIBUTION_CABINET_CONFIGURATION("配电柜类型组态"),
        DISTRIBUTION_CABINET_TYPE("配电柜类型"),
        DISTRIBUTION_CABINET_TYPE_SITE("配电柜类型位置"),
        RULE_COMPONENT("规则组件"),
        DATA_FLUSH("刷历史数据"),
        RULE_ENGINE("规则引擎"),
        ROLE("角色"),
        AREA("区域"),
        SPACE("空间"),
        TASK_SCHEDULER("定时任务"),
        TENANT("租户"),
        KNOWLEDGE_BASE("知识库"),
        KNOWLEDGE_TYPE("知识类别");


        /**
         * 名称
         */
        private String name;

        /**
         * 获取值
         */
        public String getName() {
            return name;
        }

        LogModule(String name) {
            this.name = name;
        }
    }

    interface LogOperateType {

        // 设备模块
        String DEVICE_ADD = "新增设备";
        String DEVICE_UPDATE = "修改设备";
        String DEVICE_DELETE = "删除设备";

        // 资产模块
        String ASSET_ADD = "新增资产";
        String ASSET_UPDATE = "修改资产";
        String ASSET_DELETE = "删除资产";

        String ASSET_BINDING_DEVICE = "绑定设备";
        String ASSET_UNBIND_DEVICE = "解绑设备";
    }

}
