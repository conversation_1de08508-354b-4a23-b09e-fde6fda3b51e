package com.smartPark.business.hazardousMonitoring.entity.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.smartPark.business.hazardousMonitoring.entity.HazardousPoint;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 危险源点表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Data
public class HazardousPointVo extends HazardousPoint {

    /**
     * 是否监测（1是0否）
     */
    private Integer monitor;
    /**
     * 监测水体等级(1:Ⅰ类,2:Ⅱ类,3:Ⅲ类,4:Ⅳ类,5:Ⅴ类),默认Ⅴ类
     */
    private Integer qualityGrade;

    /**
     * 危险源点标识码集合
     */
    private List<String> hazardousBsms;

    /**
     * 设备code集合
     */
    private List<String> DeviceCodes;
    /**
     * 设备集合
     */
    private List<DeviceExtendInfo> deviceExtendInfoList;

}
