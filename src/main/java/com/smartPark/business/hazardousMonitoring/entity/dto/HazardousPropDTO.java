package com.smartPark.business.hazardousMonitoring.entity.dto;

import com.smartPark.business.airquality.device.constant.AirQualityDeviceConstant;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 危险源属性值和超标数
 * <AUTHOR> <PERSON><PERSON>
 * @Date 2023/10/26 19:48
 */
@Data
public class HazardousPropDTO {
    /**
     * 属性key
     */
    private String value;

    /**
     * 属性显示名
     */
    private String name;

    /**
     * 超标次数
     */
    private Integer num;

    private HazardousPropDTO(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 创建污染源需要的属性集合
     */
    public static List<HazardousPropDTO> getList(){
        List<HazardousPropDTO> list = new ArrayList<>();
        //空气污染属性
        List<String> airProps = AirQualityDeviceConstant.PROPERTY_LIST;
        airProps.forEach(a ->{
            HazardousPropDTO hazardousPropDTO = new HazardousPropDTO(a, StringUtils.upperCase(a));
            list.add(hazardousPropDTO);
        });
        //水质
        list.add(new HazardousPropDTO("cod","COD"));
        list.add(new HazardousPropDTO("oxygen","溶解氧"));
        list.add(new HazardousPropDTO("nitrogen","总氮"));
        list.add(new HazardousPropDTO("phosphorus","总磷"));
        list.add(new HazardousPropDTO("ammonia","氨氮"));
        return list;
    }
}
