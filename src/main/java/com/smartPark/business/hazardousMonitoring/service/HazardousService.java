package com.smartPark.business.hazardousMonitoring.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.hazardousMonitoring.entity.Hazardous;
import com.smartPark.business.hazardousMonitoring.entity.vo.HazardousPointVo;
import com.smartPark.business.hazardousMonitoring.entity.vo.HazardousVo;
import com.smartPark.common.base.model.RequestModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.common.entity.device.DeviceCommonDto;
import com.smartPark.common.entity.device.DeviceExtendInfoVo;

import java.util.List;

/**
 * <p>
 * 危险源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/04/04
 */
public interface HazardousService extends IService<Hazardous> {

    IPage<HazardousPointVo> queryListByPage(RequestModel<HazardousPointVo> requestModel);

    /**
     * 删除
     * @param ids
     */
    void delBatch(List<Long> ids);

    /**
     * @Description: 根据id查询危险源详情
     * <AUTHOR> yuanfeng
     * @date 2023/04/04 11:42
     */
    HazardousVo findById(Long id);

    /**
     * @Description: 添加危险源点
     * <AUTHOR> yuanfeng
     * @date 2023/04/04 11:42
     */
    void insertPoint(HazardousPointVo hazardousPointVo);

    /**
     * @Description: 修改危险源点
     * <AUTHOR> yuanfeng
     * @date 2023/04/04 11:42
     */
    void updatePoint(HazardousPointVo hazardousPointVo);

    /**
     * @Description: 关联设备
     * <AUTHOR> yuanfeng
     * @date 2023/04/04 11:42
     */
    void refDevice(HazardousVo hazardousVo);

    /**
     * 关联设备-查询列表
     * @param requestModel
     * @return
     */
    IPage<DeviceExtendInfoVo> findDeviceList(String bsm,RequestModel<DeviceCommonDto> requestModel);
}
