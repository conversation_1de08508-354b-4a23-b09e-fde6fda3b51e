package com.smartPark.business.smartTravel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.smartTravel.entity.SmartTravelInformationDetail;
import com.smartPark.business.smartTravel.entity.SmartTravelInformationDetailRef;
import com.smartPark.business.smartTravel.entity.SmartTravelRelease;
import com.smartPark.business.smartTravel.entity.vo.SmartTravelGuidanceVo;
import com.smartPark.business.smartTravel.entity.vo.SmartTravelInformationDetailRefVo;
import com.smartPark.business.smartTravel.mapper.SmartTravelReleaseMapper;
import com.smartPark.business.smartTravel.service.SmartTravelInformationDetailRefService;
import com.smartPark.business.smartTravel.service.SmartTravelInformationDetailService;
import com.smartPark.business.smartTravel.service.SmartTravelReleaseService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.mapper.DeviceMapper;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.Device;
import com.smartPark.common.entity.device.DeviceApplicationModelRef;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.business.smartTravel.entity.SmartTravelGuidance;
import com.smartPark.business.smartTravel.mapper.SmartTravelGuidanceMapper;
import com.smartPark.business.smartTravel.service.SmartTravelGuidanceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 诱导屏 服务实现类
 * </p>
 *
 * <AUTHOR> yuanfeng
 * @since 2022-04-13
 */
@Service
public class SmartTravelGuidanceServiceImpl extends ServiceImpl<SmartTravelGuidanceMapper, SmartTravelGuidance> implements SmartTravelGuidanceService {

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private SmartTravelInformationDetailRefService refService;
  @Autowired
  private SmartTravelInformationDetailService detailService;
  @Autowired
  private SmartTravelReleaseService releaseService;
  @Autowired
  private DeviceMapper deviceMapper;
  @Autowired
  private SmartTravelReleaseMapper smartTravelReleaseMapper;
  @Autowired
  private RedisUtil redisUtil;

  /**
   * 增加
   * @param smartTravelGuidance
   */
  @Override
  public void insert(SmartTravelGuidance smartTravelGuidance) {
    this.saveOrUpdate(smartTravelGuidance,(t) -> save(t));
    //获取应用名，应用id
    DeviceApplicationModelRef device = getDeviceApplicationModelRef();
    device.setDeviceCode(smartTravelGuidance.getDeviceCode());
    //远程保存
    EventUtil.publishRefEvent(device);
  }

  /**
   * 或者保存设备关联的实体
   * @return
   */
  private DeviceApplicationModelRef getDeviceApplicationModelRef() {
    BaseApplication baseApplication = (BaseApplication)redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);
    DeviceApplicationModelRef device = DeviceApplicationModelRef.getDevice(baseApplication);
    device.setModelId(DeviceModelConstant.SMART_TRAVEL_GUIDANCE);
    return device;
  }

  /**
   * @return
   * @Description: 删除巡检类型（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  public void delBatch(Set<Long> ids) {
    ids.forEach(id ->{
      SmartTravelGuidance smartTravelGuidance = baseMapper.selectById(id);
      LogHelper.setLogMemo("删除诱导屏,诱导屏设备编号:"+smartTravelGuidance.getDeviceCode());
      baseMapper.deleteById(id);
      //删除关联
      SmartTravelInformationDetailRef detailRef = new SmartTravelInformationDetailRef();
      detailRef.setRefType(SmartTravelInformationDetailRefVo.REFTYPE_GUIDANCE);
      detailRef.setRefId(id);
      refService.deleteRef(detailRef);
      //删除基础设关联
      DeviceApplicationModelRef device = getDeviceApplicationModelRef();
      device.setDeviceCode(smartTravelGuidance.getDeviceCode());
      device.setActionType(EventUtil.DELETE);
      //保存 关联
      EventUtil.publishRefEvent(device);
    });
  }

  /**
   * 根据id编辑
   * @param smartTravelGuidanceVo
   */
  @Override
  public void updateOne(SmartTravelGuidanceVo smartTravelGuidanceVo) {
    SmartTravelGuidance smartTravelGuidance = BeanUtil.toBean(smartTravelGuidanceVo, SmartTravelGuidance.class);
    this.saveOrUpdate(smartTravelGuidance,(t) -> updateById(t));

    //保存详情和关联
    SmartTravelInformationDetailRefVo informationDetailRefVo =
            new SmartTravelInformationDetailRefVo(SmartTravelInformationDetailRefVo.REFTYPE_GUIDANCE,smartTravelGuidanceVo.getId(),smartTravelGuidanceVo.getDetailList());
    refService.insertBatch(informationDetailRefVo);
    //保存发布信息
    SmartTravelGuidance guidance = baseMapper.selectById(smartTravelGuidanceVo.getId());
    SmartTravelRelease smartTravelRelease = new SmartTravelRelease();
    smartTravelRelease.setDeviceCode(guidance.getDeviceCode());
    smartTravelRelease.setInformationType(guidance.getInformationType());
    smartTravelRelease.setSrc("01");
    smartTravelRelease.setChannel("1");
    releaseService.insert(smartTravelRelease);
    //保存关联
    informationDetailRefVo.setRefType(SmartTravelInformationDetailRefVo.REFTYPE_RELEASE);
    informationDetailRefVo.setRefId(smartTravelRelease.getId());
    refService.insertBatch(informationDetailRefVo);
  }

  /**
   * @Description: 根据id查询诱导屏详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  public SmartTravelGuidanceVo findById(Long id) {
    SmartTravelGuidance smartTravelGuidance = baseMapper.selectById(id);
    SmartTravelGuidanceVo smartTravelGuidanceVo = BeanUtil.toBean(smartTravelGuidance, SmartTravelGuidanceVo.class);
    //设备名称状态
    LambdaQueryWrapper<Device> deviceWrapper = new LambdaQueryWrapper<>();
    deviceWrapper.eq(Device::getCode,smartTravelGuidance.getDeviceCode());

    deviceMapper.selectList(deviceWrapper).stream().findFirst().ifPresent(device ->{
      smartTravelGuidanceVo.setDeviceStatus(device.getStatus());
      smartTravelGuidanceVo.setDeviceName(device.getName());
    });
    //查询信息详情
    List<SmartTravelInformationDetail> detailList = detailService.getInformationDetail(SmartTravelInformationDetailRefVo.REFTYPE_GUIDANCE,id);
    smartTravelGuidanceVo.setDetailList(detailList);
    return smartTravelGuidanceVo;
  }

  @Override
  public IPage<SmartTravelGuidanceVo> queryListByPage(RequestModel<SmartTravelGuidanceVo> requestModel) {
    Page page = requestModel.getPage();
    SmartTravelGuidanceVo smartTravelGuidanceVo = requestModel.getCustomQueryParams();
    IPage<SmartTravelGuidanceVo> smartTravelGuidanceIPage = baseMapper.queryListByPage(page, smartTravelGuidanceVo);
    smartTravelGuidanceIPage.getRecords().forEach(s->{
      //查询信息详情
      List<SmartTravelInformationDetail> informationDetail = detailService.getInformationDetail(SmartTravelInformationDetailRefVo.REFTYPE_GUIDANCE, s.getId());
      s.setDetailList(informationDetail);
      //查询信息发布
      LambdaQueryWrapper<SmartTravelRelease> releaseLambdaQueryWrapper = new LambdaQueryWrapper<>();
      releaseLambdaQueryWrapper.eq(SmartTravelRelease::getDeviceCode,s.getDeviceCode())
                      .orderByDesc(SmartTravelRelease::getCreateTime);
      List<SmartTravelRelease> smartTravelReleases = smartTravelReleaseMapper.selectList(releaseLambdaQueryWrapper);
      if (CollectionUtil.isNotEmpty(smartTravelReleases)){
        s.setPubNumber(smartTravelReleases.size());
        s.setPubTime(smartTravelReleases.get(0).getCreateTime());
      }
    });
    return smartTravelGuidanceIPage;
  }

  /**
   * 执行新增和更新
   * @param smartTravelGuidance
   * @param consumer 自定义执行
   */
  private void saveOrUpdate(SmartTravelGuidance smartTravelGuidance, Consumer<SmartTravelGuidance> consumer) {
    /**
     * 验证重复
     */
    this.checkExist(smartTravelGuidance);
    //设置基本属性
    this.setBase(smartTravelGuidance);
    consumer.accept(smartTravelGuidance);
  }

  /**
   * 验证重复
   */
  private void checkExist(SmartTravelGuidance smartTravelGuidance) {
    LambdaQueryWrapper<SmartTravelGuidance> queryWrapper = new LambdaQueryWrapper<>();
    //设置判断重复条件
    queryWrapper.eq(SmartTravelGuidance::getDeviceCode,smartTravelGuidance.getDeviceCode());
    //编辑的时候存在id
    Optional.ofNullable(smartTravelGuidance.getId()).ifPresent(id -> queryWrapper.ne(SmartTravelGuidance::getId,smartTravelGuidance.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该诱导屏已存在");
    }
  }

  /**
   * 设置基本属性
   * @param smartTravelGuidance
   */
  private void setBase(SmartTravelGuidance smartTravelGuidance) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    smartTravelGuidance.setModifyTime(new Date());
    smartTravelGuidance.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == smartTravelGuidance.getId()){
      smartTravelGuidance.setCreatorId(userId);
      smartTravelGuidance.setCreateTime(new Date());
    }
  }
}
