package com.smartPark.business.smartTravel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.smartTravel.entity.SmartTravelAutomatic;
import com.smartPark.business.smartTravel.entity.vo.SmartTravelAutomaticVo;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
public interface SmartTravelAutomaticMapper extends BaseMapper<SmartTravelAutomatic> {

    IPage<SmartTravelAutomaticVo> queryListByPage(@Param("page") Page page, @Param("entity") SmartTravelAutomaticVo smartTravelAutomaticVo);
}
