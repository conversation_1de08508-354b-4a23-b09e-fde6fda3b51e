package com.smartPark.business.smartTravel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.intelligentparking.overview.entity.dto.ParkingStatisticsDTO;
import com.smartPark.business.smartTravel.entity.SmartTravelRelease;
import com.smartPark.business.smartTravel.entity.dto.SmartTravelStatisticsDTO;
import com.smartPark.business.smartTravel.entity.dto.SmartTravelStatisticsQueryDTO;
import com.smartPark.business.smartTravel.entity.vo.SmartTravelReleaseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
public interface SmartTravelReleaseMapper extends BaseMapper<SmartTravelRelease> {

    IPage<SmartTravelReleaseVo> queryListByPage(@Param("page") Page page, @Param("entity") SmartTravelReleaseVo smartTravelReleaseVo);

    /**
     * 按时间分组统计
     *
     * @param statisticsQueryDTO
     * @return
     */
    List<SmartTravelStatisticsDTO> countGroupByTime(SmartTravelStatisticsQueryDTO statisticsQueryDTO);
}

