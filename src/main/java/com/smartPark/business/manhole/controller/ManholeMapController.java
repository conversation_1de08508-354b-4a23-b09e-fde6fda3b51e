package com.smartPark.business.manhole.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.smartPark.business.manhole.entity.vo.ManholeAlarmVo;
import com.smartPark.business.manhole.entity.vo.ManholeVo;
import com.smartPark.business.manhole.service.ManholeAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;
import java.util.Map;

/**
 * 智慧井盖/井盖地图
 * @author: kan yuan<PERSON>
 * @Date: 2020/11/04 11:42
 * @Description: 智慧井盖/井盖地图
 */
@RestController
@RequestMapping("manholeMap")
@Api(tags = "井盖地图")
public class ManholeMapController {
    @Autowired
    private ManholeAlarmService manholeAlarmService;

    /**
     * @Description: 根据区域查询告警情况
     * <AUTHOR> yuan<PERSON>
     * @date 2020/11/04 11:42
     */
    @PostMapping("alarms")
    @ApiOperation("根据区域查询告警情况")
    public RestMessage findAlarmStatistics(@RequestBody ManholeVo manholeVo){
        Map<String,Object> map = manholeAlarmService.findAlarmStatistics(manholeVo);
        return RestBuilders.successBuilder().data(map).build();
    }

    /**
     * @Description: 根据设备编码查询井盖告警日志
     * <AUTHOR> yuanfeng
     * @date 2020/11/04 11:42
     */
    @GetMapping("alarm")
    @ApiOperation("根据设备编码查询井盖告警日志")
    public RestMessage findAlarmByDeviceCode(String deviceCode){
        Assert.notNull(deviceCode,"设备编码不能为空");
        List<ManholeAlarmVo> manholeStatisticsDTOs = manholeAlarmService.findAlarmByDeviceCode(deviceCode);
        if (CollectionUtil.isNotEmpty(manholeStatisticsDTOs) && manholeStatisticsDTOs.size() > 5){
            manholeStatisticsDTOs = manholeStatisticsDTOs.subList(0, 5);
        }
        return RestBuilders.successBuilder().data(manholeStatisticsDTOs).build();
    }
}
