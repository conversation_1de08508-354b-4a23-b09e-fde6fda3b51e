package com.smartPark.business.toilet.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.toilet.entity.ToiletEnergyStatistics;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.toilet.entity.dto.SmartServeDeviceDTO;
import com.smartPark.business.toilet.entity.dto.ToiletEnergyAlarmDTO;
import com.smartPark.business.toilet.entity.dto.ToiletInfoDTO;
import com.smartPark.business.toilet.entity.vo.ToiletEnergyStatisticsPageVo;
import com.smartPark.business.toilet.entity.vo.ToiletInfoVo;

/**
 * 公厕能源统计表 服务类
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
public interface ToiletEnergyStatisticsService extends IService<ToiletEnergyStatistics> {
    /**
     * 能耗详情分页查询
     *
     * @param page 分页对象
     * @param customQueryParams 查询分页对象
     * @return 所有数据
     */
    IPage<ToiletEnergyStatistics> energyDtoPage(Page page, ToiletEnergyStatisticsPageVo customQueryParams);

    /**
     * 告警详情分页查询
     * @param page 分页对象
     * @param customQueryParams 查询分页对象
     * @return 所有数据
     */
    IPage<ToiletEnergyAlarmDTO> energyAlarmDtoPage(Page page, ToiletEnergyStatisticsPageVo customQueryParams);

    /**
     * 查询设备信息Dto list
     * @param page 分页
     * @param toiletInfoVo 查询条件
     * @return 所有数据
     */
    IPage<ToiletInfoDTO> selectToiletDtoPage(Page page, ToiletInfoVo toiletInfoVo);
}
