package com.smartPark.business.toilet.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.toilet.entity.ToiletDevice;
import com.smartPark.business.toilet.entity.dto.SmartServeDeviceDTO;
import com.smartPark.business.toilet.entity.dto.ToiletDeviceDTO;
import com.smartPark.business.toilet.entity.vo.SmartServeDeviceControlVo;
import com.smartPark.business.toilet.entity.vo.SmartServeDeviceVo;
import com.smartPark.business.toilet.entity.vo.ToiletDevicePageVo;
import com.smartPark.business.toilet.entity.vo.ToiletDeviceVo;
import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.common.entity.device.dto.DeviceUnitDTO;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * ToiletDevice表服务接口
 *
 * <AUTHOR>
 * @date 2023/04/18
 */
public interface ToiletDeviceService extends IService<ToiletDevice> {

    /**
     * 新增
     *
     * @param livableToiletDevice 实体对象
     * @return 操作结果
     */
    boolean saveOne(ToiletDevice livableToiletDevice);

    /**
     * 修改单条
     *
     * @param livableToiletDevice 实体对象
     * @return 修改结果
     */
    boolean updateOne(ToiletDevice livableToiletDevice);

    /**
     * 查询分页
     *
     * @param page                分页对象
     * @param livableToiletDevice 分页参数对象
     * @return 查询分页结果
     */
    IPage<ToiletDevice> selectPage(Page page, ToiletDevice livableToiletDevice);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    ToiletDevice getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param livableToiletDevice 过滤条件实体对象
     * @param request             请求
     * @param response            响应
     */
    void export(ToiletDevice livableToiletDevice, HttpServletRequest request, HttpServletResponse response);

    /**
     * 批量新增数据
     *
     * @param toiletDevicePageVo 页面对象
     * @return 统一出参
     */
    RestMessage batchInsert(ToiletDevicePageVo toiletDevicePageVo);

    /**
     * 根据条件查询dto信息
     *
     * @param page              页码信息
     * @param customQueryParams 条件对象
     * @return dto信息list
     */
    IPage<ToiletDeviceDTO> selectDtoPage(Page page, ToiletDeviceVo customQueryParams);

    /**
     * 根据id查询dto信息
     *
     * @param toiletDevice 公厕设备信息
     * @return dto信息
     */
    ToiletDeviceDTO selectDtoOne(ToiletDevice toiletDevice);

    /**
     * 批量启用/停用
     *
     * @param toiletDevicePageVo 页面对象
     * @return 统一出参
     */
    RestMessage batchStartAndStop(ToiletDevicePageVo toiletDevicePageVo);

    /**
     * 分页查询所有数据
     *
     * @param page               分页对象
     * @param smartServeDeviceVo 查询分页对象
     * @return 所有数据
     */
    IPage<SmartServeDeviceDTO> selectModelDeviceDtoPage(Page page, SmartServeDeviceVo smartServeDeviceVo);

    /**
     * 查询子模块设备类型和型号
     *
     * @param page               分页对象
     * @param smartServeDeviceVo 查询分页对象
     * @return 所有数据
     */
    List<DeviceUnitDTO> selectSubModelDeviceTypeAndUnit(Page page, SmartServeDeviceVo smartServeDeviceVo);

    /**
     * 控制设备
     *
     * @param deviceControlVo 查询分页对象
     * @return 所有数据
     */
    RestMessage deviceControl(SmartServeDeviceControlVo deviceControlVo);

    /**
     * 解析kafka消息
     *
     * @param record 消息
     */
    void flowTacticParse(FlowPushData flowData);

    /**
     * 公厕设备控制
     *
     * @param param 传参param字符串（json）
     * @return 新增结果
     */
    RestMessage taskToiletKillControl(String param);


}

