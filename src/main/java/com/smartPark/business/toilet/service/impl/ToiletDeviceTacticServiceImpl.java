package com.smartPark.business.toilet.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.toilet.entity.ToiletDevice;
import com.smartPark.business.toilet.entity.ToiletDeviceTactic;
import com.smartPark.business.toilet.entity.vo.SmartServeDeviceTacticVo;
import com.smartPark.business.toilet.mapper.ToiletDeviceMapper;
import com.smartPark.business.toilet.mapper.ToiletDeviceTacticMapper;
import com.smartPark.business.toilet.service.ToiletDeviceTacticService;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.CommonConstant;
import com.smartPark.common.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * ToiletDeviceTactic表服务实现类
 *
 * <AUTHOR>
 * @date 2023/04/21
 */
@Slf4j
@Service
public class ToiletDeviceTacticServiceImpl extends ServiceImpl
        <ToiletDeviceTacticMapper, ToiletDeviceTactic> implements ToiletDeviceTacticService {
    @Resource
    private CommonService commonService;

    @Resource
    private ToiletDeviceMapper toiletDeviceMapper;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        for (Long id : idList) {
            removeById(id);
        }
        return true;
    }


    @Override
    public boolean saveOne(ToiletDeviceTactic livableToiletDeviceTactic) {
        commonService.setCreateAndModifyInfo(livableToiletDeviceTactic);

        validParamRequired(livableToiletDeviceTactic);
        validRepeat(livableToiletDeviceTactic);
        validParamFormat(livableToiletDeviceTactic);
        return save(livableToiletDeviceTactic);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(ToiletDeviceTactic livableToiletDeviceTactic) {
        Assert.notNull(livableToiletDeviceTactic.getId(), "id不能为空");
        commonService.setModifyInfo(livableToiletDeviceTactic);

        validRepeat(livableToiletDeviceTactic);
        validParamFormat(livableToiletDeviceTactic);
        return updateById(livableToiletDeviceTactic);
    }

    @Override
    public IPage<ToiletDeviceTactic> selectPage(Page page, ToiletDeviceTactic livableToiletDeviceTactic) {
        return baseMapper.selectPage(page, livableToiletDeviceTactic);
    }

    @Override
    public void export(ToiletDeviceTactic livableToiletDeviceTactic, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public RestMessage addTactic(SmartServeDeviceTacticVo smartServeDeviceTacticVo) {
        ToiletDeviceTactic toiletDeviceTactic = smartServeDeviceTacticVo.getToiletDeviceTactic();
        Long id = toiletDeviceTactic.getId();
        Long triggerDeviceId = toiletDeviceTactic.getTriggerDeviceId();
        if (id != null && triggerDeviceId == null) {
            //取消策略
            ToiletDeviceTactic dbTactic = baseMapper.getOneById(id);
            if (dbTactic != null) {
                commonService.setModifyInfo(dbTactic);
                baseMapper.updateById(dbTactic);
                baseMapper.deleteById(id);
            }
            return RestBuilders.successBuilder().build();
        }
        //参数校验
        validParamRequired(toiletDeviceTactic);
        //校验重复
        validRepeat(toiletDeviceTactic);

        commonService.setCreateAndModifyInfo(toiletDeviceTactic);

        //查询触发设备信息
        ToiletDevice triggerDevice = toiletDeviceMapper.selectById(triggerDeviceId);
        Assert.notNull(triggerDevice, "触发设备不存在");
        toiletDeviceTactic.setTriggerDeviceCode(triggerDevice.getDeviceCode());
        //保存
        baseMapper.insert(toiletDeviceTactic);
        return RestBuilders.successBuilder().build();
    }

    @Override
    public RestMessage updateTactic(SmartServeDeviceTacticVo smartServeDeviceTacticVo) {
        ToiletDeviceTactic toiletDeviceTactic = smartServeDeviceTacticVo.getToiletDeviceTactic();
        Long id = toiletDeviceTactic.getId();
        Long triggerDeviceId = toiletDeviceTactic.getTriggerDeviceId();
        if (id != null && triggerDeviceId == null) {
            //取消策略
            ToiletDeviceTactic dbTactic = baseMapper.getOneById(id);
            if (dbTactic != null) {
                commonService.setModifyInfo(dbTactic);
                baseMapper.updateById(dbTactic);
                baseMapper.deleteById(id);
            }
            return RestBuilders.successBuilder().build();
        }
        //参数校验
        validParamRequired(toiletDeviceTactic);
        //校验重复
        validRepeat(toiletDeviceTactic);

        if (triggerDeviceId == null) {
            //取消策略
            baseMapper.deleteById(id);
        }

        //查询数据库中的数据
        ToiletDeviceTactic dbTactic = baseMapper.selectById(id);
        if (dbTactic != null) {
            //传参赋值
            BeanUtil.copyProperties(toiletDeviceTactic, dbTactic, CopyOptions.create().ignoreNullValue());
            //更新
            //查询触发设备信息
            ToiletDevice triggerDevice = toiletDeviceMapper.selectById(triggerDeviceId);
            Assert.notNull(triggerDevice, "触发设备不存在");
            dbTactic.setTriggerDeviceCode(triggerDevice.getDeviceCode());
            commonService.setModifyInfo(dbTactic);
            baseMapper.updateById(dbTactic);
        }
        return RestBuilders.successBuilder().build();
    }

    @Override
    public ToiletDeviceTactic getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(ToiletDeviceTactic livableToiletDeviceTactic) {

        List<ToiletDeviceTactic> effTacticList = new ArrayList<>();
        List<ToiletDeviceTactic> list = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(ToiletDeviceTactic::getDeviceCode, livableToiletDeviceTactic.getDeviceCode())
                .eq(ToiletDeviceTactic::getDeleted, CommonConstant.NOT_DELETE)
                .list();
        //查询触发设备是否删除
        if(CollectionUtil.isNotEmpty(list)){
            list.forEach(dt->{
                Long deviceId = dt.getDeviceId();
                Long triggerDeviceId = dt.getTriggerDeviceId();
                ToiletDevice sourceToiletDevice = toiletDeviceMapper.selectById(deviceId);
                ToiletDevice triggerToiletDevice = toiletDeviceMapper.selectById(triggerDeviceId);
                if(sourceToiletDevice != null && triggerToiletDevice != null){
                    effTacticList.add(dt);
                }
            });
        }
        if (effTacticList.size() > 0 &&
                (effTacticList.size() > 1
                        || ObjectUtils.isEmpty(livableToiletDeviceTactic.getId())
                        || !livableToiletDeviceTactic.getId().equals(effTacticList.get(0).getId())
                )
        ) {
            throw new BusinessException("设备已存在策略");
        }

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(ToiletDeviceTactic toiletDeviceTactic) {
        //校验参数，校验公厕设备id是否空，使用Assert
        Assert.notNull(toiletDeviceTactic.getDeviceId(), "公厕设备id不能为空");
        //校验触发设备id是否空，使用Assert
        //Assert.notNull(toiletDeviceTactic.getTriggerDeviceId(), "触发设备id不能为空");
        Long triggerDeviceId = toiletDeviceTactic.getTriggerDeviceId();
        if(triggerDeviceId != null){
            //校验触发设备流水属性key是否空，使用Assert
            Assert.notNull(toiletDeviceTactic.getTriggerPropKey(), "触发设备流水属性key不能为空");
            //校验触发设备流水属性value是否空，使用Assert
            Assert.notNull(toiletDeviceTactic.getTriggerPropValue(), "触发设备流水属性value不能为空");
            //校验触发设备流水属性value是否空，使用Assert
            Assert.notNull(toiletDeviceTactic.getTriggerPropName(), "触发设备流水属性name不能为空");
            //校验触发设备流水属性value是否空，使用Assert
            Assert.notNull(toiletDeviceTactic.getTriggerLogic(), "触发策略逻辑不能为空");
        }
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(ToiletDeviceTactic livableToiletDeviceTactic) {
        //Assert.isTrue(livableToiletDeviceTactic.getName() == null || livableToiletDeviceTactic.getName().length() <= 50,
        //        "名称超长");
    }
}

