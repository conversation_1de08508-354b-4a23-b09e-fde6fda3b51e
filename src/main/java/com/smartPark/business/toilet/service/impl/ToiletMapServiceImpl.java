package com.smartPark.business.toilet.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.smartPark.business.toilet.entity.ToiletEnergyStatistics;
import com.smartPark.business.toilet.entity.ToiletInfo;
import com.smartPark.business.toilet.entity.vo.ToiletEnergyStatisticsPageVo;
import com.smartPark.business.toilet.entity.vo.ToiletInfoPageVo;
import com.smartPark.business.toilet.mapper.ToiletDeviceMapper;
import com.smartPark.business.toilet.mapper.ToiletEnergyStatisticsMapper;
import com.smartPark.business.toilet.mapper.ToiletInfoMapper;
import com.smartPark.business.toilet.service.ToiletDeviceService;
import com.smartPark.business.toilet.service.ToiletInfoService;
import com.smartPark.business.toilet.service.ToiletMapService;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.device.mapper.DeviceApplicationModelDeviceUnitConfigSubMapper;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.elasticsearch.entity.ESconfig;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.DeviceApplicationModelDeviceUnitConfigSub;
import com.smartPark.common.monitor.service.MonitorService;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.utils.EsCalendar;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ToiletMapServiceImpl implements ToiletMapService {

    @Resource
    private ToiletInfoMapper toiletInfoMapper;

    @Resource
    private ToiletDeviceMapper toiletDeviceMapper;

    @Resource
    private DeviceApplicationModelDeviceUnitConfigSubMapper deviceApplicationModelDeviceUnitConfigSubMapper;

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private ToiletDeviceService toiletDeviceService;
    @Autowired
    private ToiletInfoService toiletInfoService;

    @Resource
    private MonitorService monitorService;

    @Resource
    private DeviceStatusMapper deviceStatusMapper;

    @Resource
    private ToiletEnergyStatisticsMapper toiletEnergyStatisticsMapper;

    @Resource
    private ESconfig eSconfig;

    @Override
    public RestMessage spaceCount(ToiletInfoPageVo toiletInfoPageVo) {
        ToiletInfo toiletInfo = toiletInfoMapper.spaceCount(toiletInfoPageVo);
        toiletInfo.setUseManNum(toiletInfoMapper.queryUseNumByToiletCode(Collections.singletonList(toiletInfo.getToiletCode()),0));
        toiletInfo.setUseWomanNum(toiletInfoMapper.queryUseNumByToiletCode(Collections.singletonList(toiletInfo.getToiletCode()),1));
        return RestBuilders.successBuilder().data(toiletInfo).build();
    }

    public Map<String, Object> queryToiletInfoStatusByLastly(Long toiletId){
        List<String> deviceCodeList = toiletInfoService.selectDeviceCodeByToiletId(toiletId);
        LocalDateTime currentDate = LocalDateTime.now();
        List<Map<String, Object>> mapList = queryLastDateFromEsByParams("CSKQ-4G-01-0185", deviceCodeList,
                DateUtil.format(currentDate.plusDays(-1), DatePattern.NORM_DATETIME_PATTERN)
                , DateUtil.format(currentDate, DatePattern.NORM_DATETIME_PATTERN));
        return CollectionUtil.isEmpty(mapList) ? new HashMap<>(0) : mapList.get(0);
    }

    @Override
    public List<Map<String, Object>> queryToiletInfoStatus(Long toiletId, String queryTimeStart, String queryTimeEnd) {
        List<String> deviceCodeList = toiletInfoService.selectDeviceCodeByToiletId(toiletId);
        List<Map<String, Object>> mapList = queryLastDateFromEsByParams("CSKQ-4G-01-0185", deviceCodeList,
                queryTimeStart, queryTimeEnd);
        return CollectionUtil.isEmpty(mapList) ? new ArrayList<>(0) : mapList;
    }

    @Override
    public List<Map<String, Object>> queryToiletInfoStatus(String toiletCode, String queryTimeStart, String queryTimeEnd) {
        List<String> deviceCodeList = null;
        if(StringUtils.isNotEmpty(toiletCode)){
            deviceCodeList = toiletInfoService.selectDeviceCodeByToiletCode(toiletCode);
        }
        List<Map<String, Object>> mapList = queryLastDateFromEsByParams("CSKQ-4G-01-0185", deviceCodeList,
                queryTimeStart, queryTimeEnd);
        return CollectionUtil.isEmpty(mapList) ? new ArrayList<>(0) : mapList;
    }


    @Override
    public RestMessage environmentInfo(ToiletInfoPageVo toiletInfoPageVo) {
        Long toiletId = toiletInfoPageVo.getToiletInfo().getId();
        Map<String, Object> stringObjectMap = queryToiletInfoStatusByLastly(toiletId);
        return RestBuilders.successBuilder().data(stringObjectMap).build();
//        List<Long> modelIds = toiletInfoPageVo.getModelIds();
//        List<String> areaPaths = toiletInfoPageVo.getAreaPaths();
//
//        // 根据子模块id查询设备类型、型号
//        List<Long> unitIdList = new ArrayList<>();
//        if (CollectionUtil.isNotEmpty(modelIds)) {
//            modelIds.forEach(modelId -> {
//                List<Long> subModelUnitIdList = getSubModelUnitIdList(modelId);
//                unitIdList.addAll(subModelUnitIdList);
//            });
//        }
//
//        List<EnvironmentMapDTO> ls = new ArrayList<>();
//
//        // 根据设备类型、型号查询设备
//        List<ToiletDeviceDTO> modelDeviceList = toiletDeviceMapper.selectDeviceUnitIds(unitIdList,toiletId);
//
//        if (CollectionUtil.isNotEmpty(modelDeviceList)) {
//            modelDeviceList.forEach(toiletDeviceDTO -> {
//                EnvironmentMapDTO dto = new EnvironmentMapDTO();
//                Device device = toiletDeviceDTO.getDevice();
//                if (device != null) {
//                    dto.setDevice(device);
//                    String code = device.getCode();
//                    if (StringUtils.isNotBlank(code)) {
//                        MonitorQueryVO monitorQueryVO = new MonitorQueryVO();
//                        monitorQueryVO.setDeviceCode(code);
//                        JSONArray physicModel = monitorService.getPhysicModelPropAndFilter(monitorQueryVO);
//                        dto.setPhysicModel(physicModel);
//                    }
//                    QueryWrapper<DeviceStatus> statusQw = new QueryWrapper<>();
//                    statusQw.eq("device_code_", code);
//                    List<DeviceStatus> statusList = deviceStatusMapper.selectList(statusQw);
//                    if(CollectionUtil.isNotEmpty(statusList)){
//                        dto.setDeviceStatusList(statusList);
//                        ls.add(dto);
//                    }
//                }
//            });
//        }

//        return RestBuilders.successBuilder().data(ls).build();
    }

    @Override
    public RestMessage energyCount(ToiletEnergyStatisticsPageVo toiletEnergyStatisticsPageVo) {
        //厕所能耗统计，根据类型分组
        Date startTime = toiletEnergyStatisticsPageVo.getStartTime();
        Date endTime = toiletEnergyStatisticsPageVo.getEndTime();
        if(startTime == null && endTime == null){
            Date now = DateUtil.date();
            startTime = DateUtil.offsetDay(now,-6);
            endTime = DateUtil.endOfDay(now);
        }
        startTime = DateUtil.beginOfDay(startTime);
        endTime = DateUtil.endOfDay(endTime);
        toiletEnergyStatisticsPageVo.setStartTime(startTime);
        toiletEnergyStatisticsPageVo.setEndTime(endTime);

        List<ToiletEnergyStatistics> energyList = toiletEnergyStatisticsMapper.queryEnergyStatisticsGroupByType(toiletEnergyStatisticsPageVo);
        Map<String,BigDecimal> eleMp = new HashMap<>();
        Map<String,BigDecimal> waterMp = new HashMap<>();
        //转map
        if(CollectionUtil.isNotEmpty(energyList)){
            energyList.forEach(e->{
                Integer type = e.getType();
                Date statisticsDate = e.getStatisticsDate();
                String dateStr = DateUtil.format(statisticsDate, DatePattern.NORM_DATE_PATTERN);
                BigDecimal useDosage = e.getUseDosage();
                if(type.intValue() == 1){
                    //水
                    waterMp.put(dateStr,useDosage);
                }
                if(type.intValue() == 2){
                    //电
                    eleMp.put(dateStr,useDosage);
                }
            });
        }


        //数据补全
        Map<String, Map<String,BigDecimal>> mp = new HashMap<>();
        Map<String,BigDecimal> finEleMp = new TreeMap<>();
        Map<String,BigDecimal> finWaterMp = new TreeMap<>();
        while (startTime.before(endTime)){
            String curStr = DateUtil.format(startTime, DatePattern.NORM_DATE_PATTERN);
            if(waterMp.get(curStr) == null){
                finWaterMp.put(curStr,BigDecimal.ZERO);
            }else {
                finWaterMp.put(curStr,waterMp.get(curStr));
            }
            if(eleMp.get(curStr) == null){
                finEleMp.put(curStr,BigDecimal.ZERO);
            }else {
                finEleMp.put(curStr,eleMp.get(curStr));
            }
            startTime = DateUtil.offsetDay(startTime,1);
        }

        mp.put("ele",finEleMp);
        mp.put("water",finWaterMp);
        return RestBuilders.successBuilder().data(mp).build();
    }

    /**
     * @param subModelId 子模块modelId
     * @return
     */
    private List<Long> getSubModelUnitIdList(Long subModelId) {
        Assert.isTrue(subModelId != null, "subModelId不能为空");
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);

        QueryWrapper<DeviceApplicationModelDeviceUnitConfigSub> qw = new QueryWrapper<>();
        qw.eq("model_id_", subModelId);
        qw.eq("application_id_", baseApplication.getId());
        DeviceApplicationModelDeviceUnitConfigSub configSub = deviceApplicationModelDeviceUnitConfigSubMapper.selectOne(qw);
        Assert.isTrue(configSub != null, "未查询到子模块设备类型、型号配置信息");
        String deviceUnitIdStr = configSub.getDeviceUnitId();
        Assert.isTrue(StringUtils.isNotBlank(deviceUnitIdStr), "子模块设备类型、型号配置为空");
        String[] deviceUnitIdArr = deviceUnitIdStr.split(",");
        List<Long> deviceUnitIdList = Arrays.stream(deviceUnitIdArr).distinct().map(Long::valueOf).collect(Collectors.toList());
        return deviceUnitIdList;
    }

    @Override
    public List<Map<String, Object>> queryLastDateFromEsByParams(String deviceUnitCode,List<String> deviceCodeList,
            String queryTimeStart,String queryTimeEnd){
        if(StringUtils.isEmpty(deviceUnitCode)){
            return new ArrayList<>(0);
        }
        SearchHits deviceDataListFromEs = getDeviceDataListFromES(deviceUnitCode,deviceCodeList,queryTimeStart,queryTimeEnd);
        return deviceDataListFromEs == null ? new ArrayList<>(0) :
                Stream.of(deviceDataListFromEs.getHits()).filter(Objects::nonNull)
                        .map(e->{
                            Object data = e.getSourceAsMap().getOrDefault("data", "{}");
                            return JSON.parseObject(JSON.toJSONString(data));
                        }).collect(Collectors.toList());
    }

    private SearchHits getDeviceDataListFromES(String deviceUnitCode,List<String> deviceCodeList,
                                               String queryTimeStart,String queryTimeEnd) {

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 以下是查询条件，可以参考官网手册
        // 筛选查询字段
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        // 查询条件
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(StringUtils.isNotEmpty(deviceUnitCode)){
            boolQuery.filter(QueryBuilders.termQuery("deviceUnitCode", deviceUnitCode));
        }
        if(CollectionUtil.isNotEmpty(deviceCodeList)){
            boolQuery.filter(QueryBuilders.termsQuery("deviceCode", deviceCodeList));
        }
        boolQuery.filter(queryBuilder);

        searchSourceBuilder.query(boolQuery);

        // 字段过滤
        String[] includeFields = new String[]{};
        String[] excludeFields = new String[]{"sourceRef"};
        searchSourceBuilder.fetchSource(includeFields, excludeFields);
        // 构建排序规则
        searchSourceBuilder.sort("createTime", SortOrder.DESC);
        searchSourceBuilder.size(1);

        // 生成索引名称
        String[] indices = EsCalendar.getDeviceFlowArrayIndex(queryTimeStart, queryTimeEnd);

        // 忽略不可用索引，允许索引不不存在，通配符表达式将扩展为打开的索引
        SearchRequest searchRequest = new SearchRequest(indices);
        searchRequest.indicesOptions(IndicesOptions.fromOptions(true, true, true, false));
        searchRequest.source(searchSourceBuilder);

        try {
            long startTime = System.currentTimeMillis();
            log.info("设备数据详情ES请求数据：" + searchRequest);
            RestHighLevelClient hclient = eSconfig.getFactory().getRhlClient();
            SearchResponse searchResponse = hclient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits hits = searchResponse.getHits();
            long sysEndTime = System.currentTimeMillis();
            log.info("es查询用时:" + (sysEndTime - startTime) + "ms");
            return hits;
        } catch (Exception e) {
            log.error("getDeviceDataListFromES 搜索ES数据出错", e);
        }
        return null;
    }
}
