package com.smartPark.business.toilet.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.toilet.entity.*;
import com.smartPark.business.toilet.entity.dto.DeviceControlRecordDTO;
import com.smartPark.business.toilet.entity.dto.SmartServeDeviceDTO;
import com.smartPark.business.toilet.entity.dto.ToiletDeviceDTO;
import com.smartPark.business.toilet.entity.vo.*;
import com.smartPark.business.toilet.mapper.*;
import com.smartPark.business.toilet.service.ToiletDeviceService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.CommonConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.common.device.mapper.*;
import com.smartPark.common.device.service.DeviceUnitPropertyService;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.*;
import com.smartPark.common.entity.device.dto.DeviceUnitDTO;
import com.smartPark.common.monitor.service.MonitorService;
import com.smartPark.common.monitor.vo.MonitorQueryVO;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.rpc.RpcEnum;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * ToiletDevice表服务实现类
 *
 * <AUTHOR>
 * @date 2023/04/18
 */
@Slf4j
@Service
public class ToiletDeviceServiceImpl extends ServiceImpl
        <ToiletDeviceMapper, ToiletDevice> implements ToiletDeviceService {
    @Resource
    private CommonService commonService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private DeviceStatusMapper deviceStatusMapper;

    @Resource
    private DeviceTypeMapper deviceTypeMapper;

    @Resource
    private ToiletInfoMapper toiletInfoMapper;

    @Resource
    private DeviceUnitMapper deviceUnitMapper;

    @Resource
    private DeviceApplicationModelDeviceUnitConfigSubMapper deviceApplicationModelDeviceUnitConfigSubMapper;

    @Resource
    private DeviceControlRecordMapper deviceControlRecordMapper;

    @Resource
    private DeviceControlParamMapper deviceControlParamMapper;

    @Resource
    private ToiletDeviceTacticMapper toiletDeviceTacticMapper;

    @Resource
    private ToiletDevicePlanMapper toiletDevicePlanMapper;

    @Resource
    private ToiletDevicePlanParamMapper toiletDevicePlanParamMapper;

    @Resource
    private ToiletDeviceAdMapper toiletDeviceAdMapper;

    @Resource
    private ToiletEnergyStatisticsMapper toiletEnergyStatisticsMapper;

    @Resource
    private MonitorService monitorService;
    @Autowired
    private DeviceUnitPropertyService deviceUnitPropertyService;


    /**
     * 或者保存设备\设施关联的实体
     *
     * @return
     */
    private DeviceApplicationModelRef getDeviceApplicationModelRef(Integer type) {
        DeviceApplicationModelRef device = new DeviceApplicationModelRef();
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);
        if (type != null) {
            if (type == 0) {
                device = DeviceApplicationModelRef.getDevice(baseApplication);
            } else {
                device = DeviceApplicationModelRef.getInstall(baseApplication);
            }
        }
        device.setModelId(DeviceModelConstant.TOILET);
        return device;
    }

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        cn.hutool.core.lang.Assert.isTrue(CollectionUtil.isNotEmpty(idList), "至少选择一条数据");
        StringJoiner sj = new StringJoiner(",");
        sj.add("删除设备,设备编号:");
        for (int i = 0; i < idList.size(); i++) {
            String id = idList.get(i).toString();
            ToiletDevice toiletDevice = baseMapper.getOneById(id);
            if (toiletDevice != null) {
                if (i < 5) {
                    sj.add(toiletDevice.getDeviceCode());
                }
                if (i == 5) {
                    sj.add("等等");
                }
                DeviceApplicationModelRef damf = getDeviceApplicationModelRef(0);
                damf.setActionType(EventUtil.DELETE);
                damf.setDeviceCode(toiletDevice.getDeviceCode());
                //业务数据删除
                removeById(id);
                //删除关系
                EventUtil.publishRefEvent(damf);
            }
        }
        LogHelper.setLogInfo(null, sj.toString(), null, null, sj.toString());
        return true;
    }


    @Override
    public boolean saveOne(ToiletDevice livableToiletDevice) {
        commonService.setCreateAndModifyInfo(livableToiletDevice);

        validParamRequired(livableToiletDevice);
        validRepeat(livableToiletDevice);
        validParamFormat(livableToiletDevice);
        return save(livableToiletDevice);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(ToiletDevice livableToiletDevice) {
        Assert.notNull(livableToiletDevice.getId(), "id不能为空");
        commonService.setModifyInfo(livableToiletDevice);

        validRepeat(livableToiletDevice);
        validParamFormat(livableToiletDevice);
        ToiletDevice toiletDevice = baseMapper.getOneById(livableToiletDevice.getId());
        Long toiletId = toiletDevice.getToiletId();
        ToiletInfo oldToiletInfo = null;
        ToiletInfo newToiletInfo = null;
        if (toiletId != null) {
            oldToiletInfo = toiletInfoMapper.getOneById(toiletId);
        }
        Long newToiletId = livableToiletDevice.getToiletId();
        if (newToiletId != null) {
            newToiletInfo = toiletInfoMapper.getOneById(toiletId);
        }
        boolean ok = updateById(livableToiletDevice);
        StringJoiner sj = new StringJoiner(",");
        sj.add("修改设备,设备编号:");
        sj.add(toiletDevice.getDeviceCode());
        if (oldToiletInfo != null) {
            sj.add("原公厕[" + oldToiletInfo.getToiletName() + "]");
        } else {
            sj.add("原公厕[无]");
        }
        if (newToiletInfo != null) {
            sj.add("新公厕[" + newToiletInfo.getToiletName() + "]");
        } else {
            sj.add("新公厕[无]");
        }

        LogHelper.setLogInfo(null, "公厕id:" + livableToiletDevice.getToiletId(), JSONUtil.toJsonStr(toiletDevice), JSONUtil.toJsonStr(livableToiletDevice), sj.toString());
        return ok;
    }

    @Override
    public IPage<ToiletDevice> selectPage(Page page, ToiletDevice livableToiletDevice) {
        return baseMapper.selectPage(page, livableToiletDevice);
    }

    @Override
    public void export(ToiletDevice livableToiletDevice, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public RestMessage batchInsert(ToiletDevicePageVo toiletDevicePageVo) {
        StringJoiner sj = new StringJoiner(",");
        sj.add("关联设备,设备编号:");

        List<DeviceExtendInfo> ls = toiletDevicePageVo.getDeviceExtendInfoList();
        //终端类型，1气体检测，2水表设备，3电表设备，4广告信息屏，5杀菌设备，6人流检测设备
        //TODO
        AtomicInteger num = new AtomicInteger();
        Integer type = toiletDevicePageVo.getType();
        if (CollectionUtil.isNotEmpty(ls)) {
            ls.forEach(dei -> {
                ToiletDevice toiletDevice = initToiletDeviceInfo(dei, type);
                if (toiletDevice != null) {
                    Device device = deviceMapper.selectById(dei.getDeviceId());
                    if (device != null) {
                        toiletDevice.setDeviceName(device.getName());
                        if (num.get() <5) {
                            sj.add(device.getCode());
                        }
                        else if (num.get() == 5) {
                            sj.add("等等");
                        }
                        num.getAndIncrement();
                    }
                    baseMapper.insert(toiletDevice);
                    DeviceApplicationModelRef damf = getDeviceApplicationModelRef(0);
                    damf.setDeviceCode(toiletDevice.getDeviceCode());
                    //增加关系
                    EventUtil.publishRefEvent(damf);
                }
            });
        }
        LogHelper.setLogInfo(null, sj.toString(), null, null, sj.toString());
        return RestBuilders.successBuilder().build();
    }

    @Override
    public IPage<ToiletDeviceDTO> selectDtoPage(Page page, ToiletDeviceVo toiletDeviceVo) {
        IPage<ToiletDeviceDTO> iPage = baseMapper.selectDtoPage(page, toiletDeviceVo);
        iPage.getRecords().forEach(dto -> {
            DeviceUnit deviceUnit = dto.getDeviceUnit();
            if (deviceUnit != null) {
                Long deviceTypeId = deviceUnit.getDeviceTypeId();
                DeviceType deviceType = deviceTypeMapper.selectById(deviceTypeId);
                dto.setDeviceType(deviceType);
            }
        });
        return iPage;
    }

    @Override
    public ToiletDeviceDTO selectDtoOne(ToiletDevice toiletDevice) {
        Long id = toiletDevice.getId();
        Assert.isTrue(id != null, "id不能为空");
        ToiletDeviceDTO dto = baseMapper.selectDtoById(id);
        if (dto != null) {
            DeviceUnit deviceUnit = dto.getDeviceUnit();
            if (deviceUnit != null) {
                Long deviceTypeId = deviceUnit.getDeviceTypeId();
                DeviceType deviceType = deviceTypeMapper.selectById(deviceTypeId);
                dto.setDeviceType(deviceType);
            }
            //属性查询
            setDevicePropertyStatus(dto);
            String code = dto.getDeviceCode();
            MonitorQueryVO monitorQueryVO = new MonitorQueryVO();
            monitorQueryVO.setDeviceCode(code);
            JSONArray physicModel = monitorService.getPhysicModelPropAndFilter(monitorQueryVO);
            dto.setPhysicModel(physicModel);
        }
        return dto;
    }

    @Override
    public RestMessage batchStartAndStop(ToiletDevicePageVo toiletDevicePageVo) {
        String ids = toiletDevicePageVo.getIds();
        Integer useStatus = toiletDevicePageVo.getUseStatus();
        StringJoiner sj = new StringJoiner(",");
        if (useStatus == 1) {
            sj.add("启用,设备名称：");
        } else if (useStatus == 0) {
            sj.add("停用，设备名称：");
        }
        if (StringUtils.isNotBlank(ids)) {
            String[] idArr = ids.split(",");
            for (int i = 0; i < idArr.length; i++) {
                String id = idArr[i];
                ToiletDevice dbDevice = baseMapper.getOneById(id);
                if (i < 5) {
                    sj.add(dbDevice.getDeviceName());
                }
                if (i == 5) {
                    sj.add("等等");
                }
                if (dbDevice != null) {
                    sj.add(dbDevice.getDeviceName());
                }
                ToiletDevice toiletDevice = new ToiletDevice();
                toiletDevice.setId(Long.valueOf(id));
                toiletDevice.setUseStatus(useStatus);
                commonService.setModifyInfo(toiletDevice);
                baseMapper.updateById(toiletDevice);
            }
        }
        LogHelper.setLogInfo(null, ids, null, null, sj.toString());
        return RestBuilders.successBuilder().build();
    }

    @Override
    public IPage<SmartServeDeviceDTO> selectModelDeviceDtoPage(Page page, SmartServeDeviceVo smartServeDeviceVo) {
        //先根据modelId查询出所有的设备
        Long subModelId = smartServeDeviceVo.getSubModelId();

        List<Long> deviceUnitIdList = getSubModelUnitIdList(subModelId);
        smartServeDeviceVo.setDeviceUnitIdList(deviceUnitIdList);

        Map<Long,JSONArray> pmMap = new HashMap<>();

        IPage<SmartServeDeviceDTO> iPage = baseMapper.selectModelDeviceDtoPage(page, smartServeDeviceVo);
        iPage.getRecords().forEach(dto -> {
            //查询物模型
            queryDevicePhysicModel(dto,pmMap);

            //查询最新上报记录
            String deviceCode = dto.getDeviceCode();
            ToiletDeviceDTO tdd = new ToiletDeviceDTO();
            tdd.setDeviceCode(deviceCode);
            setDevicePropertyStatus(tdd);
            dto.setDevicePropertyStatusList(tdd.getDevicePropertyStatusList());

            //查询最后一次控制记录
            queryLastControlSetTime(dto, deviceCode, subModelId,deviceUnitIdList);

        });

        return iPage;
    }

    /**
     * 查询设备物模型
     * @param dto 设备dto
     * @param pmMap 物模型缓存
     */
    private void queryDevicePhysicModel(SmartServeDeviceDTO dto, Map<Long, JSONArray> pmMap) {
        JSONArray physicModel = null;
        String code = dto.getDeviceCode();
        if(dto.getDevice() != null){
            Long unitId = dto.getDevice().getDeviceUnitId();
            if(unitId != null){
                physicModel = pmMap.get(unitId);
            }
        }
        if(physicModel == null){
            MonitorQueryVO monitorQueryVO = new MonitorQueryVO();
            monitorQueryVO.setDeviceCode(code);
            physicModel = monitorService.getPhysicModelPropAndFilter(monitorQueryVO);
        }
        dto.setPhysicModel(physicModel);
    }

    /**
     * 查询最后一次控制记录
     *
     * @param dto              传入的dto
     * @param deviceCode       设备编码
     * @param subModelId       子模块modelId
     * @param deviceUnitIdList
     */
    private void queryLastControlSetTime(SmartServeDeviceDTO dto, String deviceCode, Long subModelId, List<Long> deviceUnitIdList) {
        //1901气体监测、1902能耗，1903广告，1904智能联动，1906多网智能
        if (subModelId == 1904) {
            QueryWrapper<DeviceControlRecord> qw = new QueryWrapper<>();
            qw.eq("device_code_", deviceCode);
            qw.eq("deleted_", CommonConstant.NOT_DELETE);
            qw.orderByDesc("create_time_");
            qw.last("limit 1");
            DeviceControlRecord dcr = deviceControlRecordMapper.selectOne(qw);
            if (dcr != null) {
                dto.setLastControlTime(dcr.getCreateTime());
            }
        }
        if (subModelId == 1903){
            queryAdExtendInfo(dto, deviceCode);
        }
        if (subModelId == 1902){
            //queryEnergyExtendInfo(dto, deviceCode, subModelId,deviceUnitIdList);
        }
    }

    private void queryEnergyExtendInfo(SmartServeDeviceDTO dto, String deviceCode, Long subModelId, List<Long> deviceUnitIdList) {
        //设备编号不会重复，所以直接用设备编号维度
        Date today = DateUtil.date();
        Date startTime = DateUtil.beginOfDay(today);
        Date endTime = DateUtil.endOfDay(today);
        ToiletDevice toiletDevice = new ToiletDevice();
        toiletDevice.setToiletId(dto.getToiletId());
        ToiletEnergyStatisticsPageVo pageVo = new ToiletEnergyStatisticsPageVo();
        pageVo.setToiletDevice(toiletDevice);

        pageVo.setStatisticsDate(DateUtil.format(today, DatePattern.NORM_DATE_PATTERN));

        Map<String,Object> extendMap = new HashMap<>();
        extendMap.put("todayUseWater",0);
        extendMap.put("todayUseEle",0);
        extendMap.put("totalUseWater",0);
        extendMap.put("totalUseEle",0);
        //查询今日用电、用水
        List<ToiletEnergyStatistics> todayUseLs = toiletEnergyStatisticsMapper.queryEnergyStatisticsGroupByType(pageVo);
        if(CollectionUtil.isNotEmpty(todayUseLs)){
            todayUseLs.stream().forEach(d->{
                Integer type = d.getType();
                if("1".equals(type)){
                    extendMap.put("todayUseWater",d.getUseDosage());
                }else if("2".equals(type)){
                    extendMap.put("todayUseEle",d.getUseDosage());
                }
            });
        }
        //查询总用电、用水
        pageVo.setStatisticsDate(null);
        List<ToiletEnergyStatistics> totalUseLs = toiletEnergyStatisticsMapper.queryEnergyStatisticsGroupByType(pageVo);
        if(CollectionUtil.isNotEmpty(totalUseLs)){
            totalUseLs.stream().forEach(d->{
                Integer type = d.getType();
                if("1".equals(type)){
                    extendMap.put("totalUseWater",d.getUseDosage());
                }else if("2".equals(type)){
                    extendMap.put("totalUseEle",d.getUseDosage());
                }
            });
        }
        //查询报警数量,090502\090503
        List<String> alarmTypeList = new ArrayList<>();
        alarmTypeList.add("090502");
        alarmTypeList.add("090503");
        int alarmCount = toiletEnergyStatisticsMapper.queryAlarmCountBy(pageVo);
        extendMap.put("alarmCount",alarmCount);
        dto.setExtendMap(extendMap);
    }

    private void queryAdExtendInfo(SmartServeDeviceDTO dto, String deviceCode) {
        QueryWrapper<ToiletDeviceAd> qw = new QueryWrapper<>();
        qw.eq("toilet_device_id_", dto.getId());
        qw.eq("deleted_", CommonConstant.NOT_DELETE);
        List<ToiletDeviceAd> adList = toiletDeviceAdMapper.selectList(qw);
        AtomicReference<Date> lastAdUpdateTime = new AtomicReference<>();
        if(CollectionUtil.isNotEmpty(adList)){
            AtomicInteger hours = new AtomicInteger();
            AtomicInteger minutes = new AtomicInteger();
            AtomicInteger seconds = new AtomicInteger();

            adList.stream().forEach(d->{
                Date lastUpTime = lastAdUpdateTime.get();
                String adPlayTime = d.getAdPlayTime();
                if(StringUtils.isNotBlank(adPlayTime)){
                    String[] adPlayTimeArr = adPlayTime.split(":");
                    hours.addAndGet(Integer.parseInt(adPlayTimeArr[0]));
                    minutes.addAndGet(Integer.parseInt(adPlayTimeArr[1]));
                    seconds.addAndGet(Integer.parseInt(adPlayTimeArr[2]));
                }
                if(d.getModifyTime() != null && lastUpTime != null && lastUpTime.before(d.getModifyTime())){
                    lastAdUpdateTime.set(d.getModifyTime());
                }else{
                    lastAdUpdateTime.set(d.getModifyTime());
                }
            });
               /* Double seToMinNum = NumberUtil.div(seconds.get(), 60,0, RoundingMode.HALF_DOWN);
                int finSeNum = seconds.get() % 60;
                Double mins = NumberUtil.add(seToMinNum.doubleValue(), minutes.get());
                int finMins = (int) (mins % 60);
                Double minToHourNum = mins / 60;
                Double hour = NumberUtil.add(minToHourNum.doubleValue(), hours.get());
                dto.setTotalPlayTime(hour.intValue() + "小时" + finMins + "分" + finSeNum + "秒");*/
            Double seToMinNum = NumberUtil.div(seconds.get(), 60,1, RoundingMode.DOWN);
            int finSeNum = seconds.get() % 60;
            Double mins = NumberUtil.add(seToMinNum.doubleValue(), minutes.get());
            dto.setTotalPlayTime(mins.intValue() + "分" + finSeNum + "秒");
            dto.setAdCount(adList.size());
            dto.setLastAdUpdateTime(lastAdUpdateTime.get());
        }
    }

    /**
     * @param subModelId 子模块modelId
     * @return
     */
    private List<Long> getSubModelUnitIdList(Long subModelId) {
        Assert.isTrue(subModelId != null, "subModelId不能为空");
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);

        QueryWrapper<DeviceApplicationModelDeviceUnitConfigSub> qw = new QueryWrapper<>();
        qw.eq("model_id_", subModelId);
        qw.eq("application_id_", baseApplication.getId());
        DeviceApplicationModelDeviceUnitConfigSub configSub = deviceApplicationModelDeviceUnitConfigSubMapper.selectOne(qw);
        Assert.isTrue(configSub != null, "未查询到子模块设备类型、型号配置信息");
        String deviceUnitIdStr = configSub.getDeviceUnitId();
        Assert.isTrue(StringUtils.isNotBlank(deviceUnitIdStr), "子模块设备类型、型号配置为空");
        String[] deviceUnitIdArr = deviceUnitIdStr.split(",");
        List<Long> deviceUnitIdList = Arrays.stream(deviceUnitIdArr).distinct().map(Long::valueOf).collect(Collectors.toList());
        return deviceUnitIdList;
    }

    @Override
    public List<DeviceUnitDTO> selectSubModelDeviceTypeAndUnit(Page page, SmartServeDeviceVo smartServeDeviceVo) {
        List<DeviceUnitDTO> dtoLs = new ArrayList<>();
        //先根据modelId查询出所有的设备
        Long subModelId = smartServeDeviceVo.getSubModelId();
        List<Long> deviceUnitIdList = getSubModelUnitIdList(subModelId);
        //其实可以用sql一次性关联查询实现，这里用基础方法实际也是一样的
        QueryWrapper<DeviceUnit> qw = new QueryWrapper<>();
        qw.in("id", deviceUnitIdList);
        List<DeviceUnit> unitList = deviceUnitMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(unitList)) {
            unitList.forEach(unit -> {
                DeviceUnitDTO dto = new DeviceUnitDTO();
                BeanUtils.copyProperties(unit, dto);
                Long deviceTypeId = dto.getDeviceTypeId();
                DeviceType deviceType = deviceTypeMapper.selectById(deviceTypeId);
                dto.setDeviceType(deviceType);
                dtoLs.add(dto);
            });
        }

        return dtoLs;
    }

    @Override
    public RestMessage deviceControl(SmartServeDeviceControlVo deviceControlVo) {
        ONode oNode = ONode.loadObj(deviceControlVo);
        AtomicReference<Integer> switchState = new AtomicReference<>();
        AtomicReference<Double> duration = new AtomicReference<>();

        oNode.select("$.deviceControlParamList").forEach(obj -> {
            String key = obj.get("commandKey").getString();
            if ("xs_cc_switch".equals(key)) {
                switchState.set(obj.get("commandValue").getInt());
            }
            if ("duration".equals(key)) {
                duration.set(obj.get("commandValue").getDouble());
            }
        });

        //先根据id查询出设备
        ToiletDevice toiletDevice = deviceControlVo.getToiletDevice();
        Assert.notNull(toiletDevice, "公厕设备不存在");
        Long toiletDeviceId = toiletDevice.getId();
        ToiletDevice dbToiletDevice = baseMapper.selectById(toiletDeviceId);
        //判断设备是否存在，再根据设备关联的公厕id查询公厕信息
        Assert.notNull(dbToiletDevice, "公厕设备不存在");
        Long toiletId = dbToiletDevice.getToiletId();
        Assert.notNull(toiletId, "公厕设备未关联公厕");
        ToiletInfo toiletInfo = toiletInfoMapper.selectById(toiletId);
        Assert.notNull(toiletInfo, "公厕不存在");

        Integer controlType = deviceControlVo.getControlType();
        Assert.notNull(controlType, "控制类型不能为空");
        StringJoiner sj = new StringJoiner("-");
        StringJoiner coreParamSj = new StringJoiner("-");

        sj.add(toiletInfo.getToiletName());
        sj.add(dbToiletDevice.getDeviceName());
        coreParamSj.add("公厕名称：" + toiletInfo.getToiletName());
        coreParamSj.add("设备名称：" + dbToiletDevice.getDeviceName());

        //单控
        if (controlType == 1) {
            if (switchState.get() == 1) {
                sj.add("操作（开启）");
                sj.add("下发杀菌时长：" + duration.get() + "分钟");
            }
            if (switchState.get() == 0) {
                sj.add("操作（关闭）");
            }
        }
        //策略
        if (controlType == 2) {
            tacticLog(deviceControlVo, sj, coreParamSj, switchState, duration);
        }
        //定时任务
        if (controlType == 3) {
            if (switchState.get() == 1) {
                sj.add("操作（开启）");
                sj.add("下发杀菌时长：" + duration.get() + "分钟");
            }
        }
        coreParamSj.add("操作来源：" + controlType);
        coreParamSj.add("操作：" + switchState.get());

        //组织操作记录
        DeviceControlRecordDTO deviceControlRecordDto = initKillDeviceControlRecord(deviceControlVo, dbToiletDevice, controlType, toiletInfo);
        DeviceControlRecord controlRecord = new DeviceControlRecord();
        BeanUtil.copyProperties(deviceControlRecordDto, controlRecord, CopyOptions.create().ignoreNullValue());
        deviceControlRecordMapper.insert(controlRecord);
        List<DeviceControlParam> paramList = deviceControlRecordDto.getParamList();
        if (CollectionUtil.isNotEmpty(paramList)) {
            paramList.forEach(param -> {
                param.setRecordId(controlRecord.getId());
                deviceControlParamMapper.insert(param);
            });
        }
        LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
        //TODO 下发指令,不确定是否要关闭，邓物模型、真是设备再改

        return RestBuilders.successBuilder().build();
    }

    private void tacticLog(SmartServeDeviceControlVo deviceControlVo, StringJoiner sj, StringJoiner coreParamSj, AtomicReference<Integer> switchState, AtomicReference<Double> duration) {

        if (switchState.get() == 1) {
            sj.add("操作（策略开启）");
        }
        if (switchState.get() == 0) {
            sj.add("操作（策略关闭）");
        }
        //策略查询
        ToiletDeviceTactic tactic = deviceControlVo.getToiletDeviceTactic();
        if (tactic != null && tactic.getId() != null) {
            Long tacticId = tactic.getId();
            ToiletDeviceTactic dbTactic = toiletDeviceTacticMapper.selectById(tacticId);
            Assert.notNull(dbTactic, "策略不存在");

            coreParamSj.add("操作来源：策略");
            coreParamSj.add("策略ID：" + dbTactic.getId());

            Long triggerDeviceId = dbTactic.getTriggerDeviceId();
            ToiletDevice triggerDevice = baseMapper.selectById(triggerDeviceId);
            if (triggerDevice != null) {
                sj.add("触发设备：" + triggerDevice.getDeviceName() + "," + tactic.getTriggerPropName() + " " + tactic.getTriggerLogic() + " " + tactic.getTriggerPropValue());

                sj.add("下发杀菌时长:" + duration.get() + "分钟");
                coreParamSj.add("触发设备：" + triggerDevice.getDeviceName());
            }
        }
    }

    @Override
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEVICE_TACTIC, menuCode = "smartPublicToilet:smartServe:linkageKill:tactic", desc = "控制设备")
    public void flowTacticParse(FlowPushData flowPushData) {
        //FlowPushData
        log.debug("----生态宜居：公厕联动设备策略解析----");
        try {
            //流水取deviceStatus信息
            //List<DeviceStatus> deviceStatusList = flowPushData.getDeviceStatusList();
            //查触发设备的最新流水
            QueryWrapper<DeviceStatus> deviceStatusQw = new QueryWrapper<>();
            deviceStatusQw.eq("device_code_", flowPushData.getDevice_id());
            List<DeviceStatus> deviceStatusList = deviceStatusMapper.selectList(deviceStatusQw);
            if(CollectionUtil.isEmpty(deviceStatusList)){
                log.error("触发设备流水不存在，deviceCode:{}",flowPushData.getDevice_id());
                return;
            }

            String deviceCode = flowPushData.getDevice_id();
            QueryWrapper<ToiletDevice> deviceQw = new QueryWrapper<>();
            //1启用，0禁用
            // deviceQw.eq("use_status_",1);
            // deviceQw.eq("device_code_", deviceCode);
            // deviceQw.eq("deleted_", CommonConstant.NOT_DELETE);
            // ToiletDevice triggerDevice = baseMapper.selectOne(deviceQw);
            ToiletDevice triggerDevice = null;
            ToiletDeviceVo toiletDeviceVo = new ToiletDeviceVo();
            toiletDeviceVo.setUseStatus(1);
            toiletDeviceVo.setDeviceCode(deviceCode);
            IPage<ToiletDeviceDTO> toiletDeviceDTOIPage = baseMapper.selectDtoPage(new Page(), toiletDeviceVo);
            if (ObjectUtil.isNotEmpty(toiletDeviceDTOIPage) && ObjectUtil.isNotEmpty(toiletDeviceDTOIPage.getRecords())) {
                triggerDevice = toiletDeviceDTOIPage.getRecords().get(0);
            }

            //触发设备存在，且是启用
            if(triggerDevice == null){
                log.warn("触发设备不存在或者已禁用，deviceCode:{}",deviceCode);
                return;
            }

            // 推送数据到智慧楼宇
            forwardToSmartPark(flowPushData);

            //查询策略
            QueryWrapper<ToiletDeviceTactic> qw = new QueryWrapper<>();
            qw.eq("trigger_device_code_", deviceCode);
            qw.eq("deleted_", CommonConstant.NOT_DELETE);
            List<ToiletDeviceTactic> tacticLs = toiletDeviceTacticMapper.selectList(qw);
            if (CollectionUtil.isNotEmpty(tacticLs) && CollectionUtil.isNotEmpty(deviceStatusList)) {
                tacticLs.forEach(tactic -> {
                    //TODO 根据deviceCode查询设备信息，再查询公厕策略表的deviceCode，是否有策略，如果有策略就判断上报属性值是否超过阈值
                    String tacticKey = tactic.getTriggerPropKey();
                    String logicStr = "#" + tactic.getTriggerPropKey() + "# " + tactic.getTriggerLogic() + " " + tactic.getTriggerPropValue();

                    deviceStatusList.stream().filter(deviceStatus -> tacticKey.equals(deviceStatus.getProp())).forEach(deviceStatus -> {
                        String propKey = deviceStatus.getProp();
                        String propValue = deviceStatus.getValue();
                        if (StringUtils.isNotBlank(propKey) && StringUtils.isNotBlank(propValue)) {
                            String tmpLogicStr = logicStr.replace("#" + propKey + "#", propValue);
                            ScriptEngine eng = new ScriptEngineManager().getEngineByName("js");
                            try {
                                Object eval = eng.eval(tmpLogicStr);
                                if (eval instanceof Boolean) {
                                    boolean isPass = (Boolean) eval;
                                    if (isPass) {
                                        //上报数据符合阈值条件
                                        SmartServeDeviceControlVo deviceControlVo = getTacticDeviceControlVo(tactic);
                                        //转控制指令
                                        deviceControl(deviceControlVo);
                                    }
                                }

                            } catch (ScriptException e) {
                                throw new RuntimeException(e);
                            }
                        }

                    });
                });
            }

        } catch (Exception e) {
            log.error("---生态宜居：公厕联动设备策略解析异常，无法解析---");
        }

    }

    private static final String syncBathroomUrl = "/sync/syncBathroom";
    /**
     * 推送数据到智慧楼宇
     *
     * @param flowPushData
     */
    void forwardToSmartPark(FlowPushData flowPushData){
        try {
            JSONObject data = (JSONObject) flowPushData.getData();
            if (data == null) {
                log.error("---生态宜居：推送数据到智慧楼宇解析异常，推送数据为空---");
                return;
            }
            JSONObject dataJSONObject = data.getJSONObject("data");
            if (dataJSONObject == null) {
                log.error("---生态宜居：推送数据到智慧楼宇异常，推送数据格式不正确 ,{}", dataJSONObject);
                return;
            }
            JSONObject reqData = dataJSONObject.getJSONObject("data");
            if (reqData == null) {
                log.error("---生态宜居：推送数据到智慧楼宇异常，推送数据格式不正确-, {}", reqData);
                return;
            }
            log.info("---生态宜居：推送数据到智慧楼宇,开始转发数据，{}", reqData);
            JSONObject jsonObject = RpcEnum.DATA_CENTER_SERVICE.postForObject(syncBathroomUrl,
                reqData, JSONObject.class);
            log.info("---生态宜居：推送数据到智慧楼宇,转发数据结果：{}", jsonObject);
            if (jsonObject == null || jsonObject.getInteger("resCode")!= 0){
                log.error("---生态宜居：推送数据到智慧楼宇,转发数据失败，{}", jsonObject==null?"null":jsonObject);
            }
        }catch (Exception e){
            log.error("---生态宜居：推送数据到智慧楼宇,转发数据失败", e);
        }
    }

    @Override
    public RestMessage taskToiletKillControl(String param) {
        Assert.notNull(param, "参数不能为空");
        JSONObject paramJson = JSON.parseObject(param);
        Assert.notNull(paramJson, "参数格式不正确");
        Long id = paramJson.getLong("id");
        Long paramId = paramJson.getLong("paramId");
        Integer switchState = paramJson.getIntValue("switchState");
        String controlType = paramJson.getString("controlType");
        String controlSource = paramJson.getString("controlSource");

        ToiletDevicePlan plan = toiletDevicePlanMapper.selectById(id);
        Assert.notNull(plan, "计划不存在");
        ToiletDevicePlanParam planParam = toiletDevicePlanParamMapper.selectById(paramId);
        Assert.notNull(planParam, "计划参数不存在");

        List<DeviceControlParam> deviceControlParamList = new ArrayList<>();
        DeviceControlParam param1 = new DeviceControlParam();
        param1.setCommandKey("xs_cc_switch");
        param1.setCommandValue("1");
        deviceControlParamList.add(param1);
        DeviceControlParam param2 = new DeviceControlParam();
        param2.setCommandKey("duration");
        param2.setCommandValue(planParam.getDuration().toString());
        deviceControlParamList.add(param2);

        ToiletDevice toiletDevice = baseMapper.selectById(plan.getDeviceId());

        SmartServeDeviceControlVo deviceControlVo = new SmartServeDeviceControlVo();
        deviceControlVo.setToiletDevice(toiletDevice);
        deviceControlVo.setDeviceControlParamList(deviceControlParamList);
        deviceControlVo.setControlType(Integer.parseInt(controlType));

        //控制
        deviceControl(deviceControlVo);

        return RestBuilders.successBuilder().build();
    }


    /**
     * 转换控制指令
     *
     * @param deviceControlVo
     */
    private SmartServeDeviceControlVo getTacticDeviceControlVo(ToiletDeviceTactic tactic) {
        SmartServeDeviceControlVo smartServeDeviceControlVo = new SmartServeDeviceControlVo();

        List<DeviceControlParam> paramList = new ArrayList<>();
        //TODO 临时写死，后面改,物模型没有定
        DeviceControlParam paramSwitch = new DeviceControlParam();
        paramSwitch.setCommandKey("xs_cc_switch");
        paramSwitch.setCommandValue(tactic.getControlSwitch());
        paramList.add(paramSwitch);


        //时长
        DeviceControlParam paramDuration = new DeviceControlParam();
        paramDuration.setCommandKey("duration");
        paramDuration.setCommandValue(tactic.getControlSwitch());
        paramList.add(paramDuration);

        QueryWrapper<ToiletDevice> qw = new QueryWrapper<>();
        qw.eq("device_code_", tactic.getDeviceCode());
        qw.eq("deleted_", CommonConstant.NOT_DELETE);

        ToiletDevice toiletDevice = baseMapper.selectOne(qw);
        smartServeDeviceControlVo.setToiletDevice(toiletDevice);
        //策略控制
        smartServeDeviceControlVo.setControlType(2);
        smartServeDeviceControlVo.setDeviceControlParamList(paramList);
        smartServeDeviceControlVo.setToiletDeviceTactic(tactic);
        return smartServeDeviceControlVo;
    }

    private DeviceControlRecordDTO initKillDeviceControlRecord(SmartServeDeviceControlVo deviceControlVo, ToiletDevice toiletDevice, Integer controlType, ToiletInfo toiletInfo) {
        DeviceControlRecordDTO deviceControlRecordDTO = new DeviceControlRecordDTO();

        deviceControlRecordDTO.setDeviceCode(toiletDevice.getDeviceCode());
        List<DeviceControlParam> paramList = deviceControlVo.getDeviceControlParamList();
        AtomicReference<String> controlName = new AtomicReference<>();
        if (CollectionUtil.isNotEmpty(paramList)) {
            paramList.forEach(param -> {
                String commandKey = param.getCommandKey();
                String commandValue = param.getCommandValue();
                if ("xs_cc_switch".equals(commandKey)) {
                    //开关
                    if ("1".equals(commandValue)) {
                        //开
                        param.setCommandName("开启");
                        controlName.set("开启");
                        deviceControlRecordDTO.setControlType(1);
                    } else {
                        //关
                        param.setCommandName("关闭");
                        controlName.set("关闭");
                        deviceControlRecordDTO.setControlType(0);
                    }
                }
                if ("duration".equals(commandKey)) {
                    //时长
                    param.setCommandName("时长");
                }
            });
        }

        deviceControlRecordDTO.setParamList(paramList);
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);
        deviceControlRecordDTO.setApplicationId(baseApplication.getId());
        deviceControlRecordDTO.setControlName(controlName.get());
        deviceControlRecordDTO.setControlSource(deviceControlVo.getControlType());

        commonService.setCreateAndModifyInfo(deviceControlRecordDTO);
        return deviceControlRecordDTO;
    }

    private ToiletDevice initToiletDeviceInfo(DeviceExtendInfo dei, Integer type) {
        ToiletDevice toiletDevice = new ToiletDevice();
        toiletDevice.setUseStatus(1);
        toiletDevice.setDeviceCode(dei.getDeviceId());
        toiletDevice.setType(type);
        toiletDevice.setDeviceId(dei.getDeviceId());
        toiletDevice.setObjId(dei.getObjId());
        //toiletDevice.setToiletId();

        //验证是否存在
        QueryWrapper<ToiletDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted_", 0);
        queryWrapper.eq("device_code_", dei.getDeviceId());
        int count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            log.warn("智慧公厕--设备已存在");
            return null;
        }
        //设置基础信息
        commonService.setCreateAndModifyInfo(toiletDevice);
        return toiletDevice;
    }

    @Override
    public ToiletDevice getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(ToiletDevice livableToiletDevice) {

        /* List<ToiletDevice> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(ToiletDevice::getDeviceCode, livableToiletDevice.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(livableToiletDevice.getId()) || !livableToiletDevice.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("定位设备重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(ToiletDevice livableToiletDevice) {
        //Assert.notNull(livableToiletDevice, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(livableToiletDevice.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(ToiletDevice livableToiletDevice) {
        //Assert.isTrue(livableToiletDevice.getName() == null || livableToiletDevice.getName().length() <= 50,
        //        "名称超长");
    }

    /**
     * 设置设备属性
     *
     * @param deviceDTO
     */
    private void setDevicePropertyStatus(ToiletDeviceDTO deviceDTO) {
//        Map<String,String> includePropName = new HashMap<>(4);
//        includePropName.put("h2s_concentration","硫化氢浓度");
//        includePropName.put("nh3_concentration","氨气浓度");
//        includePropName.put("humidity","湿度");
//        includePropName.put("temperature","温度");
//        Set<String> includeProps = CollectionUtil.newHashSet("h2s_concentration","nh3_concentration","humidity","temperature");
        //获取配置中要显示的属性值
        List<DeviceUnitProperty> deviceUnitProperties = deviceUnitPropertyService.queryProperty(deviceDTO.getDeviceCode());
        List<DeviceUnitProperty> configProperties = deviceUnitProperties.stream().filter(item -> item.getShow().equals(1)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(configProperties)){
            Map<String,String> includePropName = new HashMap<>();
            Map<String,Integer> includePropSort = new HashMap<>();
            Set<String> includeProps = configProperties.stream().map(DeviceUnitProperty::getPropCode).collect(Collectors.toSet());
            configProperties.forEach(item -> includePropName.put(item.getPropCode(),item.getPropName()));
            configProperties.forEach(item -> includePropSort.put(item.getPropCode(),item.getSort()));
            List<DevicePropertyStatus> devicePropertyStatusList = new ArrayList<>();
            List<DeviceStatus> deviceStatusList = queryDeviceStatus(deviceDTO.getDeviceCode(),includeProps);
            deviceStatusList.forEach(m -> {
                DevicePropertyStatus devicePropertyStatus = new DevicePropertyStatus();
                devicePropertyStatus.setProp(m.getProp());
                devicePropertyStatus.setPropName(includePropName.getOrDefault(m.getProp(),""));
                devicePropertyStatus.setValue(m.getValue());
                devicePropertyStatus.setModifyTime(m.getModifyTime());
                devicePropertyStatus.setDeviceCode(m.getDeviceCode());
                devicePropertyStatus.setSort(includePropSort.get(m.getProp())==null?0:includePropSort.get(m.getProp()));
                devicePropertyStatusList.add(devicePropertyStatus);
            });
            devicePropertyStatusList.sort(Comparator.comparing(DevicePropertyStatus::getSort));
            deviceDTO.setDevicePropertyStatusList(devicePropertyStatusList);
        }
    }

    public List<DeviceStatus> queryDeviceStatus(String deviceCode,Set<String> includeProps){
        List<DeviceStatus> deviceStatuses = deviceStatusMapper.selectList(Wrappers.<DeviceStatus>lambdaQuery().eq(DeviceStatus::getDeviceCode, deviceCode));
        return deviceStatuses.stream().filter((e)->includeProps.contains(e.getProp())).collect(Collectors.toList());
    }
}

