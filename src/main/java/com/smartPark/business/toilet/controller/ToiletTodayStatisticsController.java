package com.smartPark.business.toilet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.toilet.entity.LivableToiletFlowRecords;
import com.smartPark.business.toilet.entity.vo.*;
import com.smartPark.business.toilet.service.LivableToiletFlowRecordsService;
import com.smartPark.business.toilet.service.LivableToiletIdleRecordsService;
import com.smartPark.business.toilet.service.LivableToiletStageStandardStatService;
import com.smartPark.business.toilet.service.ToiletTodayStatisticsService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;
import java.util.Map;

/**
 * 智慧公厕/统计分析/今日数据
 * @Description 今日数据
 * <AUTHOR> <PERSON><PERSON>
 * @Date 2023/10/10 15:28
 */
@RequestMapping("toiletTodayStatistics")
@RestController
@Api(tags = "今日数据")
public class ToiletTodayStatisticsController {
    @Autowired
    private ToiletTodayStatisticsService toiletTodayStatisticsService;

    @Autowired
    private LivableToiletFlowRecordsService toiletFlowRecordsService;

    /**
     * 汇总统计
     */
    @GetMapping("/summary")
    @ApiOperation("汇总统计")
    public RestMessage getSummary(ToiletInfoVo toiletInfoVo) {
        //统计汇总
        List<Map<String,Object>> record = toiletTodayStatisticsService.getSummary(toiletInfoVo);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * 客流量统计
     */
    @GetMapping("/toiletFlow/stats")
    @ApiOperation("客流量统计")
    public RestMessage stats() {
        ToiletFlowQueryStatus stats = toiletFlowRecordsService.stats();
        return RestBuilders.successBuilder().data(stats).build();
    }

    /**
     * 客流量：折线图统计
     */
    @PostMapping("/toiletFlow/lineChart")
    @ApiOperation("客流量折线图统计")
    public RestMessage lineChart(@RequestBody QueryLineChartVo lineChartVo) {
        List<ToiletFlowTimeStatusVo> toiletFlowTimeStatusVos = toiletFlowRecordsService.lineChart(lineChartVo);
        return RestBuilders.successBuilder().data(toiletFlowTimeStatusVos).build();
    }

    /**
     * 客流量：列表
     */
    @PostMapping("/toiletFlow/queryPage")
    @ApiOperation("客流量列表")
    public RestMessage lineChart(@RequestBody RequestModel<QueryLineChartVo> model) {
        IPage<LivableToiletFlowRecords> result = toiletFlowRecordsService.queryPage(model);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * 客流量:今日排名
     * @return
     */
    @GetMapping("/toiletFlow/rank")
    @ApiOperation("客流量今日排名")
    public RestMessage rank(ToiletInfoVo toiletInfoVo) {
        List<ToiletFlowGroupStatusVo> result = toiletFlowRecordsService.todayList(toiletInfoVo);
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * 客流量:今日流量时段
     * @return
     */
    @GetMapping("/toiletFlow/timeStats")
    @ApiOperation("今日流量时段客流量")
    public RestMessage timeStats() {
        List<ToiletFlowTimeStatusVo> result = toiletFlowRecordsService.timeStats(null);
        return RestBuilders.successBuilder().data(result).build();
    }

}
