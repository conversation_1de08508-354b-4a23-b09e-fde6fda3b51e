package com.smartPark.business.toilet.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.toilet.entity.dto.ToiletCleanRecordDTO;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.business.toilet.entity.ToiletCleanRecord;
import com.smartPark.business.toilet.service.ToiletCleanRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 智慧公厕/保洁管理/清洁记录
 *
 * <AUTHOR>
 * @date 2023/04/24
 */
@Slf4j
@RestController
@RequestMapping("/toiletCleanRecord")
public class ToiletCleanRecordController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private ToiletCleanRecordService toiletCleanRecordService;

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/selectDtoPage")
    @ApiOperation("查询分页")
    public RestMessage selectDtoPage(@RequestBody RequestModel<ToiletCleanRecordDTO> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<ToiletCleanRecordDTO> record = toiletCleanRecordService.selectDtoPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }


}

