package com.smartPark.business.toilet.entity.dto;

import cn.hutool.json.JSONArray;
import com.smartPark.business.toilet.entity.ToiletDevice;
import com.smartPark.business.toilet.entity.ToiletInfo;
import com.smartPark.common.entity.device.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 公厕设备实体类DTO
 *
 * <AUTHOR>
 * @date 2023/04/18
 */

@Data
@Accessors(chain = true)
public class ToiletDeviceDTO extends ToiletDevice {
    /**
     * 设备部件信息
     */
    private ObjInfo objInfo;
    /**
     * 设备扩展信息
     */
    private DeviceExtendInfo deviceExtendInfo;

    /**
     * 设备型号信息
     */
    private DeviceUnit deviceUnit;
    /**
     * 设备类型信息
     */
    private DeviceType deviceType;

    /**
     * 公厕信息
     */
    private ToiletInfo toiletInfo;

    /**
     * 设备属性状态信息
     */
    private List<DevicePropertyStatus> devicePropertyStatusList;
    /**
     * 设备信息
     */
    private Device device;

    /**
     * 设备物模型
     */
    private JSONArray physicModel;
}
