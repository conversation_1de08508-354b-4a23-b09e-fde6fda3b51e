package com.smartPark.business.toilet.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备操作记录
 * 先放这里，后面再考虑放到公共模块
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("base_device_control_record")
public class DeviceControlRecord extends Model<DeviceControlRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 应用id
     */
    @TableField("application_id_")
    private Long applicationId;

    /**
     * 设备编码
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 控制名称
     */
    @TableField("control_name_")
    private String controlName;

    /**
     * 控制来源，1单控，2系统
     */
    @TableField("control_source_")
    private Integer controlSource;

    /**
     * 控制类型
     */
    @TableField("control_type_")
    private Integer controlType;

    /**
     * 控制指令
     */
    @TableField("control_command_")
    private String controlCommand;

    /**
     * 结果code
     */
    @TableField("result_code_")
    private String resultCode;

    /**
     * 结果内容
     */
    @TableField("result_content_")
    private String resultContent;

    /**
     * 结果，1成功，2失败，仅仅代表下发动作
     */
    @TableField("result_")
    private Integer result;

    /**
     * 操作人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，0否，1删除
     */
    @TableField("deleted_")
    private Integer deleted;


}
