package com.smartPark.business.toilet.entity.vo;

import com.smartPark.business.toilet.entity.ToiletInfo;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.entity.device.ObjInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ToiletInfoPageVo {

    /**
     * 公厕设施信息
     */
    private List<ObjInfo> objInfoList;

    /**
     * 设备扩展信息list
     */
    private List<DeviceExtendInfo> deviceExtendInfoList;

    private ToiletInfo toiletInfo;

    /**
     * 区域路径
     */
    private List<String> areaPaths;

    /**
     * 模型id
     */
    private List<Long> modelIds;


}
