package com.smartPark.business.toilet.entity.dto;

import com.smartPark.business.toilet.entity.ToiletDevicePlanParam;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ToiletDevicePlanParam实体类DTO
 *
 * <AUTHOR>
 * @date 2023/04/21
 */

@Data
@Accessors(chain = true)
public class ToiletDevicePlanParamDTO extends ToiletDevicePlanParam {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public ToiletDevicePlanParamDTO(ToiletDevicePlanParam livableToiletDevicePlanParam) {
        //this.setName(livableToiletDevicePlanParam.getName());
    }
}
