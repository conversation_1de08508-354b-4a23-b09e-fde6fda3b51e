package com.smartPark.business.toilet.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.toilet.entity.ToiletEnergyStatistics;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smartPark.business.toilet.entity.dto.ToiletEnergyAlarmDTO;
import com.smartPark.business.toilet.entity.vo.ToiletEnergyStatisticsPageVo;
import com.smartPark.business.toilet.entity.vo.ToiletInfoPageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公厕能源统计表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
public interface ToiletEnergyStatisticsMapper extends BaseMapper<ToiletEnergyStatistics> {

    /**
     * 查询能源统计
     * @param pageVo 页面vo
     * @return 统计list
     */
    List<ToiletEnergyStatistics> queryEnergyStatisticsGroupByType(ToiletEnergyStatisticsPageVo pageVo);

    /**
     * 查询厕所告警数量
     * @param pageVo 条件vo
     * @return 告警数量
     */
    int queryAlarmCountBy(ToiletEnergyStatisticsPageVo pageVo);

    /**
     * 查询能源统计
     * @param page 页面vo
     * @param energyStatisticsPageVo 条件vo
     * @return 统计list
     */
    IPage<ToiletEnergyStatistics> selectStatisticsPage(Page page, @Param("energyStatisticsPageVo") ToiletEnergyStatisticsPageVo energyStatisticsPageVo);

    /**
     * 能耗告警dto page
     * @param page 页码
     * @param customQueryParams 条件vo
     * @return dto page
     */
    IPage<ToiletEnergyAlarmDTO> energyAlarmDtoPage(Page page, @Param("energyStatisticsPageVo") ToiletEnergyStatisticsPageVo customQueryParams);


}
