package com.smartPark.business.airstandard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.airstandard.entity.AirStandardDetail;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * AirStandardDetail表数据库访问层
 *
 * <AUTHOR>
 * @since 2023/06/19
 */
public interface AirStandardDetailMapper extends BaseMapper<AirStandardDetail> {


    /**
     * 查询分页
     *
     * @param page                     分页参数对象
     * @param livableAirStandardDetail 过滤参数对象
     * @return 查询分页结果
     */
    IPage<AirStandardDetail> selectPage(Page page, @Param("livableAirStandardDetail") AirStandardDetail livableAirStandardDetail);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    AirStandardDetail getOneById(@Param("id") Serializable id);

    /**
     * 根据配置id查询配置详情
     *
     * @param standardId 配置id
     * @return 配置详情list
     */
    List<AirStandardDetail> queryDetailByStandardId(@Param("standardId") Long standardId);
}

