package com.smartPark.business.pass.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.pass.entity.PassViolation;
import com.smartPark.business.pass.entity.vo.*;
import com.smartPark.business.pass.mapper.PassViolationOverviewMapper;
import com.smartPark.business.pass.service.PassViolationOverviewService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class PassViolationOverviewServiceImpl extends ServiceImpl<PassViolationOverviewMapper, PassViolation> implements PassViolationOverviewService {
    @Resource
    private PassViolationOverviewMapper passViolationOverviewMapper;

    @Override
    public BasicIndexVO basicIndex(PassViolationVo passViolationVo) {
        final Date now = new Date();
        passViolationVo.setEndTime(now);
        long today = passViolationOverviewMapper.selectBasicIndex(passViolationVo.setStartTime(DateUtil.beginOfDay(now)));
        long thisWeek = passViolationOverviewMapper.selectBasicIndex(passViolationVo.setStartTime(DateUtil.beginOfWeek(now)));
        long thisMonth = passViolationOverviewMapper.selectBasicIndex(passViolationVo.setStartTime(DateUtil.beginOfMonth(now)));
        long thisYear = passViolationOverviewMapper.selectBasicIndex(passViolationVo.setStartTime(DateUtil.beginOfYear(now)));
        long yesterday = passViolationOverviewMapper.selectBasicIndex(
                passViolationVo.setStartTime(DateUtil.offsetDay(DateUtil.beginOfDay(now), -1))
                        .setEndTime(DateUtil.offsetDay(now, -1))
        );
        long lastWeek = passViolationOverviewMapper.selectBasicIndex(
                passViolationVo.setStartTime(DateUtil.offsetWeek(DateUtil.beginOfWeek(now), -1))
                        .setEndTime(DateUtil.offsetWeek(now, -1))
        );
        long lastMonth = passViolationOverviewMapper.selectBasicIndex(
                passViolationVo.setStartTime(DateUtil.offsetMonth(DateUtil.beginOfMonth(now), -1))
                        .setEndTime(DateUtil.offsetMonth(now, -1))
        );
        long lastYear = passViolationOverviewMapper.selectBasicIndex(
                passViolationVo.setStartTime(DateUtil.offsetMonth(DateUtil.beginOfYear(now), -12))
                        .setEndTime(DateUtil.offsetMonth(now, -12))
        );
        return new BasicIndexVO(today, yesterday, thisWeek, lastWeek, thisMonth, lastMonth, thisYear, lastYear);
    }

    /**
     * 电镜抓怕的 类型有8种：
     * 闯红灯、逆行、闯禁令、不按所需行进方向驶入导向车道、不按规定车道行驶、违章变道、违章停车、骑线压线
     *
     * @param passViolationVo 查询参数
     * @return 趋势列表
     */
    @Override
    public List<TrendByTimeVO> trendByTime(PassViolationVo passViolationVo) {
        Map<String, TrendByTimeVO> map = new LinkedHashMap<>();
        for (TrendByTimeVO trendByTimeVO : passViolationOverviewMapper.trendByTime(passViolationVo)) {
            map.put(trendByTimeVO.getTime(), trendByTimeVO);
        }
        String format = passViolationVo.getDateFormat().equals("%Y-%m-%d %H") ? "yyyy-MM-dd HH" : "yyyy-MM-dd";
        int calendarUnit = passViolationVo.getDateFormat().equals("%Y-%m-%d %H") ? Calendar.HOUR : Calendar.DAY_OF_MONTH;

        List<String> dateTimes = getDateTimeRange(passViolationVo.getStartTime(), passViolationVo.getEndTime(), format, calendarUnit);

        List<TrendByTimeVO> list = new LinkedList<>();
        for (String dateTime : dateTimes) {
            if (map.get(dateTime) == null) {
                list.add(new TrendByTimeVO(dateTime, null));
            } else {
                list.add(map.get(dateTime));
            }
        }
        return list;
    }

    /**
     * 获取时间跨度内的列表
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param format       时间格式
     * @param calendarUnit 日历单位 如，Calendar.HOUR，Calendar.DAY_OF_MONTH
     * @return 时间字符串的列表
     */
    private static List<String> getDateTimeRange(Date startTime, Date endTime, String format, int calendarUnit) {
        if (startTime.compareTo(endTime) > 0) {
            return Collections.emptyList();
        }
        Calendar start = Calendar.getInstance();
        start.setTime(startTime);
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        List<String> dateTimes = new LinkedList<>();
        String endStr = DateUtil.format(end.getTime(), format);
        while (true) {
            String dateStr = DateUtil.format(start.getTime(), format);
            if (dateStr.compareTo(endStr) > 0) {
                break;
            }
            dateTimes.add(dateStr);
            start.add(calendarUnit, 1);
        }
        return dateTimes;
    }

    @Override
    public List<CountByNameVO> countByName(PassViolationVo passViolationVo) {
        if ("deviceName".equals(passViolationVo.getGroupByField())) {
            passViolationVo.setGroupByField("`device_name_`");
        } else if ("category".equals(passViolationVo.getGroupByField())) {
            passViolationVo.setGroupByField("`category_`");
        } else {
            throw new IllegalArgumentException("不支持的聚合类型");
        }
        return passViolationOverviewMapper.countByName(passViolationVo);
    }

    @Override
    public LinkedHashMap<String, String> getCategory(PassViolationVo passViolationVo) {
        Assert.notNull(passViolationVo, "参数为空");
        Assert.notNull(passViolationVo.getType(), "参数type为空");
        List<EventType> values = Arrays.stream(EventType.values())
                .filter(eventType -> eventType.getType().equals(passViolationVo.getType()))
                .collect(Collectors.toList());
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        for (EventType e : values) {
            map.put(e.getNo(), e.getName());
        }
        return map;
    }
}
