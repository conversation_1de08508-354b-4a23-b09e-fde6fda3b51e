package com.smartPark.business.pass.entity.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.smartPark.business.pass.entity.PassLamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 信号灯表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
public class PassLampVo extends PassLamp {
    /**
     * 区域全路径集合
     */
    private List<String> areaPaths;
}
