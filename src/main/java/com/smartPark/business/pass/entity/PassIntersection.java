package com.smartPark.business.pass.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.smartPark.common.annotation.GaodeText;
import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * 路口事件表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@GaodeText
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("traffic_pass_intersection")
public class PassIntersection extends BaseEntity<PassIntersection> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 事件编号
     */
    @TableField("code_")
    private String code;

    /**
     * 事件类型(字典表)
     */
    @TableField("category_")
    @Trans(type= TransType.DICTIONARY,key = "intersection_events")
    private String category;

    /**
     * 事件详情
     */
    @TableField("content_")
    private String content;

    /**
     * 识别图片/视频url(多个逗号分隔)
     */
    @TableField("attachment_url_")
    private String attachmentUrl;

    /**
     * 记录时间
     */
    @TableField("record_time_")
    private Date recordTime;

    /**
     * 位置定位信息X坐标
     */
    @TableField("objX_")
    private Double objx;

    /**
     * 位置定位信息Y坐标
     */
    @TableField("objY_")
    private Double objy;

    /**
     * 所在街道
     */
    @TableField("szjd_")
    private String szjd;

    /**
     * 所在社区
     */
    @TableField("szsq_")
    private String szsq;

    /**
     * 所在单元网格
     */
    @TableField("szdywg_")
    private String szdywg;

    /**
     * 区域全路径以@拼接
     */
    @TableField("area_path_")
    private String areaPath;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，1删除，0存在
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private Set<Long> ids;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
