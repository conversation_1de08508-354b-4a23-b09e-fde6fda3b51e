package com.smartPark.business.streetlight.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.streetlight.entity.vo.StreetlightAlarmVo;
import com.smartPark.business.streetlight.excel.model.StreetlightAlarmExportModel;
import com.smartPark.business.streetlight.service.StreetlightAlarmService;
import com.smartPark.common.asyncexcel.handler.CommonExportHandler;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@ExcelHandle
public class StreetlightAlarmHandler extends CommonExportHandler<StreetlightAlarmExportModel> {

    @Resource
    private StreetlightAlarmService streetlightAlarmService;

    @Resource
    private RedisUtil redisUtil;



    @Override
    public void init(ExcelContext ctx, DataParam param) {
        // 初始化导出上下文
        ExportContext context = (ExportContext) ctx;
        //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
        WriteSheet sheet = EasyExcel.writerSheet(0, "第一个sheet").head(StreetlightAlarmExportModel.class).build();
        context.setWriteSheet(sheet);
    }


    @Override
    public ExportPage<StreetlightAlarmExportModel> exportData(int startPage, int limit, DataExportParam param) {
        StreetlightAlarmVo manholeAlarmVo = (StreetlightAlarmVo)param.getParam();
        RequestModel<StreetlightAlarmVo> requestModel = new RequestModel<>();
        requestModel.setCustomQueryParams(manholeAlarmVo);
        requestModel.setPage(new Page(1,-1));
        IPage<StreetlightAlarmVo> alarmVoIPage = streetlightAlarmService.queryListByPage(requestModel);
        List<StreetlightAlarmExportModel> exportList = StreetlightAlarmExportModel.getList4Export(alarmVoIPage.getRecords());
        ExportPage<StreetlightAlarmExportModel> result = new ExportPage<>();
        result.setTotal(alarmVoIPage.getTotal());
        result.setCurrent(alarmVoIPage.getCurrent());
        result.setSize(alarmVoIPage.getSize());
        result.setRecords(exportList);
        return result;
    }


}

