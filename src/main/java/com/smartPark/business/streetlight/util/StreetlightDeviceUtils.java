package com.smartPark.business.streetlight.util;

import com.smartPark.business.streetlight.entity.StreetlightControlRecords;
import com.smartPark.business.streetlight.service.StreetlightControlRecordsService;
import com.smartPark.common.device.dto.ApiDownResultDTO;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 路灯工具类
 * <AUTHOR> yuan<PERSON>
 * @Date 2023/5/16 12:15
 */
@Slf4j
@Component
public class StreetlightDeviceUtils implements ApplicationContextAware {

    private static DeviceService deviceService;

    private static StreetlightControlRecordsService streetlightControlRecordsService;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        deviceService = applicationContext.getBean(DeviceService.class);
        streetlightControlRecordsService = applicationContext.getBean(StreetlightControlRecordsService.class);
    }

    /**
     * 路灯控制
     */
    public static void controlToDevice(StreetlightControlRecords controlRecords){
        boolean controlLightExcExecResult = controlLight(controlRecords);
        boolean controlLightStripExecResult = controlLightStrip(controlRecords);
        if (controlLightExcExecResult || controlLightStripExecResult) {
            streetlightControlRecordsService.insert(controlRecords);
        }
    }

    /**
     * 路灯控制
     */
    public static boolean controlLight(StreetlightControlRecords controlRecords){
        Map<String, Object> downParameter = new HashMap<>(2);
        downParameter.put("service_id","open");
        downParameter.put("device_id", controlRecords.getDeviceCode());
        Map<String, Object> parameter = new HashMap<>(3);

        Integer controlType = controlRecords.getControlType();
        if(controlType == null || controlType == 0){
            return false;
        }
        // 主灯逻辑
        if(controlType > 0 && controlType < 10){
            parameter.put("openType",0);
        } else if(controlType > 10 && controlType < 20){
            // 副灯逻辑
            parameter.put("openType",1);
            controlType = controlType - 10;
        } else if(controlType > 20 && controlType < 30){
            // 双灯逻辑
            parameter.put("openType",2);
            controlType = controlType - 20;
        }

        if (Integer.valueOf(1).equals(controlType)){
            parameter.put("open",1);
            parameter.put("limit",100);
        } else if (Integer.valueOf(2).equals(controlType)){
            parameter.put("open",0);
            parameter.put("limit",0);
        } else if (Integer.valueOf(3).equals(controlType)){
            parameter.put("open",1);
            parameter.put("limit", controlRecords.getControlParam());
        } else {
            return true;
        }
        downParameter.put("parameter",parameter);
        try{
            deviceService.send(downParameter);
        } catch (Exception e){
            log.info("路灯控制失败："+controlRecords.getDeviceCode() ,e);
        }
        return true;
    }

    /**
     * 灯带控制
     */
    public static boolean controlLightStrip(StreetlightControlRecords controlRecords){
        // 不存在灯带
        if (!controlRecords.getExistLightStrip()) {
            return false;
        }
        Map<String, Object> downParameter = new HashMap<>(2);
        downParameter.put("device_id", controlRecords.getDeviceCode());
        Map<String, Object> parameter = new HashMap<>(2);
        if(controlRecords.getLightStripControlType() == null || controlRecords.getLightStripControlType() == 0){
            return false;
        }
        if(Integer.valueOf(1).equals(controlRecords.getLightStripControlType())){
            downParameter.put("service_id","lightStripOpen");
            parameter.put("open",1);
        } else if(Integer.valueOf(2).equals(controlRecords.getLightStripControlType())){
            downParameter.put("service_id","lightStripOpen");
            parameter.put("open",0);
        } else if(Integer.valueOf(3).equals(controlRecords.getLightStripControlType())){
            downParameter.put("service_id","setColor");
            parameter.put("color",controlRecords.getLightStripControlParam());
        } else {
            return true;
        }
        downParameter.put("parameter",parameter);
        try{
            deviceService.send(downParameter);
        } catch (Exception e){
            log.info("路灯灯带控制失败："+controlRecords.getDeviceCode() ,e);
        }
        return true;
    }
}
