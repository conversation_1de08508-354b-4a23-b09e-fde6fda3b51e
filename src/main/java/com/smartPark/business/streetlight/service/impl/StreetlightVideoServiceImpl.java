package com.smartPark.business.streetlight.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.streetlight.entity.Streetlight;
import com.smartPark.business.streetlight.entity.StreetlightPlanRef;
import com.smartPark.business.streetlight.entity.StreetlightVideo;
import com.smartPark.business.streetlight.entity.vo.StreetlightDeviceDTO;
import com.smartPark.business.streetlight.entity.vo.StreetlightVideoDeviceDTO;
import com.smartPark.business.streetlight.entity.vo.StreetlightVideoVo;
import com.smartPark.business.streetlight.entity.vo.StreetlightVo;
import com.smartPark.business.streetlight.mapper.StreetlightVideoMapper;
import com.smartPark.business.streetlight.service.StreetlightVideoService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.mapper.DeviceExtendInfoMapper;
import com.smartPark.common.device.util.DeviceUtils;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.DeviceApplicationModelRef;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.rpc.RpcEnum;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import com.smartPark.common.utils.energy.EnergyUtils;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 井盖表 服务实现类
 * </p>
 *
 * <AUTHOR> yuanfeng
 * @since 2022-04-13
 */
@Service
public class StreetlightVideoServiceImpl implements StreetlightVideoService {
  private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(StreetlightVideoServiceImpl.class);

  @Autowired
  private DeviceExtendInfoMapper deviceExtendInfoMapper;
  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private StreetlightVideoMapper streetlightVideoMapper;
  @Autowired
  private RedisUtil redisUtil;

  /**
   * @Description: 单个回放
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public String play(JSONObject jsonObject) {
    JSONObject result = RpcEnum.NB_SERVER.postForObject("/lamp/sxt/queryRecordPlay", jsonObject, JSONObject.class);
    check(result);
    String url = result.getString("data");
    return url;
  }

  /**
   * @Description: 获取历史回放列表
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public JSONArray getRecordById(String id,String clientKey) {
    JSONObject object = RpcEnum.NB_SERVER.getForObject("/lamp/sxt/queryRecord/" + id + "?clientKey=" + clientKey, JSONObject.class);
    check(object);
    JSONArray jsonArray = object.getJSONArray("data");
    return jsonArray;
  }

  /**
   * @Description: 增加路灯视频(批量)
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public void insertBatch(Set<String> deviceCodes) {
    StringJoiner sj = new StringJoiner("，");
    deviceCodes.forEach(code ->{
      StreetlightVideo streetlightVideo = new StreetlightVideo();
      streetlightVideo.setDeviceCode(code);
      /**
       * 验证重复
       */
      this.checkExist(streetlightVideo);
      //验证设备是否符合
//    this.getDeviceExtendInfos(streetlight.getDeviceCode(),true);
      //设置基本属性
      this.setBase(streetlightVideo);
      streetlightVideoMapper.insert(streetlightVideo);
      //保存base一份
      //获取应用名，应用id
      DeviceApplicationModelRef device = getDeviceApplicationModelRef();
      device.setDeviceCode(streetlightVideo.getDeviceCode());
      //保存 关联库
      EventUtil.publishRefEvent(device);
      sj.add(code);
    });
    LogHelper.setLogInfo("",deviceCodes.toString(),null,null,"新增设备，设备编码："+sj);
  }

  /**
   * @Description: 删除路灯视频（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public void delBatch(Set<Long> ids) {
    StringJoiner sj = new StringJoiner("，");
    //获取设备id集合
    List<StreetlightVideo> streetlightList = streetlightVideoMapper.selectBatchIds(ids);
    List<String> deviceIds = streetlightList.stream().map(m -> m.getDeviceCode()).collect(Collectors.toList());
    streetlightVideoMapper.deleteBatchIds(ids);
    //从base中删除
    //获取应用名，应用id
    deviceIds.forEach(code ->{
      DeviceApplicationModelRef device = getDeviceApplicationModelRef();
      device.setDeviceCode(code);
      device.setActionType(EventUtil.DELETE);
      //保存 关联
      EventUtil.publishRefEvent(device);
      sj.add(code);
    });
    LogHelper.setLogInfo("",ids.toString(),null,null,"删除设备，设备编码："+sj);
  }

  /**
   * @Description: 根据id查询路灯视频详情
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public StreetlightVideoDeviceDTO findById(Long id) {
    //设备详情
    StreetlightVideo streetlightVideo = streetlightVideoMapper.selectById(id);
    if (null == streetlightVideo){
      throw new BusinessException("路灯视频已删除");
    }
    StreetlightVideoDeviceDTO deviceDTO = BeanUtil.toBean(streetlightVideo, StreetlightVideoDeviceDTO.class);
    //设置设备属性
    DeviceUtils.setDeviceDetail(deviceDTO);
    if (null != deviceDTO.getDevice()){
      deviceDTO.setStatus(deviceDTO.getDevice().getStatus());
      deviceDTO.setAlarmState(deviceDTO.getDevice().getAlarmState());
    }
    return deviceDTO;
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public IPage<StreetlightVideoVo> queryListByPage2(RequestModel<StreetlightVideoVo> requestModel) {
    Page page = requestModel.getPage();
    StreetlightVideoVo StreetlightVideoVo = requestModel.getCustomQueryParams();
    IPage<StreetlightVideoVo> streetlightIPage = streetlightVideoMapper.queryListByPage(page, StreetlightVideoVo);
    streetlightIPage.getRecords().forEach(m ->{
      //区域范围
      if (StringUtils.isNotBlank(m.getAreaPath())){
        m.setAreaPath(m.getAreaPath().replace("@","/"));
      }
    });
    return streetlightIPage;
  }

  @Override
  public IPage<JSONObject> queryListByPage(RequestModel<JSONObject> requestModel) {
    Page page = requestModel.getPage();
    JSONObject jsonObject = requestModel.getCustomQueryParams();
    jsonObject.put("is_list",0);
    //调用节目列表接口
    JSONObject result = RpcEnum.NB_SERVER.postForObject("/lamp/sxt/queryAll",jsonObject ,JSONObject.class);
    check(result);
    JSONArray jsonArray = result.getJSONArray("data");
    List<JSONObject> list = new ArrayList<>();
    if (null != jsonArray) {
      List<JSONObject> objectList = jsonArray.toJavaList(JSONObject.class);
      //数据再组织一次
      for (JSONObject object : objectList) {
        JSONObject newObject = object.getJSONObject("sxtObject");
        newObject.put("clientKey", object.getString("clientKey"));
        list.add(newObject);
      }
    }
    //关键字过滤
    if (StringUtils.isNotBlank(jsonObject.getString("keyword"))){
      list = list.stream().filter(item -> StringUtils.isNotBlank(item.getString("name")) && item.getString("name").contains(jsonObject.getString("keyword")))
              .collect(Collectors.toList());
    }
    IPage<JSONObject> iPage = EnergyUtils.toPage(list, page);
    return iPage;
  }

  /**
   * 或者保存设备关联的实体
   * @return
   */
  private DeviceApplicationModelRef getDeviceApplicationModelRef() {
    BaseApplication baseApplication = (BaseApplication)redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.TRAFFIC);
    DeviceApplicationModelRef device = DeviceApplicationModelRef.getDevice(baseApplication);
    device.setModelId(DeviceModelConstant.STREET_LIGHT_VIDEO);
    return device;
  }

  /**
   * 验证重复
   */
  private void checkExist(StreetlightVideo streetlightVideo) {
    QueryWrapper<StreetlightVideo> queryWrapper = new QueryWrapper<>();
    //设置判断重复条件
    queryWrapper.eq("device_code_",streetlightVideo.getDeviceCode())
            .eq("deleted_",0);
    //编辑的时候存在id
    Optional.ofNullable(streetlightVideo.getId()).ifPresent(id -> queryWrapper.ne("id_",streetlightVideo.getId()));
    Integer integer = streetlightVideoMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该路灯视频已存在");
    }
  }

  /**
   * 设置基本属性
   * @param streetlightVideo
   */
  private void setBase(StreetlightVideo streetlightVideo) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    streetlightVideo.setModifyTime(new Date());
    streetlightVideo.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == streetlightVideo.getId()){
      streetlightVideo.setCreatorId(userId);
      streetlightVideo.setCreateTime(new Date());
    }
  }

  /**
   * 验证结果
   * @param jsonObject
   */
  private static void check(JSONObject jsonObject) {
    if (!Integer.valueOf(200).equals(jsonObject.getInteger("code"))){
      throw new BusinessException(jsonObject.getString("message"));
    }
  }
}
