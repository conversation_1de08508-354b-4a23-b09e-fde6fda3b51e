package com.smartPark.business.streetlight.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.importer.DataImportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.streetlight.constant.StreetlightControl;
import com.smartPark.business.streetlight.entity.Streetlight;
import com.smartPark.business.streetlight.entity.StreetlightControlRecords;
import com.smartPark.business.streetlight.entity.StreetlightGroup;
import com.smartPark.business.streetlight.entity.dto.StreetlightCountResponseDTO;
import com.smartPark.business.streetlight.entity.StreetlightPlanRef;
import com.smartPark.business.streetlight.entity.vo.StreetlightDeviceAreaVo;
import com.smartPark.business.streetlight.entity.vo.StreetlightDeviceDTO;
import com.smartPark.business.streetlight.entity.vo.StreetlightVo;
import com.smartPark.business.streetlight.excel.handler.StreetlightHandler;
import com.smartPark.business.streetlight.excel.handler.StreetlightImportHandler;
import com.smartPark.business.streetlight.excel.handler.StreetlightImportUpdateHandler;
import com.smartPark.business.streetlight.excel.model.StreetlightExportModelDTO;
import com.smartPark.business.streetlight.excel.model.StreetlightImportModel;
import com.smartPark.business.streetlight.mapper.StreetlightGroupMapper;
import com.smartPark.business.streetlight.mapper.StreetlightMapper;
import com.smartPark.business.streetlight.mapper.StreetlightPlanRefMapper;
import com.smartPark.business.streetlight.service.StreetlightControlRecordsService;
import com.smartPark.business.streetlight.service.StreetlightService;
import com.smartPark.business.streetlight.util.StreetlightDeviceUtils;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.areaTree.service.DeviceAreaTreeService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.mapper.DeviceExtendInfoMapper;
import com.smartPark.common.device.mapper.DeviceMapper;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.device.service.DeviceStatusService;
import com.smartPark.common.device.util.DeviceUtils;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.*;
import com.smartPark.common.entity.deviceArea.DeviceAreaTree;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 井盖表 服务实现类
 * </p>
 *
 * <AUTHOR> yuanfeng
 * @since 2022-04-13
 */
@Service
public class StreetlightServiceImpl extends ServiceImpl<StreetlightMapper, Streetlight> implements StreetlightService {
  private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(StreetlightServiceImpl.class);

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private DeviceExtendInfoMapper deviceExtendInfoMapper;
  @Autowired
  private ObjInfoMapper objInfoMapper;
  @Autowired
  private DeviceMapper deviceMapper;
  @Autowired
  private DeviceStatusMapper deviceStatusMapper;
  @Autowired
  private DeviceStatusService deviceStatusService;
  @Autowired
  private StreetlightControlRecordsService streetlightControlRecordsService;
  @Autowired
  private StreetlightGroupMapper streetlightGroupMapper;
  @Autowired
  private StreetlightPlanRefMapper streetlightPlanRefMapper;
  @Autowired
  private RedisUtil redisUtil;
  @Autowired
  private ExcelService excelService;

  @Autowired
  private DeviceAreaTreeService deviceAreaTreeService;

  @Resource
  private DeviceService deviceService;

  /**
   * 增加
   * @param streetlight
   */
  @Override
  @Transactional
  public void insert(Streetlight streetlight) {
    /**
     * 验证重复
     */
    this.checkExist(streetlight);
    //验证设备是否符合
//    this.getDeviceExtendInfos(streetlight.getDeviceCode(),true);
    //设置基本属性
    this.setBase(streetlight);
    this.save(streetlight);
    //保存base一份
    //获取应用名，应用id
    DeviceApplicationModelRef device = getDeviceApplicationModelRef();
    device.setDeviceCode(streetlight.getDeviceCode());
    //保存 关联库
    EventUtil.publishRefEvent(device);
  }

  /**
   * 或者保存设备关联的实体
   * @return
   */
  private DeviceApplicationModelRef getDeviceApplicationModelRef() {
    BaseApplication baseApplication = (BaseApplication)redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.TRAFFIC);
    DeviceApplicationModelRef device = DeviceApplicationModelRef.getDevice(baseApplication);
    device.setModelId(DeviceModelConstant.STREET_LIGHT);
    return device;
  }

  /**
   * 根据id编辑
   * @param streetlight
   */
  @Override
  public void updateOne(Streetlight streetlight) {
    /**
     * 验证重复
     */
    this.checkExist(streetlight);
    //设置基本属性
    this.setBase(streetlight);
    this.updateById(streetlight);
    Streetlight byId = baseMapper.selectById(streetlight.getId());
    LogHelper.setLogInfo("",streetlight.toString(),null,null,"修改设备，设备编码："+byId.getDeviceCode());

  }

  /**
   * @Description: 查询设备信息
   * <AUTHOR> yuanfeng
   * @param flag true 验证设备码 false不验证
   * @date 2020/11/04 11:42
   */
  @Override
  public StreetlightDeviceDTO findDeviceByDeviceId(String deviceCode, Boolean flag) {
    //校验重复
    if (flag){
      Streetlight streetlight = new Streetlight();
      streetlight.setDeviceCode(deviceCode);
      this.checkExist(streetlight);
    }
    StreetlightDeviceDTO streetlightDeviceDTO = new StreetlightDeviceDTO();
    DeviceExtendInfo deviceExtendInfo = getDeviceExtendInfos(deviceCode,flag);
    streetlightDeviceDTO.setExtendInfo(deviceExtendInfo);
    if (null != deviceExtendInfo && StringUtils.isNotBlank(deviceExtendInfo.getDwbsm())){
        ObjInfo objInfo = objInfoMapper.findByMonitorPointBsm(deviceExtendInfo.getDwbsm());
      streetlightDeviceDTO.setObjInfo(objInfo);
    }
    return streetlightDeviceDTO;
  }

  /**
   * @Description: 删除井盖（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  @Transactional
  public void delBatch(Set<Long> ids) {
    StringJoiner sj = new StringJoiner("，");
    //获取设备id集合
    List<Streetlight> streetlightList = baseMapper.selectBatchIds(ids);
    List<String> deviceIds = streetlightList.stream().map(m -> m.getDeviceCode()).collect(Collectors.toList());
    this.removeByIds(ids);
    //从base中删除
    //获取应用名，应用id
    deviceIds.forEach(code ->{
      DeviceApplicationModelRef device = getDeviceApplicationModelRef();
      device.setDeviceCode(code);
      device.setActionType(EventUtil.DELETE);
      //保存 关联
      EventUtil.publishRefEvent(device);
      sj.add(code);
    });
    //删除分组中设备
    del4Group(ids);
    //删除对应的控制计划
    QueryWrapper<StreetlightPlanRef> queryWrapper = new QueryWrapper<>();
    queryWrapper.in("ref_id_",ids).eq("ref_type_",2);
    streetlightPlanRefMapper.delete(queryWrapper);
    LogHelper.setLogInfo("",ids.toString(),null,null,"删除设备，设备编码："+sj);
  }

  /**
   * 删除分组中的设备
   * @param ids
   */
  private void del4Group(Set<Long> ids) {
    ids.forEach(id ->{
      QueryWrapper<StreetlightGroup> queryWrapper = new QueryWrapper<>();
      queryWrapper.like("streetlight_ids",","+id+",");
      List<StreetlightGroup> streetlightGroups = streetlightGroupMapper.selectList(queryWrapper);
      if (CollectionUtil.isNotEmpty(streetlightGroups)){
        streetlightGroups.forEach(s->{
          String streetlightIds = s.getStreetlightIds();
          if (streetlightIds.equals(","+id+",")){
            s.setStreetlightIds(null);
          }else {
            s.setStreetlightIds(streetlightIds.replace(","+id,""));
          }
          streetlightGroupMapper.updateById(s);
        });
      }
    });
  }

  /**
   * @Description: 根据id查询井盖详情
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public StreetlightDeviceDTO findById(Long id) {
    //设备详情
    Streetlight streetlight = baseMapper.selectById(id);
    if (null == streetlight){
      throw new BusinessException("路灯已删除");
    }
    StreetlightDeviceDTO deviceDTO = BeanUtil.toBean(streetlight, StreetlightDeviceDTO.class);
    //设置设备属性
    DeviceUtils.setDeviceDetail(deviceDTO);
    if (null != deviceDTO.getDevice()){
      deviceDTO.setStatus(deviceDTO.getDevice().getStatus());
      deviceDTO.setAlarmState(deviceDTO.getDevice().getAlarmState());
    }
    return deviceDTO;
  }

  /**
   * @Description: 路灯控制
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public void control(StreetlightControlRecords controlRecords) {
    Streetlight streetlight = baseMapper.selectById(controlRecords.getRefId());

    if (null != streetlight){
      // 是否有灯带
      boolean existLightStrip = deviceStatusService.exist(streetlight.getDeviceCode(), "lightStripType");
      controlRecords.setDeviceCode(streetlight.getDeviceCode());
      controlRecords.setControlSource(StreetlightControl.SOURCE_DEVICE);
      controlRecords.setType(StreetlightControl.TYPE_HAND);
      controlRecords.setBusinessId(controlRecords.getRefId());
      controlRecords.setLightingType(streetlight.getLightingType());
      controlRecords.setExistLightStrip(existLightStrip);
      StreetlightDeviceUtils.controlToDevice(controlRecords);
      LogHelper.setLogInfo("",controlRecords.toString(),null,null,"路灯控制，设备编码："+streetlight.getDeviceCode());
    }
  }

  /**
   * @Description: 根据条件导出
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public Long export(StreetlightVo streetlightVo, HttpServletRequest request, HttpServletResponse response) {
    Long userId = baseUserContextProducer.getCurrent().getId();
    DataExportParam dataExportParam = new DataExportParam();
    dataExportParam.setParam(streetlightVo);
    dataExportParam.setExportFileName("井盖列表");
    dataExportParam.setTenantCode("traffic");
    dataExportParam.setBusinessCode("streetlight");
    dataExportParam.setCreateUserCode(userId.toString());
    Long taskId = excelService.doExport(dataExportParam, StreetlightHandler.class);
    return taskId;
  }

  /**
   * 查询导出excel数据
   * @param streetlightVo
   * @return
   */
  @Override
  public List<StreetlightExportModelDTO> queryList4Export(StreetlightVo streetlightVo) {
    List<StreetlightExportModelDTO> streetlightExportModelDTOS = baseMapper.queryList4Export(streetlightVo);
    return streetlightExportModelDTOS;
  }

  /**
   * 根据设备id获取设备部件码信息
   * @param deviceCode
   * @return
   */
  @Override
  public DeviceExtendInfo getDeviceExtendInfos(String deviceCode, Boolean flag) {
    //根据id查询设备部件码
    QueryWrapper<DeviceExtendInfo> example = new QueryWrapper<>();
    example.eq("device_id", deviceCode);
    List<DeviceExtendInfo> extendInfoList = deviceExtendInfoMapper.selectList(example);
    if (CollectionUtil.isNotEmpty(extendInfoList)){
      DeviceExtendInfo extendInfo = extendInfoList.get(0);
      if (flag){
        checkBsm(extendInfo.getBsm());
      }
      return extendInfo;
    }else if (flag){
      throw new BusinessException("设备编码错误,校验不通过");
    }
    return null;
  }

  /**
   * @Description: 井盖导入修改
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public Long importUpdate(MultipartFile file) throws IOException {
    Long userId = baseUserContextProducer.getCurrent().getId();
    DataImportParam dataImportParam = new DataImportParam()
            .setStream(file.getInputStream())
            .setModel(StreetlightImportModel.class)
            .setBatchSize(3)
            .setFilename("井盖批量修改");
    dataImportParam.setTenantCode("triffic");
    dataImportParam.setBusinessCode("streetlight");
    dataImportParam.setCreateUserCode(userId.toString());
    Long taskId = excelService.doImport(StreetlightImportUpdateHandler.class, dataImportParam);
    return taskId;
  }

  /**
   * @Description: 增加路灯(批量)
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public void insertBatch(Set<String> deviceCodes) {
    StringJoiner sj = new StringJoiner("，");
    deviceCodes.forEach(code ->{
      Streetlight streetlight = new Streetlight();
      streetlight.setDeviceCode(code);
      DeviceExtendInfo deviceExtendInfo = deviceExtendInfoMapper.findByDeviceId(code);
      if(deviceExtendInfo != null){
        // 双灯控制器
        if (StringUtils.startsWith(deviceExtendInfo.getSbxh(),"SDKZQ")) {
          streetlight.setLightingType(2);
        }
        // 灯带控制器
        if (StringUtils.startsWith(deviceExtendInfo.getSbxh(),"DDKZQ-RS485-02")) {
          streetlight.setLightingType(3);
        }
      }
      this.insert(streetlight);
      sj.add(code);
    });
    LogHelper.setLogInfo("",deviceCodes.toString(),null,null,"新增设备，设备编码："+sj);
  }



  /**
   * @Description: 井盖导入
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public Long imports(MultipartFile file) throws IOException {
    Long userId = baseUserContextProducer.getCurrent().getId();
    DataImportParam dataImportParam = new DataImportParam()
            .setStream(file.getInputStream())
            .setModel(StreetlightImportModel.class)
            .setBatchSize(3)
            .setFilename("井盖导入");
    dataImportParam.setTenantCode("traffic");
    dataImportParam.setBusinessCode("streetlight");
    dataImportParam.setCreateUserCode(userId.toString());
    Long taskId = excelService.doImport(StreetlightImportHandler.class, dataImportParam);
    return taskId;
  }

  /**
   * 设置设备属性
   * @param deviceDTO
   */
  private void setDevicePropertyStatus(StreetlightDeviceDTO deviceDTO) {
    //设备属性先写死
    List<DevicePropertyStatus> devicePropertyStatusList = new ArrayList<>();
    //查询设备属性
    QueryWrapper<DeviceStatus> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("device_code_",deviceDTO.getDeviceCode());
    List<DeviceStatus> deviceStatuses = deviceStatusMapper.selectList(queryWrapper);
    //状态
    DevicePropertyStatus devicePropertyStatus1 = new DevicePropertyStatus();
    devicePropertyStatus1.setProp("light");
    devicePropertyStatus1.setPropName("状态");
//    devicePropertyStatus1.setValue("1");
    devicePropertyStatus1.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus1);
    //调光
    DevicePropertyStatus devicePropertyStatus2 = new DevicePropertyStatus();
    devicePropertyStatus2.setProp("angle");
    devicePropertyStatus2.setPropName("调光");
    devicePropertyStatus2.setValue("40%");
    devicePropertyStatus2.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus2);
    devicePropertyStatusList.forEach(d ->{
      List<DeviceStatus> statusList = deviceStatuses.stream().filter(de -> d.getProp().equals(de.getProp()))
              .collect(Collectors.toList());
      if (statusList.size() > 0){
        d.setValue(statusList.get(0).getValue());
      }
    });
    deviceDTO.setDevicePropertyStatusList(devicePropertyStatusList);
  }

  @Override
  public IPage<StreetlightVo> queryListByPage(RequestModel<StreetlightVo> requestModel) {
    Page page = requestModel.getPage();
    StreetlightVo streetlightVo = requestModel.getCustomQueryParams();
    IPage<StreetlightVo> streetlightIPage = baseMapper.queryListByPage(page, streetlightVo);
    streetlightIPage.getRecords().forEach(m ->{
      //区域范围
      if (StringUtils.isNotBlank(m.getAreaPath())){
        m.setAreaPath(m.getAreaPath().replace("@","/"));
      }
      m.setOtherStatus(deviceStatusService.queryMapDeviceStatus(m.getDeviceCode()));
    });
    return streetlightIPage;
  }

  @Override
  public List<StreetlightDeviceAreaVo> getTreeIncludeDevice() {
    List<StreetlightDeviceAreaVo> streetlightDeviceAreaVoList = new ArrayList<>();
    List<DeviceAreaTree> treeList = deviceAreaTreeService.getAreaTree("traffic_streetlight", 1,null);
    // 递归遍历treeList
    treeList.forEach(tree ->{
      StreetlightDeviceAreaVo streetlightDeviceAreaVo = new StreetlightDeviceAreaVo();
      BeanUtils.copyProperties(tree,streetlightDeviceAreaVo);
      StreetlightVo streetlightVo = new StreetlightVo();
      streetlightVo.setAreaPath(tree.getValue());
      List<StreetlightVo> streetlightVoList = baseMapper.queryListByPage(streetlightVo);
      streetlightDeviceAreaVo.setDeviceList(streetlightVoList);
      streetlightDeviceAreaVo.setChildren(getChildren(tree.getChildren()));
      streetlightDeviceAreaVoList.add(streetlightDeviceAreaVo);
    });
    return streetlightDeviceAreaVoList;
  }

  private List<StreetlightDeviceAreaVo> getChildren(List<DeviceAreaTree> children) {
    List<StreetlightDeviceAreaVo> deviceAreaTreeList = new ArrayList<>();
    if(children == null || children.size() == 0){
      return null;
    }
    children.forEach(tree ->{
      StreetlightDeviceAreaVo deviceAreaTree = new StreetlightDeviceAreaVo();
      BeanUtils.copyProperties(tree,deviceAreaTree);
      StreetlightVo streetlightVo = new StreetlightVo();
      streetlightVo.setAreaPath(tree.getValue());
      List<StreetlightVo> streetlightVoList = baseMapper.queryListByPage(streetlightVo);
      deviceAreaTree.setDeviceList(streetlightVoList);
      deviceAreaTree.setChildren(getChildren(tree.getChildren()));
      deviceAreaTreeList.add(deviceAreaTree);
    });
    return deviceAreaTreeList;
  }

  @Override
  public StreetlightCountResponseDTO getStreetlightCount(String lightType) {
    return baseMapper.getStreetlightCount(lightType);
  }

  /**
   * 验证重复
   */
  private void checkExist(Streetlight streetlight) {
    QueryWrapper<Streetlight> queryWrapper = new QueryWrapper<>();
    //设置判断重复条件
    queryWrapper.eq("device_code_",streetlight.getDeviceCode())
            .eq("deleted_",0);
    //编辑的时候存在id
    Optional.ofNullable(streetlight.getId()).ifPresent(id -> queryWrapper.ne("id_",streetlight.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该路灯已存在");
    }
  }

  /**
   * 设置基本属性
   * @param streetlight
   */
  private void setBase(Streetlight streetlight) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    streetlight.setModifyTime(new Date());
    streetlight.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == streetlight.getId()){
      streetlight.setCreatorId(userId);
      streetlight.setCreateTime(new Date());
    }
  }

  /**
   * 验证设备是否符合
   * @param bsm
   */
  private void checkBsm(String bsm) {
    //验证设备标识码是不是20位
    if (bsm.length() != 20){
      throw new BusinessException("设备编码错误,校验不通过");
    }
    String code = bsm.substring(12, 16);
    if (!"0308".equals(code)){
      throw new BusinessException("设备编码错误,校验不通过");
    }
  }
}
