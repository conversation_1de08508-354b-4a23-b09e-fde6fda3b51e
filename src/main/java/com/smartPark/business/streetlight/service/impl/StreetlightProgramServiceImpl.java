package com.smartPark.business.streetlight.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.streetlight.entity.StreetlightGroup;
import com.smartPark.business.streetlight.entity.vo.StreetlightDeviceDTO;
import com.smartPark.business.streetlight.entity.vo.StreetlightProgramDTO;
import com.smartPark.business.streetlight.entity.vo.StreetlightSourceDTO;
import com.smartPark.business.streetlight.mapper.StreetlightGroupMapper;
import com.smartPark.business.streetlight.service.StreetlightProgramService;
import com.smartPark.business.streetlight.service.StreetlightService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.rpc.RpcEnum;
import com.smartPark.common.utils.energy.EnergyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 井盖表 服务实现类
 * </p>
 *
 * <AUTHOR> yuanfeng
 * @since 2022-04-13
 */
@Service
public class StreetlightProgramServiceImpl implements StreetlightProgramService {
  private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(StreetlightProgramServiceImpl.class);

  @Autowired
  private StreetlightGroupMapper streetlightGroupMapper;

  @Autowired
  private StreetlightService streetlightService;

  /**
   * 增加
   * @param streetlightProgramDTO
   */
  @Override
  @Transactional
  public void insert(StreetlightProgramDTO streetlightProgramDTO) {
    //todo 保存
    JSONObject jsonObject = RpcEnum.NB_SERVER.postForObject("/lamp/program/addOrUpdate", streetlightProgramDTO, JSONObject.class);
    check(jsonObject);
    LogHelper.setLogInfo("", streetlightProgramDTO.toString(),"增加节目:"+streetlightProgramDTO.getName());
  }

  /**
   * 根据id编辑
   * @param streetlightProgramDTO
   */
  @Override
  public void updateOne(StreetlightProgramDTO streetlightProgramDTO) {
    //todo 保存
  }

  /**
   * @Description: 删除井盖（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  @Transactional
  public void delById(StreetlightProgramDTO streetlightProgramDTO) {
    //调用nb接口
    JSONObject jsonObject = RpcEnum.NB_SERVER.getForObject("/lamp/program/del/"+streetlightProgramDTO.getId(), JSONObject.class);
    check(jsonObject);
    LogHelper.setLogInfo("", streetlightProgramDTO.getId(),null,null,"删除节目:"+streetlightProgramDTO.getName());
  }

  @Override
  public void play(StreetlightProgramDTO streetlightProgramDTO) {
    List<String> noProps = new ArrayList<>();
    List<String> noPlays = new ArrayList<>();
    //判断是分组还是播放
    Integer refType = streetlightProgramDTO.getRefType();
    List<Long> refIds = streetlightProgramDTO.getRefIds();
    if (Integer.valueOf(1).equals(refType)){
      //分组
      QueryWrapper<StreetlightGroup> queryWrapper = new QueryWrapper<>();
      queryWrapper.in("id_",refIds);
      List<StreetlightGroup> groups = streetlightGroupMapper.selectList(queryWrapper);
      refIds = groups.stream().filter(g -> StringUtils.isNotBlank(g.getStreetlightIds()))
              .map(g -> g.getStreetlightIds()).flatMap(g -> Arrays.asList(g.split(",")).stream())
              .filter(g -> StringUtils.isNotBlank(g)).map(g->Long.valueOf(g))
              .collect(Collectors.toList());
    }
    if (CollectionUtil.isEmpty(refIds)){
      throw new BusinessException("没有设备");
    }
    //播放
    Map<String,Object> map = new HashMap<>();
    map.put("programId",streetlightProgramDTO.getId());
    map.put("type",0);
    refIds.forEach(id ->{
      StreetlightDeviceDTO service = streetlightService.findById(id);
      List<DevicePropertyStatus> propertyStatusList = service.getDevicePropertyStatusList();
      if (null == propertyStatusList){
        propertyStatusList = new ArrayList<>();
      }
      List<DevicePropertyStatus> statuses = propertyStatusList.stream().filter(p -> "lampPoleId".equals(p.getProp())).collect(Collectors.toList());
      //获取属性clientKey
      List<DevicePropertyStatus> clientKeyStatus = propertyStatusList.stream().filter(p -> "clientKey".equals(p.getProp())).collect(Collectors.toList());
      if (CollectionUtil.isEmpty(statuses) || CollectionUtil.isEmpty(clientKeyStatus)){
        noProps.add(service.getExtendInfo().getSbmc());
      }else {
        String clientKey = clientKeyStatus.get(0).getValue();
        if (StringUtils.isBlank(statuses.get(0).getValue()) || StringUtils.isBlank(clientKey)){
          noProps.add(service.getExtendInfo().getSbmc());
        }else {
          map.put("lampPoleId",statuses.get(0).getValue());
          try {
            JSONObject jsonObject = RpcEnum.NB_SERVER.postForObject("/lamp/program/play?clientKey="+clientKey, map, JSONObject.class);
            check(jsonObject);
          } catch (Exception e) {
            noPlays.add(service.getExtendInfo().getSbmc());
          }
        }
      }
    });

    //如果有设备失败
    if (CollectionUtil.isNotEmpty(noProps) || CollectionUtil.isNotEmpty(noPlays)){
      String msg = "部分设备播放失败";
      if (CollectionUtil.isNotEmpty(noProps)){
        String noProp = StringUtils.join(noProps, ",");
        msg += "；"+noProp+"没有对应的灯杆编号属性或灯杆编号没有值或clientKey属性值为空";
      }
      if (CollectionUtil.isNotEmpty(noPlays)){
        String noPlay = StringUtils.join(noPlays, ",");
        msg += "；"+noPlay+"调用播放接口失败";
      }
      throw new BusinessException(msg);
    }
  }

  /**
   * @Description: 查询素材列表
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public Map<String,List<StreetlightSourceDTO>> querySourceList() {
    Map<String,List<StreetlightSourceDTO>> map = new HashMap<>();
    //调用素材列表接口
    JSONObject jsonObject = RpcEnum.NB_SERVER.getForObject("/lamp/source/queryAll", JSONObject.class);
    check(jsonObject);
    JSONObject data = jsonObject.getJSONObject("data");
    //获取domain
    String domain = data.getString("domain");
    //视频
    JSONArray videoList = data.getJSONArray("videoList");
    if (null != videoList){
      List<StreetlightSourceDTO> video = videoList.toJavaList(StreetlightSourceDTO.class);
      video.forEach(i->{
        i.setPath(domain+i.getPath());
      });
      map.put("videoList",video);
    }
    //图片
    JSONArray imageList = data.getJSONArray("imageList");
    if (null != imageList){
      List<StreetlightSourceDTO> image = imageList.toJavaList(StreetlightSourceDTO.class);
      image.forEach(i->{
        i.setPath(domain+i.getPath());
      });
      map.put("imageList",image);
    }
    return map;
  }

  /**
   * @Description: 查询LED屏数据
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public JSONObject getLedByStreetlightId(Long streetlightId) {
    //查询路灯
    StreetlightDeviceDTO deviceDTO = streetlightService.findById(streetlightId);
    List<DevicePropertyStatus> propertyStatusList = deviceDTO.getDevicePropertyStatusList();
    if (null == propertyStatusList){
      propertyStatusList = new ArrayList<>();
    }
    List<DevicePropertyStatus> statuses = propertyStatusList.stream().filter(p -> "lampPoleId".equals(p.getProp())).collect(Collectors.toList());
    //获取属性clientKey
    List<DevicePropertyStatus> clientKeyStatus = propertyStatusList.stream().filter(p -> "clientKey".equals(p.getProp())).collect(Collectors.toList());
    if (CollectionUtil.isNotEmpty(statuses) && CollectionUtil.isNotEmpty(clientKeyStatus)){
      String value = statuses.get(0).getValue();
      String clientKey = clientKeyStatus.get(0).getValue();
      if (StringUtils.isNotBlank(value) && StringUtils.isNotBlank(clientKey)){
        JSONObject forObject = RpcEnum.NB_SERVER.getForObject("/lamp/led/queryLampPoleScreenData/" + value+"?clientKey="+clientKey, JSONObject.class);
//        check(forObject);
        if(Integer.valueOf(200).equals(forObject.getInteger("code"))){
          return forObject.getJSONObject("data");
        }
      }
    }
    return null;
  }

  @Override
  public IPage<StreetlightProgramDTO> queryListByPage(RequestModel<StreetlightProgramDTO> requestModel) {
    Page page = requestModel.getPage();
    StreetlightProgramDTO streetlightProgramDTO = requestModel.getCustomQueryParams();
    //调用节目列表接口
    JSONObject jsonObject = RpcEnum.NB_SERVER.getForObject("lamp/program/queryAll", JSONObject.class);
    check(jsonObject);
    JSONArray jsonArray = jsonObject.getJSONArray("data");
    List<StreetlightProgramDTO> list = jsonArray.toJavaList(StreetlightProgramDTO.class);
    IPage<StreetlightProgramDTO> iPage = EnergyUtils.toPage(list, page);
    return iPage;
  }

  /**
   * 验证结果
   * @param jsonObject
   */
  private static void check(JSONObject jsonObject) {
    if (!Integer.valueOf(200).equals(jsonObject.getInteger("code"))){
      throw new BusinessException(jsonObject.getString("message"));
    }
  }
}
