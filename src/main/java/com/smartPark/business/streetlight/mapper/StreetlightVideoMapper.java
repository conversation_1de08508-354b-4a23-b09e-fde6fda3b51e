package com.smartPark.business.streetlight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.streetlight.entity.Streetlight;
import com.smartPark.business.streetlight.entity.StreetlightVideo;
import com.smartPark.business.streetlight.entity.vo.StreetlightVideoVo;
import com.smartPark.business.streetlight.entity.vo.StreetlightVo;
import com.smartPark.business.streetlight.excel.model.StreetlightExportModelDTO;
import com.smartPark.common.entity.deviceArea.DeviceArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 路灯表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
public interface StreetlightVideoMapper extends BaseMapper<StreetlightVideo> {

    IPage<StreetlightVideoVo> queryListByPage(@Param("page") Page page, @Param("streetlightVo") StreetlightVideoVo streetlightVo);
}
