package com.smartPark.business.streetlight.controller;


import com.smartPark.business.streetlight.entity.vo.StreetlightCountQueryDTO;
import com.smartPark.business.streetlight.entity.vo.StreetlightCountVo;
import com.smartPark.business.streetlight.service.StreetlightCountService;
import com.smartPark.business.streetlight.service.StreetlightStatisticsService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;

/**
 * 智慧路灯/路灯统计分析
 *
 * <AUTHOR>
 * @since 2024-01-07
 */
@RestController
@RequestMapping("/streetlightStatistics")
public class StreetlightStatisticsController {

  @Autowired
  private StreetlightCountService streetlightCountService;

  @Autowired
  private StreetlightStatisticsService streetlightStatisticsService;

  /**
   * 路灯数量统计
   * <AUTHOR>
   * @date 2020/11/04 11:42
   */
  @GetMapping("numCount")
  @ApiOperation("路灯数量统计")
  public RestMessage countNum() {
    StreetlightCountVo streetlightCountVo = streetlightCountService.countNum();
    return RestBuilders.successBuilder().data(streetlightCountVo).build();
  }

  /**
   * 故障总数（当前已处理、未处理）
   * <AUTHOR>
   * @date 2020/11/04 11:42
   */
  @PostMapping("currentAlarmCount")
  @ApiOperation("故障总数（当前）")
  public RestMessage currentAlarmCount(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
    return RestBuilders.successBuilder().data(streetlightStatisticsService.currentAlarmCount(streetlightCountQueryDTO)).build();
  }

  /**
   * 今年新增告警数据（本周、本月、本年）
   * <AUTHOR>
   * @date 2020/11/04 11:42
   */
  @PostMapping("currentYearAlarmCount")
  @ApiOperation("今年新增告警数据")
  public RestMessage currentYearAlarmCount(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
    return RestBuilders.successBuilder().data(streetlightStatisticsService.currentYearAlarmCount(streetlightCountQueryDTO)).build();
  }

  /**
   * 路灯分类
   * <AUTHOR>
   * @return
   */
  @PostMapping("typeCount")
  @ApiOperation("路灯分类统计")
  public RestMessage typeCount(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
    List<StreetlightCountVo> streetlightCountVoList = streetlightStatisticsService.typeCount(streetlightCountQueryDTO);
    return RestBuilders.successBuilder().data(streetlightCountVoList).build();
  }

  /**
   * 每月养护记录（近6月养护记录）
   * <AUTHOR>
   * @return
   */
  @PostMapping("maintenanceCount")
  @ApiOperation("养护情况柱状图")
  public RestMessage maintenanceCount(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
    List<StreetlightCountVo> streetlightCountVoList = streetlightStatisticsService.maintenanceCount(streetlightCountQueryDTO);
    return RestBuilders.successBuilder().data(streetlightCountVoList).build();
  }

  /**
   * 养护情况波形图（近6月每月异常、正常路灯数）
   * <AUTHOR>
   * @return
   */
  @PostMapping("alarmStreetLightCount")
  @ApiOperation("养护情况波形图")
  public RestMessage alarmStreetLightCount(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
    List<StreetlightCountVo> streetlightCountVoList = streetlightStatisticsService.alarmStreetLightCount(streetlightCountQueryDTO);
    return RestBuilders.successBuilder().data(streetlightCountVoList).build();
  }

  /**
   * 养护情况波形图（近6月每月告警数）
   * <AUTHOR>
   * @return
   */
  @PostMapping("alarmCount")
  @ApiOperation("故障情况柱状图")
  public RestMessage alarmCount(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
    List<StreetlightCountVo> streetlightCountVoList = streetlightStatisticsService.alarmCount(streetlightCountQueryDTO);
    return RestBuilders.successBuilder().data(streetlightCountVoList).build();
  }

  /**
   * 故障处理情况对比图（近6月每月告警处理、未处理数）
   * <AUTHOR>
   * @return
   */
  @PostMapping("alarmHandleCount")
  @ApiOperation("故障处理情况对比图")
  public RestMessage alarmHandleCount(@RequestBody StreetlightCountQueryDTO streetlightCountQueryDTO) {
    return RestBuilders.successBuilder().data(streetlightStatisticsService.alarmHandleCount(streetlightCountQueryDTO)).build();
  }

}
