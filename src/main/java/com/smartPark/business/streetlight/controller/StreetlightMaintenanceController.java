package com.smartPark.business.streetlight.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.streetlight.entity.StreetlightMaintenance;
import com.smartPark.business.streetlight.service.StreetlightMaintenanceService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 智慧路灯/养护记录管理
 * @author: kan yuan<PERSON>
 * @Date: 2020/11/04 11:42
 * @Description: 智慧路灯/养护记录管理
 */
@RestController
@RequestMapping("streetlightMaintenance")
@Api(tags = "防护记录管理")
public class StreetlightMaintenanceController {
  
  @Autowired
  private StreetlightMaintenanceService streetlightMaintenanceService;

  /**
   * @Description: 增加防护记录
   * <AUTHOR> yuan<PERSON>
   * @date 2020/11/04 11:42
   */
  @PostMapping
  @ApiOperation("增加防护记录")
  public RestMessage insert(@RequestBody StreetlightMaintenance streetlightMaintenance){
    //todo 参数验证
    streetlightMaintenanceService.insert(streetlightMaintenance);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除防护记录（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @DeleteMapping
  @ApiOperation("删除防护记录（包含批量删除）")
  public RestMessage delBatch(@RequestBody StreetlightMaintenance streetlightMaintenance){
    Assert.notEmpty(streetlightMaintenance.getIds(),"id不能为空");
    streetlightMaintenanceService.removeByIds(streetlightMaintenance.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑防护记录
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PutMapping
  @ApiOperation("编辑防护记录")
  public RestMessage updateById(@RequestBody StreetlightMaintenance streetlightMaintenance){
    Assert.notNull(streetlightMaintenance.getId(),"id不能为空");
    streetlightMaintenanceService.updateOne(streetlightMaintenance);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询防护记录详情
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询防护记录详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    StreetlightMaintenance streetlightMaintenance = streetlightMaintenanceService.getById(id);
    return RestBuilders.successBuilder().data(streetlightMaintenance).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<StreetlightMaintenance> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<StreetlightMaintenance> record =  streetlightMaintenanceService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }
}

