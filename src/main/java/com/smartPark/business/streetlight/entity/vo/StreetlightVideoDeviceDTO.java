package com.smartPark.business.streetlight.entity.vo;

import cn.hutool.json.JSONArray;
import com.smartPark.common.entity.device.*;
import lombok.Data;

import java.util.List;

/**
 * @Description 路灯视频设备的信息详情
 * <AUTHOR>
 * @Date 2023/3/21 13:46
 */
@Data
public class StreetlightVideoDeviceDTO extends StreetlightVideoVo {
    /**
     * 设备基础信息
     */
    private DeviceExtendInfo extendInfo;

    /**
     * 关联设备
     */
    private Device device;

    /**
     * 监测点位信息
     */
    private MonitorPoint monitorPoint;

    /**
     *部件码基本信息
     */
    private ObjInfo objInfo;

    /**
     * 设备属性
     */
    private List<DevicePropertyStatus> devicePropertyStatusList;

    /**
     * 设备物模型
     */
    private JSONArray physicModel;
}
