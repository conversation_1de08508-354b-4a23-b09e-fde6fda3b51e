package com.smartPark.business.streetlight.entity.vo;

import com.smartPark.business.streetlight.entity.StreetlightPlan;
import com.smartPark.business.streetlight.entity.StreetlightPlanParam;
import com.smartPark.business.streetlight.entity.StreetlightPlanRef;
import lombok.Data;

import java.util.List;

/**
 * @Description 路灯控制计划
 * <AUTHOR>
 * @Date 2023/4/7 10:34
 */
@Data
public class StreetlightPlanVo extends StreetlightPlan {
    /**
     * 参数
     */
    private List<StreetlightPlanParam> paramList;

    /**
     * 分组/设备
     */
    private List<StreetlightPlanRefVo> planRefList;

    /**
     * 已选择路灯数量
     */
    private int chooseNum;
}
