package com.smartPark.business.streetlight.entity.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.asyncexcel.core.ExportRow;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/05/31
 * @description
 */
@Data
@HeadRowHeight(value = 30) // 头部行高
@ContentRowHeight(value = 25) // 内容行高
@ColumnWidth(value = 20) // 列宽
public class StreetlightHourElectricityStatisticsVo extends ExportRow {

  /**
   * 采集时间
   */
  @ExcelProperty(value ="采集时间")
  @DateTimeFormat("yyyy-MM-dd")
  @ColumnWidth(value = 30)
  private String collectTime;
  /**
   * 采集范围
   */
  @ExcelProperty(value ="采集范围")
  @ColumnWidth(value = 30)
  private String collectRange;
  /**
   * 设备编号
   */
  @ExcelProperty(value ="设备编号")
  private String deviceCode;
  /**
   * 设备名称
   */
  @ExcelProperty(value ="设备名称")
  private String deviceName;

  /**
   * 期初电量
   */
  @ExcelProperty(value ="期初电量")
  private Double startReading;

  /**
   * 用电量
   */
  @ExcelProperty(value ="用电量")
  private Double electricyIncrement;

  /**
   * 期末电量
   */
  @ExcelProperty(value ="期末电量")
  private Double endReading;

}
