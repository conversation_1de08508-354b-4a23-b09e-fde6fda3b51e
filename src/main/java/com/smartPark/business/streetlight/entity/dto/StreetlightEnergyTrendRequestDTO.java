package com.smartPark.business.streetlight.entity.dto;

import lombok.Data;

/**
 * 路灯用电量趋势统计请求参数DTO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
public class StreetlightEnergyTrendRequestDTO {
    
    /**
     * 路灯类型，可为空
     * 可选值：road（道路路灯）,park（公园路灯）
     */
    private String lightType;
    
    /**
     * 用电趋势统计维度，可为空
     * 可选值：day7(近七日),month6(近半年)
     */
    private String energyStat;
    
    /**
     * 获取路灯类型对应的枚举值
     * @return 路灯类型枚举值
     */
    public String getLightType() {
        if (lightType == null || lightType.trim().isEmpty()) {
            return lightType;
        }
        
        switch (lightType.trim()) {
            case "road":
                return "road";
            case "park":
                return "park";
            default:
                return lightType;
        }
    }
    
    /**
     * 获取统计维度对应的日期类型
     * @return 日期类型：1-小时，2-天，3-月
     */
    public Byte getDateType() {
        if (energyStat == null || energyStat.trim().isEmpty()) {
            return 2; // 默认按天统计
        }
        
        switch (energyStat.trim()) {
            case "day7":
                return 2; // 按天统计
            case "month6":
                return 3; // 按月统计
            default:
                return 2; // 默认按天统计
        }
    }
}
