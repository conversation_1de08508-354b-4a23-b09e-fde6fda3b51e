package com.smartPark.business.greenLand.entity.vo;

import com.smartPark.business.greenLand.entity.GreenLand;
import com.smartPark.common.area.entity.Area;
import lombok.Data;

import java.util.List;
import java.util.StringJoiner;

/**
 * 区域绿地统计Vo
 * <AUTHOR>
 */
@Data
public class AreaGreenLandCountVo {

    /**
     * id
     */
    private Long id;

    /**
     * 当前区域
     */
    private Area area;

    /**
     *
     */
    private StringJoiner sj = new StringJoiner("@");

    /**
     * 绿地对象
     */
    private GreenLand greenLand;

    /**
     * 绿地统计汇总对象
     */
    private GreenLandSummaryVo greenLandSummaryVo;

    /**
     * 子集
     */
    private List<AreaGreenLandCountVo> children;

    /**
     * 区域,1:街道,2:社区,3:单元网格,4:绿地
     */
    private Integer dataType;

    public void setArea(Area area) {
        this.area = area;
        sj.add(area.getName());
    }

    public void setSj(StringJoiner sj) {
        this.sj = sj;
    }
}
