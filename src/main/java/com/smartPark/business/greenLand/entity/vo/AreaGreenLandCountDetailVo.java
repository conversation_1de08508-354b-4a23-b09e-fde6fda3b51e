package com.smartPark.business.greenLand.entity.vo;


import lombok.Data;

import java.util.Date;

/**
 * 区域绿地统计明细Vo
 * <AUTHOR>
 */
@Data
public class AreaGreenLandCountDetailVo {

    /**
     * 区域编码统计时间
     */
    private Date recordTime;

    /**
     * 统计时间字符串
     */
    private String recordTimeStr;

    /**
     * 期初绿地面积
     */
    private Double startLandArea;

    /**
     * 期初绿化面积
     */
    private Double startGreenArea;

    /**
     * 期末绿地面积
     */
    private Double endLandArea;

    /**
     * 期末绿化面积
     */
    private Double endGreenArea;

    /**
     * 绿地变化面积
     */
    private Double landChangeArea;

    /**
     * 绿化变化面积
     */
    private Double greenChangeArea;

    /**
     * 绿地变化率
     */
    private Double landChangeRate;

    /**
     * 绿化变化率
     */
    private Double greenChangeRate;

    /**
     * 绿地变化率
     */
    private Integer operateNum;

}
