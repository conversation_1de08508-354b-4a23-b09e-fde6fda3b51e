package com.smartPark.business.greenLand.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.greenLand.entity.GreenLandStandard;
import com.smartPark.business.greenLand.entity.vo.GreenLandStandardVo;
import com.smartPark.business.greenLand.mapper.GreenLandStandardMapper;
import com.smartPark.business.greenLand.service.GreenLandStandardService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * GreenLandStandard表服务实现类
 *
 * <AUTHOR>
 * @date 2023/09/05
 */
@Slf4j
@Service
public class GreenLandStandardServiceImpl extends ServiceImpl
        <GreenLandStandardMapper, GreenLandStandard> implements GreenLandStandardService {
    @Resource
    private CommonService commonService;

    @Override
    public boolean removeById(Serializable id) {
    return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        for (Long id : idList) {
        removeById(id);
        }
        return true;
    }


    @Override
    public boolean saveOne(GreenLandStandard livableGreenLandStandard) {
        commonService.setCreateAndModifyInfo(livableGreenLandStandard);

        validParamRequired(livableGreenLandStandard);
        validRepeat(livableGreenLandStandard);
        validParamFormat(livableGreenLandStandard);
        return save(livableGreenLandStandard);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(GreenLandStandard livableGreenLandStandard) {
        Assert.notNull(livableGreenLandStandard.getId(), "id不能为空");
        commonService.setModifyInfo(livableGreenLandStandard);

        validRepeat(livableGreenLandStandard);
        validParamFormat(livableGreenLandStandard);
        return updateById(livableGreenLandStandard);
    }

    @Override
    public IPage<GreenLandStandard> selectPage(Page page, GreenLandStandard livableGreenLandStandard) {
        return baseMapper.selectPage(page, livableGreenLandStandard);
    }

    @Override
    public void export(GreenLandStandard livableGreenLandStandard, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public GreenLandStandard getLastStandard(GreenLandStandardVo param) {
        QueryWrapper<GreenLandStandard> qw = new QueryWrapper<>();
        qw.eq("deleted_", 0);
        qw.gt("effect_end_time_", param.getCurrentTime());
        qw.lt("effect_start_time_", param.getCurrentTime());
        qw.orderByDesc("version_");
        qw.last("limit 1");
        List<GreenLandStandard> list = baseMapper.selectList(qw);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public GreenLandStandard saveStandard(GreenLandStandard greenLandStandard) {
        Date now = DateUtil.date();
        Long id = greenLandStandard.getId();
        LogHelper.setLogInfo("", greenLandStandard.toString(), null, null,"保存绿地标准");
        //id为空则新增
        if (id == null){
            commonService.setCreateAndModifyInfo(greenLandStandard);
            greenLandStandard.setVersion(1);
            greenLandStandard.setEffectStartTime(now);
            greenLandStandard.setEffectEndTime(DateUtil.offset(now, DateField.YEAR, 100));
            baseMapper.insert(greenLandStandard);
            return greenLandStandard;
        }else {
            GreenLandStandard dbGreenLandStandard = baseMapper.selectById(id);
            if (dbGreenLandStandard == null){
                throw new RuntimeException("绿地标准不存在");
            }
            commonService.setModifyInfo(dbGreenLandStandard);
            dbGreenLandStandard.setEffectEndTime(now);
            baseMapper.updateById(dbGreenLandStandard);
            //新版本
            GreenLandStandard newStandard = new GreenLandStandard();
            BeanUtil.copyProperties(greenLandStandard, newStandard);
            newStandard.setId(null);
            commonService.setCreateAndModifyInfo(newStandard);
            newStandard.setVersion(dbGreenLandStandard.getVersion() + 1);
            newStandard.setEffectStartTime(now);
            newStandard.setEffectEndTime(DateUtil.offset(now, DateField.YEAR, 100));
            baseMapper.insert(newStandard);
            return newStandard;
        }
    }

    @Override
    public GreenLandStandard getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(GreenLandStandard livableGreenLandStandard) {
        /* QueryWrapper<GreenLandStandard> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", livableGreenLandStandard.getName());
        queryWrapper.eq("tenant_id", linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<GreenLandStandard> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("名称有重复");
        }
        if (ObjectUtils.isEmpty(livableGreenLandStandard.getId())) {
            throw new BusinessException("名称已存在");
        }
        if (!livableGreenLandStandard.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称已存在");
        }
                    */

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(GreenLandStandard livableGreenLandStandard) {
        //Assert.notNull(livableGreenLandStandard, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(livableGreenLandStandard.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(GreenLandStandard livableGreenLandStandard) {
        //Assert.isTrue(livableGreenLandStandard.getName() == null || livableGreenLandStandard.getName().length() <= 50,
        //        "名称超长");
    }
}

