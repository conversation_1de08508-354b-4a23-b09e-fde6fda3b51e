package com.smartPark.business.greenLand.controller;


import com.smartPark.business.greenLand.entity.dto.GreenLandCountDTO;
import com.smartPark.business.greenLand.entity.dto.GreenLandDTO;
import com.smartPark.business.greenLand.entity.vo.GreenLandCountVO;
import com.smartPark.business.greenLand.service.GreenLandService;
import io.swagger.annotations.Api;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 绿地地图
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@RestController
@RequestMapping("/greenLand/greenLandMap")
@Api(tags = "绿地地图")
public class GreenLandMapController {

  @Resource
  private GreenLandService greenLandService;

  /**
   * 区域数据设施总览统计
   *
   * @param greenLandCountVo 统计入参vo
   * @return 所有数据
   */
  @PostMapping("/areaCount")
  public RestMessage areaCount(@RequestBody GreenLandCountVO greenLandCountVo) {
    List<GreenLandCountDTO> record = greenLandService.areaCount(greenLandCountVo);
    return RestBuilders.successBuilder(record).build();
  }

  /**
   * 绿地按照绿地类型分组统计
   *
   * @param greenLandCountVo 统计入参vo
   * @return 所有数据
   */
  @PostMapping("countGroupByType")
  public RestMessage countGroupByType(@RequestBody GreenLandCountVO greenLandCountVo) {
    List<GreenLandCountDTO> record = greenLandService.countGroupByType(greenLandCountVo);
    return RestBuilders.successBuilder(record).build();
  }

  /**
   * 绿地运营统计
   * @param greenLandCountVo 统计入参条件vo
   * @return 养护趋势统计数据
   */
  @PostMapping("/operatortionRecordsCount")
  public RestMessage maintenanceTrendCount(@RequestBody GreenLandCountVO greenLandCountVo) {
    List<GreenLandCountDTO> record = greenLandService.operatortionRecordsCount(greenLandCountVo);
    return RestBuilders.successBuilder(record).build();
  }

}
