package com.smartPark.business.baseDevice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.baseDevice.dto.DeviceUnitQueryDTO;
import com.smartPark.business.baseDevice.service.BaseDeviceUnitService;
import com.smartPark.common.device.dto.DeviceUnitDto;
import com.smartPark.common.device.dto.FieldSortDto;
import com.smartPark.common.device.mapper.DeviceApplicationModelDeviceUnitConfigMapper;
import com.smartPark.common.device.mapper.DeviceUnitMapper;
import com.smartPark.common.device.model.PhysicsModel;
import com.smartPark.common.device.service.*;
import com.smartPark.common.entity.device.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/19
 * @description
 */
@Service
public class BaseDeviceUnitServiceImpl extends ServiceImpl<DeviceUnitMapper, DeviceUnit> implements BaseDeviceUnitService {


  @Autowired
  private DeviceApplicationModelDeviceUnitConfigMapper damdeviceUnitMapper;

  @Autowired
  private DeviceApplicationModelDeviceUnitConfigService damDeviceUnitService;

  @Autowired
  private DeviceUnitMapper deviceUnitMapper;

  @Autowired
  private DeviceVersionService deviceVersionService;

  @Autowired
  private DeviceTypeService deviceTypeService;

  @Autowired
  private DeviceUnitPropertyService propertyService;

  @Autowired
  private BaseDeviceUnitServiceService deviceUnitServiceService;

  @Autowired
  private DeviceUnitService deviceUnitService;

  @Override
  public List<DeviceUnit> selectAll() {
    return baseMapper.listForSelect(null);
  }

  @Override
  public List<DeviceUnitVo> selectByCondition(DeviceUnitQueryDTO queryDTO) {
    LambdaQueryWrapper<DeviceApplicationModelDeviceUnitConfig> qw = new LambdaQueryWrapper();
    if(queryDTO.getModel()!=null){
      qw.eq(DeviceApplicationModelDeviceUnitConfig::getModel,queryDTO.getModel());
    }
    List<DeviceApplicationModelDeviceUnitConfig> deviceUnitConfigs = damdeviceUnitMapper.selectList(qw);
    if(CollectionUtil.isEmpty(deviceUnitConfigs)){
      return null;
    }
    List<Long> deviceUnitIds = new ArrayList<>();
    List<Long> deviceTypeIds = new ArrayList<>();
    if(CollectionUtil.isNotEmpty(deviceUnitConfigs)){
      deviceUnitConfigs.forEach(deviceUnitConfig->{
        String deviceTypeId = deviceUnitConfig.getDeviceTypeId();
        String deviceUnitId = deviceUnitConfig.getDeviceUnitId();
        if(StringUtils.isNotBlank(deviceTypeId)){
          String[] deviceTypes = deviceTypeId.split(",");
          for (int i = 0;i<deviceTypes.length;i++){
            if(NumberUtil.isNumber(deviceTypes[i])){
              deviceTypeIds.add(Long.valueOf(deviceTypes[i]));
            }
//            deviceTypeIds.add(Long.valueOf(deviceTypes[i]));
          }
        }
        if(StringUtils.isNotBlank(deviceUnitId)){
          String[] deviceUnits = deviceUnitId.split(",");
          for (int i = 0;i<deviceUnits.length;i++){
            if (NumberUtil.isNumber(deviceUnits[i])){
              deviceUnitIds.add(Long.valueOf(deviceUnits[i]));
            }
//            deviceUnitIds.add(Long.valueOf(deviceUnits[i]));
          }
        }
      });
    }
    DeviceCommonDto damdConfig = new DeviceCommonDto();
    //获取参数中的deviceTypeIds，取交集
    List<Long> paramTypeIds = damdConfig.getDeviceTypeIds();
    if(CollectionUtil.isNotEmpty(deviceTypeIds)){
      if(CollectionUtil.isNotEmpty(paramTypeIds)){
        deviceTypeIds.retainAll(paramTypeIds);
      }
      damdConfig.setDeviceTypeIds(deviceTypeIds);
    }
    if(CollectionUtil.isNotEmpty(deviceUnitIds)){
      damdConfig.setDeviceUnitIds(deviceUnitIds);
    }
    return deviceUnitMapper.getConfigDeviceUnit(damdConfig);
  }

  @Override
  public IPage<DeviceUnitVo> selectPage(Page page, DeviceUnitDto dto) {
    Page<DeviceUnitVo> objectPage = new Page<>(page.getCurrent(), page.getSize());
    return getBaseMapper().getPage(objectPage,dto).convert((e)->{
      // 类型
      DeviceType type = deviceTypeService.getById(e.getDeviceTypeId());
      if(type != null){
        e.setTypeName(type.getName());
      }
      // 关联模块名称
      e.setModelRef(damDeviceUnitService.queryModelNameByDeviceUnitId(e.getId()));

      //获取版本信息
      DeviceVersion deviceVersion = deviceVersionService.queryCurrentVersionByDeviceUnitId(e.getId());
      if(deviceVersion != null){
        e.setVersion(deviceVersion.getDeviceVersion());
      }
      return e;
    });
  }

  @Override
  public PhysicsModel selectPhysicsModel(Long deviceUnitId) {
    return deviceUnitService.selectPhysicsModel(deviceUnitId);
  }

  @Override
  public void updateProp(Long deviceUnitId, List<FieldSortDto> dto) {
    propertyService.updatePropertySort(deviceUnitId,dto);
  }

  @Override
  public void updateService(Long deviceUnitId, List<FieldSortDto> dto) {
    deviceUnitServiceService.updatePropertySort(deviceUnitId,dto);
  }

  @Override
  public void updatePropShow(Long deviceUnitId, List<FieldSortDto> dto) {
    propertyService.updatePropertyShow(deviceUnitId,dto);
  }

  @Override
  public void updateServiceShow(Long deviceUnitId, List<FieldSortDto> dto) {
    deviceUnitServiceService.updatePropertyShow(deviceUnitId,dto);
  }
}
