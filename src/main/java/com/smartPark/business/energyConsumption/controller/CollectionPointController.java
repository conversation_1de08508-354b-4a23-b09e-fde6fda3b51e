package com.smartPark.business.energyConsumption.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.energyConsumption.dto.CollectionPointDTO;
import com.smartPark.business.energyConsumption.service.CollectionPointService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant.LogOperateActionType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 能源消耗/采集点管理
 *
 * <AUTHOR>
 * @since 2023-04-22
 */
@RestController
@RequestMapping("/energyConsumption/collectionPoint")
@Api(tags = "采集点管理")
public class CollectionPointController {

  @Resource
  private CollectionPointService collectionPointService;

  /**
   * 增加采集点
   * @param collectionPointDTO
   * @return
   */
  @PostMapping
  @ApiOperation("增加采集点")
  @BusinessLogAnnotate(actionType = LogOperateActionType.ADD,menuCode = "consumption:baseInfo:collectionPoint:add",desc = "增加采集点")
  public RestMessage insert(@RequestBody CollectionPointDTO collectionPointDTO){
    collectionPointService.insertOne(collectionPointDTO);
    return RestBuilders.successBuilder().build();
  }

  /**
   * 编辑采集点
   * @param collectionPointDTO
   * @return
   */
  @PutMapping
  @ApiOperation("编辑采集点")
  @BusinessLogAnnotate(actionType = LogOperateActionType.EDIT,menuCode = "consumption:baseInfo:collectionPoint:edit",desc = "编辑采集点")
  public RestMessage update(@RequestBody CollectionPointDTO collectionPointDTO){
    collectionPointService.updateOne(collectionPointDTO);
    return RestBuilders.successBuilder().build();
  }

  /**
   * 删除采集点
   * @param idList
   * @return
   */
  @DeleteMapping
  @ApiOperation("批量删除")
  @BusinessLogAnnotate(actionType = LogOperateActionType.DEL,menuCode = "consumption:baseInfo:collectionPoint:del",desc = "批量删除")
  public RestMessage delete(@RequestParam("idList") List<Long> idList) {
    return RestBuilders.successBuilder().success(this.collectionPointService.deleteByIds(idList)).build();
  }

  /**
   * 根据条件，树形结构查询
   * @param collectionPointDTO
   * @return
   */
  @PostMapping("tree")
  @ApiOperation("根据条件，树形结构查询")
  public RestMessage queryTree(@RequestBody CollectionPointDTO collectionPointDTO) {
    if (null == collectionPointDTO.getType()){
      collectionPointDTO.setType(1);// 默认查询用水
    }
    List<CollectionPointDTO> record = collectionPointService.queryTree(collectionPointDTO);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * 根据条件，分页(不分页)查询
   * @param collectionPointDTO
   * @return
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryList(@RequestBody RequestModel<CollectionPointDTO> requestModel) {
    CollectionPointDTO customQueryParams = requestModel.getCustomQueryParams();
    Assert.notNull(customQueryParams, "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    if (null == customQueryParams.getType()){
      customQueryParams.setType(1);// 默认查询用水
    }
    IPage<CollectionPointDTO> page = collectionPointService.queryList(requestModel.getPage(),customQueryParams);
    return RestBuilders.successBuilder().data(page).build();
  }
}
