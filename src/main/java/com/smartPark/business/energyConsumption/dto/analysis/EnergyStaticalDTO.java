package com.smartPark.business.energyConsumption.dto.analysis;

import com.smartPark.common.utils.energy.EnergyUtils;
import lombok.Data;

/**
 * @Description 能耗统计
 * <AUTHOR> y<PERSON>
 * @Date 2023/7/21 11:12
 */
@Data
public class EnergyStaticalDTO {
    /**
     * 时间
     */
    private String timeStr;

    /**
     * 本期能耗
     */
    private Double bqNum;

    /**
     * 同期能耗
     */
    private Double tqNum;

    /**
     * 同期能耗对比率
     */
    private Double tqRateComp;

    /**
     * 同期能耗对比率(字符串)
     */
    private String tqRateCompStr;

    /**
     * 上期能耗
     */
    private Double sqNum;

    /**
     * 上期能耗对比率
     */
    private Double sqRateComp;

    /**
     * 上期能耗比率(字符串)
     */
    private String sqRateCompStr;

    /**
     * 本期日均能耗
     */
    private Double bqAvgNum;

    /**
     * 同期日均能耗
     */
    private Double tqAvgNum;

    /**
     * 上期日均能耗
     */
    private Double sqAvgNum;

    /**
     * 同期日均能耗
     */
    private Double tqAvgRateComp;

    /**
     * 上期日均能耗
     */
    private Double sqAvgRateComp;

    /**
     * 同期日均能耗环比
     */
    private String tqAvgRateCompStr;

    /**
     * 上期日均能耗环比
     */
    private String sqAvgRateCompStr;

    public Double getTqAvgRateComp() {
        Double aDouble = EnergyUtils.calComRatio(bqAvgNum, tqAvgNum);
        tqAvgRateComp = aDouble;
        return tqAvgRateComp;
    }

    public Double getSqAvgRateComp() {
        Double aDouble = EnergyUtils.calComRatio(bqAvgNum, sqAvgNum);
        sqAvgRateComp = aDouble;
        return sqAvgRateComp;
    }

    public Double getTqRateComp() {
        Double aDouble = EnergyUtils.calComRatio(bqNum, tqNum);
        tqRateComp = aDouble;
        return tqRateComp;
    }

    public Double getSqRateComp() {
        Double aDouble = EnergyUtils.calComRatio(bqNum, sqNum);
        sqRateComp = aDouble;
        return sqRateComp;
    }

    public EnergyStaticalDTO() {
    }

    public EnergyStaticalDTO(String timeStr, Double bqNum, Double tqNum, Double sqNum) {
        this.timeStr = timeStr;
        this.bqNum = bqNum;
        this.tqNum = tqNum;
        this.sqNum = sqNum;
    }

    public Double getBqNum() {
        return EnergyUtils.formatDouble(bqNum);
    }

    public Double getTqNum() {
        return EnergyUtils.formatDouble(tqNum);
    }

    public Double getSqNum() {
        return EnergyUtils.formatDouble(sqNum);
    }

    public Double getBqAvgNum() {
        return EnergyUtils.formatDouble(bqAvgNum);
    }

    public Double getTqAvgNum() {
        return EnergyUtils.formatDouble(tqAvgNum);
    }

    public Double getSqAvgNum() {
        return EnergyUtils.formatDouble(sqAvgNum);
    }
}
