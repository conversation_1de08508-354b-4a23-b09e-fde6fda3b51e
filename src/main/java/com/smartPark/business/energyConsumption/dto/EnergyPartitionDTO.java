package com.smartPark.business.energyConsumption.dto;

import com.smartPark.business.energyConsumption.entity.EnergyPartition;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/23
 * @description
 */
@Data
public class EnergyPartitionDTO extends EnergyPartition {
  /**
   * 子节点
   */
  private List<EnergyPartitionDTO> children;
  /**
   * 是否包含当前父级别
   */
  private Boolean isIncludParent;

  /**
   * 是否展示全部
   * 1.展示全部
   * 0.不展示全部
   */
  private Integer showAll;

}
