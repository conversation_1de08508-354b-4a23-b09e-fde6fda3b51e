package com.smartPark.business.energyConsumption.dto.analysis;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/05/09
 * @description
 */
@Data
public class DetailDTO {

  private Integer deviceNum;

  private Double todayConsumption;

  private Double thisMonthConsumption;
  private List<CountDTO> monthConsumptionList;

  private List<CountDTO> mConsumptionList;

  private List<CountDTO> yConsumptionList;

}
