package com.smartPark.business.energyConsumption.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 能耗分区表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_energy_partition")
public class EnergyPartition extends Model<EnergyPartition> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 父id(一级父类别为0)
     */
    @TableField("parent_id_")
    private Long parentId;

    /**
     * 父路径id
     */
    @TableField("full_id_")
    private String fullId;

    /**
     * 全名
     */
    @TableField("full_name_")
    private String fullName;

    /**
     * 编号
     */
    @TableField("code_")
    private String code;

    /**
     * 名称
     */
    @TableField("name_")
    private String name;

    /**
     * 层级
     */
    @TableField("level_")
    private Integer level;

    /**
     * 排序
     */
    @TableField("order_")
    private Integer order;

//    /**
//     * 围栏id
//     */
//    @TableField("fence_id_")
//    private String fenceId;

    /**
     * 1:圆,2:多边形
     */
    @TableField("fence_type_")
    private Integer fenceType;


    /***
     * 圆形围栏中心点 longitude,latitude
     */
    @TableField(value = "center_" ,strategy= FieldStrategy.IGNORED)
    private String center;

    /**
     * 圆形围栏半径
     */
    @Size(min = 0, max = 50000)
    @TableField(value = "radius_" ,strategy= FieldStrategy.IGNORED)
    private Integer radius;

    /***
     * 多边形围栏坐标点 lon1,lat1;lon2,lat2;（3<=点个数<=5000）。多边形围栏外接圆半径最大为5000米。
     */
    @Size(min = 3, max = 5000)
    @TableField(exist = false)
    private List<String> points;

    @TableField(value = "points_" ,strategy= FieldStrategy.IGNORED)
    private String pointsStr;

    @TableField(exist = false)
    private List<Integer> ids;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 创建人
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 修改人
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 是否删除字段 0:未删除; 其他：删除
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    /**
     * 备注/说明
     */
    @TableField("remark_")
    private String remark;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
