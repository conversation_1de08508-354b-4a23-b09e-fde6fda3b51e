package com.smartPark.business.energyConsumption.entity.vo;

import com.smartPark.business.energyConsumption.entity.EnergyConsumptionDevice;
import java.util.Date;
import java.util.List;

import com.smartPark.common.annotation.GaodeText;
import lombok.Data;


/**
 * @Description 能源消耗设备
 * <AUTHOR> @Date
 */
@GaodeText
@Data
public class EnergyConsumptionDeviceVo extends EnergyConsumptionDevice {
    /**
     * 设备名称
     */
    private String sbmc;

    /**
     *设备标识码
     */
    private String bsm;

    private String szjd;
    private String szsq;
    private String szdywg;

    /**
     * 区域全路径
     */
    private String areaPath;

    /**
     * 区域全路径集合
     */
    private List<String> areaPaths;

    /**
     * 在线状态（1在线0离线）
     */
    private Integer status;

    /**
     * 告警状态
     * @apiNote 0:正常; 1:告警
     */
    private Integer alarmState;

    /**
     * 报文最后推送时间
     */
    private Date lastPushTime;

    /**
     * 权属单位
     */
    private String ownerEnterpriseName;

    /**
     * 位置定位信息X坐标
     */
    private Double objX;

    /**
     * 位置定位信息Y坐标
     */
    private Double objY;

    /**
     * 采集点列表
     */
    private List<Long> collectionPointIds;

    private String typeName;

    private String deviceUnitCode;

    /**
     * 采集点全路径
     */
    private String collcectionPointFullName;

    /**
     * 是否算入总量
     */
    private Boolean isCollect;

    /**
     * 采集点的fullID
     */
    private String collectionPointFullId;
}
