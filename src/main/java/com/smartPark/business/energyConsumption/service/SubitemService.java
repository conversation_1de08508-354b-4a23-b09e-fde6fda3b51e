package com.smartPark.business.energyConsumption.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.energyConsumption.entity.Subitem;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 分项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-22
 */
public interface SubitemService extends IService<Subitem> {
  /**
   * 新增
   *
   * @param subitem 实体对象
   * @return 操作结果
   */
  boolean saveOne(Subitem subitem);

  /**
   * 修改单条
   *
   * @param subitem 实体对象
   * @return 修改结果
   */
  boolean updateOne(Subitem subitem);

  /**
   * 查询分页
   *
   * @param page        分页对象
   * @param subitem 分页参数对象
   * @return 查询分页结果
   */
  IPage<Subitem> selectPage(Page page, Subitem subitem);

  /**
   * 查询所有数据
   *
   * @return 查询结果
   */
  List<Subitem> findAll();


  /**
   * 获取单条
   *
   * @param id 主键id
   * @return 查询结果
   */
  Subitem getOneById(Serializable id);

  /**
   * 根据id批量删除
   *
   * @param idList 主键列表
   * @return 删除结果
   */
  boolean deleteByIds(List<Long> idList);
}
