package com.smartPark.business.energyConsumption.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.energyConsumption.constant.EnergyConsumptionConstant.ElectricitySwitchEunm;
import com.smartPark.business.energyConsumption.constant.EnergyConsumptionConstant.EnergyConsumptionPhisicalEnum;
import com.smartPark.business.energyConsumption.constant.EnergyConsumptionConstant.GasSwitchEunm;
import com.smartPark.business.energyConsumption.constant.EnergyConsumptionConstant.WaterSwitchEunm;
import com.smartPark.business.energyConsumption.dto.EnergyConsumptionRecordsDTO;
import com.smartPark.business.energyConsumption.entity.EnergyConsumptionDevice;
import com.smartPark.business.energyConsumption.entity.EnergyConsumptionDeviceStatistics;
import com.smartPark.business.energyConsumption.entity.EnergyConsumptionRecords;
import com.smartPark.business.energyConsumption.entity.vo.EnergyConsumptionDeviceVo;
import com.smartPark.business.energyConsumption.entity.vo.EnergyConsumptionRecordsVO;
import com.smartPark.business.energyConsumption.mapper.EnergyConsumptionDeviceMapper;
import com.smartPark.business.energyConsumption.mapper.EnergyConsumptionDeviceStatisticsMapper;
import com.smartPark.business.energyConsumption.mapper.EnergyConsumptionRecordsMapper;
import com.smartPark.business.energyConsumption.service.EnergyConsumptionDeviceService;
import com.smartPark.business.energyConsumption.service.EnergyConsumptionRecordsService;
import com.smartPark.business.energyConsumption.service.EnergyDeviceRecordService;
import com.smartPark.business.energyConsumption.util.NumberUtil;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.device.service.DeviceStatusService;
import com.smartPark.common.entity.device.Device;
import com.smartPark.common.entity.device.DeviceStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * <p>
 * 能耗记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Slf4j
@Service
public class EnergyConsumptionRecordsServiceImpl extends ServiceImpl<EnergyConsumptionRecordsMapper, EnergyConsumptionRecords> implements EnergyConsumptionRecordsService {

  @Resource
  private CommonService commonService;

  @Resource
  private EnergyConsumptionDeviceService energyConsumptionDeviceService;

  @Resource
  private DeviceStatusMapper deviceStatusMapper;

  @Resource
  private EnergyConsumptionDeviceStatisticsMapper energyConsumptionDeviceStatisticsMapper;

  @Resource
  private DeviceStatusService deviceStatusService;

  @Resource
  private DeviceService deviceService;

  @Resource
  private EnergyDeviceRecordService energyDeviceRecordService;

  @Resource
  private EnergyConsumptionDeviceMapper energyConsumptionDeviceMapper;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void insertOne(EnergyConsumptionRecords records) {
    StringJoiner sj = new StringJoiner("-");
    StringJoiner coreParamSj = new StringJoiner("-");

    commonService.setCreateAndModifyInfo(records);
    addRecords(records);

    //日志
    sj.add("新增能耗记录,设备编码：" + records.getDeviceCode());
    coreParamSj.add("新增能耗记录,设备编码：" + records.getDeviceCode());
    LogHelper.setLogInfo(null, coreParamSj.toString(), null, null, sj.toString());
  }


  private void addRecords(EnergyConsumptionRecords records){

    EnergyConsumptionDevice consumptionDevice = validParamRequired(records);
    // 一、修改设备状态 如果当前设备状态时间小于新增记录时间 更新设备状态
    // 查询设备 lastDataTime
    Device device = deviceService.getDeviceByCode(records.getDeviceCode());
    if(null != device && null != device.getLastPushTime() && device.getLastPushTime().getTime() < records.getCollectTime().getTime()){
      device.setLastPushTime(records.getCollectTime());
      deviceService.updateSimpleDevice(device);
      // 更新设备状态
      DeviceStatus stopReadingStatus = new DeviceStatus();
      stopReadingStatus.setDeviceCode(records.getDeviceCode());
      switch(records.getType()) {
        case 1:
          stopReadingStatus.setProp(EnergyConsumptionPhisicalEnum.ELECTRICITY.getEnergy());
          break;
        case 2:
          stopReadingStatus.setProp(EnergyConsumptionPhisicalEnum.WATER.getEnergy());
          break;
        case 3:
          stopReadingStatus.setProp(EnergyConsumptionPhisicalEnum.GAS.getEnergy());
          break;
      }
      stopReadingStatus.setValue(records.getStopReading().toString());
      stopReadingStatus.setModifyTime(new Timestamp(new Date().getTime()));
      deviceStatusService.save(stopReadingStatus);

      if(null != records.getSwitchState()){
        DeviceStatus stateStatus = new DeviceStatus();
        stateStatus.setDeviceCode(records.getDeviceCode());
        switch(records.getType()) {
          case 1:
            stateStatus.setProp(EnergyConsumptionPhisicalEnum.ELECTRICITY.getState());
            if(records.getSwitchState() == 0){
              stateStatus.setValue(ElectricitySwitchEunm.CLOSE.getOldStatus().get(0));
            }else{
              stateStatus.setValue(ElectricitySwitchEunm.OPEN.getOldStatus().get(0));
            }
            break;
          case 2:
            stateStatus.setProp(EnergyConsumptionPhisicalEnum.WATER.getState());
            if(records.getSwitchState() == 0){
              stateStatus.setValue(WaterSwitchEunm.CLOSE.getOldStatus().get(0));
            }else{
              stateStatus.setValue(WaterSwitchEunm.OPEN.getOldStatus().get(0));
            }
            break;
          case 3:
            stateStatus.setProp(EnergyConsumptionPhisicalEnum.GAS.getState());
            if(records.getSwitchState() == 0){
              stateStatus.setValue(GasSwitchEunm.CLOSE.getOldStatus().get(0));
            }else{
              stateStatus.setValue(GasSwitchEunm.OPEN.getOldStatus().get(0));
            }
            break;
        }
        stateStatus.setModifyTime(new Timestamp(new Date().getTime()));
        deviceStatusService.save(stateStatus);
      }
    }


    // 二、统计设备能耗
    // 设备用水列表 倒序
    LambdaQueryWrapper<EnergyConsumptionRecords> qw = new LambdaQueryWrapper<>();
    qw.eq(EnergyConsumptionRecords::getDeviceCode,records.getDeviceCode());
    qw.orderByDesc(EnergyConsumptionRecords::getCollectTime).last("limit 2");
    List<EnergyConsumptionRecords> list = baseMapper.selectList(qw);
    EnergyConsumptionDeviceStatistics deviceStatistics = new EnergyConsumptionDeviceStatistics();
    if(CollectionUtil.isNotEmpty(list)){
      // 1.只有一条记录
      EnergyConsumptionRecords endFlow = list.get(0);// 最新一条 计算增量
      if(list.size() == 1){
        Double electricyIncrement = 0d;
        if(records.getCollectTime().getTime() > endFlow.getCollectTime().getTime()){
          if(records.getStopReading().compareTo(endFlow.getStopReading()) == 1){
            electricyIncrement = NumberUtil.sub(records.getStopReading(),endFlow.getStopReading(),2);
            deviceStatistics.setEnergyConsumption(electricyIncrement);
            deviceStatistics.setRecordTime(records.getCollectTime());
            insert(consumptionDevice,deviceStatistics);
          }
        }else{
          if(endFlow.getStopReading().compareTo(records.getStopReading()) == 1){
            electricyIncrement = NumberUtil.sub(endFlow.getStopReading(),records.getStopReading(),2);
            deviceStatistics.setEnergyConsumption(electricyIncrement);
            deviceStatistics.setRecordTime(endFlow.getCollectTime());
            insert(consumptionDevice,deviceStatistics);
          }
        }

      }
      // 2.有多条记录
      if(list.size() > 1){

        // 2.1 最新一条记录时间小于当前流水时间
        if(endFlow.getCollectTime().getTime() < records.getCollectTime().getTime()){
          Double electricyIncrement = 0d;
          if(records.getStopReading().compareTo(endFlow.getStopReading()) == 1){
            electricyIncrement = NumberUtil.sub(records.getStopReading(),endFlow.getStopReading(),2);
            deviceStatistics.setEnergyConsumption(electricyIncrement);
            deviceStatistics.setRecordTime(records.getCollectTime());
            insert(consumptionDevice,deviceStatistics);
          }
        }else{
          // 2.2 最新一条记录时间大于当前流水时间
          // 查找小于当前流水时间的最大一条记录
          LambdaQueryWrapper<EnergyConsumptionRecords> qw2 = new LambdaQueryWrapper<>();
          qw2.eq(EnergyConsumptionRecords::getDeviceCode,records.getDeviceCode());
          qw2.lt(EnergyConsumptionRecords::getCollectTime,records.getCollectTime());
          qw2.orderByDesc(EnergyConsumptionRecords::getCollectTime).last("limit 1");// 时间最大
          EnergyConsumptionRecords endFlow2 = baseMapper.selectOne(qw2);
          // 查找大于当前流水时间的最小一条记录
          LambdaQueryWrapper<EnergyConsumptionRecords> qw3 = new LambdaQueryWrapper<>();
          qw3.eq(EnergyConsumptionRecords::getDeviceCode,records.getDeviceCode());
          qw3.gt(EnergyConsumptionRecords::getCollectTime,records.getCollectTime());
          qw3.orderByAsc(EnergyConsumptionRecords::getCollectTime).last("limit 1");// 时间最小
          EnergyConsumptionRecords endFlow3 = baseMapper.selectOne(qw3);


          if(null == endFlow2&&null != endFlow3){// 2.2.1 流水时间为最大
            Double electricyIncrement = 0d;
            if(endFlow3.getStopReading().compareTo(records.getStopReading()) == 1){
              electricyIncrement = NumberUtil.sub(endFlow3.getStopReading(),records.getStopReading(),2);
              deviceStatistics.setEnergyConsumption(electricyIncrement);
              deviceStatistics.setRecordTime(records.getCollectTime());
              insert(consumptionDevice,deviceStatistics);
            }
          }
          if(null != endFlow2&&endFlow3 != null){ // 2.2.2 有大于流水时间的记录 也有小于流水的记录
            // 查找大于等于流水时间的最近一条统计记录
            LambdaQueryWrapper<EnergyConsumptionDeviceStatistics> qw4 = new LambdaQueryWrapper<>();
            qw4.eq(EnergyConsumptionDeviceStatistics::getDeviceCode,records.getDeviceCode());
            qw4.ge(EnergyConsumptionDeviceStatistics::getRecordTime,records.getCollectTime());
            qw4.orderByAsc(EnergyConsumptionDeviceStatistics::getRecordTime).last("limit 1");
            EnergyConsumptionDeviceStatistics deviceStatistics1 = energyConsumptionDeviceStatisticsMapper.selectOne(qw4);
            // 2.2.3 计算当前流水时间到大于当前流水时间的一条记录的增量
            Double electricyIncrement = 0d;
            if(records.getStopReading().compareTo(endFlow2.getStopReading()) == 1){
              electricyIncrement = NumberUtil.sub(records.getStopReading(),endFlow2.getStopReading(),2);
              deviceStatistics.setEnergyConsumption(electricyIncrement);
              deviceStatistics.setRecordTime(records.getCollectTime());
              insert(consumptionDevice, deviceStatistics);
            }
            // 2.2.4 计算流水时间到小于当前流水时间的一条记录的增量
            Double electricyIncrement2 = 0d;
            if(endFlow3.getStopReading().compareTo(records.getStopReading()) == 1){
              if(deviceStatistics1 != null){
                electricyIncrement2 = NumberUtil.sub(endFlow3.getStopReading(),records.getStopReading(),2);
                deviceStatistics1.setEnergyConsumption(electricyIncrement2);
                deviceStatistics1.setRecordTime(endFlow3.getCollectTime());
                deviceStatistics1.setModifyTime(new Date());
                energyConsumptionDeviceStatisticsMapper.updateById(deviceStatistics1);
              }
            }
          }

        }
      }

    }

    // 三、保存记录
    save(records);

  }

  private void insert(EnergyConsumptionDevice consumptionDevice, EnergyConsumptionDeviceStatistics deviceStatistics) {
    deviceStatistics.setDeviceCode(consumptionDevice.getDeviceCode());
    deviceStatistics.setType(consumptionDevice.getType());
    deviceStatistics.setCreateTime(new Date());
    deviceStatistics.setModifyTime(new Date());

    //是否计入总量已经采集点区域(插入当天的)
    EnergyConsumptionDeviceVo deviceCode = energyDeviceRecordService.getByTimeAndDeviceCode(consumptionDevice.getDeviceCode(), deviceStatistics.getRecordTime(), consumptionDevice.getType());
    deviceStatistics.setCollectionPointId(deviceCode.getCollectionPointId());
    deviceStatistics.setPartitionId(deviceCode.getPartitionId());
    deviceStatistics.setIsCollect(deviceCode.getIsCollect());
    energyConsumptionDeviceStatisticsMapper.insert(deviceStatistics);
  }

  private EnergyConsumptionDevice validParamRequired(EnergyConsumptionRecords records) {
    Assert.isTrue(StringUtils.isNotEmpty(records.getDeviceCode()), "设备编号不能为空");
    Assert.isTrue(records.getCollectTime() != null, "时间不能为空");
    Assert.isTrue(records.getStopReading() != null, "能耗不能为空");
    EnergyConsumptionDevice device = energyConsumptionDeviceService.findByDeviceCode(
        records.getDeviceCode());
    Assert.isTrue(device != null, "设备不存在");
    records.setType(device.getType());
    return device;
  }

  @Override
  public IPage<EnergyConsumptionRecordsVO> queryList(Page page,
      EnergyConsumptionRecordsDTO customQueryParams) {
    return baseMapper.selectByCondition(page, customQueryParams);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void inserBatch(List<EnergyConsumptionRecords> saveRecordss) {
    // 校验参数不能为空
    Assert.notEmpty(saveRecordss, "参数不能为空");
    // 批量新增
    saveRecordss.stream().forEach(p-> addRecords(p));
  }

  @Override
  public void flowEnergyRecords(FlowPushData flowPushData) {
    //FlowPushData
    log.debug("----能耗记录流水处理----");
    try {
      //流水取deviceStatus信息
      //List<DeviceStatus> deviceStatusList = flowPushData.getDeviceStatusList();
      //查触发设备的最新流水
      QueryWrapper<DeviceStatus> deviceStatusQw = new QueryWrapper<>();
      deviceStatusQw.eq("device_code_", flowPushData.getDevice_id());
      List<DeviceStatus> deviceStatusList = deviceStatusMapper.selectList(deviceStatusQw);
      if(CollectionUtil.isEmpty(deviceStatusList)){
        log.error("触发设备流水不存在，deviceCode:{}",flowPushData.getDevice_id());
        return;
      }

      String deviceCode = flowPushData.getDevice_id();
      LambdaQueryWrapper<EnergyConsumptionDevice> deviceQw = new LambdaQueryWrapper<>();
      //1启用，0禁用
      // deviceQw.eq(EnergyConsumptionDevice::getUseStatus,1);
      // deviceQw.eq(EnergyConsumptionDevice::getDeviceCode, deviceCode);
      // EnergyConsumptionDevice device = energyConsumptionDeviceService.getOne(deviceQw);
      EnergyConsumptionDevice device = null;
      EnergyConsumptionDeviceVo deviceVo = new EnergyConsumptionDeviceVo();
      deviceVo.setUseStatus(1);
      deviceVo.setDeviceCode(deviceCode);
      IPage<EnergyConsumptionDeviceVo> toiletDeviceDTOIPage = energyConsumptionDeviceMapper.queryListByPage(new Page(), deviceVo);
      if (ObjectUtil.isNotEmpty(toiletDeviceDTOIPage) && ObjectUtil.isNotEmpty(toiletDeviceDTOIPage.getRecords())) {
        device = toiletDeviceDTOIPage.getRecords().get(0);
      }

      //设备存在，且是启用
      if(device == null){
        log.warn("触发设备不存在或者已禁用，deviceCode:{}",deviceCode);
        return;
      }

      JSONObject flowData = (JSONObject) flowPushData.getData();
      if(null != flowData){
        EnergyConsumptionRecords energyConsumptionRecords = buildRecords(flowData, device);
        if(null == energyConsumptionRecords){
          return ;
        }
        // 设备用水列表 倒序
        LambdaQueryWrapper<EnergyConsumptionRecords> qw = new LambdaQueryWrapper<>();
        qw.eq(EnergyConsumptionRecords::getDeviceCode,device.getDeviceCode());
        qw.orderByDesc(EnergyConsumptionRecords::getCollectTime);
        List<EnergyConsumptionRecords> list = baseMapper.selectList(qw);
        EnergyConsumptionDeviceStatistics deviceStatistics = new EnergyConsumptionDeviceStatistics();
        if(CollectionUtil.isNotEmpty(list)){
          EnergyConsumptionRecords endFlow = list.get(0);// 最新一条 计算增量
          Double electricyIncrement = 0d;
          if(energyConsumptionRecords.getStopReading().compareTo(endFlow.getStopReading()) == 1){
            electricyIncrement = NumberUtil.sub(energyConsumptionRecords.getStopReading(),endFlow.getStopReading(),2);
            deviceStatistics.setDeviceCode(device.getDeviceCode());
            deviceStatistics.setEnergyConsumption(electricyIncrement);
            deviceStatistics.setType(device.getType());
            deviceStatistics.setCollectionPointId(device.getCollectionPointId());
            deviceStatistics.setPartitionId(deviceStatistics.getPartitionId());
            deviceStatistics.setRecordTime(new Date());
            deviceStatistics.setCreateTime(new Date());
            deviceStatistics.setModifyTime(new Date());
            //设置设备统计是否要算入总量中
            List<String> strings = energyDeviceRecordService.isCollect(device.getType());
            if (CollectionUtil.isNotEmpty(strings) && strings.contains(device.getDeviceCode())){
              deviceStatistics.setIsCollect(true);
            }else {
              deviceStatistics.setIsCollect(false);
            }
            energyConsumptionDeviceStatisticsMapper.insert(deviceStatistics);
            save(energyConsumptionRecords);
          }
        }else{
          save(energyConsumptionRecords);
        }

      }

    } catch (Exception e) {
      log.error("---能耗记录，无法解析---");
    }
  }

  private EnergyConsumptionRecords buildRecords(JSONObject flowData,EnergyConsumptionDevice device){
    EnergyConsumptionRecords energyConsumptionRecords = new EnergyConsumptionRecords();
    energyConsumptionRecords.setDataSource(0);
    energyConsumptionRecords.setDeviceCode(device.getDeviceCode());
    energyConsumptionRecords.setCollectTime(new Date());
    energyConsumptionRecords.setCreateTime(new Date());
    energyConsumptionRecords.setModifyTime(new Date());
    energyConsumptionRecords.setType(device.getType());
    switch(device.getType()){
      case 1:
        // 包含cumulative_energy 属性
        Double cumulativeEnergy = flowData.getDouble(EnergyConsumptionPhisicalEnum.ELECTRICITY.getEnergy());
        if (null == cumulativeEnergy) {
          log.error("能耗记录，电类型设备，cumulative_energy为空");
          return null;
        }
        //查触发设备的昨天以前最新流水
        LambdaQueryWrapper<EnergyConsumptionRecords> qw = new LambdaQueryWrapper<>();
        qw.eq(EnergyConsumptionRecords::getDeviceCode,device.getDeviceCode());
        qw.lt(EnergyConsumptionRecords::getCollectTime,DateUtil.beginOfDay(new Date()));
        qw.orderByDesc(EnergyConsumptionRecords::getCollectTime);
        qw.last("limit 1");
        EnergyConsumptionRecords lastRecord = baseMapper.selectOne(qw);
        if(null != lastRecord){
          // 计算止码 = 昨天以前最大的流水 +  今日用量
          cumulativeEnergy = NumberUtil.add(cumulativeEnergy,lastRecord.getStopReading(),2);
        }
        energyConsumptionRecords.setStopReading(cumulativeEnergy);

        /**
         * switch_state
         * 枚举值：
         * off - 断电
         * on - 通电
         */
        String switchState = flowData.getString(EnergyConsumptionPhisicalEnum.ELECTRICITY.getState());
        if(StringUtils.isNotEmpty(switchState)){
          List<String> openStatus = ElectricitySwitchEunm.OPEN.getOldStatus();
          List<String> closeStatus = ElectricitySwitchEunm.CLOSE.getOldStatus();
          if(openStatus.contains(switchState)){
            energyConsumptionRecords.setSwitchState(1); // 状态(0.断闸，1.通闸)
          }
          if(closeStatus.contains(switchState)){
            energyConsumptionRecords.setSwitchState(0); // 状态(0.断闸，1.通闸)
          }
        } else {
          energyConsumptionRecords.setSwitchState(1); // 状态(0.断闸，1.通闸)
        }
        break;
      case 2:
        Double waterFlux = flowData.getDouble(EnergyConsumptionPhisicalEnum.WATER.getEnergy());
        if(null == waterFlux){
          log.error("能耗记录，电类型设备，waterFlux为空");
          return null;
        }
        energyConsumptionRecords.setStopReading(waterFlux);
        /**
         * valve_state
         * 枚举值：
         * 0 - 开阀
         * 1 - 关阀
         * 2 - 异常
         */
        String valveState = flowData.getString("valve_state");
        if(StringUtils.isNotEmpty(valveState)){
          List<String> openStatus = WaterSwitchEunm.OPEN.getOldStatus();
          List<String> closeStatus = WaterSwitchEunm.CLOSE.getOldStatus();
          if(openStatus.contains(valveState)){
            energyConsumptionRecords.setSwitchState(1); // 状态(0.断闸，1.通闸)
          }
          if(closeStatus.contains(valveState)){
            energyConsumptionRecords.setSwitchState(0); // 状态(0.断闸，1.通闸)
          }
        }
        break;
      case 3:
        Double gasFlux = flowData.getDouble(EnergyConsumptionPhisicalEnum.GAS.getEnergy());
        if(null == gasFlux){
          log.error("能耗记录，气类型设备，gasFlux为空");
          return null;
        }
        energyConsumptionRecords.setStopReading(gasFlux);
        /**
         * equipment_state
         * 枚举值：
         * 0 - 阀门开，电池电压正常
         * 1 - 阀门关，电池电压正常
         * 2 - 阀门异常，电池电压正常
         * 3 - 阀门开，电池电压低
         * 4 - 阀门关，电池电压低
         * 5 - 阀门异常，电池电压低
         */
        String equipmentState = flowData.getString("equipment_state");
        if(StringUtils.isNotEmpty(equipmentState)){
          List<String> openStatus = GasSwitchEunm.OPEN.getOldStatus();
          List<String> closeStatus = GasSwitchEunm.CLOSE.getOldStatus();
          if(openStatus.contains(equipmentState)){
            energyConsumptionRecords.setSwitchState(1); // 状态(0.断闸，1.通闸)
          }
          if(closeStatus.contains(equipmentState)){
            energyConsumptionRecords.setSwitchState(0); // 状态(0.断闸，1.通闸)
          }
        }
        break;
      default:
        break;
    }
    return energyConsumptionRecords;
  }
}
