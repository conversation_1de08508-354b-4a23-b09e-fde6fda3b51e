package com.smartPark.business.garbagehouse.device.service.impl;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.garbagehouse.device.entity.vo.GarbageHouseDeviceAlarmVo;
import com.smartPark.business.garbagehouse.device.entity.vo.GarbageHouseDeviceVo;
import com.smartPark.business.garbagehouse.device.excel.handler.GarbageHouseDeviceAlarmHandler;
import com.smartPark.business.garbagehouse.device.mapper.GarbageHouseDeviceAlarmMapper;
import com.smartPark.business.garbagehouse.device.service.GarbageHouseDeviceAlarmService;
import com.smartPark.business.garbagehouse.device.service.GarbageHouseDeviceService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * @Description 垃圾屋告警服务
 * 
 * @Date
 */
@Service
public class GarbageHouseDeviceAlarmServiceImpl implements GarbageHouseDeviceAlarmService {
    @Autowired
    private GarbageHouseDeviceAlarmMapper garbageHouseDeviceAlarmMapper;
    @Autowired
    private GarbageHouseDeviceService garbageHouseDeviceService;
    @Autowired
    private GarbageHouseDeviceAlarmService garbageHouseDeviceAlarmService;
    @Autowired
    private BaseUserContextProducer baseUserContextProducer;
    @Autowired
    private ExcelService excelService;

    /**
     * @Description: 根据区域查询告警情况
     * 
     * @date 
     */
    @Override
    public Map<String, Object> findAlarmStatistics(GarbageHouseDeviceVo garbageHouseDeviceVo) {
        Map<String, Object> map = new HashMap<>();
        //查询实时告警数目
        List<GarbageHouseDeviceVo> garbageHouseDeviceVos = this.getGarbageHouseDeviceVos(garbageHouseDeviceVo);
        long count = garbageHouseDeviceVos.stream().filter(m -> Integer.valueOf(1).equals(m.getAlarmState())).count();
        map.put("alarm",count);
        RequestModel<GarbageHouseDeviceAlarmVo> requestModel = new RequestModel<>();
        GarbageHouseDeviceAlarmVo garbageHouseDeviceAlarmVo = new GarbageHouseDeviceAlarmVo();
        garbageHouseDeviceAlarmVo.setAreaPaths(garbageHouseDeviceVo.getAreaPaths());
        requestModel.setCustomQueryParams(garbageHouseDeviceAlarmVo);
        requestModel.setPage(new Page(1,5));
        IPage<GarbageHouseDeviceAlarmVo> alarmVoIPage = garbageHouseDeviceAlarmService.queryListByPage(requestModel);
        //查询告警总数量  20230407 修改为正常设备数
        long total = garbageHouseDeviceVos.size();
        map.put("alarmTotal",total-count);
        //查询告警列表
        map.put("alarmList",alarmVoIPage.getRecords());
        return map;
    }

    /**
     * @Description: 根据条件，分页(不分页)查询
     * 
     * @date 
     */
    @Override
    public IPage<GarbageHouseDeviceAlarmVo> queryListByPage(RequestModel<GarbageHouseDeviceAlarmVo> requestModel) {
        Page page = requestModel.getPage();
        GarbageHouseDeviceAlarmVo garbageHouseDeviceAlarmVo = requestModel.getCustomQueryParams();
        //todo 暂定垃圾屋告警为1
        garbageHouseDeviceAlarmVo.setModel(String.valueOf(DeviceModelConstant.GARBAGE_HOUSE));
        IPage<GarbageHouseDeviceAlarmVo> garbageHouseDeviceList = garbageHouseDeviceAlarmMapper.findGarbageHouseDeviceList(page, garbageHouseDeviceAlarmVo);
        garbageHouseDeviceList.getRecords().forEach(m ->{
            //区域范围
            if (StringUtils.isNotBlank(m.getAreaPath())){
                m.setAreaPath(m.getAreaPath().replace("@","/"));
            }
        });
        return garbageHouseDeviceList;
    }

    /**
     * @Description: 根据设备编码查询垃圾屋告警日志
     * 
     * @date 
     */
    @Override
    public List<GarbageHouseDeviceAlarmVo> findAlarmByDeviceCode(String deviceCode) {
        List<GarbageHouseDeviceAlarmVo> records = getGarbageHouseDeviceAlarmVos((vo) -> vo.setDeviceCode(deviceCode));
        return records;
    }

    /**
     * @Description: 根据id查询单条设备告警详情
     * 
     * @date 
     */
    @Override
    public GarbageHouseDeviceAlarmVo findById(Long id) {
        List<GarbageHouseDeviceAlarmVo> records = getGarbageHouseDeviceAlarmVos((vo) -> vo.setId(id));
        GarbageHouseDeviceAlarmVo garbageHouseDeviceAlarmVo = records.get(0);
        return garbageHouseDeviceAlarmVo;
    }

    /**
     * @Description: 根据条件导出
     * 
     * @date 
     */
    @Override
    public Long export(GarbageHouseDeviceAlarmVo garbageHouseDeviceAlarmVo, HttpServletRequest request, HttpServletResponse response) {
        Long userId = baseUserContextProducer.getCurrent().getId();
        DataExportParam dataExportParam = new DataExportParam();
        dataExportParam.setParam(garbageHouseDeviceAlarmVo);
        dataExportParam.setExportFileName("垃圾屋告警");
        dataExportParam.setTenantCode("traffic");
        dataExportParam.setBusinessCode("garbageHouseDeviceAlarm");
        dataExportParam.setCreateUserCode(userId.toString());
        Long taskId = excelService.doExport(dataExportParam, GarbageHouseDeviceAlarmHandler.class);
        return taskId;
    }

    /**
     * 查询垃圾屋设备
     * @param garbageHouseDeviceVo
     * @return
     */
    private List<GarbageHouseDeviceVo> getGarbageHouseDeviceVos(GarbageHouseDeviceVo garbageHouseDeviceVo) {
        //查询符合条件的设备
        RequestModel<GarbageHouseDeviceVo> requestModel  = new RequestModel<>();
        requestModel.setCustomQueryParams(garbageHouseDeviceVo);
        requestModel.setPage(new Page(1,-1));
        IPage<GarbageHouseDeviceVo> iPage = garbageHouseDeviceService.queryListByPage(requestModel);
        List<GarbageHouseDeviceVo> records = iPage.getRecords();
        return records;
    }

    /**
     * 不分页查询告警集合
     * @param consumer 调用的地方自己设置值
     * @return
     */
    private List<GarbageHouseDeviceAlarmVo> getGarbageHouseDeviceAlarmVos(Consumer<GarbageHouseDeviceAlarmVo> consumer) {
        RequestModel<GarbageHouseDeviceAlarmVo> requestModel = new RequestModel<>();
        GarbageHouseDeviceAlarmVo garbageHouseDeviceAlarmVo = new GarbageHouseDeviceAlarmVo();
        consumer.accept(garbageHouseDeviceAlarmVo);
        requestModel.setCustomQueryParams(garbageHouseDeviceAlarmVo);
        requestModel.setPage(new Page(0,-1));
        IPage<GarbageHouseDeviceAlarmVo> alarmVoIPage = queryListByPage(requestModel);
        List<GarbageHouseDeviceAlarmVo> records = alarmVoIPage.getRecords();
        return records;
    }
}
