package com.smartPark.business.garbagehouse.device.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.smartPark.business.garbagehouse.device.entity.vo.GarbageHouseDeviceVo;
import com.smartPark.business.garbagehouse.device.excel.model.GarbageHouseDeviceExportModel;
import com.smartPark.business.garbagehouse.device.excel.model.GarbageHouseDeviceExportModelDTO;
import com.smartPark.business.garbagehouse.device.service.GarbageHouseDeviceService;
import com.smartPark.common.asyncexcel.handler.CommonExportHandler;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * 
 */
@Slf4j
@ExcelHandle
public class GarbageHouseDeviceHandler extends CommonExportHandler<GarbageHouseDeviceExportModel> {

    @Resource
    private GarbageHouseDeviceService garbageHouseDeviceService;

    @Resource
    private RedisUtil redisUtil;



    @Override
    public void init(ExcelContext ctx, DataParam param) {
        // 初始化导出上下文
        ExportContext context = (ExportContext) ctx;
        //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
        WriteSheet sheet = EasyExcel.writerSheet(0, "第一个sheet").head(GarbageHouseDeviceExportModel.class).build();
        context.setWriteSheet(sheet);
    }


    @Override
    public ExportPage<GarbageHouseDeviceExportModel> exportData(int startPage, int limit, DataExportParam param) {
        GarbageHouseDeviceVo garbageHouseDeviceVo = (GarbageHouseDeviceVo)param.getParam();
        List<GarbageHouseDeviceExportModelDTO> list = garbageHouseDeviceService.queryList4Export(garbageHouseDeviceVo);
        List<GarbageHouseDeviceExportModel> exportList = GarbageHouseDeviceExportModel.getList4Export(list);
        ExportPage<GarbageHouseDeviceExportModel> result = new ExportPage<>();
        result.setTotal(new Long(list.size()));
        result.setCurrent(1L);
        result.setSize(new Long(list.size()));
        result.setRecords(exportList);
        return result;
    }


}

