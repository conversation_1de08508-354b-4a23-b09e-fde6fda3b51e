package com.smartPark.business.garbagehouse.device.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.smartPark.business.garbagehouse.device.entity.vo.GarbageHouseDeviceAlarmVo;
import com.smartPark.business.garbagehouse.device.entity.vo.GarbageHouseDeviceVo;
import com.smartPark.business.garbagehouse.device.service.GarbageHouseDeviceAlarmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;
import java.util.Map;

/**
 * 智慧垃圾屋/垃圾屋地图
 * 
 *  
 * @Description: 智慧垃圾屋/垃圾屋地图
 */
@RestController
@RequestMapping("garbageHouseDeviceMap")
@Api(tags = "垃圾屋地图")
public class GarbageHouseDeviceMapController {
    @Autowired
    private GarbageHouseDeviceAlarmService garbageHouseDeviceAlarmService;

    /**
     * @Description: 根据区域查询告警情况
     * 
     * @date 
     */
    @PostMapping("alarms")
    @ApiOperation("根据区域查询告警情况")
    public RestMessage findAlarmStatistics(@RequestBody GarbageHouseDeviceVo garbageHouseDeviceVo){
        Map<String,Object> map = garbageHouseDeviceAlarmService.findAlarmStatistics(garbageHouseDeviceVo);
        return RestBuilders.successBuilder().data(map).build();
    }

    /**
     * @Description: 根据设备编码查询垃圾屋告警日志
     * 
     * @date 
     */
    @GetMapping("alarm")
    @ApiOperation("根据设备编码查询垃圾屋告警日志")
    public RestMessage findAlarmByDeviceCode(String deviceCode){
        Assert.notNull(deviceCode,"设备编码不能为空");
        List<GarbageHouseDeviceAlarmVo> garbageHouseDeviceStatisticsDTOs = garbageHouseDeviceAlarmService.findAlarmByDeviceCode(deviceCode);
        if (CollectionUtil.isNotEmpty(garbageHouseDeviceStatisticsDTOs) && garbageHouseDeviceStatisticsDTOs.size() > 5){
            garbageHouseDeviceStatisticsDTOs = garbageHouseDeviceStatisticsDTOs.subList(0, 5);
        }
        return RestBuilders.successBuilder().data(garbageHouseDeviceStatisticsDTOs).build();
    }
}
