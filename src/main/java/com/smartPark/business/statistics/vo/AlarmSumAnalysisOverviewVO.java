package com.smartPark.business.statistics.vo;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/31
 * @description 告警汇总分析概览vo
 */
@Data
public class AlarmSumAnalysisOverviewVO {
  /**
   * 本期开始时间
   */
  private Date bqStartTime;
  /**
   * 本期结束时间
   */
  private Date bqEndTime;
  /**
   * 本期告警数量
   */
  private Long bqAlarmCount;

  /**
   * 同期开始时间
   */
  private Date tqStartTime;

  /**
   * 同期结束时间
   */
  private Date tqEndTime;

  /**
   * 同期告警数量
   */
  private Long tqAlarmCount;

  /**
   * 上期开始时间
   */
  private Date sqStartTime;
  /**
   * 上期结束时间
   */
  private Date sqEndTime;
  /**
   * 上期告警数量
   */
  private Long sqAlarmCount;

  /**
   * 同期达标对比
   */
  private Double tqRateComp;

  /**
   * 同期达标对比字符串
   */
  private String tqRateCompStr;

  /**
   * 上期达标对比
   */
  private Double sqRateComp;

  /**
   * 上期达标对比字符串
   */
  private String sqRateCompStr;

  /**
   * 告警汇总分析vo列表
   */
  private List<AlarmSumAnalysisVO> alarmSumAnalysisVOList;

  /**
   * 告警类型分析vo列表
   */
  private List<AlarmTypeAnalysisVO> alarmTypeAnalysisVOList;

}
