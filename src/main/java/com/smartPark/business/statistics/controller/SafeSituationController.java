package com.smartPark.business.statistics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.statistics.service.SafeSituationService;
import com.smartPark.business.statistics.vo.AlarmDataOverviewVO;
import com.smartPark.business.statistics.vo.AlarmSumAnalysisOverviewVO;
import com.smartPark.business.statistics.vo.AlarmSumAnalysisVO;
import com.smartPark.business.statistics.vo.AlarmTrendVO;
import com.smartPark.business.statistics.vo.AlarmTypeAnalysisVO;
import com.smartPark.common.alarm.entity.vo.AlarmVo;
import com.smartPark.common.alarm.entity.vo.AlarmWorkOrderStatisticsVO;
import com.smartPark.common.alarm.entity.vo.CountAlarmByType;
import com.smartPark.common.async.AsyncUtil;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.syshomepage.vo.DataOverviewVo;
import com.smartPark.common.syshomepage.vo.SysHomePageParamVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 统计分析/安防态势统计分析
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/30
 * @description
 */
@RestController
@RequestMapping("/safeSituation")
@Api(tags = "安防态势统计分析")
public class SafeSituationController {

  @Resource
  private SafeSituationService safeSituationService;

  /**
   * 告警数据数据概览
   *
   * @param alarmVO
   * @return
   */
  @PostMapping("/alarmOverviewData")
  @ApiOperation("根据类型统计告警数量")
  public RestMessage alarmOverviewData(@RequestBody AlarmVo alarmVO) {
    List<AlarmDataOverviewVO> alarmWorkOrderCount = safeSituationService.alarmOverviewData(alarmVO);
    return RestBuilders.successBuilder(alarmWorkOrderCount).build();
  }


  /**
   * 告警事件趋势
   *
   * @param alarmVO
   * @return 告警事件总数变化情况，告警事件总数、数量top10的告警事件列表（包含告警事件名称和对应的数量）
   */
  @PostMapping("/alarmTrend")
  @ApiOperation("告警事件趋势")
  public RestMessage alarmTrend(@RequestBody AlarmVo alarmVO) {
    List<AlarmTrendVO> alarmTrendVOList = safeSituationService.alarmTrend(alarmVO);
    return RestBuilders.successBuilder(alarmTrendVOList).build();
  }


  /**
   * 根据类型统计告警数量
   *
   * @param alarmVO
   * @return
   */
  @PostMapping("countAlarmByType")
  @ApiOperation("根据类型统计告警数量")
  public RestMessage countAlarmByType(@RequestBody AlarmVo alarmVO){
    List<CountAlarmByType> list = safeSituationService.countAlarmByType(alarmVO);
    return RestBuilders.successBuilder().data(list).build();
  }


  /**
   * 告警工单耗时k线图
   *
   * @param alarmVO
   * @return
   */
  @PostMapping("/alarmWorkOrderKxDataAnalysis")
  @ApiOperation("告警工单耗时k线图")
  public RestMessage alarmWorkOrderKxDataAnalysis(@RequestBody AlarmVo alarmVO) {
    List<AlarmWorkOrderStatisticsVO> alarmWorkOrderCount = safeSituationService.alarmWorkOrderKxDataAnalysis(alarmVO);
    return RestBuilders.successBuilder(alarmWorkOrderCount).build();
  }

  /**
   * 告警工单饼图
   *
   * @param alarmVO
   * @return
   */
  @PostMapping("/alarmWorkOrderPieChart")
  @ApiOperation("告警工单饼图")
  public RestMessage alarmWorkOrderPieChart(@RequestBody AlarmVo alarmVO) {
    List<DataOverviewVo> alarmWorkOrderCount = safeSituationService.alarmWorkOrderPieChart(alarmVO);
    return RestBuilders.successBuilder(alarmWorkOrderCount).build();
  }

  /**
   * 告警汇总分析数据概览
   *
   * @param alarmVO
   * @return
   */
  @PostMapping("/alarmSumAnalysisOverviewData")
  @ApiOperation("告警汇总分析数据概览")
  public RestMessage alarmSumAnalysisOverviewData(@RequestBody AlarmVo alarmVO) {
    Assert.notNull(alarmVO, "查询参数不能为空");
    Assert.notNull(alarmVO.getQueryStartTime(), "开始时间不能为空");
    Assert.notNull(alarmVO.getQueryEndTime(),"结束时间不能为空");
    Assert.notNull(alarmVO.getDateType(),"查询类别（1小时2日3月4年）不能为空");
    AlarmSumAnalysisOverviewVO overviewData = safeSituationService.alarmSumAnalysisOverviewData(alarmVO);
    return RestBuilders.successBuilder(overviewData).build();
  }

  /**
   * 汇总分析
   *
   * @param requestModel
   * @return
   */
  @PostMapping("/alarmSumAnalysis")
  @ApiOperation("汇总分析")
  public RestMessage alarmSumAnalysis(@RequestBody RequestModel<AlarmVo> requestModel) {
    AlarmVo alarmVo = requestModel.getCustomQueryParams();
    Assert.notNull(requestModel.getPage(),"分页参数不能为空");
    Assert.notNull(alarmVo, "查询参数不能为空");
    Assert.notNull(alarmVo.getQueryStartTime(), "开始时间不能为空");
    Assert.notNull(alarmVo.getQueryEndTime(),"结束时间不能为空");
    Assert.notNull(alarmVo.getDateType(),"查询类别（1小时2日3月4年）不能为空");
    IPage<AlarmSumAnalysisVO> reachVoIPage = safeSituationService.alarmSumAnalysis(alarmVo, requestModel.getPage());
    return RestBuilders.successBuilder().data(reachVoIPage).build();
  }


  /**
   * 汇总分析分析导出
   *
   * @param alarmVo
   * @return
   */
  @PostMapping("/alarmSumAnalysis/export")
  @ApiOperation("汇总分析分析导出")
  public RestMessage alarmSumAnalysis4Export(@RequestBody AlarmVo alarmVo) {
    Assert.notNull(alarmVo, "查询参数不能为空");
    Assert.notNull(alarmVo.getQueryStartTime(), "开始时间不能为空");
    Assert.notNull(alarmVo.getQueryEndTime(),"结束时间不能为空");
    Assert.notNull(alarmVo.getDateType(),"查询类别（1小时2日3月4年）不能为空");
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
    String key = "alarmSumAnalysis:"+formatter.format(new Date());
    AsyncUtil.submitTask(key,() ->{
      //获取并组织excel数据
      String url;
      try {
        url = safeSituationService.alarmSumAnalysis4Export(alarmVo,key);
      } catch (Exception e) {
        throw new BusinessException(e.getMessage());
      }
      return url;
    });
    return RestBuilders.successBuilder().data(key).build();
  }

  /**
   * 告警类型分析
   *
   * @param requestModel
   * @return
   */
  @PostMapping("/alarmTypeAnalysis")
  @ApiOperation("告警类型分析")
  public RestMessage alarmTypeAnalysis(@RequestBody RequestModel<AlarmVo> requestModel) {
    AlarmVo alarmVo = requestModel.getCustomQueryParams();
    Assert.notNull(requestModel.getPage(),"分页参数不能为空");
    Assert.notNull(alarmVo, "查询参数不能为空");
    Assert.notNull(alarmVo.getQueryStartTime(), "开始时间不能为空");
    Assert.notNull(alarmVo.getQueryEndTime(),"结束时间不能为空");
    Assert.notNull(alarmVo.getDateType(),"查询类别（1小时2日3月4年）不能为空");
    IPage<AlarmTypeAnalysisVO> reachVoIPage = safeSituationService.alarmTypeAnalysis(alarmVo, requestModel.getPage());
    return RestBuilders.successBuilder().data(reachVoIPage).build();
  }


  /**
   * 告警类型分析导出
   *
   * @param alarmVo
   * @return
   */
  @PostMapping("/alarmTypeAnalysis/export")
  @ApiOperation("告警类型分析导出")
  public RestMessage alarmTypeAnalysis4Export(@RequestBody AlarmVo alarmVo) {
    Assert.notNull(alarmVo, "查询参数不能为空");
    Assert.notNull(alarmVo.getQueryStartTime(), "开始时间不能为空");
    Assert.notNull(alarmVo.getQueryEndTime(),"结束时间不能为空");
    Assert.notNull(alarmVo.getDateType(),"查询类别（1小时2日3月4年）不能为空");
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
    String key = "alarmTypeAnalysis:"+formatter.format(new Date());
    AsyncUtil.submitTask(key,() ->{
      //获取并组织excel数据
      String url;
      try {
        url = safeSituationService.alarmTypeAnalysis4Export(alarmVo,key);
      } catch (Exception e) {
        throw new BusinessException(e.getMessage());
      }
      return url;
    });
    return RestBuilders.successBuilder().data(key).build();
  }

  /**
   * 告警列表导出
   *
   * @param alarmVo
   * @return
   */
  @PostMapping("/alarmList4Export/export")
  @ApiOperation("告警列表导出")
  public RestMessage alarmList4Export(@RequestBody AlarmVo alarmVo) {
    Assert.notNull(alarmVo, "查询参数不能为空");
    Assert.notNull(alarmVo.getQueryStartTime(), "开始时间不能为空");
    Assert.notNull(alarmVo.getQueryEndTime(),"结束时间不能为空");
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
    String key = "alarmList:"+formatter.format(new Date());
    AsyncUtil.submitTask(key,() ->{
      //获取并组织excel数据
      String url;
      try {
        url = safeSituationService.alarmList4Export(alarmVo,key);
      } catch (Exception e) {
        throw new BusinessException(e.getMessage());
      }
      return url;
    });
    return RestBuilders.successBuilder().data(key).build();
  }


//给运营端提供的接口
  /**
   * 安全守护-基础设施统计
   */
  @ApiOperation(value = "安全守护-基础设施统计")
  @GetMapping("devices")
  public RestMessage getDevices() {
    List<Map<String,Object>> devices = safeSituationService.getDevices();
    return RestBuilders.successBuilder().data(devices).build();
  }
}
