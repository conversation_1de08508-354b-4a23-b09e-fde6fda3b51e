package com.smartPark.business.electronicfence.excel.handler;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.smartPark.business.electronicfence.entity.dto.CarAlarmDTO;
import com.smartPark.business.electronicfence.excel.model.CarAlarmExportOverviewModel;
import com.smartPark.business.electronicfence.service.ElectronicFenceOverviewService;
import com.smartPark.common.alarm.entity.EventType;
import com.smartPark.common.alarm.mapper.EventTypeMapper;
import com.smartPark.common.asyncexcel.handler.CommonExportHandler;
import com.smartPark.common.base.model.RequestModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@ExcelHandle
public class CarAlarmDTOHandler extends CommonExportHandler<CarAlarmExportOverviewModel> {

    @Resource
    private ElectronicFenceOverviewService electronicFenceOverviewService;

    @Resource
    private EventTypeMapper eventTypeMapper;


    @Override
    public void init(ExcelContext ctx, DataParam param) {
        // 初始化导出上下文
        ExportContext context = (ExportContext) ctx;
        //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
        WriteSheet sheet = EasyExcel.writerSheet(0, "第一个sheet").head(CarAlarmExportOverviewModel.class).build();
        context.setWriteSheet(sheet);
    }


    @Override
    public ExportPage<CarAlarmExportOverviewModel> exportData(int startPage, int limit, DataExportParam param) {
        CarAlarmDTO manholeAlarmVo = (CarAlarmDTO)param.getParam();
        RequestModel<CarAlarmDTO> requestModel = new RequestModel<>();
        requestModel.setCustomQueryParams(manholeAlarmVo);
        requestModel.setPage(new Page(startPage,limit));
        IPage<CarAlarmDTO> alarmDTOIPage = electronicFenceOverviewService.detail4Page(requestModel);
        List<CarAlarmDTO> records = alarmDTOIPage.getRecords();
        Set<String> alarmTypes = records.stream().map(CarAlarmDTO::getAlarmType).collect(Collectors.toSet());
        List<EventType> list = new LambdaQueryChainWrapper<>(eventTypeMapper)
                .select(EventType::getName, EventType::getCode).in(EventType::getCode, alarmTypes).list();
        Map<String, String> map = list.stream().collect(Collectors.toMap(EventType::getCode, EventType::getName));

        List<CarAlarmExportOverviewModel> alarmExportOverviewModels = new ArrayList<>();
        records.forEach(l -> {
            CarAlarmExportOverviewModel overviewModel = BeanUtil.toBean(l, CarAlarmExportOverviewModel.class);
            // 转换城alarmTypeName
            overviewModel.setAlarmTypeName(map.get(l.getAlarmType()));
            alarmExportOverviewModels.add(overviewModel);
        });
        ExportPage<CarAlarmExportOverviewModel> result = new ExportPage<>();
        result.setTotal(alarmDTOIPage.getTotal());
        result.setCurrent(alarmDTOIPage.getCurrent());
        result.setSize(alarmDTOIPage.getSize());
        result.setRecords(alarmExportOverviewModels);
        return result;
    }


}

