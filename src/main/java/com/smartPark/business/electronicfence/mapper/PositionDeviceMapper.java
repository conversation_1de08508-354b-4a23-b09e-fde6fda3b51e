package com.smartPark.business.electronicfence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.electronicfence.entity.PositionDevice;
import com.smartPark.business.electronicfence.entity.dto.PositionDeviceDTO;
import org.apache.ibatis.annotations.Param;

/**
 * PositionDevice表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
public interface PositionDeviceMapper extends BaseMapper<PositionDevice> {


    /**
     * 查询分页
     *
     * @param page              分页参数对象
     * @param positionDeviceDTO 过滤参数对象
     * @return 查询分页结果
     */
    IPage<PositionDeviceDTO> selectPage(Page page, @Param("positionDeviceDTO") PositionDeviceDTO positionDeviceDTO);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    // PositionDevice getOneById(@Param("id") Serializable id);
}

