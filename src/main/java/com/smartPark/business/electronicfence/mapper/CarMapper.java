package com.smartPark.business.electronicfence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.electronicfence.entity.Car;
import com.smartPark.business.electronicfence.entity.dto.CarDTO;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * Car表数据库访问层
 *
 * <AUTHOR>
 * @date 2023/04/08
 */
public interface CarMapper extends BaseMapper<Car> {


    /**
     * 查询分页
     *
     * @param page       分页参数对象
     * @param car 过滤参数对象
     * @return 查询分页结果
     */
    IPage<Car> selectPage(Page page, @Param("car") CarDTO car);

    /**
     * 查询单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    Car getOneById(@Param("id") Serializable id);

    /**
     * 查询分页
     *
     * @param page   分页参数对象
     * @param carDTO 过滤参数对象
     * @return 查询分页结果
     */
    IPage<CarDTO> getPageWithDevice(Page page, @Param("carDTO") CarDTO carDTO);

    /**
     * 查询单条
     *
     * @param carDTO 过滤参数对象
     * @return 查询结果
     */
    CarDTO queryOneWithDevice(@Param("carDTO") CarDTO carDTO);

    /**
     * 查询车辆报警次数
     *
     * @param ids
     * @param bind
     * @return
     */
    List<CarDTO> getAlarmCountGroupByCarId(@Param("ids") List<Integer> ids, @Param("bind") Boolean bind);

    List<String> selectCarNos();
}

