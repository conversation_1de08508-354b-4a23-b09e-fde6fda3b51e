package com.smartPark.business.electronicfence.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.electronicfence.entity.People;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smartPark.business.electronicfence.entity.dto.PeopleQueryDTO;
import com.smartPark.common.security.entity.Worker;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 人员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface PeopleMapper extends BaseMapper<People> {

  List<String> selectMobiles();

  IPage<Worker> findPage(Page page, @Param("customQueryParams") PeopleQueryDTO customQueryParams);
}
