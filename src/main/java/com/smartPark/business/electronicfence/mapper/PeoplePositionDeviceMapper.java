package com.smartPark.business.electronicfence.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.electronicfence.entity.PeoplePositionDevice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smartPark.business.electronicfence.entity.dto.PeoplePositionDeviceDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 人员定位设备表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
public interface PeoplePositionDeviceMapper extends BaseMapper<PeoplePositionDevice> {

  IPage<PeoplePositionDeviceDTO> selectPageDTO(Page page, @Param("customQueryParams") PeoplePositionDeviceDTO customQueryParams);
}
