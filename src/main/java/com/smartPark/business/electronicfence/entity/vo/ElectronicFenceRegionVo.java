package com.smartPark.business.electronicfence.entity.vo;

import com.smartPark.business.electronicfence.entity.ElectronicFenceRegion;
import com.smartPark.business.electronicfence.entity.dto.RegionDTO;
import lombok.Data;

import java.util.List;

/**
 * @Description 电子围栏
 * <AUTHOR>
 * @Date 2023/9/21 14:16
 */
@Data
public class ElectronicFenceRegionVo extends ElectronicFenceRegion {
    /**
     * 区域范围
     */
    private List<RegionDTO> coordinateList;
}
