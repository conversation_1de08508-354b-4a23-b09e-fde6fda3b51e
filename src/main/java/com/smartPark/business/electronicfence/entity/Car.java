package com.smartPark.business.electronicfence.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_car")

public class Car extends Model<Car> {
    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Integer id;

    /**
     * 车牌号
     */
    @TableField("car_no_")
    private String carNo;

    /**
     * 车辆名称
     */
    @TableField(exist = false)
    private String name;

    /**
     * deviceCode
     */
    @TableField(exist = false)
    private String deviceCode;

    /**
     * 车辆类型
     */
    @TableField(exist = false)
    @Trans(type= TransType.DICTIONARY,key = "base_obj_vehicle_type")
    private String carCategory;

    /**
     * 车辆状态
     */
    @TableField(exist = false)
    @Trans(type= TransType.DICTIONARY,key = "base_obj_vehicle_status")
    private String carStatus;

    /**
     * 车辆品牌
     */
    @TableField(exist = false)
    private String carBrand;

    /**
     * 车辆参数
     */
    @TableField(exist = false)
    private String description;

    @TableField(exist = false)
    private String remark;

    /**
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     */
    @TableField("deleted_")
    @TableLogic(value = "0")
    private Integer deleted;

}
