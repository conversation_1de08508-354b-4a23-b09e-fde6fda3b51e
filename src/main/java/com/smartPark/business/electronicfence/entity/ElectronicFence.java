package com.smartPark.business.electronicfence.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 电子围栏表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_electronic_fence")
public class ElectronicFence extends Model<ElectronicFence> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField("name_")
    private String name;

    /**
     * 是否进入围栏(1是0否)
     */
    @TableField("fence_in_")
    private Integer fenceIn;

    /**
     * 进入围栏后告警时间(分钟)
     */
    @TableField(value = "in_time_",updateStrategy = FieldStrategy.IGNORED)
    private Integer inTime;

    /**
     * 是否离开围栏(1是0否)
     */
    @TableField("fence_out_")
    private Integer fenceOut;

    /**
     * 离开围栏后告警时间(分钟)
     */
    @TableField(value = "out_time_",updateStrategy = FieldStrategy.IGNORED)
    private Integer outTime;

    /**
     * 时间段，多个，隔开
     */
    @TableField(value = "time_values_",updateStrategy = FieldStrategy.IGNORED)
    private String timeValues;

    /**
     * 围栏类型(字典code electronic_fence_type 1圆形围栏、2多边形围栏、3路径围栏 )
     */
    @TableField(value = "type_")
    @Trans(type= TransType.DICTIONARY,key = "electronic_fence_type",ref = "typeName")
    private String type;

    /**
     * 备注说明
     */
    @TableField("note_")
    private String note;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，1删除，0存在
     */
    @TableField("deleted_")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
