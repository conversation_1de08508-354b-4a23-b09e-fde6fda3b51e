package com.smartPark.business.electronicfence.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.electronicfence.service.ICarTrackService;
import com.smartPark.common.monitor.vo.MonitorQueryVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;

/**
 * 车辆定位轨迹
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("carTrack")
public class CarTrackController extends ApiController {

    @Resource
    private ICarTrackService service;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("list")
    @ApiOperation("根据条件查询车辆轨迹")
    public RestMessage list(@RequestParam("carNo") String carNo, @RequestBody MonitorQueryVO monitorQueryVO) {
        double[][] result  = service.list(carNo,monitorQueryVO);
        return RestBuilders.successBuilder().data(result).build();
    }


}

