package com.smartPark.business.electronicfence.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.electronicfence.entity.PositionDevice;
import com.smartPark.business.electronicfence.entity.dto.PositionDeviceDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * PositionDevice表服务接口
 *
 * <AUTHOR>
 * @date 2023/04/10
 */
public interface PositionDeviceService extends IService<PositionDevice> {

    /**
     * 新增
     *
     * @param livablePositionDevices 实体对象
     * @return 操作结果
     */
    boolean saveOne(List<PositionDevice> livablePositionDevices);

    /**
     * 修改单条
     *
     * @param livablePositionDevice 实体对象
     * @return 修改结果
     */
    boolean updateOne(PositionDevice livablePositionDevice);

    /**
     * 查询分页
     *
     * @param page              分页对象
     * @param positionDeviceDTO 分页参数对象
     * @return 查询分页结果
     */
    IPage<PositionDeviceDTO> selectPage(Page page, PositionDeviceDTO positionDeviceDTO);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    PositionDevice getOneById(Integer id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param livablePositionDevice 过滤条件实体对象
     * @param request               请求
     * @param response              响应
     */
    void export(PositionDevice livablePositionDevice, HttpServletRequest request, HttpServletResponse response);

}

