package com.smartPark.business.electronicfence.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.smartPark.business.electronicfence.entity.PeoplePositionDevice;
import com.smartPark.business.electronicfence.entity.dto.PeoplePositionDeviceDTO;
import com.smartPark.business.electronicfence.mapper.PeoplePositionDeviceMapper;
import com.smartPark.business.electronicfence.service.PeoplePositionDeviceService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.dto.DeviceStatusDTO;
import com.smartPark.common.device.mapper.DeviceMapper;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.Device;
import com.smartPark.common.entity.device.DeviceApplicationModelRef;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

/**
 * <p>
 * 人员定位设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Service
public class PeoplePositionDeviceServiceImpl extends ServiceImpl<PeoplePositionDeviceMapper, PeoplePositionDevice> implements PeoplePositionDeviceService {


  @Resource
  private CommonService commonService;
  @Resource
  private DeviceMapper deviceMapper;
  @Autowired
  private RedisUtil redisUtil;

  @Resource
  private DeviceStatusMapper deviceStatusMapper;

  @Override
  public PeoplePositionDeviceDTO getOneById(Long id) {
    PeoplePositionDeviceDTO peoplePositionDeviceDTO = new PeoplePositionDeviceDTO();
    peoplePositionDeviceDTO.setId(id);
    IPage<PeoplePositionDeviceDTO> peoplePositionDeviceDTOIPage = selectPage(new Page(),
        peoplePositionDeviceDTO);
    List<PeoplePositionDeviceDTO> records = peoplePositionDeviceDTOIPage.getRecords();
    if (CollectionUtil.isNotEmpty(records)) {
      return records.get(0);
    }
    return null;
  }

  @Override
  public IPage<PeoplePositionDeviceDTO> selectPage(Page page, PeoplePositionDeviceDTO customQueryParams) {
    IPage<PeoplePositionDeviceDTO> peoplePositionDeviceIPage = baseMapper.selectPageDTO(page,
        customQueryParams);
    List<PeoplePositionDeviceDTO> records = peoplePositionDeviceIPage.getRecords();
    if (CollectionUtil.isNotEmpty(records)) {
      records.forEach((item) -> {
        setLongitudeAndLatitude(item);
      });
    }
    return peoplePositionDeviceIPage;
  }

  @Override
  public boolean saveOne(List<PeoplePositionDevice> peoplePositionDevices) {
    StringJoiner sj = new StringJoiner("，");
    for (PeoplePositionDevice peoplePositionDevice : peoplePositionDevices) {
      commonService.setCreateAndModifyInfo(peoplePositionDevice);
      if (peoplePositionDevice.getUseStatus() == null) {
        peoplePositionDevice.setUseStatus(1);
      }
      validParamRequired(peoplePositionDevice);
      validRepeat(peoplePositionDevice);
      validParamFormat(peoplePositionDevice);
      save(peoplePositionDevice);
      publishRefEvent(peoplePositionDevice,false);
      sj.add(peoplePositionDevice.getDeviceCode());
    }
    LogHelper.setLogInfo("", peoplePositionDevices.toString(), null, null,"添加设备，设备编码："+sj);

    return true;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean removeById(Serializable id) {
    PeoplePositionDevice peoplePositionDevice = getById(id);
    if (peoplePositionDevice != null) {
      publishRefEvent(peoplePositionDevice, true);
      baseMapper.deleteById(id);
    }
    return true;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean deleteByIds(List<Long> idList) {
    StringJoiner sj = new StringJoiner("，");
    for (Long id : idList) {
      PeoplePositionDevice peoplePositionDevice = getById(id);
      if (peoplePositionDevice != null) {
        publishRefEvent(peoplePositionDevice, true);
        baseMapper.deleteById(id);
        sj.add(peoplePositionDevice.getDeviceCode());
      }
    }
    LogHelper.setLogInfo("", "idList:"+idList, null, null,"删除设备，设备编码："+sj);
    return true;
  }

  @Override
  public boolean updateOne(PeoplePositionDevice peoplePositionDevice) {
    Assert.notNull(peoplePositionDevice.getId(), "id不能为空");
    commonService.setModifyInfo(peoplePositionDevice);

    validRepeat(peoplePositionDevice);
    validParamFormat(peoplePositionDevice);
    updateById(peoplePositionDevice);
    publishRefEvent(peoplePositionDevice, false);
    return true;
  }

  private void publishRefEvent(PeoplePositionDevice peoplePositionDevice,boolean isDelete) {
    try {
      DeviceApplicationModelRef damf = getDeviceApplicationModelRef();
      damf.setDeviceCode(peoplePositionDevice.getDeviceCode());
      damf.setType(0);
      damf.setModelId(DeviceModelConstant.PEOPLE_POSITION_DEVICE);
      if (isDelete) {
        damf.setActionType("delete");
      }
      EventUtil.publishRefEvent(damf);
    } catch (Exception e) {
      log.error("发布设备关联事件失败", e);
    }
  }

  /**
   * 或者保存设备关联的实体
   *
   * @return
   */
  private DeviceApplicationModelRef getDeviceApplicationModelRef() {
    BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);
    DeviceApplicationModelRef device = DeviceApplicationModelRef.getDevice(baseApplication);
    device.setModelId(DeviceModelConstant.PEOPLE_POSITION_DEVICE);
    return device;
  }

  /**
   * 校验重复
   */
  private void validRepeat(PeoplePositionDevice peoplePositionDevice) {
    List<PeoplePositionDevice> list = new LambdaQueryChainWrapper<>(baseMapper)
        .eq(PeoplePositionDevice::getDeviceCode, peoplePositionDevice.getDeviceCode())
        .list();
    if (list.size() == 0) {

    } else if (list.size() > 1 || ObjectUtils.isEmpty(peoplePositionDevice.getId()) || !peoplePositionDevice.getId().equals(list.get(0).getId())) {
      throw new BusinessException("人员定位设备重复");
    }

    list = new LambdaQueryChainWrapper<>(baseMapper)
        .eq(PeoplePositionDevice::getDeviceCode, peoplePositionDevice.getDeviceCode())
        .list();
    if (list.size() == 0) {

    } else if (list.size() > 1 || ObjectUtils.isEmpty(peoplePositionDevice.getId()) || !peoplePositionDevice.getId().equals(list.get(0).getId())) {
      throw new BusinessException("车牌号重复");
    }
  }


  /**
   * 校验参数必填
   */
  private void validParamRequired(PeoplePositionDevice peoplePositionDevice) {
    Assert.notNull(peoplePositionDevice, "参数为空");
    Assert.isTrue(StringUtils.isNotBlank(peoplePositionDevice.getDeviceCode()), "设备code为空");
    Integer count = new LambdaQueryChainWrapper<>(deviceMapper).eq(Device::getCode, peoplePositionDevice.getDeviceCode()).count();
    Assert.isTrue(count > 0, "设备不存在");

  }

  /**
   * 校验参数格式
   */
  private void validParamFormat(PeoplePositionDevice peoplePositionDevice) {
    //Assert.isTrue(livablePositionDevice.getName() == null || livablePositionDevice.getName().length() <= 50,
    //        "名称超长");
  }

  /**
   * 设置设备属性
   *
   * @param deviceStatusDTO
   * @return
   */
  private List<DevicePropertyStatus> getDevicePropertyStatus(DeviceStatusDTO deviceStatusDTO) {
    List<DevicePropertyStatus> devicePropertyStatusList = deviceStatusMapper.listVisiblePropertyStatus(deviceStatusDTO);
    devicePropertyStatusList.forEach((item) -> {
      if (item.getUnit().equals("enum") || "bool".equals(item.getUnit())) {
        JSONObject specsMap = item.getSpecsMap();
        if (specsMap != null && specsMap.containsKey(item.getValue())) {
          item.setValue(specsMap.get(item.getValue()).toString());
        }
      }
    });
    return devicePropertyStatusList;
  }

  // 设置经纬度PeoplePositionDeviceDTO
  private void setLongitudeAndLatitude(PeoplePositionDeviceDTO peoplePositionDeviceDTO) {
    List<DevicePropertyStatus> devicePropertyStatusList = getDevicePropertyStatus(
        new DeviceStatusDTO().setDeviceCodes(
            Lists.newArrayList(peoplePositionDeviceDTO.getDeviceCode())));
    if(CollectionUtil.isNotEmpty(devicePropertyStatusList)){
      devicePropertyStatusList.forEach(devicePropertyStatus -> {
        if(devicePropertyStatus.getProp().equals("longitude")){
          peoplePositionDeviceDTO.setLongitude(Double.valueOf(devicePropertyStatus.getValue()));
        }
        if(devicePropertyStatus.getProp().equals("latitude")){
          peoplePositionDeviceDTO.setLatitude(Double.valueOf(devicePropertyStatus.getValue()));
        }
      });
    }
  }


}
