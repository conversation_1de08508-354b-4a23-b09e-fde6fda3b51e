package com.smartPark.business.electronicfence.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.electronicfence.entity.PositionDevice;
import com.smartPark.business.electronicfence.entity.dto.PositionDeviceDTO;
import com.smartPark.business.electronicfence.mapper.PositionDeviceMapper;
import com.smartPark.business.electronicfence.service.ICarTrackService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.monitor.service.MonitorService;
import com.smartPark.common.monitor.vo.MonitorQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 车辆告警
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CarTrackServiceImpl implements ICarTrackService {

    @Resource
    private MonitorService monitorService;
    @Resource
    private PositionDeviceMapper positionDeviceMapper;

    @Override
    public double[][] list(String carNo, MonitorQueryVO monitorQueryVO) {
        log.info("车辆轨迹:{}", carNo);

        //todo tj
        double[][] d = {{116.478935, 39.997761}, {116.478939, 39.997825}, {116.478912, 39.998549}, {116.478912, 39.998549}, {116.478998, 39.998555}, {116.478998, 39.998555}, {116.479282, 39.99856}, {116.479658, 39.998528}, {116.480151, 39.998453}, {116.480784, 39.998302}, {116.480784, 39.998302}, {116.481149, 39.998184}, {116.481573, 39.997997}, {116.481863, 39.997846}, {116.482072, 39.997718}, {116.482362, 39.997718}, {116.483633, 39.998935}, {116.48367, 39.998968}, {116.484648, 39.999861}};
        if (ObjectUtils.isNotEmpty(carNo)) {
            // 如何设备编号的char的字符是双数
            if (carNo.charAt(carNo.length() - 1) % 2 == 0) {
                d = new double[][]{{116.478935, 39.997761}, {116.478939, 39.997825}, {116.478912, 39.998549}, {116.478912, 39.998549}, {116.478998, 39.998555}, {116.478998, 39.998555}, {116.483633, 39.998935}, {116.48367, 39.99990}, {116.484648, 39.999998}};
            }
        }
        if (d.length == 0) {
            //查询 定位设备，根据车牌号
            PositionDeviceDTO param = new PositionDeviceDTO();
            param.setCarNo(carNo);
            param.setBind(true);
            PositionDevice positionDevice = positionDeviceMapper.selectPage(new Page(), param).getRecords().get(0);
            if (positionDevice != null) {
                RequestModel<MonitorQueryVO> requestModel = new RequestModel<>();
                requestModel.setPage(new Page(1, -1));
                requestModel.setCustomQueryParams(monitorQueryVO.setDeviceCode(positionDevice.getDeviceCode()));
                IPage<Map<String, Object>> map = monitorService.queryHistoricDataFromEsPage(requestModel);
                // map 提取经纬度二维数组
                d = new double[map.getRecords().size()][2];
                for (int i = 0; i < map.getRecords().size(); i++) {
                    Map<String, Object> record = map.getRecords().get(i);
                    d[i][0] = Double.parseDouble(record.get("longitude").toString());
                    d[i][1] = Double.parseDouble(record.get("latitude").toString());
                }
            }
        }
        return d;
    }
}
