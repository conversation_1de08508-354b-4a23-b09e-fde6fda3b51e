package com.smartPark.business.electronicfence.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.electronicfence.entity.ElectronicFence;
import com.smartPark.business.electronicfence.entity.dto.CarDTO;
import com.smartPark.business.electronicfence.entity.vo.ElectronicFenceRegionVo;
import com.smartPark.business.electronicfence.entity.vo.ElectronicFenceVo;
import com.smartPark.common.base.model.RequestModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 电子围栏表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/04/04
 */
public interface ElectronicFenceService extends IService<ElectronicFence> {

    IPage<ElectronicFence> queryListByPage(RequestModel<ElectronicFence> requestModel);

    /**
    * 增加
    * @param electronicFenceVo
    */
    void insert(ElectronicFenceVo electronicFenceVo);

    /**
     * 删除
     * @param ids
     */
    void delBatch(List<Long> ids);

    /**
    * 根据id编辑
    * @param electronicFenceVo
    */
    void updateOne(ElectronicFenceVo electronicFenceVo);

    /**
     * @Description: 查询车辆列表
     * <AUTHOR> yuanfeng
     * @date 2023/04/04 11:42
     */
    List<CarDTO> findCars(Long id);

    /**
     * @Description: 设置电子围栏范围
     * <AUTHOR> yuanfeng
     * @date 2023/04/04 11:42
     */
    void saveRegion(ElectronicFenceVo electronicFenceVo);

    /**
     * @Description: 查询电子范围区域详情(包含多个查询)
     * <AUTHOR> yuanfeng
     * @date 2023/04/04 11:42
     */
    List<ElectronicFenceRegionVo> findRegionByIds(List<Long> ids);
}
