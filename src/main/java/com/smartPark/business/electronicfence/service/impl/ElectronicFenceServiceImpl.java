package com.smartPark.business.electronicfence.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.electronicfence.entity.Car;
import com.smartPark.business.electronicfence.entity.ElectronicFence;
import com.smartPark.business.electronicfence.entity.ElectronicFenceCar;
import com.smartPark.business.electronicfence.entity.ElectronicFenceRegion;
import com.smartPark.business.electronicfence.entity.dto.CarDTO;
import com.smartPark.business.electronicfence.entity.dto.RegionDTO;
import com.smartPark.business.electronicfence.entity.vo.ElectronicFenceRegionVo;
import com.smartPark.business.electronicfence.entity.vo.ElectronicFenceVo;
import com.smartPark.business.electronicfence.mapper.CarMapper;
import com.smartPark.business.electronicfence.mapper.ElectronicFenceCarMapper;
import com.smartPark.business.electronicfence.mapper.ElectronicFenceMapper;
import com.smartPark.business.electronicfence.mapper.ElectronicFenceRegionMapper;
import com.smartPark.business.electronicfence.service.ElectronicFenceService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.security.context.BaseUserContextProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 * 电子围栏 服务实现类
 * </p>
 *
 * <AUTHOR> yuanfeng
 * @since 2022-04-13
 */
@Service
public class ElectronicFenceServiceImpl extends ServiceImpl<ElectronicFenceMapper, ElectronicFence> implements ElectronicFenceService {

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private ElectronicFenceCarMapper electronicFenceCarMapper;
  @Autowired
  private CarMapper carMapper;
  @Autowired
  private ElectronicFenceRegionMapper electronicFenceRegionMapper;

  /**
   * 增加
   * @param electronicFenceVo
   */
  @Override
  @Transactional
  public void insert(ElectronicFenceVo electronicFenceVo) {
    this.saveOrUpdate(electronicFenceVo,(t) -> save(t));
    LogHelper.setLogInfo("", electronicFenceVo.toString(), null, null,"新增电子围栏，围栏名称："+electronicFenceVo.getName());
  }

  /**
   * @return
   * @Description: 删除巡检类型（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  @Transactional
  public void delBatch(List<Long> ids) {
    //删除车辆
    LambdaQueryWrapper<ElectronicFenceCar> carLambdaQueryWrapper = new LambdaQueryWrapper<>();
    carLambdaQueryWrapper.in(ElectronicFenceCar::getElectronicFenceId,ids);
    electronicFenceCarMapper.delete(carLambdaQueryWrapper);
    //删除电子围栏范围
    LambdaQueryWrapper<ElectronicFenceRegion> regionLambdaQueryWrapper = new LambdaQueryWrapper<>();
    regionLambdaQueryWrapper.in(ElectronicFenceRegion::getElectronicFenceId,ids);
    electronicFenceRegionMapper.delete(regionLambdaQueryWrapper);
    //删除电子围栏
    StringJoiner sj = new StringJoiner("，");
    ids.forEach(id->{
      ElectronicFence byId = baseMapper.selectById(id);
      sj.add(byId.getName());
    });
    baseMapper.deleteBatchIds(ids);
    LogHelper.setLogInfo("", "ids:"+ids, null, null,"删除电子围栏，围栏名称："+sj);
  }

  /**
   * 根据id编辑
   * @param electronicFenceVo
   */
  @Override
  @Transactional
  public void updateOne(ElectronicFenceVo electronicFenceVo) {
    this.saveOrUpdate(electronicFenceVo,(t) -> updateById(t));
    ElectronicFence byId = baseMapper.selectById(electronicFenceVo.getId());
    LogHelper.setLogInfo("", electronicFenceVo.toString(), null, null,"编辑电子围栏，围栏名称："+byId.getName());
  }

  /**
   * @Description: 查询车辆列表
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  public List<CarDTO> findCars(Long id) {
    List<Long> carIds;
    //查询已经监控的车辆
    if (null != id){
      LambdaQueryWrapper<ElectronicFenceCar> carWrapper = new LambdaQueryWrapper<>();
      carWrapper.select(ElectronicFenceCar::getCarId).eq(ElectronicFenceCar::getElectronicFenceId,id);
      List<ElectronicFenceCar> fenceCars = electronicFenceCarMapper.selectList(carWrapper);
      carIds = fenceCars.stream().map(f->f.getCarId()).collect(Collectors.toList());
    } else {
      carIds = new ArrayList<>();
    }
    //查询所有车辆
    List<Car> carList = carMapper.selectList(new LambdaQueryWrapper<>());
    List<CarDTO> carDTOS = BeanUtil.copyToList(carList, CarDTO.class);
    carDTOS.forEach(c->{
      //设置哪些已经被选中
      if (CollectionUtil.isNotEmpty(carIds) && carIds.contains(Long.valueOf(c.getId()))){
        c.setChoose(true);
      }
    });
    return carDTOS;
  }

  /**
   * @Description: 设置电子围栏范围
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  public void saveRegion(ElectronicFenceVo electronicFenceVo) {
    //删除电子围栏范围
    Long electronicFenceId = electronicFenceVo.getId();
    LambdaQueryWrapper<ElectronicFenceRegion> regionLambdaQueryWrapper = new LambdaQueryWrapper<>();
    regionLambdaQueryWrapper.eq(ElectronicFenceRegion::getElectronicFenceId,electronicFenceId);
    electronicFenceRegionMapper.delete(regionLambdaQueryWrapper);

    //添加
    Long userId;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    } else {
      userId = null;
    }
    Date date = new Date();
    electronicFenceVo.getRegionList().forEach(r ->{
      ElectronicFenceRegion electronicFenceRegion = BeanUtil.toBean(r, ElectronicFenceRegion.class);
      electronicFenceRegion.setElectronicFenceId(electronicFenceId);
      electronicFenceRegion.setCreatorId(userId);
      electronicFenceRegion.setModifyId(userId);
      electronicFenceRegion.setCreateTime(date);
      electronicFenceRegion.setModifyTime(date);
      //设置区域
      if (CollectionUtil.isNotEmpty(r.getCoordinateList())){
        String string = JSONArray.toJSONString(r.getCoordinateList());
        electronicFenceRegion.setCoordinate(string);
      }
      electronicFenceRegionMapper.insert(electronicFenceRegion);
    });
    ElectronicFence byId = baseMapper.selectById(electronicFenceVo.getId());
    LogHelper.setLogInfo("", electronicFenceVo.toString(), null, null,"设置电子围栏范围，围栏名称："+byId.getName());
  }

  /**
   * @Description: 查询电子范围区域详情(包含多个查询)
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  public List<ElectronicFenceRegionVo> findRegionByIds(List<Long> ids) {
    //查询电子范围
    LambdaQueryWrapper<ElectronicFenceRegion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.in(ElectronicFenceRegion::getElectronicFenceId,ids).orderByAsc(ElectronicFenceRegion::getId);
    List<ElectronicFenceRegion> fenceRegions = electronicFenceRegionMapper.selectList(lambdaQueryWrapper);
    List<ElectronicFenceRegionVo> list = new ArrayList<>();
    fenceRegions.forEach(r ->{
      ElectronicFenceRegionVo regionVo = BeanUtil.toBean(r, ElectronicFenceRegionVo.class);
      if (StringUtils.isNotBlank(r.getCoordinate())){
        List<RegionDTO> regionDTOList = JSONArray.parseArray(r.getCoordinate(), RegionDTO.class);
        regionVo.setCoordinateList(regionDTOList);
      }
      list.add(regionVo);
    });
    return list;
  }

  @Override
  public IPage<ElectronicFence> queryListByPage(RequestModel<ElectronicFence> requestModel) {
    Page page = requestModel.getPage();
    ElectronicFence electronicFence = requestModel.getCustomQueryParams();
    LambdaQueryWrapper<ElectronicFence> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.like(StringUtils.isNotBlank(electronicFence.getName()),ElectronicFence::getName,electronicFence.getName())
            .orderByDesc(ElectronicFence::getModifyTime);
    IPage<ElectronicFence> electronicFenceIPage = baseMapper.selectPage(page, lambdaQueryWrapper);
    return electronicFenceIPage;
  }

  /**
   * 执行新增和更新
   * @param electronicFenceVo
   * @param consumer 自定义执行
   */
  private void saveOrUpdate(ElectronicFenceVo electronicFenceVo, Consumer<ElectronicFence> consumer) {
    /**
     * 验证重复
     */
    this.checkExist(electronicFenceVo);
    //如果是编辑删除之前车辆
    if (null != electronicFenceVo.getId()){
      LambdaQueryWrapper<ElectronicFenceCar> lambdaQueryWrapper = new LambdaQueryWrapper<>();
      lambdaQueryWrapper.eq(ElectronicFenceCar::getElectronicFenceId,electronicFenceVo.getId());
      electronicFenceCarMapper.delete(lambdaQueryWrapper);
    }
    ElectronicFence electronicFence = BeanUtil.toBean(electronicFenceVo, ElectronicFence.class);
    //设置基本属性
    this.setBase(electronicFence);
    //当进入围栏离开围栏为0时。设置时间为null
    if (Integer.valueOf(0).equals(electronicFence.getFenceIn())){
      electronicFence.setInTime(null);
    }
    if (Integer.valueOf(0).equals(electronicFence.getFenceOut())){
      electronicFence.setOutTime(null);
    }
    consumer.accept(electronicFence);
    //保存车辆
    List<Long> carIds = electronicFenceVo.getCarIds();
    if (CollectionUtil.isNotEmpty(carIds)){
      carIds.forEach(carId ->{
        ElectronicFenceCar electronicFenceCar = BeanUtil.toBean(electronicFence, ElectronicFenceCar.class);
        electronicFenceCar.setId(null);
        electronicFenceCar.setElectronicFenceId(electronicFence.getId());
        electronicFenceCar.setCarId(carId);
        electronicFenceCarMapper.insert(electronicFenceCar);
      });
    }
  }

  /**
   * 验证重复
   */
  private void checkExist(ElectronicFenceVo electronicFenceVo) {
    LambdaQueryWrapper<ElectronicFence> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    //设置判断重复条件
    lambdaQueryWrapper.eq(ElectronicFence::getName,electronicFenceVo.getName());
    //编辑的时候存在id
    Optional.ofNullable(electronicFenceVo.getId()).ifPresent(id -> lambdaQueryWrapper.ne(ElectronicFence::getId,electronicFenceVo.getId()));
    Integer integer = baseMapper.selectCount(lambdaQueryWrapper);
    if (integer>0){
      throw new BusinessException("该电子围栏已存在");
    }
  }

  /**
   * 设置基本属性
   * @param electronicFence
   */
  private void setBase(ElectronicFence electronicFence) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    electronicFence.setModifyTime(new Date());
    electronicFence.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == electronicFence.getId()){
      electronicFence.setCreatorId(userId);
      electronicFence.setCreateTime(new Date());
    }
  }
}
