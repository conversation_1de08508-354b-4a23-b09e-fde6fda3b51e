package com.smartPark.business.smartNetwork.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public interface DrainageRectificationPlanConstant {

    interface RectificationStatus {
        String NOT_RECTIFIED = "0";
        String TO_BE_REVIEWED = "1";
        String RECTIFICATION_NOT_PASSED = "2";
        String RECTIFICATION_PASSED = "3";
        String RECTIFIED = "4";
        //待实施 提交之后就是待实施 本来应该在 0后面。但是1已经有人占了
        String TO_BE_IMPLEMENTED = "5";
    }

    HashMap<String, List<String>> rectificationStatusMap = new HashMap<String, List<String>>() {
        {
            put("1", new ArrayList<String>(){{add(RectificationStatus.NOT_RECTIFIED);add(RectificationStatus.RECTIFICATION_NOT_PASSED);}});
            put("0", new ArrayList<String>(){{add(RectificationStatus.TO_BE_REVIEWED);add(RectificationStatus.RECTIFIED);add(RectificationStatus.RECTIFICATION_PASSED);}});
        }
    };
}
