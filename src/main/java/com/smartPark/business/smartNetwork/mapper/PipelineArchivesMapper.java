package com.smartPark.business.smartNetwork.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.smartNetwork.entity.PipelineArchives;
import com.smartPark.business.smartNetwork.entity.vo.PipelineArchivesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PipelineArchivesMapper extends BaseMapper<PipelineArchives> {
    IPage<PipelineArchivesVo> selectPage(Page page, @Param("pipelineArchives")PipelineArchives pipelineArchives);

    PipelineArchivesVo findById(Long id);

    PipelineArchivesVo findByObjId(String objId);

    List<PipelineArchives> findByCondition(@Param("pipelineArchives")PipelineArchives pipelineArchives);
}
