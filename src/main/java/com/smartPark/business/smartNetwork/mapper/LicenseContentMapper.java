package com.smartPark.business.smartNetwork.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.smartNetwork.entity.LicenseContent;

import java.util.List;

public interface LicenseContentMapper extends BaseMapper<LicenseContent> {

}
