package com.smartPark.business.smartNetwork.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.asyncexcel.core.importer.DataImportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.smartNetwork.entity.CountDto;
import com.smartPark.business.smartNetwork.entity.PipeMonitorDevice;
import com.smartPark.business.smartNetwork.entity.PipelineArchives;
import com.smartPark.business.smartNetwork.entity.PipelineStatic;
import com.smartPark.business.smartNetwork.entity.vo.PipeMonitorDeviceDTO;
import com.smartPark.business.smartNetwork.entity.vo.PipeMonitorDeviceVo;
import com.smartPark.business.smartNetwork.entity.vo.PipelineArchivesVo;
import com.smartPark.business.smartNetwork.excel.handle.PipelineArchivesImportHandler;
import com.smartPark.business.smartNetwork.excel.model.PipelineArchivesImportModel;
import com.smartPark.business.smartNetwork.mapper.PipelineArchivesMapper;
import com.smartPark.business.smartNetwork.service.DrainageHouseholdArchivesService;
import com.smartPark.business.smartNetwork.service.PipeLineArchivesService;
import com.smartPark.business.smartNetwork.service.PipeMonitorDeviceService;
import com.smartPark.common.alarm.entity.vo.AlarmVo;
import com.smartPark.common.alarm.service.DeviceAlarmService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.dto.MonitorPointDTO;
import com.smartPark.common.device.service.BaseObjInfoService;
import com.smartPark.common.device.service.MonitorPointService;
import com.smartPark.common.device.service.ObjInfoService;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.DeviceApplicationModelRef;
import com.smartPark.common.entity.device.MonitorPoint;
import com.smartPark.common.entity.device.ObjInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.utils.DateUtil;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PipeLineArchivesServiceImpl extends ServiceImpl<PipelineArchivesMapper, PipelineArchives> implements PipeLineArchivesService {

    @Resource
    private CommonService commonService;

    @Autowired
    private ObjInfoService objInfoService;

    @Autowired
    private MonitorPointService monitorPointService;

    @Resource
    private PipeMonitorDeviceService pipeMonitorDeviceService;

    @Resource
    private DrainageHouseholdArchivesService drainageHouseholdArchivesService;

    @Resource
    private DeviceAlarmService deviceAlarmService;

    @Resource
    private RedisUtil redisUtil;

    @Autowired
    private BaseObjInfoService baseObjInfoService;

    @Resource
    private BaseUserContextProducer baseUserContextProducer;

    @Resource
    private ExcelService excelService;

    @Override
    public IPage<PipelineArchivesVo> selectPage(Page page, PipelineArchives pipelineArchives) {
        return baseMapper.selectPage(page, pipelineArchives);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOne(PipelineArchivesVo pipelineArchivesVo) {
        StringBuilder sj = new StringBuilder();
        sj.append("新增管道档案,管道档案编号:");
        sj.append(pipelineArchivesVo.getObjId());
        commonService.setCreateAndModifyInfo(pipelineArchivesVo);
        validParamRequired(pipelineArchivesVo);
        validRepeat(pipelineArchivesVo);
        validParamFormat(pipelineArchivesVo);
        //处理点位与设施信息
        insertObjInfo(pipelineArchivesVo);
        //关联表
        //获取应用名，应用id
        DeviceApplicationModelRef device = getDeviceApplicationModelRef();
        device.setObjId(pipelineArchivesVo.getObjId());
        //保存 关联库
        EventUtil.publishRefEvent(device);
        save(pipelineArchivesVo);
        //保存相关的已选择设备
        if(CollectionUtil.isNotEmpty(pipelineArchivesVo.getPipeMonitorDeviceCodeList())){
            //清除掉以前所有相关的信息
            pipeMonitorDeviceService.deleteByPipeObjId(pipelineArchivesVo.getObjId());
            pipelineArchivesVo.getPipeMonitorDeviceCodeList().forEach(pipeMonitorDeviceCode -> {
                PipeMonitorDevice existOne = pipeMonitorDeviceService.findByDeviceCode(pipeMonitorDeviceCode);
                existOne.setMonitorPipeId(pipelineArchivesVo.getObjId());
                pipeMonitorDeviceService.update(existOne);
            });
        }
        LogHelper.setLogInfo("smartNetwork:pipelineManagement:pipelineArchives:add", JSON.toJSONString(pipelineArchivesVo), null,JSON.toJSONString(pipelineArchivesVo),sj.toString());
        return true;
    }

    private static void setAreaPath(Object object) {
        List<String> areaPath = new ArrayList<>();
        areaPath.add((String) ReflectUtil.getFieldValue(object,"szjd"));
        areaPath.add((String) ReflectUtil.getFieldValue(object,"szsq"));
        //判断属性
        if (ReflectUtil.hasField(object.getClass(),"szdywg")){
            areaPath.add((String) ReflectUtil.getFieldValue(object,"szdywg"));
        }else {
            areaPath.add((String) ReflectUtil.getFieldValue(object,"szwg"));
        }
        //删除空串和null
        areaPath.removeIf(a-> StringUtils.isBlank(a));
        String join = StringUtils.join(areaPath, "@");
        ReflectUtil.setFieldValue(object,"areaPath",join);
    }

    /**
     * 处理点位与设施信息
     * @param drainageHouseholdArchives
     */
    private void insertObjInfo(PipelineArchivesVo drainageHouseholdArchives) {
        //新增设施信息
        ObjInfo objInfo = new ObjInfo();
        BeanUtils.copyProperties(drainageHouseholdArchives.getObjInfo(),objInfo);
        objInfo.setSource(1);
        setAreaPath(objInfo);
        objInfoService.saveOrUpdate(objInfo);
//            objInfo.setObjId(drainageHouseholdArchives.getObjInfo().getObjId());
//            objInfo.setObjName(drainageHouseholdArchives.getObjInfo().getObjName());
//            objInfo.setDeptName(drainageHouseholdArchives.getObjInfo().getDeptName());
//            objInfo.setOwnerEnterpriseName(drainageHouseholdArchives.getObjInfo().getOwnerEnterpriseName());
//            objInfo.setObjState(drainageHouseholdArchives.getObjInfo().getObjState());
//            objInfo.setContactPerson(drainageHouseholdArchives.getObjInfo().getContactPerson());
//            objInfo.setContactPhone(drainageHouseholdArchives.getObjInfo().getContactPhone());
//            objInfo.setObjX(drainageHouseholdArchives.getObjInfo().getObjX());
//            objInfo.setObjY(drainageHouseholdArchives.getObjInfo().getObjY());
//            objInfo.setRemark(drainageHouseholdArchives.getObjInfo().getRemark());

        //新增点位与设施信息
        MonitorPoint monitorPoint = new MonitorPoint();
        monitorPoint.setSource(1);
        monitorPoint.setBsm(drainageHouseholdArchives.getObjInfo().getObjId());//将这个点位的编号暂时与设施的保持一致
        monitorPoint.setObjId(drainageHouseholdArchives.getObjInfo().getObjId());
        monitorPoint.setDwmc(drainageHouseholdArchives.getObjInfo().getObjName());
        monitorPoint.setZbx(drainageHouseholdArchives.getObjInfo().getObjX());
        monitorPoint.setZby(drainageHouseholdArchives.getObjInfo().getObjY());
        monitorPoint.setSzjd(drainageHouseholdArchives.getObjInfo().getSzjd());
        monitorPoint.setSzsq(drainageHouseholdArchives.getObjInfo().getSzsq());
        monitorPoint.setSzwg(drainageHouseholdArchives.getObjInfo().getSzdywg());
        setAreaPath(monitorPoint);
        monitorPointService.saveOrUpdate(monitorPoint);
    }

    private DeviceApplicationModelRef getDeviceApplicationModelRef() {
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.SAFE);
        DeviceApplicationModelRef device = DeviceApplicationModelRef.getInstall(baseApplication);
        device.setModelId(DeviceModelConstant.PIPE_LINE_ARCHIVES);
        return device;
    }

    private void validRepeat(PipelineArchivesVo pipelineArchivesVo) {
        //关联的objId唯一
        objIdUnique(pipelineArchivesVo);

        QueryWrapper<PipelineArchives> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("obj_id_", pipelineArchivesVo.getObjId());
        queryWrapper.eq("deleted_", 0);
        List<PipelineArchives> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if(list.size()==1 && !Objects.equals(pipelineArchivesVo.getId(),list.get(0).getId())){
            throw new BusinessException("部件码已存在");
        }
        if (list.size() > 1) {
            throw new BusinessException("部件码已存在");
        }
        if (ObjectUtils.isEmpty(pipelineArchivesVo.getObjId())) {
            throw new BusinessException("部件码已存在");
        }
        if (!pipelineArchivesVo.getObjId().equals(list.get(0).getObjId())) {
            throw new BusinessException("部件码已存在");
        }
    }

    private void objIdUnique(PipelineArchivesVo drainageHouseholdArchives) {
        if (ObjectUtils.isEmpty(drainageHouseholdArchives.getObjInfo())) {
            throw new BusinessException("缺少档案设施信息");
        }
        //如果是新增，则这个objId也要不存在
        if (ObjectUtils.isEmpty(drainageHouseholdArchives.getId())) {
            ObjInfo objInfoById = baseObjInfoService.getObjInfoById(drainageHouseholdArchives.getObjInfo().getObjId());
            if (objInfoById!=null) {
                throw new BusinessException("管道编号与系统部件id重复，请更换编号");
            }
        }
    }


    private void validParamFormat(PipelineArchivesVo pipelineArchivesVo) {
//        Assert.isTrue(pipelineArchivesVo.getObjId() == null || (pipelineArchivesVo.getObjId().length() == 16 && pipelineArchivesVo.getObjId().startsWith("0106", 6)), "部件码校验不通过");
//        Assert.isTrue(pipelineArchivesVo.getObjId() == null || null != objInfoMapper.findByMonitorPointBsm(pipelineArchivesVo.getObjId()), "部件码不存在");
        Assert.isTrue(pipelineArchivesVo.getObjId() != null, "部件码不存在");
    }

    /**
     * 校验参数必填
     */
    private void validParamRequired(PipelineArchivesVo pipelineArchivesVo) {
        Assert.notNull(pipelineArchivesVo, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(pipelineArchivesVo.getObjId()), "部件码为空");
        Assert.notNull(pipelineArchivesVo.getPipeType(), "管道类型为空");
        Assert.isTrue(StringUtils.isNotBlank(pipelineArchivesVo.getObjInfo().getObjName()), "管道名称为空");
    }

    @Override
    public PipelineArchivesVo getInfo(Long id) {
        PipelineArchivesVo result = baseMapper.findById(id);
        if(result!=null){
            //获取绑定的设备
            List<PipeMonitorDeviceVo> pipeMonitorDeviceVos =  pipeMonitorDeviceService.findByPipeObjId(result.getObjId());
            if(CollectionUtil.isNotEmpty(pipeMonitorDeviceVos)){
                List<PipeMonitorDeviceDTO> dtos = new ArrayList<>();
                pipeMonitorDeviceVos.forEach(item->{
                    PipeMonitorDeviceDTO byId = pipeMonitorDeviceService.findById(item.getId());
                    dtos.add(byId);
                });
                result.setPipeMonitorDeviceList(dtos);
                //处理告警信息
                List<String> deviceCodes = pipeMonitorDeviceVos.stream().map(PipeMonitorDevice::getDeviceCode).collect(Collectors.toList());
                RequestModel<AlarmVo> requestModel = new RequestModel<>();
                Page page = new Page();
                page.setSize(5);
                page.setCurrent(1);
                requestModel.setPage(page);
                AlarmVo alarmVo = new AlarmVo();
                alarmVo.setModel("24");
                alarmVo.setDeviceCodes(deviceCodes);
                requestModel.setCustomQueryParams(alarmVo);
                IPage<AlarmVo> alarmVoIPage = deviceAlarmService.queryListByPage(requestModel);
                if(CollectionUtil.isNotEmpty(alarmVoIPage.getRecords())){
                    result.setAlarmList(alarmVoIPage.getRecords());
                }
            }
            //区域范围
            if (StringUtils.isNotBlank(result.getObjInfo().getAreaPath())){
                result.getObjInfo().setAreaPath(result.getObjInfo().getAreaPath().replace("@","/"));
            }
        }
        return result;
    }

    @Override
    public boolean updateOne(PipelineArchivesVo pipelineArchivesVo) {
        Assert.notNull(pipelineArchivesVo.getId(), "id不能为空");
        commonService.setModifyInfo(pipelineArchivesVo);
        PipelineArchivesVo existOne = baseMapper.findById(pipelineArchivesVo.getId());
        if(existOne==null){
            throw new BusinessException("当前档案不存在");
        }
        List<PipeMonitorDeviceVo> pipeMonitorDeviceVos = pipeMonitorDeviceService.findByPipeObjId(pipelineArchivesVo.getObjId());
        if(CollectionUtil.isNotEmpty(pipeMonitorDeviceVos)){
            List<PipeMonitorDeviceDTO> result = new ArrayList<>();
            pipeMonitorDeviceVos.forEach(item->{
                PipeMonitorDeviceDTO pipeMonitorDeviceDTO = new PipeMonitorDeviceDTO();
                BeanUtils.copyProperties(item,pipeMonitorDeviceDTO);
                result.add(pipeMonitorDeviceDTO);
            });
            existOne.setPipeMonitorDeviceList(result);
        }
        validRepeat(pipelineArchivesVo);
        validParamFormat(pipelineArchivesVo);
        //处理点位与设施信息
        insertObjInfo(pipelineArchivesVo);
        StringBuilder sj = new StringBuilder();
        sj.append("修改档案,设备编号:");
        sj.append(pipelineArchivesVo.getObjId());
        pipeMonitorDeviceService.deleteByPipeObjId(pipelineArchivesVo.getObjId());
        //保存相关的许可证内容污染物项目
        if(CollectionUtil.isNotEmpty(pipelineArchivesVo.getPipeMonitorDeviceCodeList())){
            //清除掉以前所有相关的信息
            pipelineArchivesVo.getPipeMonitorDeviceCodeList().forEach(pipeMonitorDeviceCode -> {
                PipeMonitorDevice existDevice = pipeMonitorDeviceService.findByDeviceCode(pipeMonitorDeviceCode);
                existDevice.setMonitorPipeId(pipelineArchivesVo.getObjId());
                pipeMonitorDeviceService.update(existDevice);
            });
        }
        //如果类型不是污水管道，则需要清空掉以前的排水户与管道的绑定关系
        if(!Objects.equals(pipelineArchivesVo.getPipeType(),1)){
            drainageHouseholdArchivesService.clearPipeObjId(pipelineArchivesVo.getObjId());
        }
        LogHelper.setLogInfo("smartNetwork:pipelineManagement:pipelineArchives:edit", JSON.toJSONString(pipelineArchivesVo), JSON.toJSONString(existOne), JSON.toJSONString(pipelineArchivesVo), sj.toString());
        return saveOrUpdate(pipelineArchivesVo);
    }

    @Override
    public boolean deleteByIds(List<Long> idList) {
        StringBuilder sj = new StringBuilder();
        sj.append("删除设备,设备编号:");
        for (Long id : idList) {
            PipelineArchives pipeLinaArchives = baseMapper.selectById(id);
            DeviceApplicationModelRef damf = getDeviceApplicationModelRef();
            damf.setActionType(EventUtil.DELETE);
            damf.setObjId(pipeLinaArchives.getObjId());
            sj.append(pipeLinaArchives.getObjId());
            //删除关系
            EventUtil.publishRefEvent(damf);
            removeById(id);
            pipeMonitorDeviceService.deleteByPipeObjId(pipeLinaArchives.getObjId());
            drainageHouseholdArchivesService.clearPipeObjId(pipeLinaArchives.getObjId());
            //处理点位与设施信息
            deleteObjInfo(pipeLinaArchives.getObjId());
        }
        LogHelper.setLogInfo("smartNetwork:pipelineManagement:pipelineArchives:delete", sj.toString(), null, null, sj.toString());
        return true;
    }

    /**
     * 删除掉来源为1的点位与设施信息
     * @param objId
     */
    private void deleteObjInfo(String objId) {
        ObjInfo objInfo = baseObjInfoService.getObjInfoById(objId);
        if(objInfo!=null&&Objects.equals(objInfo.getSource(),1)){
            objInfoService.removeById(objInfo.getObjId());
        }
        MonitorPointDTO monitorPointDTO = monitorPointService.findOne(objId);
        if (monitorPointDTO!=null && Objects.equals(monitorPointDTO.getSource(),1)){
            monitorPointService.removeById(monitorPointDTO.getBsm());
        }

    }

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", 1).eq("id_", id).update();
    }

    @Override
    public PipelineStatic pipelineStatic(PipelineArchivesVo pipelineArchivesVo) {
        //封装结果
        PipelineStatic pipelineStatic = new PipelineStatic();
        Map<Integer,Integer> pipeTypeMap = new HashMap<>();
        Map<String,Integer> deviceTypeMap = new HashMap<>();
        List<CountDto> countDtos = new ArrayList<>();
        pipelineStatic.setPipeTypeMap(pipeTypeMap);
        pipelineStatic.setDeviceTypeMap(deviceTypeMap);
        //根据区域查询管道情况
        Page page = new Page();
        page.setSize(-1);
        page.setCurrent(1);
        IPage<PipelineArchivesVo> pipelineArchivesVoIPage = baseMapper.selectPage(page, pipelineArchivesVo);
        if (CollectionUtil.isNotEmpty(pipelineArchivesVoIPage.getRecords())){
            //处理管道类型数据
            Map<Integer, List<PipelineArchivesVo>> collect = pipelineArchivesVoIPage.getRecords().stream().filter(item -> item.getPipeType() != null).collect(Collectors.groupingBy(PipelineArchivesVo::getPipeType));
            if(CollectionUtil.isNotEmpty(collect.keySet())){
                collect.keySet().forEach(key->{
                    pipeTypeMap.put(key,collect.get(key).size());
                });
            }
            //处理设备类型数据
            List<String> objIds = pipelineArchivesVoIPage.getRecords().stream().map(PipelineArchivesVo::getObjId).collect(Collectors.toList());
            List<PipeMonitorDeviceVo> deviceVos = pipeMonitorDeviceService.findByPipeObjIds(objIds);
            if(CollectionUtil.isNotEmpty(deviceVos)){
                Map<String, List<PipeMonitorDeviceVo>> deviceMap = deviceVos.stream().filter(deviceVo -> StringUtils.isNotBlank(deviceVo.getTypeName())).collect(Collectors.groupingBy(PipeMonitorDeviceVo::getTypeName));
                if(CollectionUtil.isNotEmpty(deviceMap.keySet())){
                    deviceMap.keySet().forEach(key->{
                        deviceTypeMap.put(key,deviceMap.get(key).size());
                    });
                }
            }
            //处理告警信息，30天内前5的报警设备
            //最近30天是指
            if(CollectionUtil.isNotEmpty(deviceVos)){
                List<String> deviceCodes = deviceVos.stream().map(PipeMonitorDeviceVo::getDeviceCode).collect(Collectors.toList());
                Date endTime = new Date();
                Date startTime = DateUtil.addDay(new Date(), -29);
                startTime = DateUtil.getDayStartTime(startTime);
                RequestModel<AlarmVo> requestModel = new RequestModel<>();
                requestModel.setPage(page);
                AlarmVo alarmVo = new AlarmVo();
                alarmVo.setModel("24");
                alarmVo.setQueryStartTime(startTime);
                alarmVo.setQueryEndTime(endTime);
                alarmVo.setDeviceCodes(deviceCodes);
                requestModel.setCustomQueryParams(alarmVo);
                IPage<AlarmVo> alarmVoIPage = deviceAlarmService.queryListByPage(requestModel);
                if (CollectionUtil.isNotEmpty(alarmVoIPage.getRecords())){
                    Map<String, List<AlarmVo>> alarmMap = alarmVoIPage.getRecords().stream().collect(Collectors.groupingBy(AlarmVo::getDeviceCode));
                    for (String deviceCode:alarmMap.keySet()) {
                        CountDto countDto = new CountDto();
                        //拼接全量名称
                        PipeMonitorDeviceVo monitorDevice = pipeMonitorDeviceService.findDetailByDeviceCode(deviceCode);
                        PipelineArchivesVo pipelineArchives = baseMapper.findByObjId(monitorDevice.getMonitorPipeId());
                        if (pipelineArchives!=null){
                            countDto.setName(pipelineArchives.getObjInfo().getObjName()+"-"+monitorDevice.getSbmc());
                        }else {
                            countDto.setName(monitorDevice.getSbmc());
                        }
                        countDto.setSum(alarmMap.get(deviceCode).size());
                        countDtos.add(countDto);
                    }
                }
            }
            countDtos = countDtos.stream().sorted(Comparator.comparingInt(CountDto::getSum).reversed()).limit(5).collect(Collectors.toList());
        }
        pipelineStatic.setAlarmDevices(countDtos);
        return pipelineStatic;
    }

    @Override
    public List<PipeMonitorDeviceVo> getMonitorDevicePage(PipelineArchives pipelineArchives) {
        //根据区域查询所有的管道
        List<PipelineArchives> pipelineArchivesList = baseMapper.findByCondition(pipelineArchives);
        List<PipeMonitorDeviceVo> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(pipelineArchivesList)){
            pipelineArchivesList.forEach(item -> {
                //通过管道id查询相关的设备信息
                List<PipeMonitorDeviceVo> devices = pipeMonitorDeviceService.findDetailByPipeObjId(item.getObjId());
                devices.forEach(device->{
                    device.setPipeArchivesId(item.getId());
                    device.setPipeArchivesName(item.getObjInfo().getObjName());
                    device.setCircuit(item.getCircuit());
                });
                result.addAll(devices);
            });
        }
        return result;
    }

    @Override
    public void validForExcel(PipelineArchivesVo pipelineArchives) {
        validParamRequired(pipelineArchives);
        validRepeat(pipelineArchives);
        validParamFormat(pipelineArchives);
    }

    @Override
    public Long imports(MultipartFile file) throws IOException{
        Long userId = baseUserContextProducer.getCurrent().getId();
        String businessCode = "pipelineArchivesList";
        String msg="管网档案";
        String menuCode = "smartNetwork:pipelineManagement:pipelineArchives:import";
        DataImportParam dataImportParam = new DataImportParam()
                .setStream(file.getInputStream())
                .setModel(PipelineArchivesImportModel.class)
                .setBatchSize(100)
                .setFilename("管网档案记录导入");
        dataImportParam.setTenantCode("safe");
        dataImportParam.setBusinessCode(businessCode);
        dataImportParam.setCreateUserCode(userId.toString());


        Long taskId = excelService.doImport(PipelineArchivesImportHandler.class, dataImportParam);
        msg+= "批量导入：" + file.getOriginalFilename();
        LogHelper.setLogInfo(menuCode, msg, null, null, msg);
        return taskId;
    }
}
