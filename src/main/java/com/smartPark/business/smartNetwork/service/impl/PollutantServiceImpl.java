package com.smartPark.business.smartNetwork.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.smartNetwork.entity.LicenseContent;
import com.smartPark.business.smartNetwork.entity.Pollutant;
import com.smartPark.business.smartNetwork.mapper.LicenseContentMapper;
import com.smartPark.business.smartNetwork.mapper.PollutantMapper;
import com.smartPark.business.smartNetwork.service.LicenseContentService;
import com.smartPark.business.smartNetwork.service.PollutantService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PollutantServiceImpl extends ServiceImpl<PollutantMapper, Pollutant> implements PollutantService {

    @Resource
    private PollutantMapper pollutantMapper;

    @Override
    public void deleteByArchivesObjId(String objId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("archives_obj_id_",objId);
        pollutantMapper.delete(queryWrapper);
    }

    @Override
    public IPage<Pollutant> getByObjId(Page page, String archivesObjId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("archives_obj_id_",archivesObjId);
        return pollutantMapper.selectPage(page,queryWrapper);
    }

    @Override
    public void deleteByArchivesId(Long archivesId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("archives_id_",archivesId);
        pollutantMapper.delete(queryWrapper);
    }

    @Override
    public IPage<Pollutant> getByArchivesId(Page page, Long archivesId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("archives_id_",archivesId);
        return pollutantMapper.selectPage(page,queryWrapper);
    }
}
