package com.smartPark.business.smartNetwork.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 整改计划
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_drainage_rectification_plan")
public class DrainageRectificationPlan extends BaseEntity<DrainageRectificationPlan> implements TransPojo {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO, value = "id_")
    private Long id;

    /**
     * 整改编号
     */
    @TableField("rectification_no_")
    private String rectificationNo;

    /**
     * 排水户档案id
     */
    @TableField("archives_id_")
    private Long archivesId;

    /**
     * 整改责任人
     */
    @TableField(value = "responsible_person_")
    private String responsiblePerson;

    /**
     * 整改计划日期
     */
    @TableField(value = "plan_date_")
    @JsonFormat(timezone="GMT+8", pattern = "yyyy-MM-dd")
    private Date planDate;

    /**
     * 整改内容
     */
    @TableField(value = "rectification_content_")
    private String rectificationContent;

    /**
     * 整改状态 0:未整改 5：待实施 1：待复核 2：整改不通过 3：整改通过 4：已整改
     */
    @TableField(value = "rectification_status_")
    @Trans(type= TransType.DICTIONARY,key = "drainage_rectification_status")
    private String rectificationStatus;

    /**
     * 提交内容
     */
    @TableField(value = "submit_content_")
    private String submitContent;

    /**
     * 提交附件URL
     */
    @TableField(value = "submit_url_")
    private String submitUrl;

    /**
     * 提交人
     */
    @TableField(value = "submit_person_")
    private String submitPerson;

    /**
     * 提交时间
     */
    @TableField(value = "submit_time_")
    private Date submitTime;

    /**
     * 复核状态 0:整改不通过，1：整改通过
     */
    @TableField(value = "review_status_")
    @Trans(type= TransType.DICTIONARY,key = "drainage_review_status")
    private String reviewStatus;

    /**
     * 复核内容
     */
    @TableField(value = "review_content_")
    private String reviewContent;

    /**
     * 实施内容
     */
    @TableField(value = "implement_content_")
    private String implementationContent;

    /**
     * 实施人
     */
    @TableField(value = "implement_person_")
    private String implementationPerson;

    /**
     * 实施日期
     */
    @TableField(value = "implement_date_")
    @JsonFormat(timezone="GMT+8", pattern = "yyyy-MM-dd")
    private Date implementationDate;

    /**
     * 实施附件URL
     */
    @TableField(value = "implement_url_")
    private String implementationUrl;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 创建人
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 修改人
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 是否删除字段 0:未删除; 其他：删除
     */
    @TableLogic(value = "0")
    @TableField("deleted_")
    private Long deleted = 0L;

}
