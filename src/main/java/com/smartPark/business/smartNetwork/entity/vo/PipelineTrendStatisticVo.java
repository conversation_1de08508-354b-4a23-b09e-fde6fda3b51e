package com.smartPark.business.smartNetwork.entity.vo;

import com.smartPark.business.smartNetwork.entity.PipelineTrendStatistic;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/9/7 15:01
 */
@Data
public class PipelineTrendStatisticVo extends PipelineTrendStatistic {
    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 设备集合
     */
    private List<String> deviceCodes;
}
