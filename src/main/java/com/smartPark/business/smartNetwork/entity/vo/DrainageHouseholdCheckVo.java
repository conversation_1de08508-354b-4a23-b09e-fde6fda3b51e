package com.smartPark.business.smartNetwork.entity.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.smartPark.business.smartNetwork.entity.DrainageHouseholdArchives;
import com.smartPark.business.smartNetwork.entity.DrainageHouseholdCheck;
import com.smartPark.common.entity.device.ObjInfo;
import lombok.Data;

import java.util.Date;

/**
 * 排水户检查实体vo
 */
@Data
public class DrainageHouseholdCheckVo extends DrainageHouseholdCheck {

    private Date queryTimeStart;

    private Date queryTimeEnd;
    /**
     * 排水户档案实体
     */
    private DrainageHouseholdArchives householdArchives;
}
