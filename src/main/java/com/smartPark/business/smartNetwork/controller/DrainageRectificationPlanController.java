package com.smartPark.business.smartNetwork.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.smartNetwork.entity.DrainageRectificationPlan;
import com.smartPark.business.smartNetwork.entity.vo.DrainageRectificationPlanVo;
import com.smartPark.business.smartNetwork.service.DrainageRectificationPlanService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import java.util.List;

/**
 * 智慧管网/排水户检查管理
 */
@Slf4j
@RestController
@RequestMapping("drainageRectificationPlan")
public class DrainageRectificationPlanController {

    @Autowired
    private DrainageRectificationPlanService drainageRectificationPlanService;

    /**
     * 新增数据
     *
     * @param drainageRectificationPlan 实体对象
     * @return 新增结果
     */
    @PostMapping
    @ApiOperation("新增")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD,menuCode = "smartNetwork:drainInspect:rectifyPlan:add",desc = "新增记录")
    public RestMessage insert(@RequestBody DrainageRectificationPlan drainageRectificationPlan) {
        return RestBuilders.successBuilder().success((this.drainageRectificationPlanService.saveOne(drainageRectificationPlan))).build();
    }

    /**
     * 提交
     *
     * @param drainageHouseholdCheck 实体对象
     * @return 修改结果
     */
    @PutMapping("submit")
    @ApiOperation("提交")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "smartNetwork:drainInspect:rectifyPlan:submit",desc = "提交")
    public RestMessage submit(@RequestBody DrainageRectificationPlan drainageHouseholdCheck) {
        return RestBuilders.successBuilder().success(this.drainageRectificationPlanService.submit(drainageHouseholdCheck)).build();
    }

    /**
     * 复核
     *
     * @param drainageHouseholdCheck 实体对象
     * @return 修改结果
     */
    @PutMapping("review")
    @ApiOperation("复核")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "smartNetwork:drainInspect:rectifyPlan:review",desc = "复核")
    public RestMessage review(@RequestBody DrainageRectificationPlan drainageHouseholdCheck) {
        return RestBuilders.successBuilder().success(this.drainageRectificationPlanService.review(drainageHouseholdCheck)).build();
    }

    /**
     * 修改
     *
     * @param drainageHouseholdCheck 实体对象
     * @return 修改结果
     */
    @PutMapping("update")
    @ApiOperation("修改")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "smartNetwork:drainInspect:rectifyPlan:edit",desc = "复核")
    public RestMessage update(@RequestBody DrainageRectificationPlan drainageHouseholdCheck) {
        return RestBuilders.successBuilder().success(this.drainageRectificationPlanService.update(drainageHouseholdCheck)).build();
    }

    /**
     * 实施
     *
     * @param drainageHouseholdCheck 实体对象
     * @return 修改结果
     */
    @PutMapping("implement")
    @ApiOperation("实施")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "smartNetwork:drainInspect:rectifyPlan:Implement",desc = "实施")
    public RestMessage implement(@RequestBody DrainageRectificationPlan drainageHouseholdCheck) {
        return RestBuilders.successBuilder().success(this.drainageRectificationPlanService.implement(drainageHouseholdCheck)).build();
    }

    /**
     * 修改状态
     *
     * @param id 实体对象id
     * @return 修改结果
     */
    @PutMapping("changePlanStatus/{id}")
    @ApiOperation("修改状态")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "smartNetwork:drainInspect:rectifyPlan:switchStatus",desc = "修改状态")
    public RestMessage changePlanStatus(@PathVariable(value = "id") Long id) {
        return RestBuilders.successBuilder().success(this.drainageRectificationPlanService.changePlanStatus(id)).build();
    }

    /**
     * 查询单条记录
     *
     * @param id 主键
     * @return 修改结果
     */
    @GetMapping("/{id}")
    @ApiOperation("查询单条记录")
    public RestMessage getInfo(@PathVariable("id") Long id) {
        return RestBuilders.successBuilder(this.drainageRectificationPlanService.getInfo(id)).build();
    }

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("getPage")
    @ApiOperation("查询分页")
    public RestMessage selectPage(@RequestBody RequestModel<DrainageRectificationPlanVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DrainageRectificationPlanVo> record = drainageRectificationPlanService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL,menuCode = "smartNetwork:drainInspect:rectifyPlan:delete",desc = "删除检查")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(drainageRectificationPlanService.deleteByIds(idList)).build();
    }

}
