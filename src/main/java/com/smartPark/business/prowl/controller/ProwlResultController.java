package com.smartPark.business.prowl.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.prowl.entity.ProwlResult;
import com.smartPark.business.prowl.entity.vo.ProwlResultVo;
import com.smartPark.business.prowl.service.ProwlResultService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 园区巡逻管理/巡逻结果
 * @author: kan yuan<PERSON>
 * @Date: 2023/04/04 11:42
 * @Description: 巡逻结果
 */
@RestController
@RequestMapping("prowlResult")
@Api(tags = "园区巡逻管理/巡逻结果")
public class ProwlResultController {
  
  @Autowired
  private ProwlResultService prowlResultService;

  /**
   * @Description: 增加巡逻结果
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @PostMapping
  @ApiOperation("增加巡逻结果")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD,menuCode = "prowl:alarm:handle",desc = "新增巡逻结果")
  public RestMessage insert(@RequestBody ProwlResult prowlResult){
    prowlResultService.insert(prowlResult);
    LogHelper.setLogMemo("新增巡逻结果,巡逻结果名称:"+prowlResult);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除巡逻结果（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @DeleteMapping
  @ApiOperation("删除巡逻结果（包含批量删除）")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL,menuCode = "prowl:result:del",desc = "巡逻结果删除")
  public RestMessage delBatch(@RequestBody ProwlResult prowlResult){
    Assert.notEmpty(prowlResult.getIds(),"id不能为空");
    prowlResultService.delBatch(prowlResult.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑巡逻结果
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @PutMapping
  @ApiOperation("编辑巡逻结果")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "prowl:archive:edit",desc = "修改巡逻结果")
  public RestMessage updateById(@RequestBody ProwlResult prowlResult){
    Assert.notNull(prowlResult.getId(),"id不能为空");
    prowlResultService.updateOne(prowlResult);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 处理巡逻结果(处理告警)
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @PutMapping("handler")
  @ApiOperation("处理巡逻结果(处理告警)")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "prowl:result:edit",desc = "修改巡逻结果")
  public RestMessage handlerById(@RequestBody ProwlResult prowlResult){
    Assert.notNull(prowlResult.getId(),"id不能为空");
    prowlResultService.handlerById(prowlResult);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询巡逻结果详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询巡逻结果详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    ProwlResultVo prowlResult = prowlResultService.findById(id);
    return RestBuilders.successBuilder().data(prowlResult).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<ProwlResultVo> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<ProwlResultVo> record = prowlResultService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }

  /**
   * 导出
   * @param prowlResultVo
   */
  @PostMapping("download")
  public RestMessage download(@RequestBody ProwlResultVo prowlResultVo){
    Long taskId = prowlResultService.asyncDownload("巡逻告警导出", prowlResultVo);
    return RestBuilders.successBuilder(taskId).build();
  }
}

