package com.smartPark.business.baseInterfaceLog.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.baseInterfaceLog.dto.BaseInterfaceLogDto;
import com.smartPark.business.baseInterfaceLog.entity.BaseInterfaceLog;
import com.smartPark.business.baseInterfaceLog.entity.vo.BaseInterfaceLogVo;
import com.smartPark.business.baseInterfaceLog.mapper.BaseInterfaceLogMapper;
import com.smartPark.business.baseInterfaceLog.service.BaseInterfaceLogService;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.security.context.BaseUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * BaseInterfaceLog表服务实现类
 *
 * <AUTHOR>
 * @date 2023/03/16
 */
@Slf4j
@Service("baseInterfaceLogService")
public class BaseInterfaceLogServiceImpl extends ServiceImpl
        <BaseInterfaceLogMapper, BaseInterfaceLog> implements BaseInterfaceLogService {
    @Resource
    private CommonService commonService;

    @Resource
    private BaseUserContextProducer baseUserContextProducer;


    @Override
    public boolean saveOne(BaseInterfaceLog baseInterfaceLog) {
        commonService.setCreateAndModifyInfo(baseInterfaceLog);

        validParamRequired(baseInterfaceLog);
        validRepeat(baseInterfaceLog);
        validParamFormat(baseInterfaceLog);
        return save(baseInterfaceLog);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(BaseInterfaceLog baseInterfaceLog) {
        Assert.notNull(baseInterfaceLog.getId(), "id不能为空");
        commonService.setModifyInfo(baseInterfaceLog);

        validRepeat(baseInterfaceLog);
        validParamFormat(baseInterfaceLog);
        return updateById(baseInterfaceLog);
    }

    @Override
    public IPage<BaseInterfaceLog> selectPage(Page page, BaseInterfaceLog baseInterfaceLog) {
        return baseMapper.selectPage(page, baseInterfaceLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        return removeByIds(idList);
    }

    @Override
    public void export(BaseInterfaceLog baseInterfaceLog, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public IPage<BaseInterfaceLogDto> selectPageList(Page page, BaseInterfaceLogVo interfaceLogVo) {
        IPage<BaseInterfaceLogDto> listPage = baseMapper.selectDtoPageList(page, interfaceLogVo);
        return listPage;
    }

    @Override
    public BaseInterfaceLog getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(BaseInterfaceLog baseInterfaceLog) {
        /* QueryWrapper<BaseInterfaceLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", baseInterfaceLog.getName());
        queryWrapper.eq("tenant_id", linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<BaseInterfaceLog> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("名称有重复");
        }
        if (ObjectUtils.isEmpty(baseInterfaceLog.getId())) {
            throw new BusinessException("名称已存在");
        }
        if (!baseInterfaceLog.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称已存在");
        }
                    */

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(BaseInterfaceLog baseInterfaceLog) {
        //Assert.notNull(baseInterfaceLog, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(baseInterfaceLog.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(BaseInterfaceLog baseInterfaceLog) {
        //Assert.isTrue(baseInterfaceLog.getName() == null || baseInterfaceLog.getName().length() <= 50,
        //        "名称超长");
    }
}

