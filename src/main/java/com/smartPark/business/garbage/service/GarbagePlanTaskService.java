package com.smartPark.business.garbage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.garbage.entity.GarbagePlanTask;
import com.smartPark.business.garbage.entity.vo.GarbagePlanTaskVo;
import com.smartPark.business.garbage.entity.vo.GarbagePointVo;
import com.smartPark.common.base.model.RequestModel;

import java.util.Set;

/**
 * <p>
 * 垃圾收运计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/04/04
 */
public interface GarbagePlanTaskService extends IService<GarbagePlanTask> {

  IPage<GarbagePlanTaskVo> queryListByPage(RequestModel<GarbagePlanTaskVo> requestModel);

  /**
   * 增加
   * @param garbagePlanTask
   */
  void insert(GarbagePlanTask garbagePlanTask);

  /**
   * 根据id编辑
   * @param garbagePlanTask
   */
  void updateOne(GarbagePlanTask garbagePlanTask);

  /**
   * @Description: 根据id查询车辆详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  GarbagePlanTaskVo findById(Long id);

  /**
   * @Description: 删除收运计划（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  void delBatch(Set<Long> ids);

  /**
   * 垃圾收运计划触发
   * @return 新增结果
   */
  void toAddGarbageTask();

  /**
   * @Description: 根据计划id查询最新任务
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  GarbagePlanTaskVo newTaskByPanId(Long planId);

  /**
   * @Description: 根据收运点id查询收运点信息和收运点最近收运记录
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  GarbagePointVo getPointList(Long id);
}
