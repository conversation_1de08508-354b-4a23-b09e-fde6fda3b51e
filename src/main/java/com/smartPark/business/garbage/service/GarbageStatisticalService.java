package com.smartPark.business.garbage.service;

import com.smartPark.business.garbage.entity.dto.GarbagePointDTO;
import com.smartPark.business.garbage.entity.dto.GarbageRecordDTO;
import com.smartPark.business.garbage.entity.dto.GarbageStatisticalQueryDTO;
import com.smartPark.business.garbage.entity.dto.GarbageTreeDTO;

import java.util.List;
import java.util.Map;

/**
 * 垃圾收运统计分析
 */
public interface GarbageStatisticalService {
    /**
     * @Description: 总体概览
     * <AUTHOR> y<PERSON><PERSON>
     * @date 2023/04/17 11:42
     */
    List<Map<String, Object>> overview(Integer type);

    /**
     * 查询树结构
     */
    List<GarbageTreeDTO> tree(Integer type);

    /**
     * 垃圾收运量
     */
    List<GarbageRecordDTO> records(GarbageStatisticalQueryDTO garbageStatisticalQueryDTO);

    /**
     * 收运点完成情况
     */
    List<GarbagePointDTO> points(GarbageStatisticalQueryDTO garbageStatisticalQueryDTO);

    /**
     * 垃圾任务
     */
    List<GarbagePointDTO> task(GarbageStatisticalQueryDTO garbageStatisticalQueryDTO);

    /**
     * 收运点排名
     */
    List<GarbageRecordDTO> rank(GarbageStatisticalQueryDTO garbageStatisticalQueryDTO);

    /**
     * 垃圾收运量导出
     */
    Map<String, Object> recordsExport(GarbageStatisticalQueryDTO garbageStatisticalQueryDTO);
}
