package com.smartPark.business.garbage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.garbage.constant.GarbageEnum;
import com.smartPark.business.garbage.entity.GarbageCollectCompany;
import com.smartPark.business.garbage.entity.GarbagePlan;
import com.smartPark.business.garbage.entity.GarbagePlanRef;
import com.smartPark.business.garbage.entity.dto.ContactPersonDTO;
import com.smartPark.business.garbage.entity.vo.GarbagePlanVo;
import com.smartPark.business.garbage.entity.vo.GarbagePointVo;
import com.smartPark.business.garbage.mapper.GarbageCollectCompanyMapper;
import com.smartPark.business.garbage.mapper.GarbagePlanMapper;
import com.smartPark.business.garbage.mapper.GarbagePlanRefMapper;
import com.smartPark.business.garbage.service.GarbageCollectCompanyService;
import com.smartPark.business.garbage.service.GarbagePlanService;
import com.smartPark.business.garbage.service.GarbagePointService;
import com.smartPark.business.garbage.util.GarbageUtils;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.security.context.BaseUserContextProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 收运计划表 服务实现类
 * </p>
 *
 * <AUTHOR> yuanfeng
 * @since 2023-04-13
 */
@Service
public class GarbagePlanServiceImpl extends ServiceImpl<GarbagePlanMapper, GarbagePlan> implements GarbagePlanService {

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private GarbagePlanRefMapper garbagePlanRefMapper;
  @Autowired
  private GarbagePointService garbagePointService;
  @Autowired
  private GarbageCollectCompanyMapper garbageCollectCompanyMapper;
  @Autowired
  private GarbageCollectCompanyService garbageCollectCompanyService;

  /**
   * 增加
   * @param garbagePlanVo
   */
  @Override
  @Transactional
  public void insert(GarbagePlanVo garbagePlanVo) {
    /**
     * 验证重复
     */
    GarbagePlan garbagePlan = BeanUtil.toBean(garbagePlanVo, GarbagePlan.class);
    this.checkExist(garbagePlan);
    //设置code
    GarbageUtils.autoCode(GarbageEnum.PLAN,garbagePlan);
    //设置基本属性
    this.setBase(garbagePlan);

    //处理公司
    QueryWrapper<GarbageCollectCompany> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("name_",garbagePlanVo.getCompanyName())
                    .eq("type_",garbagePlanVo.getType());
    List<GarbageCollectCompany> companies = garbageCollectCompanyMapper.selectList(queryWrapper);
    if (CollectionUtil.isNotEmpty(companies)){
      garbagePlan.setCompanyId(companies.get(0).getId());
    }else {
      GarbageCollectCompany garbageCollectCompany = new GarbageCollectCompany();
      garbageCollectCompany.setName(garbagePlanVo.getCompanyName());
      garbageCollectCompany.setType(garbagePlanVo.getType());
      garbageCollectCompanyService.insert(garbageCollectCompany);
      garbagePlan.setCompanyId(garbageCollectCompany.getId());
    }

    this.save(garbagePlan);
    //保存关联
    garbagePlanVo.getGarbagePlanRefList().forEach(g ->{
      GarbagePlanRef garbagePlanRef = BeanUtil.toBean(garbagePlan, GarbagePlanRef.class);
      garbagePlanRef.setId(null);
      garbagePlanRef.setPlanId(garbagePlan.getId());
      garbagePlanRef.setPointId(g.getPointId());
      garbagePlanRef.setType(garbagePlan.getType());
      garbagePlanRefMapper.insert(garbagePlanRef);
    });
    LogHelper.setLogInfo((Integer.valueOf(2).equals(garbagePlan.getType())?"garbageCollectionManagement":"kitchenWasteManagement")+":collectionPlan:add", LogConstant.LogOperateActionType.ADD, "新增计划,计划名称:"+garbagePlan.getPlanName());
  }

  /**
   * 根据id编辑
   * @param garbagePlanVo
   */
  @Override
  @Transactional
  public void updateOne(GarbagePlanVo garbagePlanVo) {
    GarbagePlan garbagePlan = BeanUtil.toBean(garbagePlanVo, GarbagePlan.class);
    /**
     * 验证重复
     */
    this.checkExist(garbagePlan);
    //设置基本属性
    this.setBase(garbagePlan);
    this.updateById(garbagePlan);
    //删除之前的关联
    QueryWrapper<GarbagePlanRef> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("plan_id_",garbagePlan.getId());
    garbagePlanRefMapper.delete(queryWrapper);
    //保存关联
    garbagePlanVo.getGarbagePlanRefList().forEach(g ->{
      GarbagePlanRef garbagePlanRef = BeanUtil.toBean(garbagePlan, GarbagePlanRef.class);
      garbagePlanRef.setId(null);
      garbagePlanRef.setPlanId(garbagePlan.getId());
      garbagePlanRef.setPointId(g.getPointId());
      garbagePlanRef.setType(garbagePlan.getType());
      garbagePlanRefMapper.insert(garbagePlanRef);
    });
    LogHelper.setLogInfo((Integer.valueOf(2).equals(garbagePlan.getType())?"garbageCollectionManagement":"kitchenWasteManagement")+":collectionPlan:edit", LogConstant.LogOperateActionType.EDIT, "修改计划,计划名称:"+garbagePlan.getPlanName());
  }

  /**
   * @Description: 根据id查询车辆详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @Override
  public GarbagePlanVo findById(Long id) {
    GarbagePlan garbagePlan = baseMapper.selectById(id);
    GarbagePlanVo garbagePlanVo = BeanUtil.toBean(garbagePlan, GarbagePlanVo.class);
    //求收运点数量
    QueryWrapper<GarbagePlanRef> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("plan_id_",id);
    Integer integer = garbagePlanRefMapper.selectCount(queryWrapper);
    garbagePlanVo.setPointNum(integer);
    //查询公司
    Optional.ofNullable(garbagePlanVo).map(g->g.getCompanyId()).ifPresent(companyId->{
      GarbageCollectCompany collectCompany = garbageCollectCompanyMapper.selectById(companyId);
      if (null != collectCompany){
        garbagePlanVo.setCompanyName(collectCompany.getName());
      }
    });

    GarbageUtils.setUserInfo(garbagePlanVo);
    return garbagePlanVo;
  }

  /**
   * @Description: 根据条件，分页(不分页)查询收运点
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @Override
  public List<GarbagePointVo> queryGarbagePointList(GarbagePlan garbagePlan) {
    //查询被绑定的收运点id
    QueryWrapper<GarbagePlanRef> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("type_",garbagePlan.getType())
            .select("point_id_","plan_id_");
    //排除改计划的
    List<GarbagePlanRef> planRefs = garbagePlanRefMapper.selectList(queryWrapper);
    //过滤改计划的
    List<Long> refPlanIds;
    if (null != garbagePlan.getId()){
      refPlanIds = planRefs.stream().filter(p -> garbagePlan.getId().equals(p.getPlanId()))
              .map(p -> p.getPointId()).collect(Collectors.toList());
    } else {
      refPlanIds = new ArrayList<>();
    }
    List<Long> notIds = planRefs.stream()
            .filter(p->!p.getPlanId().equals(garbagePlan.getId()))
            .map(p->p.getPointId()).collect(Collectors.toList());

    //查询收运点
    RequestModel<GarbagePointVo> requestModel = new RequestModel();
    GarbagePointVo garbagePointVo = new GarbagePointVo();
    garbagePointVo.setNotIds(notIds);
    garbagePointVo.setType(garbagePlan.getType());
    requestModel.setCustomQueryParams(garbagePointVo);
    requestModel.setPage(new Page<>(0,-1));
    IPage<GarbagePointVo> pointVoIPage = garbagePointService.queryListByPage(requestModel);
    List<GarbagePointVo> records = pointVoIPage.getRecords();
    records.forEach(r ->{
      if (refPlanIds.contains(r.getId())){
        r.setChecked(true);
      }
    });
    return records;
  }

  /**
   * @Description: 删除收运计划（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @Override
  @Transactional
  public void delBatch(Set<Long> ids) {
    ids.forEach(id ->{
      GarbagePlan garbagePlan = baseMapper.selectById(id);
      LogHelper.setLogInfo((Integer.valueOf(2).equals(garbagePlan.getType())?"garbageCollectionManagement":"kitchenWasteManagement")+":collectionPlan:del", LogConstant.LogOperateActionType.DEL, "删除计划,计划名称:"+garbagePlan.getPlanName());
    });

    //删除对应的收运点关联
    QueryWrapper<GarbagePlanRef> queryWrapper = new QueryWrapper<>();
    queryWrapper.in("plan_id_",ids);
    garbagePlanRefMapper.delete(queryWrapper);
    //删除计划
    baseMapper.deleteBatchIds(ids);
  }

  /**
   * @Description: 启用/禁用计划
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public void updateStatusById(GarbagePlan garbagePlan) {
    this.setBase(garbagePlan);
    baseMapper.updateById(garbagePlan);
  }

  /**
   * @Description: 计划列表(地图)
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @Override
  public List<GarbagePlan> getList4Map(Integer type) {
    QueryWrapper<GarbagePlan> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("type_",type)
            .orderByDesc("plan_status_").orderByDesc("modify_time_");
    List<GarbagePlan> plans = baseMapper.selectList(queryWrapper);
    return plans;
  }

  /**
   * @Description: 收运人下拉查询
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @Override
  public List<ContactPersonDTO> findContactPersons() {
    //todo 第三方接口
//    List<ContactPersonDTO> list = new ArrayList<>();
//    ContactPersonDTO contactPersonDTO = new ContactPersonDTO();
//    contactPersonDTO.setUserId(10726L);
//    contactPersonDTO.setUserName("系统测试用户");
//    contactPersonDTO.setCompanyName("测试公司");
//    contactPersonDTO.setContactPhone("18012347777");
//    list.add(contactPersonDTO);

    List<ContactPersonDTO> list = baseMapper.findContactPersons();
    //todo 收运公司写死先
    list.forEach(l->l.setCompanyName("测试公司"));
    return list;
  }

  @Override
  public IPage<GarbagePlanVo> queryListByPage(RequestModel<GarbagePlanVo> requestModel) {
    Page page = requestModel.getPage();
    GarbagePlanVo garbagePlanVo = requestModel.getCustomQueryParams();
    Assert.notNull(garbagePlanVo.getType(),"type不能为空");
    IPage<GarbagePlanVo> garbagePlanIPage = baseMapper.queryListByPage(page,garbagePlanVo);

    //收运人
    garbagePlanIPage.getRecords().forEach(g->{
      GarbageUtils.setUserInfo(g);
    });

    //求收运点数量
    List<Long> pIds = garbagePlanIPage.getRecords().stream().map(g -> g.getId())
            .collect(Collectors.toList());
    if (pIds.size() > 0){
      QueryWrapper<GarbagePlanRef> queryWrapper = new QueryWrapper<>();
      queryWrapper.in("plan_id_",pIds);
      List<GarbagePlanRef> planRefs = garbagePlanRefMapper.selectList(queryWrapper);
      garbagePlanIPage.getRecords().forEach(r ->{
        long count = planRefs.stream().filter(p -> r.getId().equals(p.getPlanId()))
                .count();
        r.setPointNum((int)count);
      });
    }
    return garbagePlanIPage;
  }

  /**
   * 验证重复
   */
  private void checkExist(GarbagePlan garbagePlan) {
    QueryWrapper<GarbagePlan> queryWrapper = new QueryWrapper<>();
    //设置判断重复条件
    queryWrapper.eq("type_",garbagePlan.getType());
    queryWrapper.and(q -> q.eq("plan_name_",garbagePlan.getPlanName()));
    //编辑的时候存在id
    Optional.ofNullable(garbagePlan.getId()).ifPresent(id -> queryWrapper.ne("id_",garbagePlan.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该收运计划已存在");
    }
  }

  /**
   * 设置基本属性
   * @param garbagePlan
   */
  private void setBase(GarbagePlan garbagePlan) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    garbagePlan.setModifyTime(new Date());
    garbagePlan.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == garbagePlan.getId()){
      garbagePlan.setCreatorId(userId);
      garbagePlan.setCreateTime(new Date());
    }
  }
}
