package com.smartPark.business.garbage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.garbage.entity.GarbageEnterprise;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 垃圾企业表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface GarbageEnterpriseMapper extends BaseMapper<GarbageEnterprise> {

    IPage<GarbageEnterprise> queryListByPage(@Param("page") Page page, @Param("entity") GarbageEnterprise garbageEnterprise);

    /**
     * 查询所有企业(不区分删除)
     * @param type
     * @return
     */
    List<GarbageEnterprise> findAll(Integer type);
}
