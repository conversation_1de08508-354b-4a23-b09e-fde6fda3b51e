package com.smartPark.business.garbage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 垃圾收运计划任务记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_garbage_task_records_detail")
public class GarbageTaskRecordsDetail extends Model<GarbageTaskRecordsDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 记录id
     */
    @TableField("record_id_")
    private Long recordId;

    /**
     * 车辆id
     */
    @TableField(value = "car_id_",updateStrategy = FieldStrategy.IGNORED)
    private Long carId;

    /**
     * 垃圾桶数量
     */
    @TableField("num_")
    private Integer num;

    /**
     * 运收详情json
     */
    @TableField(value = "content_",updateStrategy = FieldStrategy.IGNORED)
    private String content;

    /**
     * 运收时间
     */
    @TableField(value = "time_",updateStrategy = FieldStrategy.IGNORED)
    private Date time;

    /**
     * 类型(1餐厨垃圾，2垃圾运收)
     */
    @TableField("type_")
    private Integer type;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，1删除，0存在
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private List<Long> ids;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
