package com.smartPark.business.garbage.entity.vo;

import com.smartPark.business.garbage.entity.GarbageData;
import lombok.Data;

import java.util.Date;

/**
 * @Description 数据填报
 * <AUTHOR>
 * @Date 2023/4/23 15:13
 */
@Data
public class GarbageDataVo extends GarbageData {
    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 垃圾处理厂id
     */
    private Long factoryId;

    /**
     * 垃圾处理厂名称
     */
    private String factoryName;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 收运车辆id
     */
    private Long carId;

    /**
     * 车牌号
     */
    private String carLicenseNo;
}
