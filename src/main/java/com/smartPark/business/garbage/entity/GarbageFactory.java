package com.smartPark.business.garbage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.smartPark.common.annotation.GaodeText;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * 垃圾处理厂表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@GaodeText
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_garbage_factory")
public class GarbageFactory extends Model<GarbageFactory> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField("name_")
    private String name;

    /**
     * 编号
     */
    @TableField("code_")
    private String code;

    /**
     * 区域
     */
    @TableField(value = "area_",updateStrategy = FieldStrategy.IGNORED)
    private String area;

    /**
     * 具体地址
     */
    @TableField(value = "address_",updateStrategy = FieldStrategy.IGNORED)
    private String address;

    /**
     * 位置定位信息X坐标
     */
    @TableField("objX_")
    private Double objx;

    /**
     * 位置定位信息Y坐标
     */
    @TableField("objY_")
    private Double objy;

    /**
     * 设计处理能力
     */
    @TableField(value = "capacity_",updateStrategy = FieldStrategy.IGNORED)
    private Integer capacity;

    /**
     * 处理方式
     */
    @TableField(value = "way_",updateStrategy = FieldStrategy.IGNORED)
    private String way;

    /**
     * 联系人
     */
    @TableField(value = "contact_person_",updateStrategy = FieldStrategy.IGNORED)
    private String contactPerson;

    /**
     * 联系方式
     */
    @TableField(value = "contact_phone_",updateStrategy = FieldStrategy.IGNORED)
    private String contactPhone;

    /**
     * 类型(1餐厨垃圾，2垃圾运收)
     */
    @TableField("type_")
    private Integer type;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，1删除，0存在
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private Set<Long> ids;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
