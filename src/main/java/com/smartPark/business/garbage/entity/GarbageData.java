package com.smartPark.business.garbage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * 垃圾数据填报表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_garbage_data")
public class GarbageData extends Model<GarbageData> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 计划id
     */
    @TableField("plan_id_")
    private Long planId;

    /**
     * 填报类型(1日常监管，2月度考核)
     */
    @TableField("data_type_")
    private Integer dataType;

    /**
     * 收运时间
     */
    @TableField(value = "collect_time_",updateStrategy = FieldStrategy.IGNORED)
    private String collectTime;

    /**
     * 排放数据
     */
    @TableField("emission_")
    private BigDecimal emission;

    /**
     * 填报时间
     */
    @TableField("record_time_")
    private Date recordTime;

    /**
     * 现场人员签字url
     */
    @TableField(value = "sign_url_",updateStrategy = FieldStrategy.IGNORED)
    private String signUrl;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，1删除，0存在
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private Set<Long> ids;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
