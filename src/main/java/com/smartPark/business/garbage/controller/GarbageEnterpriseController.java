package com.smartPark.business.garbage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.garbage.entity.GarbageEnterprise;
import com.smartPark.business.garbage.service.GarbageEnterpriseService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 餐厨垃圾管理(垃圾运收管理)/企业管理
 * @author: kan yuanfeng
 * @Date: 2023/04/17 11:42
 * @Description: 企业管理
 */
@RestController
@RequestMapping("garbageEnterprise")
@Api(tags = "餐厨垃圾管理(垃圾运收管理)/企业管理")
public class GarbageEnterpriseController {
  
  @Autowired
  private GarbageEnterpriseService garbageEnterpriseService;

  /**
   * @Description: 增加企业
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PostMapping
  @ApiOperation("增加企业")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "", desc = "增加企业")
  public RestMessage insert(@RequestBody GarbageEnterprise garbageEnterprise){
    //参数验证
    Assert.hasLength(garbageEnterprise.getName(),"名称不能为空");
    Assert.notNull(garbageEnterprise.getType(),"类型不能为空");
    garbageEnterpriseService.insert(garbageEnterprise);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 删除企业（包含批量删除）
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @DeleteMapping
  @ApiOperation("删除企业（包含批量删除）")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "", desc = "删除企业")
  public RestMessage delBatch(@RequestBody GarbageEnterprise garbageEnterprise){
    Assert.notEmpty(garbageEnterprise.getIds(),"id不能为空");
    garbageEnterpriseService.delBatch(garbageEnterprise.getIds());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑企业
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PutMapping
  @ApiOperation("编辑企业")
  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "", desc = "编辑企业")
  public RestMessage updateById(@RequestBody GarbageEnterprise garbageEnterprise){
    Assert.notNull(garbageEnterprise.getId(),"id不能为空");
    garbageEnterpriseService.updateOne(garbageEnterprise);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询企业详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询企业详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    GarbageEnterprise garbageEnterprise = garbageEnterpriseService.getById(id);
    return RestBuilders.successBuilder().data(garbageEnterprise).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2023/04/17 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<GarbageEnterprise> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<GarbageEnterprise> record = garbageEnterpriseService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }
}

