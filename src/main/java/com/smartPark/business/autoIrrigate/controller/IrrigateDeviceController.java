package com.smartPark.business.autoIrrigate.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.autoIrrigate.entity.IrrigateDevice;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateDevicePageVo;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateDeviceVo;
import com.smartPark.business.autoIrrigate.service.IrrigateDeviceService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 自动灌溉/设备管理
 *
 * <AUTHOR>
 * @date 2023/04/03
 */
@Slf4j
@RestController
@RequestMapping("/irrigateDevice")
public class IrrigateDeviceController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private IrrigateDeviceService irrigateDeviceService;

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/selectDtoPage")
    @ApiOperation("查询分页")
    public RestMessage selectDtoPage(@RequestBody RequestModel<IrrigateDeviceVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<IrrigateDeviceVo> record = irrigateDeviceService.selectVoPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/selectDtoOne/{id}")
    @ApiOperation("查询单条")
    public RestMessage selectDtoOne(@PathVariable Serializable id) {
        return irrigateDeviceService.getOneDtoById(id);
    }

    /**
     * 新增数据
     *
     * @param livableIrrigateDevice 实体对象
     * @return 新增结果
     */
    @PostMapping
    @ApiOperation("新增")
    public RestMessage insert(@RequestBody IrrigateDevice livableIrrigateDevice) {
        return RestBuilders.successBuilder().success((this.irrigateDeviceService.saveOne(livableIrrigateDevice))).build();
    }

    /**
     * 批量新增数据
     *
     * @param irrigateDevicePageVo 页面对象
     * @return 统一出参
     */
    @PostMapping("/batchInsert")
    @ApiOperation("批量新增")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "autoIrrigate:deviceManage:joinDevice", desc = "管理设备")
    public RestMessage batchInsert(@RequestBody IrrigateDevicePageVo irrigateDevicePageVo) {
        return irrigateDeviceService.batchInsert(irrigateDevicePageVo);
    }

    /**
     * 修改数据
     *
     * @param livableIrrigateDevice 实体对象
     * @return 修改结果
     */
    @PutMapping
    @ApiOperation("修改单条")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "autoIrrigate:deviceManage:edit", desc = "修改设备")
    public RestMessage update(@RequestBody IrrigateDevice livableIrrigateDevice) {
        return RestBuilders.successBuilder().success(this.irrigateDeviceService.updateOne(livableIrrigateDevice)).build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "autoIrrigate:deviceManage:batchDel", desc = "删除设备")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(this.irrigateDeviceService.deleteByIds(idList)).build();
    }

    /**
     * 批量启用/停用
     *
     * @param irrigateDevicePageVo 页面对象
     * @return 统一出参
     */
    @PostMapping("/batchStartAndStop")
    @ApiOperation("批量启用/停用")
    public RestMessage batchStartAndStop(@RequestBody IrrigateDevicePageVo irrigateDevicePageVo) {
        return irrigateDeviceService.batchStartAndStop(irrigateDevicePageVo);
    }

    /**
     * 全开全关
     *
     * @param irrigateDeviceVo 页面对象
     * @return 统一出参
     */
    @PostMapping("/batchOpenClose")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEVICE_CONTROL, menuCode = "autoIrrigate:deviceManage:control", desc = "控制设备")
    public RestMessage batchOpenClose(@RequestBody IrrigateDeviceVo irrigateDeviceVo) {
        return irrigateDeviceService.batchOpenClose(irrigateDeviceVo);
    }

}

