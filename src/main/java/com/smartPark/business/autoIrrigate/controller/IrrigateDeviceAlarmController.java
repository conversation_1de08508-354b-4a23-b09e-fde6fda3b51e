package com.smartPark.business.autoIrrigate.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateDeviceAlarmVo;
import com.smartPark.business.autoIrrigate.service.IrrigateDeviceAlarmService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;


/**
 * 自动灌溉/告警管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/irrigateDeviceAlarm")
public class IrrigateDeviceAlarmController extends ApiController {
    @Resource
    private IrrigateDeviceAlarmService irrigateDeviceAlarmService;

    /**
     * 根据条件，分页(不分页)查询
     *
     * @param requestModel
     * @return 统一出参
     */
    @PostMapping("/list")
    public RestMessage queryListByPage(@RequestBody RequestModel<IrrigateDeviceAlarmVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<IrrigateDeviceAlarmVo> record = irrigateDeviceAlarmService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * 根据id查询单条设备告警详情
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @ApiOperation("根据id查询单条设备告警详情")
    public RestMessage findById(@PathVariable("id") Long id) {
        Assert.notNull(id, "id 不能为空");
        IrrigateDeviceAlarmVo manholeAlarmVo = irrigateDeviceAlarmService.findById(id);
        return RestBuilders.successBuilder().data(manholeAlarmVo).build();
    }
}
