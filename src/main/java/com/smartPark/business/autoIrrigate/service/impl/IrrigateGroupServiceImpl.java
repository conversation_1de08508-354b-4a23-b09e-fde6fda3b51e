package com.smartPark.business.autoIrrigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.autoIrrigate.dto.IrrigateDeviceDTO;
import com.smartPark.business.autoIrrigate.dto.IrrigateDeviceTreeDTO;
import com.smartPark.business.autoIrrigate.dto.IrrigateGroupDTO;
import com.smartPark.business.autoIrrigate.entity.IrrigateDevice;
import com.smartPark.business.autoIrrigate.entity.IrrigateGroup;
import com.smartPark.business.autoIrrigate.entity.IrrigateGroupRef;
import com.smartPark.business.autoIrrigate.mapper.IrrigateDeviceMapper;
import com.smartPark.business.autoIrrigate.mapper.IrrigateGroupMapper;
import com.smartPark.business.autoIrrigate.mapper.IrrigateGroupRefMapper;
import com.smartPark.business.autoIrrigate.service.IrrigateGroupService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.CommonConstant;
import com.smartPark.common.device.mapper.DeviceExtendInfoMapper;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.device.util.DeviceUtils;
import com.smartPark.common.entity.device.Device;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * IrrigateGroup表服务实现类
 *
 * <AUTHOR>
 * @date 2023/04/04
 */
@Slf4j
@Service
public class IrrigateGroupServiceImpl extends ServiceImpl
        <IrrigateGroupMapper, IrrigateGroup> implements IrrigateGroupService {
    @Resource
    private CommonService commonService;

    @Resource
    private IrrigateDeviceMapper irrigateDeviceMapper;

    @Resource
    private IrrigateGroupRefMapper irrigateGroupRefMapper;

    @Resource
    private DeviceExtendInfoMapper deviceExtendInfoMapper;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        StringJoiner sj = new StringJoiner("，");
        StringJoiner coreParamSj = new StringJoiner("-");
        for (Long id : idList) {
            IrrigateGroup oneById = getOneById(id);
            sj.add(oneById.getGroupName());
            removeById(id);
            //还需要级联删除关联表
            QueryWrapper<IrrigateGroupRef> refQw = new QueryWrapper<>();
            refQw.eq("group_id_",id);
            refQw.eq("ref_type_",2);
            irrigateGroupRefMapper.delete(refQw);
        }
        LogHelper.setLogInfo(null, "idList:"+idList, null, null,"删除分组，分组名："+sj);
        return true;
    }


    @Override
    public boolean saveOne(IrrigateGroup livableIrrigateGroup) {
        commonService.setCreateAndModifyInfo(livableIrrigateGroup);

        validParamRequired(livableIrrigateGroup);
        validRepeat(livableIrrigateGroup);
        validParamFormat(livableIrrigateGroup);
        return save(livableIrrigateGroup);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(IrrigateGroup livableIrrigateGroup) {
        Assert.notNull(livableIrrigateGroup.getId(), "id不能为空");
        commonService.setModifyInfo(livableIrrigateGroup);

        validRepeat(livableIrrigateGroup);
        validParamFormat(livableIrrigateGroup);
        return updateById(livableIrrigateGroup);
    }

    @Override
    public IPage<IrrigateGroup> selectPage(Page page, IrrigateGroup livableIrrigateGroup) {
        return baseMapper.selectPage(page, livableIrrigateGroup);
    }

    @Override
    public void export(IrrigateGroup livableIrrigateGroup, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public RestMessage addIrrigateGroup(IrrigateGroupDTO irrigateGroupDTO) {
        StringJoiner sj = new StringJoiner("，");
        StringJoiner coreParamSj = new StringJoiner("-");
        Assert.notNull(irrigateGroupDTO, "参数为空");
        IrrigateGroup irrigateGroup = new IrrigateGroup();
        BeanUtil.copyProperties(irrigateGroupDTO, irrigateGroup, CopyOptions.create().ignoreNullValue());
        validParamRequired(irrigateGroup);
        validRepeat(irrigateGroup);
        commonService.setCreateAndModifyInfo(irrigateGroup);
        irrigateGroup.setDeleted(CommonConstant.NOT_DELETE);
        baseMapper.insert(irrigateGroup);
        //保持关系
        List<IrrigateDevice> irrigateDeviceList = irrigateGroupDTO.getIrrigateDeviceList();
        if (CollectionUtil.isNotEmpty(irrigateDeviceList)) {
            irrigateDeviceList.forEach(item -> {
                IrrigateGroupRef igRef = new IrrigateGroupRef();
                igRef.setGroupId(irrigateGroup.getId());
                igRef.setRefType(2);
                igRef.setRefId(item.getId());
                igRef.setDeleted(CommonConstant.NOT_DELETE);
                commonService.setCreateAndModifyInfo(igRef);
                irrigateGroupRefMapper.insert(igRef);
            });
        }
        sj.add(irrigateGroupDTO.getGroupName());
        LogHelper.setLogInfo(null, irrigateGroupDTO.toString(), null, null,"新增分组，分组名："+sj);
        return RestBuilders.successBuilder().build();
    }

    @Resource
    private DeviceService deviceService;

    @Override
    public List<IrrigateDeviceDTO> groupSelectDevice(IrrigateGroup irrigateGroup) {
        Long groupId = irrigateGroup.getId();
        Map<Long, IrrigateDevice> selMp = new HashMap<>();
        if (groupId != null) {
            List<IrrigateDevice> selLs = irrigateDeviceMapper.groupSelectDevice(groupId);
            if (CollectionUtil.isNotEmpty(selLs)) {
                selLs.forEach(item -> {
                    selMp.put(item.getId(), item);
                });
            }
        }
        //查询全部设备
        QueryWrapper<IrrigateDevice> qw = new QueryWrapper<>();
        qw.eq("deleted_", CommonConstant.NOT_DELETE);
        qw.eq("type_", 2);
        List<IrrigateDevice> ls = irrigateDeviceMapper.selectList(qw);

        //组织出参
        List<IrrigateDeviceDTO> resLs = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ls)) {
            ls.forEach(item -> {
                IrrigateDeviceDTO dto = new IrrigateDeviceDTO();
                BeanUtil.copyProperties(item, dto, CopyOptions.create().ignoreNullValue());
//                //查询设备扩展信息表
//                DeviceExtendInfo deviceExtendInfo = deviceExtendInfoMapper.selectOne(new QueryWrapper<DeviceExtendInfo>().eq("device_id", item.getDeviceId()));
//                dto.setDeviceExtendInfo(deviceExtendInfo);
                DeviceUtils.setDeviceDetail(dto,item.getDeviceCode());
                //查询设备表
                if(null != dto.getDevice()){
                    dto.setDeviceParent(dto.getDevice().getDeviceParent());
                }
                if (selMp.containsKey(item.getId())) {
                    dto.setSelected(true);
                } else {
                    dto.setSelected(false);
                }
                resLs.add(dto);
            });
        }

        return resLs;
    }

    @Override
    public RestMessage detailInfo(Serializable id) {
        IrrigateGroup irrigateGroup = baseMapper.getOneById(id);
        if (irrigateGroup == null) {
            return RestBuilders.errorBuilder().message("数据不存在").build();
        }
        IrrigateGroupDTO dto = new IrrigateGroupDTO();
        BeanUtil.copyProperties(irrigateGroup, dto, CopyOptions.create().ignoreNullValue());

        //设置创建人
        if (dto.getCreatorId() != null) {
            Object creatorNameObj = redisUtil.hget(RedisConstant.USER_PRE, String.valueOf(dto.getCreatorId()));
            if (creatorNameObj != null) {
                if (creatorNameObj instanceof BaseappUser) {
                    BaseappUser baseappUser = (BaseappUser) creatorNameObj;
                    dto.setCreatorName(baseappUser.getNickname());
                }
            }
        }

        return RestBuilders.successBuilder().data(dto).build();
    }

    @Override
    public IPage<IrrigateDeviceDTO> selectGroupDeviceDtoPage(Page page, IrrigateGroup irrigateGroup) {
        IPage<IrrigateDeviceDTO> lsPage = irrigateDeviceMapper.selectGroupDeviceDtoPage(page, irrigateGroup);
        if(CollectionUtil.isNotEmpty(lsPage.getRecords())){
            lsPage.getRecords().forEach(m -> {
                //区域范围
                DeviceExtendInfo deviceExtendInfo = m.getDeviceExtendInfo();
                if(deviceExtendInfo != null){
                    m.getDeviceExtendInfo().setAreaPath(m.getDeviceExtendInfo().getAreaPath().replaceAll("@", "/"));
                }
            });
        }
        return lsPage;
    }

    @Override
    public RestMessage updateIrrigateGroup(IrrigateGroupDTO irrigateGroupDTO) {
        StringJoiner sj = new StringJoiner("，");
        StringJoiner coreParamSj = new StringJoiner("-");
        Assert.notNull(irrigateGroupDTO, "参数为空");
        IrrigateGroup irrigateGroup = new IrrigateGroup();
        BeanUtil.copyProperties(irrigateGroupDTO, irrigateGroup, CopyOptions.create().ignoreNullValue());
        validParamRequired(irrigateGroup);
        validRepeat(irrigateGroup);
        commonService.setModifyInfo(irrigateGroup);
        baseMapper.updateById(irrigateGroup);
        //保持关系,先删除后新增
        QueryWrapper<IrrigateGroupRef> refQw = new QueryWrapper<>();
        refQw.eq("group_id_", irrigateGroup.getId());
        refQw.eq("ref_type_", 2);
        irrigateGroupRefMapper.delete(refQw);

        List<IrrigateDevice> irrigateDeviceList = irrigateGroupDTO.getIrrigateDeviceList();
        if (CollectionUtil.isNotEmpty(irrigateDeviceList)) {
            irrigateDeviceList.forEach(item -> {
                IrrigateGroupRef igRef = new IrrigateGroupRef();
                igRef.setGroupId(irrigateGroup.getId());
                igRef.setRefType(2);
                igRef.setRefId(item.getId());
                igRef.setDeleted(CommonConstant.NOT_DELETE);

                commonService.setCreateAndModifyInfo(igRef);
                irrigateGroupRefMapper.insert(igRef);

            });
        }
        LogHelper.setLogInfo(null, irrigateGroupDTO.toString(), null, null,"修改分组，新组名："+irrigateGroupDTO.getGroupName());
        return RestBuilders.successBuilder().build();
    }

    @Override
    public IPage<IrrigateGroupDTO> selectDtoPage(Page page, IrrigateGroup customQueryParams) {
        IPage<IrrigateGroupDTO> lsPage = baseMapper.selectDtoPage(page, customQueryParams);
        //处理数据
        if(CollectionUtil.isNotEmpty(lsPage.getRecords())){
            lsPage.getRecords().forEach(m -> {
                //设置创建人
                if(m.getCreatorId() != null){
                    Object creatorNameObj = redisUtil.hget(RedisConstant.USER_PRE, String.valueOf(m.getCreatorId()));
                    if(creatorNameObj != null){
                        if(creatorNameObj instanceof BaseappUser){
                            BaseappUser baseappUser = (BaseappUser) creatorNameObj;
                            m.setCreatorName(baseappUser.getNickname());
                        }
                    }
                }
                //设备数量
                IrrigateGroup irrigateGroup = new IrrigateGroup();
                irrigateGroup.setId(m.getId());
                int deviceCount = irrigateDeviceMapper.selectGroupDeviceNum(irrigateGroup);
                m.setDeviceNum(deviceCount);
            });
        }
        return lsPage;
    }

    @Override
    public IrrigateGroup getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(IrrigateGroup livableIrrigateGroup) {
        QueryWrapper<IrrigateGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_name_", livableIrrigateGroup.getGroupName());
        queryWrapper.eq("deleted_", CommonConstant.NOT_DELETE);
        List<IrrigateGroup> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("名称有重复");
        }
        if (ObjectUtils.isEmpty(livableIrrigateGroup.getId())) {
            throw new BusinessException("名称已存在");
        }
        if (!livableIrrigateGroup.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称已存在");
        }

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(IrrigateGroup livableIrrigateGroup) {
        Assert.notNull(livableIrrigateGroup, "参数为空");
        Assert.isTrue(StringUtils.isNotBlank(livableIrrigateGroup.getGroupName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(IrrigateGroup livableIrrigateGroup) {
        //Assert.isTrue(livableIrrigateGroup.getName() == null || livableIrrigateGroup.getName().length() <= 50,
        //        "名称超长");
    }

    @Override
    public List<IrrigateDeviceTreeDTO> groupSelectDeviceTree(IrrigateGroup irrigateGroup) {
        List<IrrigateDeviceDTO> irrigateDeviceDTOS = groupSelectDevice(irrigateGroup);
        Map<String,List<IrrigateDeviceDTO>> collect =  irrigateDeviceDTOS.stream().filter(
            // 过滤流水数为0的
            item-> null != item.getDeviceParent()).collect(Collectors.groupingBy(IrrigateDeviceDTO::getDeviceParent));
        List<IrrigateDeviceTreeDTO> result = collect.entrySet().stream().map(e -> {
            IrrigateDeviceTreeDTO vo = new IrrigateDeviceTreeDTO();
            String code = e.getKey();
            Device device = deviceService.getDeviceByCode(code);
            if (device == null){
                return vo;
            }
            vo.setCode(code);
            vo.setName(device.getName());
            vo.setChildren(e.getValue());
            return vo;
        }).filter(item->item.getCode()!=null).collect(Collectors.toList());
        return result;
    }
}

