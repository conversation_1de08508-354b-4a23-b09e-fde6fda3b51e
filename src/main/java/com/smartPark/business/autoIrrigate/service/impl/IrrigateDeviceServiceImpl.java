package com.smartPark.business.autoIrrigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.autoIrrigate.dto.IrrigateDeviceDTO;
import com.smartPark.business.autoIrrigate.entity.IrrigateControlRecord;
import com.smartPark.business.autoIrrigate.entity.IrrigateDevice;
import com.smartPark.business.autoIrrigate.entity.IrrigateDeviceRef;
import com.smartPark.business.autoIrrigate.entity.IrrigateWaterStatistics;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateDevicePageVo;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateDeviceRefVo;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateDeviceVo;
import com.smartPark.business.autoIrrigate.mapper.IrrigateDeviceMapper;
import com.smartPark.business.autoIrrigate.mapper.IrrigateDeviceRefMapper;
import com.smartPark.business.autoIrrigate.mapper.IrrigateWaterStatisticsMapper;
import com.smartPark.business.autoIrrigate.service.IrrigateControlRecordService;
import com.smartPark.business.autoIrrigate.service.IrrigateDeviceService;
import com.smartPark.business.autoIrrigate.util.IrrigateUtil;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.common.device.mapper.DeviceExtendInfoMapper;
import com.smartPark.common.device.mapper.DeviceMapper;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.device.util.DeviceUtils;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.*;
import com.smartPark.common.monitor.service.MonitorService;
import com.smartPark.common.monitor.vo.MonitorQueryVO;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.utils.EventUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;

/**
 * SolenoidControl表服务实现类
 *
 * <AUTHOR>
 * @date 2023/04/03
 */
@Slf4j
@Service
public class IrrigateDeviceServiceImpl extends ServiceImpl
        <IrrigateDeviceMapper, IrrigateDevice> implements IrrigateDeviceService {
    @Resource
    private CommonService commonService;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ObjInfoMapper objInfoMapper;

    @Resource
    private DeviceExtendInfoMapper deviceExtendInfoMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private DeviceStatusMapper deviceStatusMapper;

    @Resource
    private IrrigateDeviceRefMapper irrigateDeviceRefMapper;

    @Resource
    private IrrigateControlRecordService irrigateControlRecordService;

    @Resource
    private MonitorService monitorService;

    @Resource
    private IrrigateWaterStatisticsMapper irrigateWaterStatisticsMapper;


    /**
     * 或者保存设备关联的实体
     *
     * @return
     */
    private DeviceApplicationModelRef getDeviceApplicationModelRef(Integer type) {
        BaseApplication baseApplication = (BaseApplication) redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);
        DeviceApplicationModelRef device = DeviceApplicationModelRef.getDevice(baseApplication);
        if (type == 1) {
            device.setModelId(DeviceModelConstant.IRRIGATE_SOLENOID_CONTROL);
        }
        if (type == 2) {
            device.setModelId(DeviceModelConstant.IRRIGATE_SOLENOID);
        }
        if (type == 3) {
            device.setModelId(DeviceModelConstant.IRRIGATE_SENSOR);
        }
        return device;
    }

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        cn.hutool.core.lang.Assert.isTrue(CollectionUtil.isNotEmpty(idList), "选择的id不能为空");
        StringJoiner successSj = new StringJoiner("，");
        StringJoiner failSj = new StringJoiner("，");
        for (Long id : idList) {
            IrrigateDevice irrigateDevice = baseMapper.getOneById(id);
            if (irrigateDevice != null) {
                //判断灌溉设备类型,1电磁阀控制器，2电磁阀，3传感器
                //如果是删除传感器,需要判断是否被电磁阀关联
                boolean canDel = true;
                Integer type = irrigateDevice.getType();
                if (type != null){
                    QueryWrapper<IrrigateDeviceRef> refQw = new QueryWrapper<>();
                    refQw.eq("deleted_", 0);
                    switch (type){
                        case 1:
                            refQw.eq("parent_device_code_", irrigateDevice.getDeviceCode());
                            break;
                        case 3:
                            refQw.eq("device_code_", irrigateDevice.getDeviceCode());
                            break;
                        default:
                            break;
                    }
                    int refCount = irrigateDeviceRefMapper.selectCount(refQw);
                    if (refCount > 0){
                        canDel = false;
                    }
                }
                //如果没有父子关联关系,可删除
                if(canDel){
                    DeviceApplicationModelRef damf = getDeviceApplicationModelRef(irrigateDevice.getType());
                    damf.setActionType(EventUtil.DELETE);
                    damf.setDeviceCode(irrigateDevice.getDeviceCode());
                    //业务数据删除
                    removeById(id);
                    //删除关系
                    EventUtil.publishRefEvent(damf);
                    // 成功日志
                    successSj.add(irrigateDevice.getDeviceCode());
                } else {
                    // 失败日志
                    failSj.add(irrigateDevice.getDeviceCode());
                }
            }
        }
        String msg="";
        if(successSj.length()>0) {
            msg+="删除设备，成功设备编码："+successSj;
        }
        if(failSj.length()>0) {
            if (!msg.isEmpty()) {
                msg+="；";
            } else {
                msg+="删除设备，";
            }
            msg+="失败设备编码："+failSj;
        }
        LogHelper.setLogInfo(null, idList.toString(), null, null,msg);
        return true;
    }


    @Override
    public boolean saveOne(IrrigateDevice livableIrrigateDevice) {
        commonService.setCreateAndModifyInfo(livableIrrigateDevice);

        validParamRequired(livableIrrigateDevice);
        validRepeat(livableIrrigateDevice);
        validParamFormat(livableIrrigateDevice);
        return save(livableIrrigateDevice);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(IrrigateDevice livableIrrigateDevice) {
        Assert.notNull(livableIrrigateDevice.getId(), "id不能为空");
        commonService.setModifyInfo(livableIrrigateDevice);

        validRepeat(livableIrrigateDevice);
        validParamFormat(livableIrrigateDevice);
        // 日志
        IrrigateDevice byId = baseMapper.selectById(livableIrrigateDevice.getId());
        LogHelper.setLogInfo(null, livableIrrigateDevice.toString(), null, null,"修改设备，设备编码："+byId.getDeviceCode());
        return updateById(livableIrrigateDevice);
    }

    @Override
    public IPage<IrrigateDevice> selectPage(Page page, IrrigateDevice livableIrrigateDevice) {
        return baseMapper.selectPage(page, livableIrrigateDevice);
    }

    @Override
    public void export(IrrigateDevice livableIrrigateDevice, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public IPage<IrrigateDeviceVo> selectVoPage(Page page, IrrigateDeviceVo solenoidInfoVo) {

        IPage<IrrigateDeviceVo> solenoidInfoIPage = baseMapper.selectVoPage(page, solenoidInfoVo);
        solenoidInfoIPage.getRecords().forEach(m -> {
            //区域范围
            if (StringUtils.isNotBlank(m.getAreaPath())) {
                m.setAreaPath(m.getAreaPath().replace("@", "/"));
            }
            //混合查询的时候，开关状态要自己处理
            //终端类型，1电磁阀控制器，2电磁阀,3传感器
            Integer type = m.getType();
            if (solenoidInfoVo.getType() == null && type != null && type.intValue() == 2){
                IrrigateDeviceDTO dto = new IrrigateDeviceDTO();
                BeanUtil.copyProperties(m, dto,CopyOptions.create().ignoreNullValue());
                setDevicePropertyStatus(dto);
                List<DevicePropertyStatus> devicePropertyStatusList = dto.getDevicePropertyStatusList();
                if(CollectionUtil.isNotEmpty(devicePropertyStatusList)){
                    devicePropertyStatusList.stream().filter(devicePropertyStatus -> devicePropertyStatus.getProp().equals("switch_state")).forEach(devicePropertyStatus -> {
                        m.setSwitchState(devicePropertyStatus.getValue());
                    });
                }
            }
        });
        return solenoidInfoIPage;
    }

    @Override
    public RestMessage batchInsert(IrrigateDevicePageVo irrigateDevicePageVo) {
        List<DeviceExtendInfo> ls = irrigateDevicePageVo.getDeviceExtendInfoList();
        StringJoiner sj = new StringJoiner("，");
        //1电磁阀控制器，2电磁阀,3传感器
        String queryType = irrigateDevicePageVo.getQueryType();
        if (CollectionUtil.isNotEmpty(ls)) {
            ls.forEach(dei -> {
                IrrigateDevice irrigateDevice = initSolenoidInfo(dei, queryType);
                if (irrigateDevice != null) {
                    baseMapper.insert(irrigateDevice);
                    DeviceApplicationModelRef damf = getDeviceApplicationModelRef(irrigateDevice.getType());
                    damf.setDeviceCode(irrigateDevice.getDeviceCode());
                    //增加关系
                    EventUtil.publishRefEvent(damf);
                    sj.add(irrigateDevice.getDeviceCode());
                }
            });
        }
        LogHelper.setLogInfo(null, irrigateDevicePageVo.toString(), null, null, "新增设备，设备编码："+sj);
        return RestBuilders.successBuilder().build();
    }

    @Override
    public RestMessage getOneDtoById(Serializable id) {
        cn.hutool.core.lang.Assert.isTrue(id != null, "id不能为空");
        //出参转换
        IrrigateDeviceDTO dto = new IrrigateDeviceDTO();
        //这里就用单表查询拼对象了
        IrrigateDevice irrigateDevice = baseMapper.getOneById(id);
        if (irrigateDevice != null) {
            BeanUtil.copyProperties(irrigateDevice, dto, CopyOptions.create().ignoreNullValue());
            DeviceUtils.setDeviceDetail(dto,irrigateDevice.getDeviceCode());
//            //查询设备信息
//            QueryWrapper<Device> deviceQw = new QueryWrapper<>();
//            deviceQw.eq("code", irrigateDevice.getDeviceCode());
//            deviceQw.last("limit 1");
//            Device deviceInfo = deviceMapper.selectOne(deviceQw);
//            dto.setDevice(deviceInfo);
            //设置设备属性
            setDevicePropertyStatus(dto);
//
//            //部件信息
//            QueryWrapper<ObjInfo> objQw = new QueryWrapper<>();
//            objQw.eq("obj_id", irrigateDevice.getObjId());
//            objQw.last("limit 1");
//            ObjInfo objInfo = objInfoMapper.selectOne(objQw);
//            dto.setObjInfo(objInfo);
//
//            //设备扩展信息
//            QueryWrapper<DeviceExtendInfo> deviceExtendInfoQw = new QueryWrapper<>();
//            deviceExtendInfoQw.eq("device_id", irrigateDevice.getDeviceCode());
//            deviceExtendInfoQw.eq("obj_id", irrigateDevice.getObjId());
//            deviceExtendInfoQw.last("limit 1");
//            DeviceExtendInfo deviceExtendInfo = deviceExtendInfoMapper.selectOne(deviceExtendInfoQw);
//            dto.setDeviceExtendInfo(deviceExtendInfo);
//            if (deviceExtendInfo != null) {
//                //区域范围
//                if (StringUtils.isNotBlank(dto.getDeviceExtendInfo().getAreaPath())) {
//                    dto.getDeviceExtendInfo().setAreaPath(dto.getDeviceExtendInfo().getAreaPath().replace("@", "/"));
//                }
//            }
//
            //查询物模型josn
            MonitorQueryVO monitorQueryVO = new MonitorQueryVO();
            monitorQueryVO.setDeviceCode(irrigateDevice.getDeviceCode());
            JSONObject physicModel = monitorService.getPhysicModel(monitorQueryVO);
            dto.setPhysicalModel(physicModel);

        }
        return RestBuilders.successBuilder().data(dto).build();
    }

    @Override
    public RestMessage batchStartAndStop(IrrigateDevicePageVo irrigateDevicePageVo) {
        String ids = irrigateDevicePageVo.getIds();
        Integer useStatus = irrigateDevicePageVo.getUseStatus();
        if (StringUtils.isNotBlank(ids)) {
            String[] idArr = ids.split(",");
            for (String id : idArr) {
                IrrigateDevice irrigateDevice = new IrrigateDevice();
                irrigateDevice.setId(Long.valueOf(id));
                irrigateDevice.setUseStatus(useStatus);
                baseMapper.updateById(irrigateDevice);
            }
        }
        return RestBuilders.successBuilder().build();
    }

    @Override
    public IPage<IrrigateDeviceVo> queryListByPage(RequestModel<IrrigateDeviceVo> requestModel) {
        Page page = requestModel.getPage();
        IrrigateDeviceVo irrigateDeviceVo = requestModel.getCustomQueryParams();
        IPage<IrrigateDeviceVo> irrigateIPage = baseMapper.selectVoPage(page, irrigateDeviceVo);
        irrigateIPage.getRecords().forEach(m ->{
            //区域范围
            if (StringUtils.isNotBlank(m.getAreaPath())){
                m.setAreaPath(m.getAreaPath().replace("@","/"));
            }
        });
        return irrigateIPage;
    }

    @Override
    public List<IrrigateDeviceDTO> selectSensorDtoList(IrrigateDeviceRefVo refDeviceVo) {
        List<IrrigateDeviceDTO> irrigateDeviceDTOList = new ArrayList<>();
        //排序要分三段,先显示自己关联的,再显示可选的,最后好要显示被别人关联的
        //自己关联的
        List<IrrigateDeviceDTO> joinDeviceDTOList = baseMapper.selectJoinSensorDtoList(refDeviceVo);

        //可选的
        List<IrrigateDeviceDTO> canSelDeviceDTOList = baseMapper.selectCanSelSensorDtoList(refDeviceVo);

        //被别人关联的
        List<IrrigateDeviceDTO> notCanSelDeviceDTOList = baseMapper.selectNotCanSelSensorDtoList(refDeviceVo);

        //最后组织出参
        if (CollectionUtil.isNotEmpty(joinDeviceDTOList)){
            irrigateDeviceDTOList.addAll(joinDeviceDTOList);
        }
        if (CollectionUtil.isNotEmpty(canSelDeviceDTOList)){
            irrigateDeviceDTOList.addAll(canSelDeviceDTOList);
        }
        if (CollectionUtil.isNotEmpty(notCanSelDeviceDTOList)){
            notCanSelDeviceDTOList.forEach(m ->{
                m.setOtherJoin(true);
            });
            irrigateDeviceDTOList.addAll(notCanSelDeviceDTOList);
        }

        return irrigateDeviceDTOList;
    }

    @Override
    public IPage<IrrigateDeviceDTO> selectSensorDtoPage(Page page, IrrigateDeviceRefVo refVo) {
        IPage<IrrigateDeviceDTO> dtoPage = baseMapper.selectSensorDtoPage(page, refVo);
        dtoPage.getRecords().forEach(m ->{
            //区域范围
            ObjInfo objInfo = m.getObjInfo();
            if (objInfo != null && StringUtils.isNotBlank(objInfo.getAreaPath())){
                objInfo.setAreaPath(objInfo.getAreaPath().replaceAll("@","/"));
            }
            DeviceExtendInfo extendInfo = m.getDeviceExtendInfo();
            if (extendInfo != null && StringUtils.isNotBlank(extendInfo.getAreaPath())){
                extendInfo.setAreaPath(extendInfo.getAreaPath().replaceAll("@","/"));
            }

        });
        return dtoPage;
    }

    @Override
    public RestMessage batchOpenClose(IrrigateDeviceVo irrigateDeviceVo) {
        StringJoiner sj = new StringJoiner("，");
        Long id = irrigateDeviceVo.getId();
        String switchState = irrigateDeviceVo.getSwitchState();
        Assert.notNull(id,"id不能为空");
        Assert.isTrue(StringUtils.isNotBlank(switchState),"开关状态不能为空");
        Integer controlDuration = irrigateDeviceVo.getDuration();

        Date now = new Date();
        Date closeDate = now;
        //1开启，0关闭
        if ("1".equals(switchState)){
            //全开
            Assert.notNull(controlDuration,"时长不能为空");
            closeDate = DateUtil.offsetMinute(now, controlDuration);
        }

        //控制记录
        List<IrrigateControlRecord> recordList = new ArrayList<>();

        //查询设备信息
        IrrigateDevice irrigateDevice = baseMapper.getOneById(id);
        if (irrigateDevice != null){
            sj.add(irrigateDevice.getDeviceCode());
            //查询子设备电磁阀
            List<IrrigateDevice> dcfList = baseMapper.selectChildDcfDeviceList(Arrays.asList(irrigateDevice.getDeviceCode()), 2);
            if (CollectionUtil.isNotEmpty(dcfList)){
                IrrigateControlRecord irrigateControlRecord = IrrigateUtil.initParentDeviceControlRecord(irrigateDeviceVo,now,closeDate);
                commonService.setCreateAndModifyInfo(irrigateControlRecord);

                //电磁阀控制器控制,只用发一次下行指令
                //遍历子设备组织控制记录
                dcfList.forEach(dcf ->{
                    IrrigateControlRecord record = new IrrigateControlRecord();
                    BeanUtil.copyProperties(irrigateControlRecord,record,CopyOptions.create().ignoreNullValue());
                    record.setIrrigateDeviceId(dcf.getId());

                    record.setDeviceCode(dcf.getDeviceCode());
                    recordList.add(record);

                });
                //一次性保存所有的控制记录
                irrigateControlRecordService.saveBatch(recordList);

            }

        }

        //1开启，0关闭
        if ("1".equals(switchState)){
            //一次性定时器，就简单用timer了
            if(controlDuration > 0){
                //下发开指令
                IrrigateControlRecord irrigateControlRecord = IrrigateUtil.initParentDeviceControlRecord(irrigateDeviceVo,now,closeDate);
                irrigateControlRecord.setDeviceCode(irrigateDevice.getDeviceCode());

                boolean sendOk = irrigateControlRecordService.sendControlCommand(irrigateControlRecord);

                new Timer().schedule(new TimerTask() {
                    @Override
                    public void run() {
                        List<IrrigateControlRecord> closeRecordList = new ArrayList<>();
                        Date closeTime = new Date();
                        recordList.forEach(m->{
                            m.setId(null);
                            m.setSwitchState("0");
                            m.setStartTime(closeTime);
                            m.setEndTime(closeTime);
                            commonService.setCreateAndModifyInfo(m);
                            closeRecordList.add(m);
                        });
                        irrigateControlRecordService.saveBatch(closeRecordList);
                        //发送关控制指令
                        commonService.setCreateAndModifyInfo(irrigateControlRecord);
                        irrigateControlRecord.setStartTime(closeTime);
                        irrigateControlRecord.setEndTime(closeTime);
                        irrigateControlRecord.setSwitchState("0");

                        boolean sendOk = irrigateControlRecordService.sendControlCommand(irrigateControlRecord);
                    }
                }, closeDate);
            }
        }else {
            //下发关指令
            IrrigateControlRecord irrigateControlRecord = IrrigateUtil.initParentDeviceControlRecord(irrigateDeviceVo,now,closeDate);
            irrigateControlRecord.setDeviceCode(irrigateDevice.getDeviceCode());

            boolean sendOk = irrigateControlRecordService.sendControlCommand(irrigateControlRecord);

        }
        LogHelper.setLogInfo(null, irrigateDeviceVo.toString(), null, null,("1".equals(switchState)?"全开":"全关")+"，设备："+sj);
        return RestBuilders.successBuilder().build();
    }

    @Override
    public void flowTacticParse(FlowPushData flowData) {
        //取设备编码
        String deviceCode = flowData.getDevice_id();
        //流水时间
        long timestamp = flowData.getTimestamp();
        //查询设备信息
        Device device = deviceMapper.selectById(deviceCode);
        if (device == null){
            log.warn("设备不存在,deviceCode:{}",deviceCode);
            return;
        }
        Object dataObj = flowData.getData();
        if (dataObj == null){
            log.warn("流水数据为空,deviceCode:{}",deviceCode);
            return;
        }
        cn.hutool.json.JSONObject dataJson = JSONUtil.parseObj(dataObj);
        if (!dataJson.containsKey("water_flux")){
            log.warn("流水数据中不包含水流量,deviceCode:{}",deviceCode);
            return;
        }
        //水流量
        Double waterFlux = dataJson.getDouble("water_flux");
        if (waterFlux == null){
            log.warn("流水数据中水流量为空,deviceCode:{}",deviceCode);
            return;
        }
        //最后一条统计数据查出来
        QueryWrapper<IrrigateWaterStatistics> waterQw = new QueryWrapper<>();
        waterQw.eq("device_code_",deviceCode);
        //最近半个月的数据,缩小数据量应该是够了
        Date collDate = DateUtil.offsetMonth(new Date(),-6);
        waterQw.ge("statistics_date_",collDate);

        waterQw.orderByDesc("statistics_date_");
        //数据量大了肯定有性能问题,以后再说吧
        waterQw.last("limit 1");
        IrrigateWaterStatistics lastWater = irrigateWaterStatisticsMapper.selectOne(waterQw);
        //初始化统计数据
        //lastWater为空,则是今天的第一条统计数据
        IrrigateWaterStatistics newWater = initWaterStatistics(deviceCode, timestamp, waterFlux,lastWater);
        if (newWater == null){
            log.warn("初始化统计数据失败,deviceCode:{},上报数据{}异常",deviceCode,JSONUtil.toJsonStr(flowData));
            return;
        }
        irrigateWaterStatisticsMapper.insert(newWater);
    }

    /**
     * 初始化用水量统计数据
     * @param deviceCode 设备编码
     * @param timestamp 流水时间
     * @param waterFlux 水流量
     * @param lastWaterStatistics 最后一条统计数据
     * @return 用水量统计数据
     */
    private IrrigateWaterStatistics initWaterStatistics(String deviceCode, long timestamp, Double waterFlux, IrrigateWaterStatistics lastWaterStatistics) {
        IrrigateWaterStatistics newWater = new IrrigateWaterStatistics();
        newWater.setDeviceCode(deviceCode);
        Date now = DateUtil.date();
        Date collDate = new Date(timestamp);
        newWater.setStatisticsDate(collDate);
        if (lastWaterStatistics == null){
            newWater.setStartTime(DateUtil.beginOfDay(collDate));
            newWater.setDosage(waterFlux);
            newWater.setLastEndDosage(0D);
        }else {
            newWater.setStartTime(lastWaterStatistics.getEndTime());
            if (waterFlux <= 0){
                //上报值是负值,抛弃
                log.warn("设备编号:{},上报值{}是负值,抛弃",deviceCode,waterFlux);
                return null;
            }
            Double dosage = NumberUtil.sub(waterFlux, lastWaterStatistics.getLastEndDosage());
            if (dosage <= 0){
                //上报止码比之前还小,可能反转了,抛弃
                log.warn("设备编号:{},上报止码{}比之前止码{}还小,可能反转了,抛弃",deviceCode,waterFlux,lastWaterStatistics.getDosage());
                return null;
            }
            newWater.setDosage(dosage);
            Double newLastEndDosage = NumberUtil.add(lastWaterStatistics.getLastEndDosage(),lastWaterStatistics.getDosage());
            newWater.setLastEndDosage(newLastEndDosage);
        }
        newWater.setEndTime(new Date(timestamp));
        newWater.setCreatorId(-1L);
        newWater.setCreateTime(now);
        newWater.setModifyTime(now);
        newWater.setModifyId(-1L);
        return newWater;
    }

    /**
     * 设置设备属性
     *
     * @param deviceDTO 设备dto
     */
    private void setDevicePropertyStatus(IrrigateDeviceDTO deviceDTO) {
        //todo 设备属性先写死
        List<DevicePropertyStatus> devicePropertyStatusList = new ArrayList<>();
        /*
        //故障状态
        DevicePropertyStatus devicePropertyStatus1 = new DevicePropertyStatus();
        devicePropertyStatus1.setProp("status");
        devicePropertyStatus1.setPropName("故障状态");
        devicePropertyStatus1.setValue("正常");
        devicePropertyStatus1.setModifyTime(new Timestamp(System.currentTimeMillis()));
        devicePropertyStatusList.add(devicePropertyStatus1);
        Integer type = deviceDTO.getType();
        if (type != null && type == 2) {
            //开关状态
            DevicePropertyStatus devicePropertyStatus2 = new DevicePropertyStatus();
            devicePropertyStatus2.setProp("switch_state");
            //1：开启，0：关闭
            devicePropertyStatus2.setPropName("开关状态");
            devicePropertyStatus2.setValue("1");
            devicePropertyStatus2.setModifyTime(new Timestamp(System.currentTimeMillis()));
            devicePropertyStatusList.add(devicePropertyStatus2);
        }*/
        //20230411改为先只查询base_device_status表，不查询base_device_property_status表（不造物模型字段）
        QueryWrapper<DeviceStatus> deviceStatusQw = new QueryWrapper<>();
        deviceStatusQw.eq("device_code_", deviceDTO.getDeviceCode());
        List<DeviceStatus> deviceStatusList = deviceStatusMapper.selectList(deviceStatusQw);
        if(CollectionUtil.isNotEmpty(deviceStatusList)){
            deviceStatusList.forEach(m ->{
                DevicePropertyStatus devicePropertyStatus = new DevicePropertyStatus();
                devicePropertyStatus.setProp(m.getProp());
                if("switch_state".equals(m.getProp())) {
                    devicePropertyStatus.setPropName("开关状态");
                }
                if("fault_status".equals(m.getProp())) {
                    devicePropertyStatus.setPropName("故障状态");
                }
                devicePropertyStatus.setValue(m.getValue());
                devicePropertyStatus.setModifyTime(m.getModifyTime());
                devicePropertyStatus.setDeviceCode(m.getDeviceCode());
                devicePropertyStatusList.add(devicePropertyStatus);
            });
        }

        deviceDTO.setDevicePropertyStatusList(devicePropertyStatusList);
    }

    /**
     * 初始化电磁阀信息
     *
     * @param dei       设备扩展信息
     * @param queryType 设备类型 1电磁阀控制器，2电磁阀
     * @return 电磁阀设备信息
     */
    private IrrigateDevice initSolenoidInfo(DeviceExtendInfo dei, String queryType) {
        IrrigateDevice irrigateDevice = new IrrigateDevice();
        irrigateDevice.setDeviceCode(dei.getDeviceId());
        //1电磁阀控制器，2电磁阀,3传感器
        irrigateDevice.setType(Integer.parseInt(queryType));
        irrigateDevice.setDeviceId(dei.getDeviceId());
        irrigateDevice.setObjId(dei.getObjId());

        //验证是否存在
        QueryWrapper<IrrigateDevice> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deleted_", 0);
        queryWrapper.eq("device_code_", dei.getDeviceId());
        int count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            log.warn("自动灌溉--设备已存在");
            return null;
        }
        //设置基础信息
        commonService.setCreateAndModifyInfo(irrigateDevice);
        return irrigateDevice;
    }

    @Override
    public IrrigateDevice getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(IrrigateDevice livableIrrigateDevice) {
        /* QueryWrapper<SolenoidControl> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", livableSolenoidControl.getName());
        queryWrapper.eq("tenant_id", linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<SolenoidControl> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("名称有重复");
        }
        if (ObjectUtils.isEmpty(livableSolenoidControl.getId())) {
            throw new BusinessException("名称已存在");
        }
        if (!livableSolenoidControl.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称已存在");
        }
                    */

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(IrrigateDevice livableIrrigateDevice) {
        //Assert.notNull(livableSolenoidControl, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(livableSolenoidControl.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(IrrigateDevice livableIrrigateDevice) {
        //Assert.isTrue(livableSolenoidControl.getName() == null || livableSolenoidControl.getName().length() <= 50,
        //        "名称超长");
    }
}

