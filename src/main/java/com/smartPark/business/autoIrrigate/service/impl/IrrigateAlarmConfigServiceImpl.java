package com.smartPark.business.autoIrrigate.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.autoIrrigate.entity.*;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateAlarmConfigVo;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateAlarmRuleVo;
import com.smartPark.business.autoIrrigate.mapper.*;
import com.smartPark.business.autoIrrigate.service.IrrigateAlarmConfigService;
import com.smartPark.business.autoIrrigate.service.IrrigateAlarmNumberService;
import com.smartPark.common.alarm.entity.Alarm;
import com.smartPark.common.alarm.service.AlarmService;
import com.smartPark.common.rpc.RpcEnum;
import com.smartPark.common.security.context.BaseUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * IrrigateAlarmConfig表服务实现类
 *
 * <AUTHOR>
 * @date 2023/04/04
 */
@Slf4j
@Service
public class IrrigateAlarmConfigServiceImpl extends ServiceImpl
        <IrrigateAlarmConfigMapper, IrrigateAlarmConfig> implements IrrigateAlarmConfigService {
    @Autowired
    private BaseUserContextProducer baseUserContextProducer;
    @Autowired
    private IrrigateAlarmRuleMapper irrigateAlarmRuleMapper;
    @Autowired
    private IrrigateAlarmDeviceMapper irrigateAlarmDeviceMapper;
    @Autowired
    private AlarmService alarmService;
    @Autowired
    private IrrigateDeviceMapper irrigateDeviceMapper;
    @Autowired
    private IrrigateAlarmNumberService alarmNumberService;
    @Autowired
    private IrrigateWaterStatisticsMapper waterStatisticsMapper;


    /**
     * 电磁阀控制失败事件
     */
    private final String  controlFailEventCode = "090101";

    /**
     * 获取告警设置
     * @return 单条数据
     */
    @Override
    public IrrigateAlarmConfigVo detailInfo() {
        List<IrrigateAlarmConfig> alarmConfigs = baseMapper.selectList(new QueryWrapper<>());
        if (CollectionUtil.isNotEmpty(alarmConfigs)) {
            IrrigateAlarmConfig alarmConfig = alarmConfigs.get(0);
            IrrigateAlarmConfigVo alarmConfigVo = BeanUtil.toBean(alarmConfig, IrrigateAlarmConfigVo.class);
            //查询事件
            List<IrrigateAlarmRule> alarmRules = irrigateAlarmRuleMapper.selectList(new LambdaQueryWrapper<IrrigateAlarmRule>()
                    .eq(IrrigateAlarmRule::getAlarmConfigId, alarmConfig.getId())
                    .orderByAsc(IrrigateAlarmRule::getCreateTime));
            if (CollectionUtil.isNotEmpty(alarmRules)){
                List<IrrigateAlarmRuleVo> alarmRuleVos = BeanUtil.copyToList(alarmRules, IrrigateAlarmRuleVo.class);
                alarmRuleVos.forEach(rule -> {
                    if (Integer.valueOf(1).equals(rule.getDeviceScope())){
                        //查询关联设备
                        List<IrrigateAlarmDevice> alarmDevices = irrigateAlarmDeviceMapper.selectList(new LambdaQueryWrapper<IrrigateAlarmDevice>()
                                .eq(IrrigateAlarmDevice::getAlarmRuleId, rule.getId())
                                .orderByAsc(IrrigateAlarmDevice::getCreateTime));
                        rule.setIrrigateAlarmDeviceList(alarmDevices);
                    }
                });
                alarmConfigVo.setIrrigateAlarmRules(alarmRuleVos);
            }
            return alarmConfigVo;
        }
        return null;
    }

    /**
     * 新增
     * @return 新增结果
     */
    @Override
    public void add(IrrigateAlarmConfigVo irrigateAlarmConfigVo) {
        saveOrUpdate(irrigateAlarmConfigVo);
    }

    /**
     * 修改
     */
    @Override
    public void updateOne(IrrigateAlarmConfigVo irrigateAlarmConfigVo) {
        saveOrUpdate(irrigateAlarmConfigVo);
    }

    /**
     * 触发--电磁阀控制失败
     */
    @Override
    public void triggerByControlFail(String deviceCode){
        List<IrrigateAlarmConfig> alarmConfigs = baseMapper.selectList(new QueryWrapper<>());
        if (CollectionUtil.isNotEmpty(alarmConfigs)) {
            IrrigateAlarmConfig alarmConfig = alarmConfigs.get(0);
            LambdaQueryWrapper<IrrigateAlarmRule> ruleLambdaQueryWrapper = Wrappers.<IrrigateAlarmRule>lambdaQuery();
            ruleLambdaQueryWrapper.eq(IrrigateAlarmRule::getEventCode, controlFailEventCode)
                    .eq(IrrigateAlarmRule::getAlarmConfigId, alarmConfig.getId());
            //查询设备对应的规则
            List<Long> ruleIds = irrigateAlarmDeviceMapper.selectList(Wrappers.<IrrigateAlarmDevice>lambdaQuery()
                    .eq(IrrigateAlarmDevice::getDeviceCode, deviceCode)
            ).stream().map(IrrigateAlarmDevice::getAlarmRuleId).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(ruleIds)){
                ruleLambdaQueryWrapper.and(w ->w.eq(IrrigateAlarmRule::getDeviceScope, 0)
                        .or()
                        .in(IrrigateAlarmRule::getId,ruleIds));
            }else {
                ruleLambdaQueryWrapper.eq(IrrigateAlarmRule::getDeviceScope, 0);
            }
            //查询对应的规则
            ruleLambdaQueryWrapper.orderByDesc(IrrigateAlarmRule::getAlarmLevel);
            List<IrrigateAlarmRule> alarmRules = irrigateAlarmRuleMapper.selectList(ruleLambdaQueryWrapper);
            //再查询类型为全部的规则
            if (CollectionUtil.isNotEmpty(alarmRules)){
                IrrigateAlarmRule alarmRule = alarmRules.get(0);
                //保存告警
                Alarm alarm = addAlarm(alarmConfig, alarmRule, deviceCode, "电磁阀控制失败");
                if (null != alarm){
                    //保存告警
                    saveAlarm(ListUtil.toList(alarm));
                }
            }
        }
    }

    /**
     * 触发--浇水量
     * @return 单条数据
     */
    @Override
    public void triggerByRuleId(List<Long> ruleIds,Date date) {
        List<Alarm> result = new ArrayList<>();
        ruleIds.forEach(ruleId -> {
            //触发告警
            IrrigateAlarmRule irrigateAlarmRule = irrigateAlarmRuleMapper.selectById(ruleId);
            //如果是电磁阀控制失败事件那么不用处理，直接返回
            if (null != irrigateAlarmRule && !controlFailEventCode.equals(irrigateAlarmRule.getEventCode())) {
                List<String> deviceCodes = new ArrayList<>();
                if (Integer.valueOf(1).equals(irrigateAlarmRule.getDeviceScope())){
                    //查询对应的设备
                    List<IrrigateAlarmDevice> alarmDevices = irrigateAlarmDeviceMapper.selectList(new LambdaQueryWrapper<IrrigateAlarmDevice>()
                            .eq(IrrigateAlarmDevice::getAlarmRuleId, ruleId)
                            .orderByAsc(IrrigateAlarmDevice::getCreateTime));
                    if (CollectionUtil.isNotEmpty(alarmDevices)) {
                        deviceCodes = alarmDevices.stream().map(IrrigateAlarmDevice::getDeviceCode).collect(Collectors.toList());
                    }
                }
                //浇水量只有传感器有
                LambdaQueryWrapper<IrrigateDevice> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(IrrigateDevice::getType,3);
                if (CollectionUtil.isNotEmpty(deviceCodes)){
                    queryWrapper.in(IrrigateDevice::getDeviceCode, deviceCodes);
                }
                //查询全部设备
                List<IrrigateDevice> irrigateDevices = irrigateDeviceMapper.selectList(queryWrapper);
                if (CollectionUtil.isEmpty(irrigateDevices)){
                    return;
                }
                //获取规则
                //时间范围(1每日，2每周，3每月)
                Integer timeRange = irrigateAlarmRule.getTimeRange();
                Date starTime;
                Date endTime;
                String timeStr = "";
                if (Integer.valueOf(1).equals(timeRange)){
                    //当天的开始时间和结束时间
                    starTime = DateUtil.beginOfDay(date);
                    endTime = DateUtil.endOfDay(date);
                    timeStr = "日";
                }else if (Integer.valueOf(2).equals(timeRange)){
                    //每周的开始时间和结束时间
                    starTime = DateUtil.beginOfWeek(date);
                    endTime = DateUtil.endOfWeek(date);
                    timeStr = "周";
                }else {
                    //每月的开始时间和结束时间
                    starTime = DateUtil.beginOfMonth(date);
                    endTime = DateUtil.endOfMonth(date);
                    timeStr = "月";
                }
                BigDecimal max = irrigateAlarmRule.getMax();
                BigDecimal min = irrigateAlarmRule.getMin();
                //查询对应的浇水量数据
                List<IrrigateWaterStatistics> statistics = waterStatisticsMapper.selectList(Wrappers.<IrrigateWaterStatistics>lambdaQuery()
                        .in(IrrigateWaterStatistics::getDeviceCode, irrigateDevices.stream().map(IrrigateDevice::getDeviceCode).collect(Collectors.toList()))
                        .ge(IrrigateWaterStatistics::getStatisticsDate, starTime)
                        .le(IrrigateWaterStatistics::getStatisticsDate, endTime));
                if (CollectionUtil.isNotEmpty(statistics)){
                    //查询对应的规则
                    IrrigateAlarmConfig alarmConfig = baseMapper.selectById(irrigateAlarmRule.getAlarmConfigId());

                    //按设备分组
                    Map<String, List<IrrigateWaterStatistics>> statisticsMap = statistics.stream().collect(Collectors.groupingBy(IrrigateWaterStatistics::getDeviceCode));
                    String finalTimeStr = timeStr;
                    statisticsMap.forEach((k, v)->{
                        Double totalDosage = v.stream().map(IrrigateWaterStatistics::getDosage).reduce(0.0, Double::sum);
                        if (new BigDecimal(totalDosage).compareTo(max) > 0 || new BigDecimal(totalDosage).compareTo(min) < 0){
                            //触发告警
                            //告警内容
                            String content = finalTimeStr +"浇水量："+totalDosage + "吨";
                            Alarm alarm = addAlarm(alarmConfig, irrigateAlarmRule, k, content);
                            result.add(alarm);
                        }
                    });
                }
            }
        });
        //保存告警
        saveAlarm(result);
    }

    /**
     * 定时任务触发告警
     */
    @Override
    public void triggerAlarm() {
        //判断当前是否为1点。不是一点不执行
        Date date = new Date();
        if (DateUtil.hour(date,true) != 1){
            return;
        }
        //查询浇水量的规则
        List<IrrigateAlarmConfig> alarmConfigs = baseMapper.selectList(new QueryWrapper<>());
        if (CollectionUtil.isEmpty(alarmConfigs)){
            return;
        }
        IrrigateAlarmConfig alarmConfig = alarmConfigs.get(0);
        List<Long> ruleIds = irrigateAlarmRuleMapper.selectList(Wrappers.<IrrigateAlarmRule>lambdaQuery()
                        .eq(IrrigateAlarmRule::getAlarmConfigId, alarmConfig.getId())).stream()
                .map(IrrigateAlarmRule::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ruleIds)){
            return;
        }
        //触发昨天的
        triggerByRuleId(ruleIds,DateUtil.offsetDay(date,-1));
    }

    /**
     * 保存告警
     * @return 单条数据
     */
    private void saveAlarm(List<Alarm> alarms) {
        //删除null 的数据
        alarms.removeIf(Objects::isNull);
        if (CollectionUtil.isEmpty(alarms)){
            return;
        }
        // 上报最高等级告警
        List<Alarm> result = new ArrayList<>();
        //按设备分类
        Map<String, List<Alarm>> deviceMap = alarms.stream().collect(Collectors.groupingBy(Alarm::getDeviceCode));
        deviceMap.forEach((k,v)->{
            // 按照事件分类
            Map<String, List<Alarm>> map = v.stream().collect(Collectors.groupingBy(Alarm::getAlarmType));
            // 循环map
            for (Map.Entry<String, List<Alarm>> entry : map.entrySet()) {
                // 按照告警等级排序
                List<Alarm> list = entry.getValue().stream().sorted(Comparator.comparing(Alarm::getLevel)).collect(Collectors.toList());
                // 取最高等级的告警
                result.add(list.get(0));
            }
        });
        if (CollectionUtil.isNotEmpty(result)){
            /**
             * 修改推送状态为已推送
             */
            result.forEach(a->a.setPushStatus(1));
            alarmService.saveBatch(result);
            //保存成功后设置设备告警次数
            List<IrrigateAlarmNumber> alarmNumberList = result.stream().map(r ->(IrrigateAlarmNumber)r.getExtJson()).collect(Collectors.toList());
            alarmNumberService.saveOrUpdateBatch(alarmNumberList);
            //告警推送到kafka
            RpcEnum.BASE.postForObject("/baseOpenApi/alarm2Operation/alarm2Kafka", result, JSONObject.class);
        }
    }

    /**
     * 添加告警
     */
    private Alarm addAlarm(IrrigateAlarmConfig alarmConfig, IrrigateAlarmRule irrigateAlarmRule,String deviceCode,String content){
        Date date = new Date();
        //判断沉默周期和连续触发阈值是否满足
        IrrigateAlarmNumber alarmNumber = alarmNumberService.findOne(irrigateAlarmRule.getId(),deviceCode);
        if (null != alarmNumber){
            //判断是否在沉默周期
            if (null != alarmNumber.getEffectTime() && date.compareTo(alarmNumber.getEffectTime()) < 0){
                //在沉默周期不产生告警 并重置
                alarmNumber.setNum(0);
                alarmNumberService.updateById(alarmNumber);
                return null;
            }
        }
        //判断是否触发阈值
        Integer threshold = (null == alarmNumber || null == alarmNumber.getNum())?1:alarmNumber.getNum()+1;
        Integer ruleThreshold = null == alarmConfig.getThreshold()?1:alarmConfig.getThreshold();
        if (null == alarmNumber){
            alarmNumber = new IrrigateAlarmNumber();
            alarmNumber.setDeviceCode(deviceCode);
            alarmNumber.setAlarmRuleId(irrigateAlarmRule.getId());
            alarmNumber.setNum(threshold);
        }else {
            alarmNumber.setNum(threshold);
        }
        if (threshold < ruleThreshold){
            alarmNumberService.saveOrUpdate(alarmNumber);
            return null;
        }

        Alarm alarm = new Alarm();
        alarm.setCode(alarmService.generateCode());
        alarm.setModel("4");
        alarm.setDeviceCode(deviceCode);
        alarm.setAlarmType(irrigateAlarmRule.getEventCode());// 告警事件
        alarm.setLevel(irrigateAlarmRule.getAlarmLevel());// 告警等级 1-4
        alarm.setAlarmTime(new Date());
        alarm.setContent(content);
        alarm.setCreateTime(new Date());
        alarm.setModifyTime(new Date());
        alarm.setStatus(1);
        alarm.setPushStatus(1);
        alarm.setCancel(1);

        //设置沉默周期
        Integer stopCycle = alarmConfig.getStopCycle();
        if (null != stopCycle){
            alarmNumber.setEffectTime(DateUtil.offset(date, DateField.HOUR, stopCycle));
            alarmNumber.setNum(0);
        }
        alarm.setExtJson(alarmNumber);
        return alarm;
    }



    /**
     * 新增
     * @return 新增结果
     */
    private void saveOrUpdate(IrrigateAlarmConfigVo irrigateAlarmConfigVo) {
        //保存告警设置
        setBase(irrigateAlarmConfigVo);
        IrrigateAlarmConfig alarmConfig = BeanUtil.toBean(irrigateAlarmConfigVo, IrrigateAlarmConfig.class);
        if (null == alarmConfig.getId()){
            baseMapper.insert(alarmConfig);
        }else{
            baseMapper.updateById(alarmConfig);
        }
        //删除告警规则
        irrigateAlarmRuleMapper.delete(new QueryWrapper<>());
        //删除对应的关联设备
        irrigateAlarmDeviceMapper.delete(new QueryWrapper<>());
        //保存告警规则
        List<IrrigateAlarmRuleVo> irrigateAlarmRuleVos = irrigateAlarmConfigVo.getIrrigateAlarmRules();
        if (CollectionUtil.isNotEmpty(irrigateAlarmRuleVos)) {
            irrigateAlarmRuleVos.forEach(rule -> {
                rule.setAlarmConfigId(alarmConfig.getId());
                IrrigateAlarmRule alarmRule = BeanUtil.toBean(rule, IrrigateAlarmRule.class);
                irrigateAlarmRuleMapper.insert(alarmRule);
                if (Integer.valueOf(1).equals(rule.getDeviceScope())) {
                    //保存关联设备
                    List<IrrigateAlarmDevice> irrigateAlarmDevices = rule.getIrrigateAlarmDeviceList();
                    if (CollectionUtil.isNotEmpty(irrigateAlarmDevices)) {
                        irrigateAlarmDevices.forEach(device -> {
                            device.setAlarmRuleId(alarmRule.getId());
                            irrigateAlarmDeviceMapper.insert(device);
                        });
                    }
                }
            });
        }
        //清除设备的告警周期
        alarmService.remove(Wrappers.lambdaQuery());
    }

    /**
     * 设置基本属性
     * @param alarmConfigVo
     */
    private void setBase(IrrigateAlarmConfigVo alarmConfigVo) {
        Long userId = null;
        if(null != baseUserContextProducer.getCurrent()){
            userId = baseUserContextProducer.getCurrent().getId();
        }
        alarmConfigVo.setModifyTime(new Date());
        alarmConfigVo.setModifyId(userId);
        //没有id就是新增,有就是编辑
        if (null == alarmConfigVo.getId()){
            alarmConfigVo.setCreatorId(userId);
            alarmConfigVo.setCreateTime(new Date());
        }
        //保存告警规则
        List<IrrigateAlarmRuleVo> alarmRules = alarmConfigVo.getIrrigateAlarmRules();
        if (CollectionUtil.isNotEmpty(alarmRules)) {
            alarmRules.forEach(rule -> {
                rule.setCreatorId(alarmConfigVo.getCreatorId());
                rule.setCreateTime(alarmConfigVo.getCreateTime());
                rule.setModifyId(alarmConfigVo.getModifyId());
                rule.setModifyTime(alarmConfigVo.getModifyTime());
                //保存关联设备
                List<IrrigateAlarmDevice> irrigateAlarmDevices = rule.getIrrigateAlarmDeviceList();
                if (CollectionUtil.isNotEmpty(irrigateAlarmDevices)) {
                    irrigateAlarmDevices.forEach(device -> {
                        device.setCreatorId(alarmConfigVo.getCreatorId());
                        device.setCreateTime(alarmConfigVo.getCreateTime());
                        device.setModifyId(alarmConfigVo.getModifyId());
                        device.setModifyTime(alarmConfigVo.getModifyTime());
                    });
                }
            });
        }
    }

}

