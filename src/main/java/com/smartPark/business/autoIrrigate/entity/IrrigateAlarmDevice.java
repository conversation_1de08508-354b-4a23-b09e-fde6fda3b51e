package com.smartPark.business.autoIrrigate.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 自动灌溉告警管理-告警设置-设备表
 * </p>
 *
 * <AUTHOR>
 * @since [日期]
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_irrigate_alarm_device")
public class IrrigateAlarmDevice extends Model<IrrigateAlarmDevice> {

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 规则id
     */
    @TableField("alarm_rule_id_")
    private Long alarmRuleId;

    /**
     * 设备编号
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，1删除，0存在
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
