package com.smartPark.business.autoIrrigate.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 自动灌溉计划参数
 *
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_irrigate_plan_param")
public class IrrigatePlanParam extends Model<IrrigatePlanParam> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 计划id
     */
    @TableField("plan_id_")
    private Long planId;

    /**
     * 开始时间
     */
    @TableField("start_time_")
    private String startTime;

    /**
     * 灌溉时长，分钟
     */
    @TableField("irrigate_duration_")
    private Integer irrigateDuration;

    /**
     * 浇水量
     */
    @TableField("irrigate_water_")
    private Double irrigateWater;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，0否，1是
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;



}
