package com.smartPark.business.autoIrrigate.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.smartPark.business.autoIrrigate.entity.IrrigateControlRecord;
import com.smartPark.business.autoIrrigate.entity.IrrigatePlan;
import com.smartPark.business.autoIrrigate.entity.IrrigatePlanParam;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateAnalyzeVo;
import com.smartPark.business.autoIrrigate.entity.vo.IrrigateDeviceVo;
import com.smartPark.business.greenLand.entity.vo.GreenLandCountParamVo;
import com.smartPark.common.cronHelp.JobGroupConstant;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.job.JobEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class IrrigateUtil {

    /**
     * 请求任务opneapi接口
     *
     * @param baseApplication 应用信息
     * @param jie             接口信息
     * @param mge             模板分组信息
     * @param jobEntity       任务对象
     */
    public static void requestTaskApi(BaseApplication baseApplication, JobGroupConstant.JobInterfaceEnum jie, JobGroupConstant.ModelGroupNameEnum mge, HashMap<String, Object> paramMap, JobEntity jobEntity) {
        String url = baseApplication.getApiUrl() + jie.getUrl();
        //String url = "http://localhost:5042" + jie.getUrl();
        String type = jie.getType();
       /* String jobName = jobEntity.getJobName();
        String jobGroup = jobEntity.getJobGroup();
        if(paramMap == null){
            paramMap = new HashMap<>();
        }
        paramMap.put("jobName", jobName);
        paramMap.put("jobGroup", jobGroup);*/
        String body = JSONUtil.toJsonStr(jobEntity);
        String resStr = null;
        switch (type) {
            case "POST":
                //body = HttpUtil.post(url, paramMap);
                resStr = HttpUtil.post(url, body);
                break;
            case "GET":
                resStr = HttpUtil.get(url, paramMap);
                break;
            default:
                break;
        }
        log.info("--applicationId:[{}]--modelId:[{}]---业务数据任务对象:[{}]---请求任务接口url:[{}]---返回结果：{}", mge.getApplicationId(), mge.getModelId(), JSONUtil.toJsonStr(jobEntity), url, resStr);
    }

    /**
     * 初始化任务
     *
     * @param irrigatePlan          灌溉计划
     * @param irrigatePlanParamList 灌溉计划参数
     * @param baseApplication       应用信息
     * @param jie                   接口信息
     * @param mge                   模块信息
     * @return 任务对象
     */
    public static JobEntity initTaskJobEntity(IrrigatePlan irrigatePlan, List<IrrigatePlanParam> irrigatePlanParamList, BaseApplication baseApplication, JobGroupConstant.JobInterfaceEnum jie, JobGroupConstant.ModelGroupNameEnum mge) {
        String jobName = mge.getApplicationId() + "_" + mge.getModelId() + "_" + mge.getJobName() + "_" + irrigatePlan.getId();
        String jobGroup = mge.getApplicationId() + "_" + mge.getModelId() + "_" + mge.getJobGroup() + "_" + irrigatePlan.getId();
        JobEntity jobEntity = new JobEntity();
        jobEntity.setJobName(jobName);
        jobEntity.setJobGroup(jobGroup);
        jobEntity.setApplicationId(mge.getApplicationId());
        //任务调度类型，0：方法，1：直接调用http接口
        jobEntity.setJobTaskType(1);
        //任务类型，0：周期性，1：一次性
        jobEntity.setJobType(0);
        //是否立即运行，0：否，1：是
        //jobEntity.setWantNowRun(true);

        jobEntity.setFunctionPath("/openapi/task/callback/taskIrrigateControl");

        //参数param
        //Integer controlType = paramJson.getInt("controlType");
        //String switchState = paramJson.getStr("switchState");
        JSONObject json = JSONUtil.createObj();
        json.put("id", irrigatePlan.getId());
        //json.put("jobType", "start");
        //开关状态
        json.put("controlType", "1");
        //默认开启
        json.put("switchState", "1");
        jobEntity.setParams(JSONUtil.toJsonStr(json));
        return jobEntity;

    }

    /**
     * 初始化cron表达式
     *
     * @param plan          灌溉计划
     * @param planParamList 灌溉计划参数
     * @return cron表达式
     */
    private static String initCronExpression(IrrigatePlan plan, List<IrrigatePlanParam> planParamList) {
        //我们是用的Quartz，就手拼算了
        //0 0 18 * * ?
        //Java(Quartz)
        //    *    *    *    *    *    *    *
        //    -    -    -    -    -    -    -
        //    |    |    |    |    |    |    |
        //    |    |    |    |    |    |    + year [optional]
        //    |    |    |    |    |    +----- day of week (1 - 7) sun,mon,tue,wed,thu,fri,sat
        //    |    |    |    |    +---------- month (1 - 12) OR jan,feb,mar,apr ...
        //    |    |    |    +--------------- day of month (1 - 31)
        //    |    |    +-------------------- hour (0 - 23)
        //    |    +------------------------- min (0 - 59)
        //    +------------------------------ second (0 - 59)
        String cronExpression = null;
        Integer jobType = 1;
        Integer[] dayOfMonths = null;
        Integer timeType = plan.getTimeType();
        String timeValues = plan.getTimeValues();
        StringJoiner monthsSj = new StringJoiner(",");
        StringJoiner daysSj = new StringJoiner(",");
        StringJoiner weekSj = new StringJoiner(",");
        StringJoiner hoursSj = new StringJoiner(",");
        StringJoiner minutesSj = new StringJoiner(",");
        switch (timeType) {
            case 1:
                //季节
                jobType = 1;
                //1春季,2夏季,3秋季,4冬季
                String[] valStrArr = timeValues.split(",");
                if (ArrayUtils.isNotEmpty(valStrArr)) {
                    for (int i = 0; i < valStrArr.length; i++) {
                        String valStr = valStrArr[i];
                        switch (valStr) {
                            case "1":
                                //春季
                                monthsSj.add("3,4,5");
                                break;
                            case "2":
                                //夏季
                                monthsSj.add("6,7,8");
                                break;
                            case "3":
                                //秋季
                                monthsSj.add("9,10,11");
                                break;
                            case "4":
                                //冬季
                                monthsSj.add("12,1,2");
                                break;
                            default:

                                break;
                        }
                    }
                }
                break;
            case 2:
                //时间区间-月
                jobType = 2;
                daysSj.add(timeValues);
                break;
            case 3:
                //时间区间-周
                jobType = 3;
                weekSj.add(timeValues);
                break;

        }

        if (CollectionUtil.isNotEmpty(planParamList)) {
            for (IrrigatePlanParam planParam : planParamList) {
                String startTime = planParam.getStartTime();
                if (StringUtils.isNotBlank(startTime) && startTime.indexOf(":") > 0) {
                    String[] split = startTime.split(":");
                    hoursSj.add(split[0]);
                    minutesSj.add(split[1]);
                    break;
                }
            }
        }
        String second = "0";
        String minute = minutesSj.toString();
        String hour = hoursSj.toString();
        String dayOfMonth = daysSj.toString();
        if (StringUtils.isBlank(dayOfMonth)) {
            dayOfMonth = "*";
        }
        String month = monthsSj.toString();
        if (StringUtils.isBlank(month)) {
            month = "*";
        }
        String dayOfWeek = weekSj.toString();
        if (StringUtils.isBlank(dayOfWeek)) {
            dayOfWeek = "*";
        }else{
            String[] weekArr = dayOfWeek.split(",");
            //前端传1-7：周一 周日
            //Monday Mon 周一 Tuesday Tue 周二 Wednesday Wed 周三 Thursday Thu 周四 Friday Fri 周五 Saturday Sat 周六 Sunday Sun 周日
            if (ArrayUtils.isNotEmpty(weekArr)) {
                for (int i = 0; i < weekArr.length; i++) {
                    String week = weekArr[i];
                    switch (week) {
                        case "1":
                            weekArr[i] = "mon";
                            break;
                        case "2":
                            weekArr[i] = "tue";
                            break;
                        case "3":
                            weekArr[i] = "wed";
                            break;
                        case "4":
                            weekArr[i] = "thu";
                            break;
                        case "5":
                            weekArr[i] = "fri";
                            break;
                        case "6":
                            weekArr[i] = "sat";
                            break;
                        case "7":
                            weekArr[i] = "sun";
                            break;
                        default:
                            break;
                    }
                }
                dayOfWeek = String.join(",", weekArr);
            }
        }
        String year = "?";
        if (timeType == 3) {
            cronExpression = second + " " + minute + " " + hour + " " + "?" + " " + month + " " + dayOfWeek;
        } else {
            cronExpression = second + " " + minute + " " + hour + " " + dayOfMonth + " " + month + " " + year;
        }

        return cronExpression;
    }

    /**
     * 初始化删除任务
     *
     * @param irrigatePlan          灌溉计划
     * @param irrigatePlanParamList 灌溉计划参数list
     * @param baseApplication       应用
     * @param jie                   接口枚举
     * @param mge                   模块枚举
     */
    public static void initAndDelJob(IrrigatePlan irrigatePlan, List<IrrigatePlanParam> irrigatePlanParamList, BaseApplication baseApplication, JobGroupConstant.JobInterfaceEnum jie, JobGroupConstant.ModelGroupNameEnum mge) {
        JobEntity baseJobEntity = initTaskJobEntity(irrigatePlan, irrigatePlanParamList, baseApplication, jie, mge);
        //删除任务通过分组群删
        baseJobEntity.setJobName(null);
        IrrigateUtil.requestTaskApi(baseApplication, jie, mge, null, baseJobEntity);

    }

    /**
     * 初始化新建任务
     *
     * @param irrigatePlan          灌溉计划
     * @param irrigatePlanParamList 灌溉计划参数list
     * @param baseApplication       应用
     * @param jie                   接口枚举
     * @param mge                   模块枚举
     **/
    public static List<JobEntity> initAndAddJob(IrrigatePlan irrigatePlan, List<IrrigatePlanParam> irrigatePlanParamList, BaseApplication baseApplication, JobGroupConstant.JobInterfaceEnum jie, JobGroupConstant.ModelGroupNameEnum mge) {
        List<JobEntity> jobLs = new ArrayList<>();
        JobEntity baseJobEntity = initTaskJobEntity(irrigatePlan, irrigatePlanParamList, baseApplication, jie, mge);

        //开、关任务
        if (CollectionUtil.isNotEmpty(irrigatePlanParamList)) {
            for (IrrigatePlanParam irrigatePlanParam : irrigatePlanParamList) {
                JobEntity startJobEntity = new JobEntity();
                BeanUtil.copyProperties(baseJobEntity, startJobEntity, CopyOptions.create().ignoreNullValue());
                JobEntity stopJobEntity = new JobEntity();
                BeanUtil.copyProperties(baseJobEntity, stopJobEntity, CopyOptions.create().ignoreNullValue());
                List<IrrigatePlanParam> tmpParam = new ArrayList<>();
                tmpParam.add(irrigatePlanParam);

                //开任务
                String startTime = irrigatePlanParam.getStartTime();
                String cronExpression = initCronExpression(irrigatePlan, tmpParam);
                startJobEntity.setCronExpression(cronExpression);
                startJobEntity.setJobName(startJobEntity.getJobName() + "_" + startTime + "_start");

                //开，设置时长
                JSONObject param = JSONUtil.parseObj(startJobEntity.getParams()).putOpt("controlDuration",irrigatePlanParam.getIrrigateDuration());
                startJobEntity.setParams(JSONUtil.toJsonStr(param));
                IrrigateUtil.requestTaskApi(baseApplication, jie, mge, null, startJobEntity);

                jobLs.add(startJobEntity);

                //关任务
                String endTimeStr = DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN) + " " + startTime + ":00";
                Date endTime = cn.hutool.core.date.DateUtil.parse(endTimeStr);
                Integer duration = irrigatePlanParam.getIrrigateDuration();
                Date endTimeDate = cn.hutool.core.date.DateUtil.offsetMinute(endTime, duration);
                String stopTime = cn.hutool.core.date.DateUtil.format(endTimeDate, "HH:mm");
                irrigatePlanParam.setStartTime(stopTime);
                tmpParam.add(irrigatePlanParam);
                cronExpression = initCronExpression(irrigatePlan, tmpParam);
                stopJobEntity.setCronExpression(cronExpression);
                stopJobEntity.setJobName(baseJobEntity.getJobName() + "_" + stopTime + "_stop");
                JSONObject params = JSONUtil.parseObj(stopJobEntity.getParams()).putOpt("switchState", 0);
                stopJobEntity.setParams(JSONUtil.toJsonStr(params));
                IrrigateUtil.requestTaskApi(baseApplication, jie, mge, null, stopJobEntity);
                jobLs.add(stopJobEntity);
            }
        }


        return jobLs;
    }


    /**
     * 触发任务
     *
     * @param baseApplication 应用
     * @param jie             接口枚举
     * @param mge             模块枚举
     * @param jobs            任务list
     */
    public static void triggerJob(BaseApplication baseApplication, JobGroupConstant.JobInterfaceEnum jie, JobGroupConstant.ModelGroupNameEnum mge, List<JobEntity> jobs) {
        if (CollectionUtil.isNotEmpty(jobs)) {
            for (JobEntity job : jobs) {
                IrrigateUtil.requestTaskApi(baseApplication, jie, mge, null, job);
            }
        }
    }

    /**
     * 初始化批量控制灌溉控制记录
     *
     * @param irrigateDeviceVo 灌溉设备
     * @param now              当前时间
     * @param closeDate       关闭时间
     * @return 灌溉控制记录
     */
    public static IrrigateControlRecord initParentDeviceControlRecord(IrrigateDeviceVo irrigateDeviceVo, Date now, Date closeDate) {
        IrrigateControlRecord irrigateControlRecord = new IrrigateControlRecord();
        //1单控,2分组控制,3自动任务控制,4电磁阀控制器控制(批量)
        irrigateControlRecord.setControlSource(4);
        //1开关状态
        irrigateControlRecord.setControlType(1);
        irrigateControlRecord.setSwitchState(irrigateDeviceVo.getSwitchState());
        irrigateControlRecord.setControlDuration(irrigateDeviceVo.getDuration());
        irrigateControlRecord.setRefId(irrigateDeviceVo.getId());
        //irrigateControlRecord.setIrrigateDeviceId();
        //irrigateControlRecord.setDeviceCode(irrigateDeviceVo.getDeviceCode());
        irrigateControlRecord.setStartTime(now);
        irrigateControlRecord.setEndTime(closeDate);
        //irrigateControlRecord.setCreatorName();
        return irrigateControlRecord;
    }


    /**
     * 初始化时间区间
     *
     * @param irrigateAnalyzeVo 查询参数
     * @param queryType             统计类型
     */
    public static void initTimeRange(IrrigateAnalyzeVo irrigateAnalyzeVo,String queryType) {
        Date startTime = irrigateAnalyzeVo.getStartTime();
        Date endTime = irrigateAnalyzeVo.getEndTime();
        switch (queryType) {
            case "year":
                startTime = DateUtil.beginOfYear(startTime);
                endTime = DateUtil.endOfYear(endTime);
                break;
            case "month":
                startTime = DateUtil.beginOfMonth(startTime);
                endTime = DateUtil.endOfMonth(endTime);
                break;
            case "day":
                startTime = DateUtil.beginOfDay(startTime);
                endTime = DateUtil.endOfDay(endTime);
                break;
            default:
                break;
        }
        irrigateAnalyzeVo.setStartTime(DateUtil.beginOfDay(startTime));
        irrigateAnalyzeVo.setEndTime(DateUtil.endOfDay(endTime));

    }

    /**
     * 获取时间格式DateField
     * @param queryType 统计类型
     * @return DateField
     */
    public static DateField getDateField(String queryType) {
        DateField dateField = null;
        switch (queryType) {
            case "year":
                dateField = DateField.YEAR;
                break;
            case "month":
                dateField = DateField.MONTH;
                break;
            case "day":
                dateField = DateField.DAY_OF_MONTH;
                break;
            default:
                break;
        }
        return dateField;
    }

    /**
     * 获取时间格式
     * @param dateField 时间类型
     * @return 时间格式
     */
    public static String getDateFormat(DateField dateField) {
        String format = null;
        switch (dateField) {
            case YEAR:
                format = DatePattern.NORM_YEAR_PATTERN;
                break;
            case MONTH:
                format = DatePattern.NORM_MONTH_PATTERN;
                break;
            case DAY_OF_MONTH:
                format = DatePattern.NORM_DATE_PATTERN;
                break;
            default:
                break;
        }
        return format;
    }

    /**
     * 计算同比
     * @param curDosage 当前量
     * @param historyDosage 历史量
     * @return 同比
     */
    public static Double calUseWaterRate(Double curDosage, Double historyDosage) {
        Double compRate = 0.0;
        curDosage = NumberUtil.round(curDosage, 0).doubleValue();
        historyDosage = NumberUtil.round(historyDosage, 0).doubleValue();
        if (historyDosage == 0) {
            compRate = NumberUtil.div(NumberUtil.sub(curDosage, historyDosage), 1, 4);
        } else {
            compRate = NumberUtil.div(NumberUtil.sub(curDosage, historyDosage), historyDosage.doubleValue(), 4);
        }
        return compRate;
    }

    /**
     * 数据保留小数位
     * @param value 数字字符串
     * @param round 保留小树位
     * @return 数字字符串
     */
    public static String roundNum(String value, int round) {
        if (StringUtils.isNotBlank(value)) {
            Double doubleValue = NumberUtil.round(Double.valueOf(value), round).doubleValue();
            if (round == 0) {
                return String.valueOf(doubleValue.intValue());
            }
            return String.valueOf(doubleValue);
        }
        return "";
    }
}
