package com.smartPark.business.gardensoil.device.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * 园林土壤设备表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("livable_garden_soil_device")
public class GardenSoilDevice extends Model<GardenSoilDevice> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 物联网平台设备id
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 使用状态(1启用0禁用)
     */
    // @TableField("use_status_")
    @TableField(exist = false)
    private Integer useStatus;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    @TableField(exist = false)
    private Set<Long> ids;

    /**
     * 是否删除，0未删除，1已删除
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
