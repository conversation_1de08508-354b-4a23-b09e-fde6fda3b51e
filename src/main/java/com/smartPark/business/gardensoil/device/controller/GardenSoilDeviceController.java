package com.smartPark.business.gardensoil.device.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.gardensoil.device.entity.GardenSoilDevice;
import com.smartPark.business.gardensoil.device.entity.vo.GardenSoilDeviceDeviceDTO;
import com.smartPark.business.gardensoil.device.entity.vo.GardenSoilDeviceVo;
import com.smartPark.business.gardensoil.device.excel.model.GardenSoilDeviceImportTemplate;
import com.smartPark.business.gardensoil.device.service.GardenSoilDeviceService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 智慧园林土壤设备/园林土壤设备管理
 *
 * <AUTHOR>
 * @Date: 2020/11/04 11:42
 * @Description: 智慧园林土壤设备/园林土壤设备管理
 */
@RestController
@RequestMapping("gardenSoilDevice")
@Api(tags = "园林土壤设备管理")
public class GardenSoilDeviceController {

    @Autowired
    private GardenSoilDeviceService gardenSoilDeviceService;

    /**
     * @Description: 查询设备信息
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @GetMapping("device/{deviceCode}")
    @ApiOperation("根据设备编码查询设备信息")
    public RestMessage findDeviceByDeviceId(@PathVariable("deviceCode") String deviceCode) {
        //参数验证
        Assert.hasLength(deviceCode, "设备编码不能为空");
        GardenSoilDeviceDeviceDTO gardenSoilDeviceDeviceDTO = gardenSoilDeviceService.findDeviceByDeviceId(deviceCode, true);
        return RestBuilders.successBuilder().data(gardenSoilDeviceDeviceDTO).build();
    }

    /**
     * @Description: 增加园林土壤设备
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping
    @ApiOperation("增加园林土壤设备")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "environmentMonitoring:GardenSoil:deviceManage:add", desc = "关联设备")
    public RestMessage insert(@RequestBody List<GardenSoilDevice> gardenSoilDevice) {
        //参数验证
        gardenSoilDeviceService.insert(gardenSoilDevice);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 删除园林土壤设备（包含批量删除）
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @DeleteMapping
    @ApiOperation("删除园林土壤设备（包含批量删除）")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "environmentMonitoring:GardenSoil:deviceManage:batchDelete", desc = "删除园林土壤设备")
    public RestMessage delBatch(@RequestBody GardenSoilDevice gardenSoilDevice) {
        Assert.notEmpty(gardenSoilDevice.getIds(), "id不能为空");
        gardenSoilDeviceService.delBatch(gardenSoilDevice.getIds());
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 编辑园林土壤设备
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PutMapping
    @ApiOperation("编辑园林土壤设备")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "environmentMonitoring:GardenSoil:deviceManage:edit", desc = "编辑园林土壤设备")
    public RestMessage updateById(@RequestBody GardenSoilDevice gardenSoilDevice) {
        Assert.notNull(gardenSoilDevice.getId(), "id不能为空");
        gardenSoilDeviceService.updateOne(gardenSoilDevice);
        return RestBuilders.successBuilder().build();
    }

    /**
     * @Description: 根据id查询园林土壤设备详情
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @GetMapping("{id}")
    @ApiOperation("根据id查询园林土壤设备详情")
    public RestMessage findById(@PathVariable("id") Long id) {
        Assert.notNull(id, "id不能为空");
        GardenSoilDeviceDeviceDTO GardenSoilDeviceDeviceDTO = gardenSoilDeviceService.findById(id);
        return RestBuilders.successBuilder().data(GardenSoilDeviceDeviceDTO).build();
    }


    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<GardenSoilDeviceVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<GardenSoilDeviceVo> record = gardenSoilDeviceService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 根据条件导出
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("export")
    @ApiOperation("导出")
    public RestMessage export(@RequestBody GardenSoilDeviceVo gardenSoilDeviceVo, HttpServletRequest request, HttpServletResponse response) {
        Long taskId = gardenSoilDeviceService.export(gardenSoilDeviceVo, request, response);
        return RestBuilders.successBuilder().data(taskId).build();
    }

    /**
     * @Description: 园林土壤设备导入
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("import")
    public RestMessage imports(@RequestBody MultipartFile file) throws Exception {
        Long taskId = gardenSoilDeviceService.imports(file);
        return RestBuilders.successBuilder().data(taskId).build();
    }

    /**
     * @Description: 园林土壤设备导入修改
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("importUpdate")
    public RestMessage importUpdate(@RequestBody MultipartFile file) throws Exception {
        Long taskId = gardenSoilDeviceService.importUpdate(file);
        return RestBuilders.successBuilder().data(taskId).build();
    }

    /**
     * 下载导入模板
     *
     * @param response
     * @throws IOException
     */
    @GetMapping("downloadExcelTempplate")
    public void downloadExcelTempplate(HttpServletResponse response) throws IOException {
        List<GardenSoilDeviceImportTemplate> list = new ArrayList<>();
        GardenSoilDeviceImportTemplate bean = new GardenSoilDeviceImportTemplate();
        bean.setDeviceCode("11300102012");
        list.add(bean);
        // 这里URLEncoder.encode可以防止中文乱码 easyexcel没有关系
        String fileName = URLEncoder.encode("园林土壤设备导入模板", "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), GardenSoilDeviceImportTemplate.class).sheet("园林土壤设备").doWrite(list);
    }
}

