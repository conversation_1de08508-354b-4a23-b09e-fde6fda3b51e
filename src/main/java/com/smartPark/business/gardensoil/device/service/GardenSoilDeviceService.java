package com.smartPark.business.gardensoil.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.gardensoil.device.entity.GardenSoilDevice;
import com.smartPark.business.gardensoil.device.entity.vo.GardenSoilDeviceDeviceDTO;
import com.smartPark.business.gardensoil.device.entity.vo.GardenSoilDeviceVo;
import com.smartPark.business.gardensoil.device.excel.model.GardenSoilDeviceExportModelDTO;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 园林土壤设备表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface GardenSoilDeviceService extends IService<GardenSoilDevice> {

    IPage<GardenSoilDeviceVo> queryListByPage(RequestModel<GardenSoilDeviceVo> requestModel);

    /**
     * 增加
     *
     * @param gardenSoilDevice
     */
    void insert(List<GardenSoilDevice> gardenSoilDevice);

    void insert(GardenSoilDevice gardenSoilDevice);

    /**
     * 根据id编辑
     *
     * @param gardenSoilDevice
     */
    void updateOne(GardenSoilDevice gardenSoilDevice);

    /**
     * @param flag true 验证设备码 false不验证
     * @Description: 查询设备信息
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    GardenSoilDeviceDeviceDTO findDeviceByDeviceId(String deviceCode, Boolean flag);

    /**
     * @Description: 删除园林土壤设备（包含批量删除）
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    void delBatch(Set<Long> ids);

    /**
     * @Description: 根据id查询园林土壤设备详情
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    GardenSoilDeviceDeviceDTO findById(Long id);


    /**
     * @Description: 根据条件导出
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    Long export(GardenSoilDeviceVo gardenSoilDeviceVo, HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询导出excel数据
     *
     * @param gardenSoilDeviceVo
     * @return
     */
    List<GardenSoilDeviceExportModelDTO> queryList4Export(GardenSoilDeviceVo gardenSoilDeviceVo);

    /**
     * @Description: 园林土壤设备导入
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    Long imports(MultipartFile file) throws IOException;

    DeviceExtendInfo getDeviceExtendInfos(String deviceCode, Boolean flag);

    /**
     * @Description: 园林土壤设备导入修改
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    Long importUpdate(MultipartFile file) throws IOException;
}
