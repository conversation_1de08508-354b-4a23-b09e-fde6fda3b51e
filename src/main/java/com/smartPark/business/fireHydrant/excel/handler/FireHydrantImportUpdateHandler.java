package com.smartPark.business.fireHydrant.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ErrorMsg;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.importer.DataImportParam;
import com.asyncexcel.core.importer.ImportContext;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.fireHydrant.entity.FireHydrant;
import com.smartPark.business.fireHydrant.entity.vo.FireHydrantDeviceDTO;
import com.smartPark.business.fireHydrant.entity.vo.FireHydrantVo;
import com.smartPark.business.fireHydrant.excel.model.FireHydrantImportModel;
import com.smartPark.business.fireHydrant.service.FireHydrantService;
import com.smartPark.common.asyncexcel.handler.CommonImportHandler;
import com.smartPark.common.base.model.RequestModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cglib.beans.BeanCopier;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/22
 * @description
 */
@Slf4j
@ExcelHandle
public class FireHydrantImportUpdateHandler extends CommonImportHandler<FireHydrantImportModel> {

    @Resource
    private FireHydrantService fireHydrantService;

    @Override
    public void init(ExcelContext ctx, DataParam param) {
        ReadSheet readSheet = EasyExcel.readSheet().sheetNo(0).headRowNumber(1).build();
        ImportContext impCtx = (ImportContext) ctx;
        impCtx.setReadSheet(readSheet);
    }

    @Override
    public List<ErrorMsg> importData(List<FireHydrantImportModel> list, DataImportParam param) {
        //查询库中存在的数据
        RequestModel<FireHydrantVo> requestModel = new RequestModel<>();
        requestModel.setCustomQueryParams(new FireHydrantVo());
        requestModel.setPage(new Page(1,-1));
        IPage<FireHydrantDeviceDTO> fireHydrantVoIPage = fireHydrantService.queryListByPage(requestModel);
        Set<String> deviceCodes = fireHydrantVoIPage.getRecords().stream().map(m -> m.getDeviceCode()).collect(Collectors.toSet());
        List<ErrorMsg> errorList = new ArrayList<>();
        List<FireHydrant> fireHydrants = new ArrayList<>();
        //暂存一个设备编码集合
        list.forEach(userImportModel ->{
            ErrorMsg errorMsg = checkParam(userImportModel,deviceCodes);
            if (null != errorMsg) {
                errorList.add(errorMsg);
            } else {
                BeanCopier beanCopier = BeanCopier.create(FireHydrantImportModel.class, FireHydrant.class, false);
                FireHydrant fireHydrant = new FireHydrant();
                beanCopier.copy(userImportModel, fireHydrant, null);
                fireHydrants.add(fireHydrant);
            }
        });
        // 批量修改
        fireHydrants.forEach(fireHydrant -> {
            UpdateWrapper<FireHydrant> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("device_code_",fireHydrant.getDeviceCode());
            fireHydrantService.update(fireHydrant,updateWrapper);
        });
        return errorList;
    }

    /**
     * 校验导入数据
     * @param model
     * @return 有异常返回ErrorMsg 正常返回null
     */
    public ErrorMsg checkParam(FireHydrantImportModel model, Set<String> deviceCodes) {
        //校验设备编码
        if (StringUtils.isBlank(model.getDeviceCode())){
            return new ErrorMsg(model.getRow(), "设备编码不能为空");
        }
        if (!deviceCodes.contains(model.getDeviceCode())){
            return new ErrorMsg(model.getRow(), "设备编码对应的数据不存在");
        }
        return null;
    }
}
