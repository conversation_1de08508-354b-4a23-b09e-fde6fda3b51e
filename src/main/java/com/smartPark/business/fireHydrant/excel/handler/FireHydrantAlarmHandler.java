package com.smartPark.business.fireHydrant.excel.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.smartPark.business.fireHydrant.entity.vo.FireHydrantAlarmVo;
import com.smartPark.business.fireHydrant.excel.model.FireHydrantAlarmExportModel;
import com.smartPark.business.fireHydrant.service.FireHydrantAlarmService;
import com.smartPark.common.alarm.entity.EventType;
import com.smartPark.common.alarm.mapper.EventTypeMapper;
import com.smartPark.common.asyncexcel.handler.CommonExportHandler;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@ExcelHandle
public class FireHydrantAlarmHandler extends CommonExportHandler<FireHydrantAlarmExportModel> {

    @Resource
    private FireHydrantAlarmService fireHydrantAlarmService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private EventTypeMapper eventTypeMapper;



    @Override
    public void init(ExcelContext ctx, DataParam param) {
        // 初始化导出上下文
        ExportContext context = (ExportContext) ctx;
        //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
        WriteSheet sheet = EasyExcel.writerSheet(0, "第一个sheet").head(FireHydrantAlarmExportModel.class).build();
        context.setWriteSheet(sheet);
    }


    @Override
    public ExportPage<FireHydrantAlarmExportModel> exportData(int startPage, int limit, DataExportParam param) {
        FireHydrantAlarmVo fireHydrantAlarmVo = (FireHydrantAlarmVo)param.getParam();
        RequestModel<FireHydrantAlarmVo> requestModel = new RequestModel<>();
        requestModel.setCustomQueryParams(fireHydrantAlarmVo);
        requestModel.setPage(new Page(startPage,limit));
        IPage<FireHydrantAlarmVo> alarmVoIPage = fireHydrantAlarmService.queryListByPage(requestModel);
        //处理告警类型
        List<FireHydrantAlarmVo> records = alarmVoIPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)){
            Set<String> alarmTypes = records.stream().map(FireHydrantAlarmVo::getAlarmType).collect(Collectors.toSet());
            List<EventType> list = new LambdaQueryChainWrapper<>(eventTypeMapper)
                    .select(EventType::getName, EventType::getCode).in(EventType::getCode, alarmTypes).list();
            Map<String, String> map = list.stream().collect(Collectors.toMap(EventType::getCode, EventType::getName));
            // 转换城alarmTypeName
            for (FireHydrantAlarmVo record : records) {
                record.setAlarmType(map.get(record.getAlarmType()));
            }
        }
        List<FireHydrantAlarmExportModel> exportList = FireHydrantAlarmExportModel.getList4Export(alarmVoIPage.getRecords());

        ExportPage<FireHydrantAlarmExportModel> result = new ExportPage<>();
        result.setTotal(alarmVoIPage.getTotal());
        result.setCurrent(alarmVoIPage.getCurrent());
        result.setSize(alarmVoIPage.getSize());
        result.setRecords(exportList);
        return result;
    }


}

