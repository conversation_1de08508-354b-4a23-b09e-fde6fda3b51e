package com.smartPark.business.wisdomroadbridge.detectReport.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.wisdomroadbridge.bridge.entity.Bridge;
import com.smartPark.business.wisdomroadbridge.bridge.mapper.BridgeMapper;
import com.smartPark.business.wisdomroadbridge.detectReport.dto.DetectReportDTO;
import com.smartPark.business.wisdomroadbridge.detectReport.entity.DetectReport;
import com.smartPark.business.wisdomroadbridge.detectReport.excel.handler.DetectReportHandler;
import com.smartPark.business.wisdomroadbridge.detectReport.mapper.DetectReportMapper;
import com.smartPark.business.wisdomroadbridge.detectReport.service.DetectReportService;
import com.smartPark.business.wisdomroadbridge.road.entity.Road;
import com.smartPark.business.wisdomroadbridge.road.mapper.RoadMapper;
import com.smartPark.business.wisdomroadbridge.roadBridgeStatistical.controller.entity.RoadBridgeStaticDto;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.entity.device.ObjInfo;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.utils.BusinessSerialNoUtil;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * DetectReport表服务实现类
 *
 * <AUTHOR>
 * @date 2023/03/21
 */
@Slf4j
@Service
public class DetectReportServiceImpl extends ServiceImpl
        <DetectReportMapper, DetectReport> implements DetectReportService {
    @Resource
    private CommonService commonService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private BaseUserContextProducer baseUserContextProducer;

    @Resource
    private RoadMapper roadMapper;

    @Resource
    private ObjInfoMapper objInfoMapper;

    @Resource
    private BridgeMapper bridgeMapper;

    @Resource
    private ExcelService excelService;



    @Override
    public boolean saveOne(DetectReport trafficDetectReport) {
        commonService.setCreateAndModifyInfo(trafficDetectReport);

        validParamRequired(trafficDetectReport);
        validRepeat(trafficDetectReport);
        validParamFormat(trafficDetectReport);
        //生成检测编码 202003200001
        //JC-YYYYMMDD-四位顺序编号生成方法
        String detectCode = BusinessSerialNoUtil.genSeqCode(redisUtil, "JC", "-", 4);
        trafficDetectReport.setDetectCode(detectCode);
        trafficDetectReport.setDeleted(0);
        if (save(trafficDetectReport)){
            LogHelper.setLogInfo("", trafficDetectReport.toString(), null, null,"添加检测报告，检测编码："+detectCode);
            return true;
        } else {
            return false;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(DetectReport trafficDetectReport) {
        Assert.notNull(trafficDetectReport.getId(), "id不能为空");
        commonService.setModifyInfo(trafficDetectReport);

        validRepeat(trafficDetectReport);
        validParamFormat(trafficDetectReport);
        if (updateById(trafficDetectReport)){
            DetectReport byId = baseMapper.selectById(trafficDetectReport.getId());
            LogHelper.setLogInfo("", trafficDetectReport.toString(), null, null,"修改检测报告，检测编码："+byId.getDetectCode());
            return true;
        } else {
            return false;
        }
    }

    @Override
    public IPage<DetectReport> selectPage(Page page, DetectReport trafficDetectReport) {
        return baseMapper.selectPage(page, trafficDetectReport);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        StringJoiner sj = new StringJoiner("，");
        idList.forEach(id->{
            DetectReport byId = baseMapper.selectById(id);
            sj.add(byId.getDetectCode());
        });
        LogHelper.setLogInfo("", idList.toString(), null, null,"删除检测报告，检测编码："+sj);
        return removeByIds(idList);
    }

    @Override
    public RestMessage export(RequestModel<DetectReport> requestModel, HttpServletRequest request, HttpServletResponse
            response) {
        Page page = requestModel.getPage();
        Long size = page.getSize();
        Long userId = baseUserContextProducer.getCurrent().getId();
        DataExportParam dataExportParam = new DataExportParam();
        dataExportParam.setParam(requestModel.getCustomQueryParams());
        dataExportParam.setLimit(size.intValue());
        dataExportParam.setExportFileName("检查报告列表导出");
        dataExportParam.setTenantCode("traffic");
        dataExportParam.setBusinessCode("detectReport");
        dataExportParam.setCreateUserCode(userId.toString());
        Long taskId = excelService.doExport(dataExportParam, DetectReportHandler.class);
        return RestBuilders.successBuilder().data(taskId).build();
    }

    @Override
    public IPage<DetectReportDTO> selectDtoPage(Page page, DetectReport detectReport) {
        //20230330 多方讨论确定养护、检查不跟道桥id走，跟道桥的部件id走，objId
        Integer facilityType = detectReport.getFacilityType();
        Long facilityId = detectReport.getFacilityId();
        if (facilityType != null && facilityId != null) {
            String objId = null;
            if(facilityType.equals(DeviceModelConstant.ROAD)) {
                Road road = roadMapper.getOneById(facilityId);
                if(road != null) {
                    objId = road.getObjId();
                }
            }
            if(facilityType.equals(DeviceModelConstant.BRIDGE)){
                Bridge bridge = bridgeMapper.getOneById(facilityId);
                if (bridge != null) {
                    objId = bridge.getObjId();
                }
            }
            if(StringUtils.isNotBlank(objId)) {
                detectReport.setObjId(objId);
                detectReport.setFacilityId(null);
            }
        }
        IPage<DetectReportDTO> dtoPage = baseMapper.selectDtoPage(page, detectReport);
        //处理创建人
        if (CollectionUtil.isNotEmpty(dtoPage.getRecords())) {
            List<DetectReportDTO> dtoList = dtoPage.getRecords();
            dtoList.stream().forEach(dto -> {
                if (dto.getCreatorId() != null) {
                    Object creatorNameObj = redisUtil.hget(RedisConstant.USER_PRE, String.valueOf(dto.getCreatorId()));
                    if (creatorNameObj != null) {
                        if (creatorNameObj instanceof BaseappUser) {
                            BaseappUser baseappUser = (BaseappUser) creatorNameObj;
                            dto.setCreatorName(baseappUser.getNickname());
                        }
                    }
                }
                //处理设施与部件信息
                cascadeQueryInfo(dto);
            });
        }
        return dtoPage;
    }

    @Override
    public DetectReportDTO getOneDtoById(Serializable id) {
        DetectReport detectReport = baseMapper.selectById(id);
        if (detectReport != null) {
            DetectReportDTO dto = new DetectReportDTO();
            BeanUtil.copyProperties(detectReport, dto);
            //处理设施与部件信息
            cascadeQueryInfo(dto);
            return dto;
        }
        return null;
    }

    @Override
    public List<RoadBridgeStaticDto> staticsticalByFacilityType(Integer facilityType) {
        return baseMapper.staticsticalByFacilityType(facilityType);
    }

    @Override
    public List<RoadBridgeStaticDto> staticsticalByMonth(Integer facilityType, Date startTime, Date endTime) {
        return baseMapper.staticsticalByMonth(facilityType,startTime,endTime);
    }

    /**
     * 处理设施与部件信息
     *
     * @param dto 检测报告dto信息
     */
    private void cascadeQueryInfo(DetectReportDTO dto) {
        if (dto != null) {
            Long facilityId = dto.getFacilityId();
            Integer facilityType = dto.getFacilityType();
            ObjInfo objInfo = null;
            //1井盖，2道路，3桥梁
            if (facilityType.equals(DeviceModelConstant.ROAD)) {
                //道路
                Road road = roadMapper.getOneById(facilityId);
                if (road != null) {
                    dto.setFacilityInfo(road);
                    String objId = road.getObjId();
                    objInfo = road.getObjInfo();

                }
            }
            if (facilityType.equals(DeviceModelConstant.BRIDGE)) {
                //桥梁
                Bridge bridge = bridgeMapper.getOneById(facilityId);
                if (bridge != null) {
                    dto.setFacilityInfo(bridge);
                    String objId = bridge.getObjId();
                    objInfo = objInfoMapper.findByMonitorPointBsm(objId);
                }
            }
            dto.setObjInfo(objInfo);
        }
    }

    @Override
    public DetectReport getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(DetectReport trafficDetectReport) {
        /* QueryWrapper<DetectReport> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", trafficDetectReport.getName());
        queryWrapper.eq("tenant_id", linkappUserContextProducer.getNotNullCurrent().getTenantId());
        List<DetectReport> list = baseMapper.selectList(queryWrapper);
        if (list.size() == 0) {
            return;
        }
        if (list.size() > 1) {
            throw new BusinessException("名称有重复");
        }
        if (ObjectUtils.isEmpty(trafficDetectReport.getId())) {
            throw new BusinessException("名称已存在");
        }
        if (!trafficDetectReport.getId().equals(list.get(0).getId())) {
            throw new BusinessException("名称已存在");
        }
                    */

    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(DetectReport trafficDetectReport) {
        Assert.isTrue(trafficDetectReport.getFacilityType() != null, "设施类型不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(trafficDetectReport.getFacilityCode()), "设施编码不能为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(DetectReport trafficDetectReport) {
        //Assert.isTrue(trafficDetectReport.getName() == null || trafficDetectReport.getName().length() <= 50,
        //        "名称超长");
    }
}

