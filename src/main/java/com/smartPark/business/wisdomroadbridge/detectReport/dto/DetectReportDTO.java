package com.smartPark.business.wisdomroadbridge.detectReport.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.smartPark.business.wisdomroadbridge.detectReport.entity.DetectReport;
import com.smartPark.common.entity.device.ObjInfo;
import lombok.Data;
import org.springframework.data.annotation.Transient;

/**
 * 检测报告dto
 * @param <T>
 * <AUTHOR>
 */
@Data
public class DetectReportDTO<T> extends DetectReport {
    /**
     * 创建人姓名
     */
    @Transient
    @TableField(exist = false)
    private String creatorName;

    /**
     * 道路桥梁信息
     */
    private T facilityInfo;
    /**
     * 设施部件信息
     */
    private ObjInfo objInfo;
}

