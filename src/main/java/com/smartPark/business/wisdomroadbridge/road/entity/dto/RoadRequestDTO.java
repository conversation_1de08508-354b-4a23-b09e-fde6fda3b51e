package com.smartPark.business.wisdomroadbridge.road.entity.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description IOC道路分页查询
 * <AUTHOR> y<PERSON>
 * @Date 2025/6/4 14:22
 */
@Data
public class RoadRequestDTO {
//    {
//        "pageNo": 1, //页码
//            "pageSize": 10, //每页数量
//            "id":"" , //道路唯一标识，可选过滤条件
//            "roadType":"", //道路类型，可选过滤条件,可选值：主干路、次干路、支路
//            "state":"", //	道路建设状态,可选值：已建设、建设中、未建设
//    }
    /**
     * 页码
     */
    private Integer pageNo = 1;
    /**
     * 每页数量
     */
    private Integer pageSize = 10;
    /**
     * 道路唯一标识，可选过滤条件
     */
    private String id;
    /**
     * 道路类型，可选过滤条件,可选值：主干路、次干路、支路
     */
    private String roadType;
    /**
     * 道路建设状态,可选值：已建设、建设中、未建设
     */
    private String state;

    /**
     * 获取道路类型对应的枚举值
     * 将汉字转换为数据库存储的枚举值
     * @return 道路类型枚举值：1-快速路,2-主干路,3-次干路,4-支路
     */
    public String getRoadType() {
        if (StringUtils.isBlank(roadType)) {
            return roadType;
        }

        switch (roadType.trim()) {
            case "快速路":
                return "1";
            case "主干路":
                return "2";
            case "次干路":
                return "3";
            case "支路":
                return "4";
            default:
                // 如果不在范围内范围-1.防止击穿
                return "-1";
        }
    }

    /**
     * 获取建设状态对应的枚举值
     * 将汉字转换为数据库存储的枚举值
     * @return 建设状态枚举值：1-已建设、2-未建设、3-建设中
     */
    public String getState() {
        if (StringUtils.isBlank(state)) {
            return state;
        }

        switch (state.trim()) {
            case "已建设":
                return "1";
            case "未建设":
                return "2";
            case "建设中":
                return "3";
            default:
                // 如果不在范围内范围-1.防止击穿
                return "-1";
        }
    }
}
