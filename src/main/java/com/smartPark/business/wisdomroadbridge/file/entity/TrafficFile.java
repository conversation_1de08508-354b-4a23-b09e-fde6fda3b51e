package com.smartPark.business.wisdomroadbridge.file.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("traffic_file")
public class TrafficFile extends Model<TrafficFile> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO, value = "id_")
    private Long id;



    /**
     * 桥梁/道路编码
     */
    @TableField("obj_id_")
    private String objId;

    /**
     * 文件名称
     */
    @TableField(value = "file_name_")
    private String fileName;

    /**
     * 文件路径
     */
    @TableField("file_path_")
    private String filePath;

    /**
     * 0道路，1桥梁
     */
    @TableField("type_")
    private Integer type;


    /**
     * 执行检查时间
     */
    @TableField("execute_time_")
    private Date executeTime;


    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;


    /**
     * 创建人
     */
    @TableField("creator_id_")
    private Long creatorId;
}
