package com.smartPark.business.wisdomroadbridge.bridge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smartPark.business.wisdomroadbridge.bridge.entity.Bridge;
import com.smartPark.common.entity.device.ObjInfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * Bridge表服务接口
 *
 * <AUTHOR>
 * @date 2023/03/21
 */
public interface BridgeService extends IService<Bridge> {

    /**
     * 新增
     *
     * @param trafficBridge 实体对象
     * @return 操作结果
     */
    boolean saveOne(Bridge trafficBridge);

    /**
     * 修改单条
     *
     * @param trafficBridge 实体对象
     * @return 修改结果
     */
    boolean updateOne(Bridge trafficBridge);

    /**
     * 查询分页
     *
     * @param page          分页对象
     * @param trafficBridge 分页参数对象
     * @return 查询分页结果
     */
    IPage<Bridge> selectPage(Page page, Bridge trafficBridge);


    /**
     * 获取单条
     *
     * @param id 主键id
     * @return 查询结果
     */
    Bridge getOneById(Serializable id);

    /**
     * 根据id批量删除
     *
     * @param idList 主键列表
     * @return 删除结果
     */
    boolean deleteByIds(List<Long> idList);

    /**
     * 导出表格
     *
     * @param trafficBridge 过滤条件实体对象
     * @param request       请求
     * @param response      响应
     */
    void export(Bridge trafficBridge, HttpServletRequest request, HttpServletResponse response);

    /**
     * 根据objInfo获取有效的objInfo
     *
     * @param objInfo
     * @return
     */
    @Deprecated
    List<ObjInfo> getValidObjInfo(ObjInfo objInfo);
}

