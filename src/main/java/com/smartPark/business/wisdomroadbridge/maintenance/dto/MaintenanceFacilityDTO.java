package com.smartPark.business.wisdomroadbridge.maintenance.dto;

import com.smartPark.common.entity.device.ObjInfo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MaintenanceFacilityDTO<T> {
    /**
     * 设施信息
     */
    private T facilityInfo;

    /**
     * 部件信息
     */
    private ObjInfo objInfo;

    /**
     * 设施类型,1道路，2桥梁
     */
    private Integer facilityType;

    /**
     * 创建时间
     */
    private Date createTime;
}
