package com.smartPark.business.auth.excel.handler;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.asyncexcel.core.DataParam;
import com.asyncexcel.core.ExcelContext;
import com.asyncexcel.core.ExportPage;
import com.asyncexcel.core.annotation.ExcelHandle;
import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.core.exporter.ExportContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.auth.excel.model.UserExportModel;
import com.smartPark.common.security.service.UserService;
import com.smartPark.common.asyncexcel.handler.CommonExportHandler;
import com.smartPark.common.asyncexcel.util.ExportListUtil;
import com.smartPark.common.security.entity.BaseappUser;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/03/21
 * @description 用户导出处理器实现类
 */
@Slf4j
@ExcelHandle
public class UserExportHandler extends CommonExportHandler<UserExportModel> {

  @Autowired
  private UserService userService;

  @Value("{oss.minio.dir")
  private String dir;

  @Value("{oss.minio.url}")
  private String url;


  @Override
  public void init(ExcelContext ctx, DataParam param) {
    // 初始化导出上下文
    ExportContext context = (ExportContext) ctx;
    //此处的sheetNo会被覆盖，为了兼容一个文件多sheet导出
    WriteSheet sheet = EasyExcel.writerSheet(0, "第一个sheet").head(UserExportModel.class).build();
    context.setWriteSheet(sheet);
  }


  @Override
  public ExportPage<UserExportModel> exportData(int startPage, int limit, DataExportParam param) {
    // 初始化分页对象
    Page<BaseappUser> iPage = new Page<>(startPage, limit);
    // 通过map反射转换成对象
    Map<String, Object> parameters = param.getParameters();
    BaseappUser baseappUser = BeanUtil.fillBeanWithMap(parameters, new BaseappUser(), false);
    IPage page = userService.selectUsersPage(iPage, baseappUser);
    List<UserExportModel> list = ExportListUtil.transform(page.getRecords(), UserExportModel.class);
    ExportPage<UserExportModel> result = new ExportPage<>();
    result.setTotal(page.getTotal());
    result.setCurrent(page.getCurrent());
    result.setSize(page.getSize());
    result.setRecords(list);
    // 当前线程休息10秒
    try {
      Thread.sleep(10000);
    } catch (InterruptedException e) {
      e.printStackTrace();
    }
    return result;
  }

}
