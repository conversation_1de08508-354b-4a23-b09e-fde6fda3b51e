package com.smartPark.business.auth.entity;

import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.security.entity.Role;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("角色用户关联数据模型")
public class RoleRefUserDTO {

	@ApiModelProperty("用户实体集合")
	private List<BaseappUser> users;

	@ApiModelProperty("角色实体类")
	private Role role;

}
