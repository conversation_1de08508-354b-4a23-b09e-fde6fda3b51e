package com.smartPark.business.auth.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.security.entity.RoleRefPrivilegeDTO;
import com.smartPark.business.auth.entity.RoleRefUserDTO;
import com.smartPark.common.security.service.RoleService;
import com.smartPark.common.security.service.UserService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.security.entity.Role;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.boot.data.DisplayableControllerSupport;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;
import site.morn.rest.RestModel;

import javax.annotation.Resource;
import java.util.List;
import java.util.StringJoiner;

/**
 * 角色管理
 *
 * <AUTHOR>
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/linkappRole")
public class RoleController extends DisplayableControllerSupport<Role, Long, RoleService> {


	@Resource
	BaseUserContextProducer baseUserContextProducer;

	@Autowired
	private RoleService linkappRoleService;
	@Autowired
	private UserService userService;

	/**
	 * 新增角色
	 */
	@Override
  	@PostMapping("/add")
	@BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD,menuCode = "roleManagement:addRole",desc = "新建角色")
	public RestMessage add(@RequestBody RestModel<Role> restModel) {
		//处理日志
		StringJoiner sj = new StringJoiner(",");
		StringJoiner coreParam = new StringJoiner(",");
		Role linkappRole = restModel.getModel();
		linkappRoleService.initAndCheckRole(linkappRole);

		sj.add("新增角色,名称：" + linkappRole.getName());
		coreParam.add("新增角色,名称：" + linkappRole.getName());

		LogHelper.setLogInfo(null,coreParam.toString(), null,coreParam.toString(),sj.toString());
		return super.add(restModel);
	}

	/**
	 * 修改角色
	 */
	@ApiOperation("修改角色")
  @PutMapping("/update")
	@Override
	@BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "roleManagement:editRole",desc = "修改角色")
	public RestMessage update(@RequestBody RestModel<Role> restModel) {
		//处理日志
		StringJoiner sj = new StringJoiner(",");
		StringJoiner coreParam = new StringJoiner(",");

		//更新用户角色
		userService.allUserToRedis();
		RestMessage restMessage = super.update(restModel);
		sj.add("修改角色,名称：" + restModel.getModel().getName());
		coreParam.add("修改角色,名称：" + restModel.getModel().getName());

		LogHelper.setLogInfo(null,coreParam.toString(), null,coreParam.toString(),sj.toString());
		return restMessage;
	}
	
	/**
	 * 删除角色
	 */
	@Override
    @ApiOperation("删除角色")
  @PutMapping("delete/{roleId}")
	@BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL,menuCode = "roleManagement:delRole",desc = "删除角色")
	public RestMessage delete(@PathVariable Long roleId) {
		//处理日志
		StringJoiner sj = new StringJoiner(",");
		StringJoiner coreParam = new StringJoiner(",");
		//查询
		Role role = service().get(roleId);

		service().delete(roleId);

		if (role != null) {
			sj.add("删除角色,名称：" + role.getName());
			coreParam.add("删除角色,名称：" + role.getName());
		}

		LogHelper.setLogInfo(null,coreParam.toString(), null,coreParam.toString(),sj.toString());
		return RestBuilders.successMessage();
	}

	@ApiOperation(value = "根据用户Id查询用户")
	@ApiImplicitParam(name = "userId", value = "用户Id", required = true)
	@GetMapping("getRolesByUserId/{userId}")
	public RestMessage getRolesByUserId(@PathVariable Long userId) {
		return RestBuilders.successMessage(service().getByUserId(userId));
	}

	/**
	 * 查询当前用户可见的角色列表
	 * @return
	 */
	@ApiOperation(value = "查询当前用户可见的角色列表")
	@GetMapping("searchAll")
	public RestMessage getRolesForCurrentTenant() {
		return RestBuilders.successMessage(service().findRoles(new Role()));
	}

	/**
	 * 角色列表
	 * @param requestModel 角色入参
	 * @return 统一出参
	 */
	@PostMapping("/selectPage")
	public RestMessage selectPage(@RequestBody RequestModel<Role> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<Role> record = service().selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder().data(record).build();
    }

	/**
	 * 根据角色ID获取所关联的用户集合
	 * @param roleId
	 * @return
	 */
	@ApiOperation(value = "根据角色ID获取所关联的用户集合")
	@GetMapping("/selectUserByRole/{roleId}")
	public RestMessage selectUserByRole(@PathVariable Long roleId) {
		return RestBuilders.successBuilder().data(service().selectUserByRole(roleId)).build();
	}

	/**
	 * 修改、新增角色-用户关联
	 * @param roleRefUserDTO
	 * @return
	 */
	@ApiOperation(value = "修改、新增角色-用户关联")
	@PostMapping("/role2Users")
    public RestMessage role2Users(@RequestBody RoleRefUserDTO roleRefUserDTO) {
    	service().role2Users(roleRefUserDTO.getRole(), roleRefUserDTO.getUsers());
        return RestBuilders.successMessage();
    }

	/**
	 * 返回的message信息描述好像不太形象
	 * @param code
	 * @return
	 */
	@ApiOperation(value = "角色编码唯一性校验")
	@RequestMapping("/checkRole/{code}")
    public RestMessage checkRole(@PathVariable String code) {
    	Role role = new Role();
    	role.setCode(code);
    	List<Role> roles = service().checkRole(role);
    	if(roles.isEmpty()) {
    		return RestBuilders.successBuilder().message("角色编码为空").build();
    	}else {
    		return RestBuilders.failureBuilder().message("角色编码不为空").build();
    	}
    }

	/**
	 * 查询角色所有可见权限
	 * @param restModel 角色条件对象
	 * @return 统一出参
	 */
	@PostMapping({"/roleAndAllPrivilege"})
	public RestMessage roleAndAllPrivilege(@RequestBody RestModel<Role> restModel) {
		RoleRefPrivilegeDTO roleRefPrivilegeDTO = linkappRoleService.selectPrivilegeAll(restModel.getModel());
		return RestBuilders.successMessage(roleRefPrivilegeDTO);
	}

	/**
	 * 保存角色权限
	 * @param restModel 角色权限关系对象
	 * @return 统一出参
	 */
	@PostMapping({"/saveRolePrivilege"})
	public RestMessage saveRolePrivilege(@RequestBody RestModel<RoleRefPrivilegeDTO> restModel) {
		RoleRefPrivilegeDTO roleRefPrivilegeDTO = linkappRoleService.saveRolePrivilege(restModel.getModel());
		return RestBuilders.successMessage(roleRefPrivilegeDTO);
	}

}


