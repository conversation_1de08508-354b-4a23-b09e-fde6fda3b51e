package com.smartPark.business.deployManage.controller;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.deployManage.entity.DeployCamera;
import com.smartPark.business.deployManage.entity.dto.DeployCameraDTO;
import com.smartPark.business.deployManage.entity.vo.DeployCameraPageVo;
import com.smartPark.business.deployManage.entity.vo.DeployCameraVo;
import com.smartPark.business.deployManage.service.DeployCameraService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.device.service.DeviceUnitService;
import com.smartPark.common.entity.device.Device;
import com.smartPark.common.entity.device.DeviceUnit;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.security.entity.BaseappUser;
import com.smartPark.common.videomonitor.entity.vo.VideoRecordVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 布控管理/摄像头管理
 *
 * <AUTHOR>
 * @date 2023/05/06
 */
@Slf4j
@RestController
@RequestMapping("/deployCamera")
public class DeployCameraController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private DeployCameraService deployCameraService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private DeviceUnitService deviceUnitService;

    @Resource
    BaseUserContextProducer baseUserContextProducer;

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/selectDtoPage")
    @ApiOperation("查询分页")
    public RestMessage selectDtoPage(@RequestBody RequestModel<DeployCameraVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DeployCameraDTO> record = deployCameraService.selectDtoPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param deployCamera 实体对象
     * @return 单条数据
     */
    @PostMapping("/selectDtoOne")
    @ApiOperation("查询单条")
    public RestMessage selectDtoOne(@RequestBody DeployCamera deployCamera) {
        Assert.notNull(deployCamera, "参数为空");
        Assert.notNull(deployCamera.getId(), "id为空");
        return deployCameraService.selectDtoOne(deployCamera);
    }

    /**
     * 修改数据
     *
     * @param deployCameraDTO 实体对象
     * @return 修改结果
     */
    @PutMapping("/updateDtoOne")
    @ApiOperation("修改单条")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "controlManagement:cameraManagement:edit", desc = "修改")
    public RestMessage updateDtoOne(@RequestBody DeployCameraDTO deployCameraDTO) {
        return deployCameraService.updateDtoOne(deployCameraDTO);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "controlManagement:cameraManagement:batchDelete", desc = "批量删除")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(deployCameraService.deleteByIds(idList)).build();
    }


    /**
     * 批量新增数据
     *
     * @param deployCameraPageVo 页面对象
     * @return 统一出参
     */
    @PostMapping("/batchInsert")
    @ApiOperation("批量新增")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.JOIN_DEVICE, menuCode = "controlManagement:cameraManagement:joinCamera", desc = "关联摄像头")
    public RestMessage batchInsert(@RequestBody DeployCameraPageVo deployCameraPageVo) {
        return deployCameraService.batchInsert(deployCameraPageVo);
    }

    /* -----2025-02对接真实摄像头---- */

    /**
     * 设备空间树
     * @param videoType 就是accessChannel
     */
    @RequestMapping("/selectAreaTree")
    public RestMessage selectAreaTree(@RequestParam("videoType") String videoType,@RequestBody DeployCameraVo vo) {
        vo.setAccessChannel(videoType);
        return RestBuilders.successBuilder().data(deployCameraService.selectAreaTreeVo(vo)).build();
    }

    /**
     * 获取摄像头中的属性-value，hlsUrl为通用的播放格式
     * @param deviceCode 设备编号
     * @return
     */
    @GetMapping("getProp")
    public RestMessage getProp(@RequestParam("deviceCode") String deviceCode){
        Map<String, String> prop = deployCameraService.getProp(deviceCode);
        return RestBuilders.successBuilder(prop).build();
    }

    /**
     * 获取录像记录
     * @param deviceCode 设备编号
     * @param date 日期
     * @return
     */
    @GetMapping("/getRecord")
    public RestMessage getRecord(@RequestParam("deviceCode") String deviceCode,@RequestParam("date") String date){
        List<VideoRecordVo> result = deployCameraService.getRecord(deviceCode,date);
        return RestBuilders.successBuilder(result).build();
    }

    /**
     * 获取摄像头录像地址
     * @param deviceCode 设备编号
     * @param startTime 回放开始时段
     * @param endTime 回放截止时段
     * @return
     */
    @GetMapping("/getRecordPlayUrl")
    public RestMessage getRecordPlayUrl(@RequestParam("deviceCode") String deviceCode,
                                        @RequestParam("startTime") String startTime,
                                        @RequestParam("endTime") String endTime){
        Object recordPlayUrl = deployCameraService.getRecordPlayUrl(deviceCode, startTime, endTime);
        return RestBuilders.successBuilder(recordPlayUrl).build();
    }

    /**
     * 摄像头云盘控制
     * @param deviceCode 设备编号
     * @param command 命令 up,down,left,right,stop
     * @return
     */
    @RequestMapping("/control")
    public RestMessage control(@RequestParam("deviceCode") String deviceCode,@RequestParam("command") String command){
        Object control = deployCameraService.control(deviceCode, command);
        return RestBuilders.successBuilder(control).build();
    }

    /**
     * 摄像头播放
     * @param deviceCode 设备编号
     * @return
     */
    @RequestMapping("/play")
    public RestMessage play(@RequestParam("deviceCode") String deviceCode){
        Object play = deployCameraService.play(deviceCode);
        //return RestBuilders.successBuilder(play).build();
        //20230613补充逻辑，返回设备型号信息(为什么再这里加逻辑，返回的是Object，去实现里加属性，再返回，要转对象2次，不如直接在这里加逻辑)
        cn.hutool.json.JSONObject jsonObject = null;
        if(play != null){
            jsonObject = JSONUtil.parseObj(play);
            Device device = deviceService.findOneByDeviceCode(deviceCode);
            if(device != null){
                Long deviceUnitId = device.getDeviceUnitId();
                if(deviceUnitId != null){
                    DeviceUnit deviceUnit = deviceUnitService.getById(deviceUnitId);
                    jsonObject.putOpt("deviceUnit",deviceUnit);
                }
            }
        }
        return RestBuilders.successBuilder(jsonObject).build();
    }

    /**
     * 下载录像，返回任务ID
     * @return
     */
    @RequestMapping("/startDownLoad")
    public RestMessage startDownLoad(@RequestParam("deviceCode") String deviceCode,
                                     @RequestParam("startTime") String startTime,
                                     @RequestParam("endTime") String endTime){
        Object result = deployCameraService.startDownLoad(deviceCode, startTime, endTime);
        return RestBuilders.successBuilder(result).build();
    }

    /**
     * 查询下载进度
     * @param taskId 任务编号
     * @return
     */
    @RequestMapping("/queryProgress")
    public RestMessage queryProgress(@RequestParam("deviceCode") String deviceCode,@RequestParam("taskId") String taskId){
        Object result = deployCameraService.queryProgress(deviceCode,taskId);
        return RestBuilders.successBuilder(result).build();
    }

    /**
     * 查询下载结果地址
     * @param taskId 任务编号
     * @return
     */
    @RequestMapping("/getDownLoadUrl")
    public RestMessage getDownLoadUrl(@RequestParam("deviceCode") String deviceCode,@RequestParam("taskId") String taskId,
                                      @RequestParam("app") String app,@RequestParam("mediaServerId") String mediaServerId){
        Object result = deployCameraService.getDownLoadUrl(deviceCode,taskId,app,mediaServerId);
        return RestBuilders.successBuilder(result).build();
    }

    /**
     * 查询大屏当前状态
     * @return
     */
    @GetMapping("/queryViewState")
    public RestMessage queryViewState(){
        BaseappUser currentUser = baseUserContextProducer.getCurrent();
        JSONObject result = deployCameraService.queryViewState(currentUser.getId());
        return RestBuilders.successBuilder().data(result).build();
    }

    /**
     * 设置大屏状态
     * @return
     */
    @PostMapping("/setViewState")
    public RestMessage setViewState(@RequestBody JSONObject json){
        BaseappUser currentUser = baseUserContextProducer.getCurrent();
        deployCameraService.setViewState(currentUser.getId(),json);
        return RestBuilders.successBuilder().build();
    }


}

