package com.smartPark.business.deployManage.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.business.deployManage.entity.DeployCameraGroup;
import com.smartPark.business.deployManage.entity.dto.DeployCameraGroupDTO;
import com.smartPark.business.deployManage.entity.vo.DeployCameraGroupVo;
import com.smartPark.business.deployManage.service.DeployCameraGroupService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.LogConstant;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.util.List;

/**
 * 布控管理/摄像机组
 *
 * <AUTHOR>
 * @date 2023/05/06
 */
@Slf4j
@RestController
@RequestMapping("/deployCameraGroup")
public class DeployCameraGroupController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private DeployCameraGroupService deployCameraGroupService;

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/getPage")
    @ApiOperation("查询分页")
    public RestMessage selectPage(@RequestBody RequestModel<DeployCameraGroup> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DeployCameraGroup> record = deployCameraGroupService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 查询分页DTO
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("/selectDtoPage")
    @ApiOperation("查询分页DTO")
    public RestMessage selectDtoPage(@RequestBody RequestModel<DeployCameraGroupVo> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<DeployCameraGroupDTO> record = deployCameraGroupService.selectDtoPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 查询单条dto
     *
     * @param cameraGroup 摄像头机组
     * @return 单条数据
     */
    @PostMapping("/selectDtoOne")
    @ApiOperation("查询单条dto")
    public RestMessage selectDtoOne(@RequestBody DeployCameraGroup cameraGroup) {
        return deployCameraGroupService.selectDtoOne(cameraGroup);
    }

    /**
     * 新增机组
     *
     * @param deployCameraGroupVo 实体对象
     * @return 新增结果
     */
    @PostMapping("/addGroup")
    @ApiOperation("新增机组")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD, menuCode = "controlManagement:cameraGroup:add", desc = "新增机组")
    public RestMessage addGroup(@RequestBody DeployCameraGroupVo deployCameraGroupVo) {
        return deployCameraGroupService.addGroup(deployCameraGroupVo);
    }

    /**
     * 修改单条
     *
     * @param deployCameraGroupVo 实体对象
     * @return 修改结果
     */
    @PutMapping("/updateDtoOne")
    @ApiOperation("修改单条")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT, menuCode = "controlManagement:cameraGroup:edit", desc = "修改机组")
    public RestMessage updateDtoOne(@RequestBody DeployCameraGroupVo deployCameraGroupVo) {
        return deployCameraGroupService.updateDtoOne(deployCameraGroupVo);
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEL, menuCode = "controlManagement:cameraGroup:batchDelete", desc = "删除机组")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(this.deployCameraGroupService.deleteByIds(idList)).build();
    }

}

