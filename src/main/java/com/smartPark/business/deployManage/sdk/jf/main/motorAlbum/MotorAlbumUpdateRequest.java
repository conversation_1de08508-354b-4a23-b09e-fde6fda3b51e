package com.smartPark.business.deployManage.sdk.jf.main.motorAlbum;

import cn.hutool.core.collection.ListUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 更新机动车底库请求
 */
@Getter
@Setter
public class MotorAlbumUpdateRequest {

    /**
     * 更新字段掩码
     */
    private UpdateField updateMask;
    /**
     * 底库信息
     */
    private Album album;

    @Getter
    @Setter
    public static class UpdateField {
        /**
         * 要更新的字段列表
         */
        private List<String> paths = ListUtil.toList("name", "note", "level");
    }

    @Getter
    @Setter
    public static class Album {
        /**
         * 底库名称
         */
        private String name;
        /**
         * 底库备注
         */
        private String note;
        /**
         * 底库级别
         */
        private Integer level;
    }
}
