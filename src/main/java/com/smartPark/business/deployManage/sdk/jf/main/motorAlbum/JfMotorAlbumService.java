package com.smartPark.business.deployManage.sdk.jf.main.motorAlbum;

import com.smartPark.business.deployManage.sdk.jf.core.JfClient;
import com.smartPark.business.deployManage.sdk.jf.core.JfHttp;
import com.smartPark.business.deployManage.sdk.jf.core.JfHttpHeader;

/**
 * 机动车底库管理服务
 */
public class JfMotorAlbumService {

    private final JfClient client;
    private final JfHttp HTTP;

    public JfMotorAlbumService(JfClient client) {
        this.client = client;
        this.HTTP = JfClient.getHTTP();
    }

    /**
     * 创建机动车底库
     */
    public MotorAlbumCreateResponse createMotorAlbum(MotorAlbumCreateRequest request) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/motorAlbums";
        return HTTP.request(url, "POST",
                new JfHttpHeader("motorAlbum_create",client.getConfig().getAppToken()),request, MotorAlbumCreateResponse.class);
    }

    /**
     * 更新机动车底库
     */
    public MotorAlbumUpdateResponse updateMotorAlbum(String albumId, MotorAlbumUpdateRequest request) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/motorAlbums/" + albumId;
        return HTTP.request(url, "PATCH",
                new JfHttpHeader("motorAlbum_update",client.getConfig().getAppToken()),request, MotorAlbumUpdateResponse.class);
    }

    /**
     * 查询机动车底库详情
     */
    public MotorAlbumGetResponse getMotorAlbum(String albumId) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/motorAlbums/" + albumId;
        return HTTP.request(url, "GET",
                new JfHttpHeader("motorAlbum_get",client.getConfig().getAppToken()),null, MotorAlbumGetResponse.class);
    }

    /**
     * 查询机动车底库列表
     */
    public MotorAlbumPageQueryResponse getMotorAlbumPageList(MotorAlbumPageQueryRequest request) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/motorAlbums:search";
        return HTTP.request(url, "POST",
                new JfHttpHeader("motorAlbums_search",client.getConfig().getAppToken()),request, MotorAlbumPageQueryResponse.class);
    }

    /**
     * 删除机动车底库
     */
    public void deleteMotorAlbum(String albumId) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/motorAlbums/" + albumId;
        HTTP.request(url, "DELETE",
                new JfHttpHeader("motorAlbum_delete",client.getConfig().getAppToken()),null, String.class);
    }
}
