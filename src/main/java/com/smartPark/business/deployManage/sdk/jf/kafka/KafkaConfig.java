package com.smartPark.business.deployManage.sdk.jf.kafka;

import cn.hutool.core.util.IdUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * kafka配置
 * @author: <PERSON>
 */
@Getter
@Setter
public class KafkaConfig {
    /**
     * 开启/关闭
     */
    private Boolean enable = false;
    /**
     * 唯一 Id
     */
    private String id = IdUtil.fastSimpleUUID();
    /**
     * kafka地址
     */
    private String bootstrapServers;
    /**
     * 消费者组
     */
    private String groupId;
    /**
     * 主题
     */
    private String topic;
    /**
     * 处理类名称-消费者SpringBean
     */
    private String consumerClassName;
    /**
     * 消费策略
     */
    private String autoOffsetReset = "earliest";

    /**
     * 最大并行数量，受到分片数量影响，分片只有一个最大就 1 个
     */
    private Integer maxConcurrency = 1;

    /**
     * 最大消费记录数量
     */
    private Integer maxPollRecords = 500;

}
