package com.smartPark.business.deployManage.sdk.jf.kafka.core;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;

import java.util.Properties;

/**
 * kafka发送数据工具
 * @author: <PERSON>
 */
public class KafkaProducerTool {

    private static TimedCache<String, KafkaProducer<String, String>> TIMED_CACHE = CacheUtil.newTimedCache(2*60*60*1000);

    /**
     * 发送数据
     * @param message 消息
     */
    public static void sendTestMessage(TestMessage message){
        KafkaProducer<String, String> producer = TIMED_CACHE.get(message.getBootstrapServers());
        if (producer == null){
            producer = createKafka(message.getBootstrapServers());
            TIMED_CACHE.put(message.getBootstrapServers(),producer);
            TIMED_CACHE.setListener((key, value) -> value.close());
        }
        producer.send(new ProducerRecord<>(message.getTopic(), message.getMessage()));
    }

    public static KafkaProducer<String, String> createKafka(String brokerServers) {
        Properties properties = new Properties();
        properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, brokerServers);
        properties.put(ProducerConfig.CLIENT_ID_CONFIG, "producer");
        properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        return new KafkaProducer<>(properties);
    }

}
