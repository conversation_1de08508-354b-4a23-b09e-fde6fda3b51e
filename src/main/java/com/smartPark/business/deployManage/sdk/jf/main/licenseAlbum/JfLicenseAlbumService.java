package com.smartPark.business.deployManage.sdk.jf.main.licenseAlbum;

import com.smartPark.business.deployManage.sdk.jf.core.JfClient;
import com.smartPark.business.deployManage.sdk.jf.core.JfHttp;
import com.smartPark.business.deployManage.sdk.jf.core.JfHttpHeader;

/**
 * 机动车车牌底库服务
 * @author: Yan <PERSON>u
 */
public class JfLicenseAlbumService {

    private final JfClient client;
    private final JfHttp HTTP;

    public JfLicenseAlbumService(JfClient client) {
        this.client = client;
        this.HTTP = JfClient.getHTTP();
    }

    /**
     * 获取机动车车牌底库列表
     */
    public LicenseAlbumPageQueryResponse getLicenseAlbumList(LicenseAlbumPageQueryRequest request) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/licenseAlbums:search";
        return HTTP.request(url, "POST",
                new JfHttpHeader("licenseAlbums_search",client.getConfig().getAppToken()),request, LicenseAlbumPageQueryResponse.class);
    }

    /**
     * 创建机动车车牌底库
     */
    public LicenseAlbumCreateResponse createLicenseAlbum(LicenseAlbumCreateRequest request) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/licenseAlbums";
        return HTTP.request(url, "POST",
                new JfHttpHeader("licenseAlbum_create",client.getConfig().getAppToken()),request, LicenseAlbumCreateResponse.class);
    }

    /**
     * 删除机动车车牌底库
     */
    public void deleteLicenseAlbum(String albumId) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/licenseAlbums/" + albumId;
        HTTP.request(url, "DELETE",
                new JfHttpHeader("licenseAlbum_delete",client.getConfig().getAppToken()),null, String.class);
    }

    /**
     * 更新机动车车牌底库
     */
    public LicenseAlbumUpdateResponse updateLicenseAlbum(String albumId, LicenseAlbumUpdateRequest request) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/licenseAlbums/" + albumId;
        return HTTP.request(url, "PATCH",
                new JfHttpHeader("licenseAlbum_update",client.getConfig().getAppToken()), request, LicenseAlbumUpdateResponse.class);
    }

    /**
     * 查询机动车车牌底库详情
     */
    public LicenseAlbumGetResponse getLicenseAlbum(String albumId) {
        String url = client.getConfig().getBaseUrl() + "/openapi/kunlun/v1/album/licenseAlbums/" + albumId;
        return HTTP.request(url, "GET",
                new JfHttpHeader("licenseAlbum_get",client.getConfig().getAppToken()),null, LicenseAlbumGetResponse.class);
    }

}
