package com.smartPark.business.deployManage.sdk.jf.main.motorAlarm;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 查询机动车布控响应
 */
@Getter
@Setter
public class MotorAlarmGetResponse {
    /**
     * 策略id
     */
    private String id;
    /**
     * 策略名称
     */
    private String strategyName;
    /**
     * 创建者名称
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 相机列表
     */
    private List<Camera> cameraList;
    /**
     * 策略开始时间
     */
    private Long startTime;
    /**
     * 策略结束时间
     */
    private Long endTime;
    /**
     * 策略每天生效时间
     */
    private List<StrategyTime> strategyTimeList;
    /**
     * 底库列表
     */
    private List<Album> albumList;

    @Getter
    @Setter
    public static class Camera {
        /**
         * 相机Id
         */
        private Long cameraId;
        /**
         * 相机在core侧的id
         */
        private String videoId;
    }

    @Getter
    @Setter
    public static class StrategyTime {
        /**
         * 从0点开始的毫秒数
         */
        private Long startMinute;
        /**
         * 从0点开始的毫秒数
         */
        private Long endMinute;
    }

    @Getter
    @Setter
    public static class Album {
        /**
         * 底库id
         */
        private String albumId;
        /**
         * 阈值
         */
        private Float threshold;
        /**
         * 底库类型
         */
        private Integer albumType;
    }
}