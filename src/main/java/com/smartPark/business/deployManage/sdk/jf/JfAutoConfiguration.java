package com.smartPark.business.deployManage.sdk.jf;

import com.smartPark.business.deployManage.sdk.jf.core.JfClient;
import com.smartPark.business.deployManage.sdk.jf.core.JfConfig;
import com.smartPark.business.deployManage.sdk.jf.kafka.KafkaDynamicController;
import com.smartPark.business.deployManage.sdk.jf.kafka.KafkaDynamicListener;
import com.smartPark.business.deployManage.sdk.jf.kafka.KafkaDynamicProperties;
import com.smartPark.business.deployManage.sdk.jf.main.faceAlarm.JfFaceAlarmController;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SpringBoot自动配置-尖峰SDK配置
 * @author: <PERSON>
 */
@Configuration
@ConditionalOnProperty(prefix = "jf.config",name = "enable",havingValue = "true")
public class JfAutoConfiguration {

    @Bean
    public JfFaceAlarmController jfFaceAlarmController() {
        return new JfFaceAlarmController();
    }

    @Bean
    @ConfigurationProperties(prefix = "jf.config")
    public JfConfig jfConfig() {
        return new JfConfig();
    }

    @Bean
    public JfClient jfClient(JfConfig jfConfig) {
        return new JfClient(jfConfig);
    }

    @Bean
    @ConfigurationProperties(prefix = "kafka.dynamic")
    public KafkaDynamicProperties kafkaDynamicProperties() {
        return new KafkaDynamicProperties();
    }

    @Bean
    public KafkaDynamicListener kafkaDynamicListener(KafkaDynamicProperties properties) {
        return new KafkaDynamicListener(properties);
    }

    @Bean
    public KafkaDynamicController kafkaDynamicController() {
        return new KafkaDynamicController();
    }

}
