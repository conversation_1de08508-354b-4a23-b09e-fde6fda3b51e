package com.smartPark.business.deployManage.entity.dto;

import com.smartPark.business.deployManage.entity.DeployCarGroupRef;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * DeployCarGroupRef实体类DTO
 *
 * <AUTHOR>
 * @date 2023/05/06
 */

@Data
@Accessors(chain = true)
public class DeployCarGroupRefDTO extends DeployCarGroupRef {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public DeployCarGroupRefDTO(DeployCarGroupRef safeDeployCarGroupRef) {
        //this.setName(safeDeployCarGroupRef.getName());
    }
}
