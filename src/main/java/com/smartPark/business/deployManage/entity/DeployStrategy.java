package com.smartPark.business.deployManage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 布控任务信息
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_deploy_strategy")
public class DeployStrategy extends BaseEntity<DeployStrategy> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 布控名称
     */
    @TableField("strategy_name_")
    private String strategyName;

    /**
     * 布控任务类型，1人员，2车辆
     */
    @TableField("strategy_type_")
    @Trans(type = TransType.DICTIONARY, key = "deploy_strategy_type")
    private Integer strategyType;

    /**
     * 布控类型，0临时布控，1长期布控
     */
    @TableField("term_type_")
    @Trans(type = TransType.DICTIONARY, key = "deploy_term_type")
    private Integer termType;

    /**
     * 布控任务id，视频解析平台反馈
     */
    @TableField("strategy_id_")
    private String strategyId;

    /**
     * 开始时间
     */
    @TableField("start_time_")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time_")
    private Date endTime;

    /**
     * 策略时间段，09:00-11:00,13:00-15:00多个，隔开
     */
    @TableField("strategy_times_")
    private String strategyTimes;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，0否，1是
     */
    @TableField("deleted_")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 状态，0关闭，1启动
     */
    @TableField("status_")
    private Integer status;


}
