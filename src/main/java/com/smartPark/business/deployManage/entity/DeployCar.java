package com.smartPark.business.deployManage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 布控车辆信息
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_deploy_car")
public class DeployCar extends BaseEntity<DeployCar> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 车牌号
     */
    @TableField("car_no_")
    private String carNo;

    /**
     * 车辆类型
     */
    @TableField(value = "car_type_",updateStrategy = FieldStrategy.IGNORED)
    @Trans(type = TransType.DICTIONARY, key = "car_type")
    private Integer carType;

    /**
     * 车辆颜色
     */
    @TableField(value = "car_color_",updateStrategy = FieldStrategy.IGNORED)
    @Trans(type = TransType.DICTIONARY, key = "car_color")
    private Integer carColor;

    /**
     * 车辆照片url
     */
    @TableField(value = "img_url_",updateStrategy = FieldStrategy.IGNORED)
    private String imgUrl;

    /**
     * 标签，多个，隔开
     */
    @TableField(value = "tag_",updateStrategy = FieldStrategy.IGNORED)
    private String tag;

    /**
     * 备注
     */
    @TableField(value = "remark_",updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，0否，1是
     */
    @TableField("deleted_")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

}
