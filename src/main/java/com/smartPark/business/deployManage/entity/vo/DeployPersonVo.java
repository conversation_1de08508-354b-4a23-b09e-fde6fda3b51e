package com.smartPark.business.deployManage.entity.vo;

import cn.hutool.core.date.DateUtil;
import com.smartPark.business.deployManage.entity.DeployGroup;
import com.smartPark.business.deployManage.entity.DeployPerson;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 布控人员vo
 *
 * <AUTHOR>
 */
@Data
public class DeployPersonVo extends DeployPerson {

    /**
     * 布控分组列表
     */
    private List<DeployGroup> deployGroupList;
    /**
     * 当前时间
     */
    private Date now = DateUtil.date();
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 布控状态,1未布控，2布控中
     */
    private Integer deployStatus;
    /**
     * 人员标签list
     */
    private List<String> tagList;
}
