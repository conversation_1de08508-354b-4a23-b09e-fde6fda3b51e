package com.smartPark.business.deployManage.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.smartPark.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 布控摄像头信息
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_deploy_camera")
public class DeployCamera extends BaseEntity<DeployCamera> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 摄像头名称
     */
    @TableField("camera_name_")
    private String cameraName;

    /**
     * 摄像头编码
     */
    @TableField("camera_no_")
    private String cameraNo;

    /**
     * 相机类型，解析平台枚举
     */
    @Deprecated
    @TableField("camera_type_")
    private Integer cameraType;


    /**
     * 设备id
     */
    @TableField("device_id_")
    private String deviceId;

    /**
     * 设施id
     */
    @TableField("obj_id_")
    private String objId;

    /**
     * 设备编码，冗余
     */
    @TableField("device_code_")
    private String deviceCode;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 创建人id
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人id
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 是否删除，0否，1是
     */
    @TableField("deleted_")
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    /**
     * 在线状态（1在线0离线），定时同步
     */
    @TableField("status_")
    private Integer status;

    /**
     * 接入渠道，1：普通模式（国标视频中台）（默认），2：萤石云，3：乐橙云
     */
    @Trans(type = TransType.DICTIONARY, key = "safe_deploy_camera:access_channel_")
    @TableField("access_channel_")
    private String accessChannel;

    /**
     * 是否人员监控
     */
    @TableField("is_personnel_monitoring_")
    private Boolean isPersonnelMonitoring;

    /**
     * 是否车辆监控
     */
    @TableField("is_vehicle_monitoring_")
    private Boolean isVehicleMonitoring;

    /**
     * 监控类型是否取并集，true取并集，false取交集
     */
    @TableField(exist = false)
    private Boolean isUnion = true;

    /**
     * 监控类型列表，查询、编辑用，1:人员、2：车辆
     */
    @TableField(exist = false)
    private List<String> monitoringTypeList;

    /**
     * 监控类型名，返显用
     */
    @TableField(exist = false)
    private String monitoringTypeName;

    /**
     * 处理是哪种监控类型
     * @param operateType 1:新增、编辑，2：查询
     */
    public void handleIsMonitoring(int operateType){
        if(monitoringTypeList != null && !monitoringTypeList.isEmpty()){
            if (operateType == 1){
                isPersonnelMonitoring = false;
                isVehicleMonitoring = false;
            }
            monitoringTypeList.forEach(type->{
                if ("1".equals(type)) {
                    isPersonnelMonitoring = true;
                } else if ("2".equals(type)) {
                    isVehicleMonitoring = true;
                }
            });
        }
    }

    /**
     * 处理monitoringTypeList，详情返显用
     */
    public void handleMonitoringTypeList(){
        monitoringTypeList = new ArrayList<>();
        if (isPersonnelMonitoring!=null && isPersonnelMonitoring){
            monitoringTypeList.add("1");
        }
        if (isVehicleMonitoring!=null && isVehicleMonitoring){
            monitoringTypeList.add("2");
        }
    }

    public void handleMonitoringTypeName(){
        if(isPersonnelMonitoring != null && isPersonnelMonitoring && isVehicleMonitoring != null && isVehicleMonitoring){
            monitoringTypeName = "人员、车辆";
        } else if(isPersonnelMonitoring != null && isPersonnelMonitoring){
            monitoringTypeName = "人员";
        } else if(isVehicleMonitoring != null && isVehicleMonitoring){
            monitoringTypeName = "车辆";
        }
    }
}
