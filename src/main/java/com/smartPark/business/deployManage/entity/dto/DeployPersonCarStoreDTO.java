package com.smartPark.business.deployManage.entity.dto;

import lombok.Data;

import java.util.Date;

/**
 * 人车库dto
 *
 * <AUTHOR>
 */
@Data
public class DeployPersonCarStoreDTO<T> {
    /**
     * 主键
     */
    private Long id;

    /**
     * 人车库名称
     */
    private String name;

    /**
     * 编号，人库显示身份证，车库显示车牌号
     */
    private String code;
    /**
     * 图片
     */
    private String imgUrl;

    /**
     * 分组名称，多个，隔开
     */
    private String groupNames;

    /**
     * 抓拍时间
     */
    private Date capturedTime;
    /**
     * 1人，2车
     */
    private Integer dataType;
    /**
     * 首次识别时间
     */
    private Date firstCapturedTime;
    /**
     * 底库信息，车库信息、人库信息
     */
    private T storeData;
}
