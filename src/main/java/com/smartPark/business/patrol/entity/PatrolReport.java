package com.smartPark.business.patrol.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 云巡检计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("safe_patrol_report")
public class PatrolReport extends Model<PatrolReport> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id_", type = IdType.AUTO)
    private Long id;

    /**
     * 计划id
     */
    @TableField("plan_id_")
    private Long planId;

    /**
     * 巡检计划时间
     */
    @TableField("record_time_")
    private Date recordTime;

    /**
     * 巡检开始时间
     */
    @TableField("start_time_")
    private Date startTime;

    /**
     * 巡检结束时间
     */
    @TableField("end_time_")
    private Date endTime;

    /**
     * 状态(1未巡检2巡检完成3巡检异常)
     */
    @TableField("status_")
    private Integer status;

    /**
     * 是否推送(1是0否)
     */
    @TableField("push_status_")
    private Integer pushStatus;

    /**
     * 关联方案id
     */
    @TableField("programme_id_")
    private Long programmeId;

    /**
     * 方案内容
     */
    @TableField("programme_content_")
    private String programmeContent;

    /**
     * 计划巡检设备数
     */
    @TableField("plan_num_")
    private Integer planNum;

    /**
     * 实际巡检设备数
     */
    @TableField("act_num_")
    private Integer actNum;

    /**
     * 创建人
     */
    @TableField("creator_id_")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("create_time_")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modify_id_")
    private Long modifyId;

    /**
     * 修改时间
     */
    @TableField("modify_time_")
    private Date modifyTime;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;

    /**
     * 是否删除，1删除，0存在
     */
    @TableLogic(value = "0", delval = "1")
    @TableField("deleted_")
    private Integer deleted;

    @TableField(exist = false)
    private List<Long> ids;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
