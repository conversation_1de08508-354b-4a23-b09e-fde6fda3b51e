package com.smartPark.business.patrol.service.impl;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.easylinkin.oss.OSSBaseService;
import com.easylinkin.oss.OSSPolicy;
import com.smartPark.business.patrol.entity.PatrolCategory;
import com.smartPark.business.patrol.entity.PatrolDeviceRef;
import com.smartPark.business.patrol.entity.PatrolProgramme;
import com.smartPark.business.patrol.entity.dto.PatrolProgrammeContent;
import com.smartPark.business.patrol.entity.vo.PatrolDeviceRefVo;
import com.smartPark.business.patrol.entity.vo.PatrolReportVo;
import com.smartPark.business.patrol.mapper.PatrolCategoryMapper;
import com.smartPark.business.patrol.mapper.PatrolDeviceRefMapper;
import com.smartPark.business.patrol.mapper.PatrolProgrammeMapper;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.async.AsyncUtil;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.business.patrol.entity.PatrolReport;
import com.smartPark.business.patrol.mapper.PatrolReportMapper;
import com.smartPark.business.patrol.service.PatrolReportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.common.security.context.BaseUserContextProducer;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 云巡检报告 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
public class PatrolReportServiceImpl extends ServiceImpl<PatrolReportMapper, PatrolReport> implements PatrolReportService {
  private static final Logger LOGGER = LoggerFactory.getLogger(PatrolReportServiceImpl.class);

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private PatrolDeviceRefMapper deviceRefMapper;
  @Autowired
  private PatrolProgrammeMapper programmeMapper;
  @Autowired
  private PatrolCategoryMapper categoryMapper;
  @Autowired
  private OSSBaseService ossBaseService;
  // dir路径
  @Value(("${oss.dir}"))
  private String dir;

  /**
   * 增加
   * @param patrolReport
   */
  @Override
  public void insert(PatrolReport patrolReport) {
    /**
     * 验证重复
     */
//    this.checkExist(patrolReport);
    //设置基本属性
    this.setBase(patrolReport);
    this.save(patrolReport);
  }

  /**
   * 根据id编辑
   * @param patrolReport
   */
  @Override
  public void updateOne(PatrolReport patrolReport) {
    /**
     * 验证重复
     */
//    this.checkExist(patrolReport);
    //设置基本属性
    this.setBase(patrolReport);
    this.updateById(patrolReport);
  }

  @Override
  public void delBatch(List<Long> ids) {
    SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
    ids.forEach(id ->{
      PatrolReportVo reportVo = baseMapper.findById(id);
      LogHelper.setLogMemo("删除云巡检报告,云巡检报告:"+reportVo.getPlanName()+format.format(reportVo.getRecordTime()));
    });

    //删除对应的设备
    QueryWrapper<PatrolDeviceRef> queryWrapper = new QueryWrapper<>();
    queryWrapper.in("ref_id_",ids).eq("ref_type_",2);
    deviceRefMapper.delete(queryWrapper);
    removeByIds(ids);
  }

  /**
   * @Description: 根据id查询云巡检报告详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @Override
  public PatrolReportVo findById(Long id, Map<String, String> dicMap) {
    PatrolReportVo patrolReportVo = baseMapper.findById(id);
    //巡检时长
    long second = DateUtil.between(patrolReportVo.getStartTime(), patrolReportVo.getEndTime(), DateUnit.SECOND);
    if (second > 120){
      long min = DateUtil.between(patrolReportVo.getStartTime(), patrolReportVo.getEndTime(), DateUnit.MINUTE);
      patrolReportVo.setTimeSize(min+"min");
    }else {
      patrolReportVo.setTimeSize(second+"s");
    }
    //巡检内容
    String programmeContent = patrolReportVo.getProgrammeContent();
    if (StringUtils.isNotBlank(programmeContent)){
      PatrolProgrammeContent patrolProgrammeContent = JSONObject.parseObject(programmeContent, PatrolProgrammeContent.class);
      //方案名称
      if (null != patrolReportVo.getProgrammeId()){
        PatrolProgramme programme = programmeMapper.selectById(patrolReportVo.getProgrammeId());
        if (null != programme){
          patrolProgrammeContent.setProgrammeName(programme.getName());
          patrolProgrammeContent.setProgrammeCode(programme.getCode());
        }
      }
      //巡检类型
      PatrolCategory category = categoryMapper.selectById(patrolProgrammeContent.getCategoryId());
      if (null != category){
        patrolProgrammeContent.setCategoryName(category.getName());
      }
      //数据来源
      patrolProgrammeContent.setSrcStr(Integer.valueOf(1).equals(patrolProgrammeContent.getSrc())?"公共属性":"统计数据");
      //方案状态
      patrolProgrammeContent.setStatusStr(Integer.valueOf(1).equals(patrolProgrammeContent.getStatus())?"启用":"禁用");
      //内容转义
      String content = patrolProgrammeContent.getContent();
      if (StringUtils.isNotBlank(content)){
        String s = getReplace(dicMap, content);
        patrolProgrammeContent.setContent(s);
      }
      patrolReportVo.setPatrolProgrammeContent(patrolProgrammeContent);
    }
    //设备列表
    List<PatrolDeviceRefVo> deviceRefVos = deviceRefMapper.findByReportId(patrolReportVo.getId());
    deviceRefVos.forEach(d ->{
      //区域替换
      if (StringUtils.isNotBlank(d.getAreaPath())){
        d.setAreaPath(d.getAreaPath().replace("@","/"));
      }
      //内容转换
      if (StringUtils.isNotBlank(d.getContent())){
        d.setContent(getReplace(dicMap, d.getContent()));
      }
    });
    patrolReportVo.setDeviceRefVosList(deviceRefVos);
    return patrolReportVo;
  }

  /**
   * @Description: 导出云巡检报告根据id
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public XWPFDocument getWord(Long id, Map<String, String> dicMap) throws Exception {
    SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
    //查询数据
    PatrolReportVo reportVo = this.findById(id, dicMap);
    Map<String,Object> map = new HashMap<>();
    map.put("vo",reportVo);
    map.put("recordTime",format.format(reportVo.getRecordTime()));
    map.put("startTime",format.format(reportVo.getStartTime()));
    map.put("endTime",format.format(reportVo.getEndTime()));
    //状态(1未巡检2巡检完成3巡检异常)
    String[] status = {"","未巡检","巡检完成","巡检异常"};
    map.put("status",status[reportVo.getStatus()]);
    map.put("num",null== reportVo.getDeviceRefVosList()?0:reportVo.getDeviceRefVosList().size());
    //用easyPoi导出
    XWPFDocument doc = WordExportUtil.exportWord07(new ClassPathResource("templates/patrolReport.docx").getPath(), map);
    return doc;
  }

  /**
   * @Description: 批量导出报告
   * <AUTHOR> yuanfeng
   * @date 2020/11/04 11:42
   */
  @Override
  public String exportZip(PatrolReportVo patrolReportVo, Map<String, String> stringStringMap, String key) throws IOException {
    if (!CollectionUtil.isNotEmpty(patrolReportVo.getIds())){
      RequestModel<PatrolReportVo> requestModel = new RequestModel<>();
      requestModel.setPage(new Page(0,-1));
      requestModel.setCustomQueryParams(patrolReportVo);
      IPage<PatrolReportVo> reportVoIPage = this.queryListByPage(requestModel, stringStringMap);
      List<Long> set = reportVoIPage.getRecords().stream().map(r -> r.getId()).collect(Collectors.toList());
      patrolReportVo.setIds(set);
    }
    ByteArrayOutputStream zipos = new ByteArrayOutputStream();
    //创建压缩流，初始化一个输出流缓存区
    ZipOutputStream zos = new ZipOutputStream (zipos) ;
    AtomicInteger done = new AtomicInteger();
    AsyncUtil.setTotal(key,patrolReportVo.getIds().size());
    patrolReportVo.getIds().forEach(id ->{
      try {
        XWPFDocument word = getWord(id, stringStringMap);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
          word.write(os);
          ZipEntry zipEntry = new ZipEntry("云巡检报告" + System.currentTimeMillis() + ".docx");
          zipEntry.setSize(os.size());
          zipEntry.setCompressedSize(os.size());
          zos.putNextEntry(zipEntry);
          os.writeTo(zos);
          zos.closeEntry();
          os.close();
        } catch (Exception e) {
          LOGGER.info("写入ZipOutputStream异常");
        }
        done.getAndIncrement();
        AsyncUtil.setDone(key,done.get());
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    });
    //注意关闭流的顺序，在上传oss之前必须关闭流否则下载解压的时候会报“文件末端错误”的问题
    zos.close();
    zipos.close();
    ByteArrayInputStream zipis = new ByteArrayInputStream(zipos.toByteArray());
    String name = "云巡检报告"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())+".zip";
    // 再上传到oss
    OSSPolicy ossPolicy = ossBaseService.generatePostPolicy(dir);
    String url = dir + "/" + name;
    ossBaseService.uploadFile(zipis,url);
    zipis.close();
    return ossPolicy.getAccessHost() + "/" +url;
  }

  /**
   * 字典转换
   * @param dicMap
   * @param content
   * @return
   */
  private static String getReplace(Map<String, String> dicMap, String content) {
    return content.replace("(1)", dicMap.get("1"))
            .replace("(2)", dicMap.get("2"))
            .replace("(3)", dicMap.get("3"))
            .replace("==","=");
  }

  @Override
  public IPage<PatrolReportVo> queryListByPage(RequestModel<PatrolReportVo> requestModel,Map<String, String> dicMap) {
    Page page = requestModel.getPage();
    PatrolReportVo patrolReportVo = requestModel.getCustomQueryParams();
    IPage<PatrolReportVo> patrolReportIPage = baseMapper.queryListByPage(page, patrolReportVo);
    patrolReportIPage.getRecords().forEach(r ->{
      //巡检时长
      long second = DateUtil.between(r.getStartTime(), r.getEndTime(), DateUnit.SECOND);
      if (second > 120){
        long min = DateUtil.between(r.getStartTime(), r.getEndTime(), DateUnit.MINUTE);
        r.setTimeSize(min+"min");
      }else {
        r.setTimeSize(second+"s");
      }

      //巡检内容
      String programmeContent = r.getProgrammeContent();
      if (StringUtils.isNotBlank(programmeContent)){
        PatrolProgrammeContent patrolProgrammeContent = JSONObject.parseObject(programmeContent, PatrolProgrammeContent.class);
        //内容转义
        String content = patrolProgrammeContent.getContent();
        if (StringUtils.isNotBlank(content)){
          String s = getReplace(dicMap, content);
          r.setProgrammeContent(s);
        }else {
          r.setProgrammeContent(null);
        }

      }
    });
    return patrolReportIPage;
  }

  /**
   * 验证重复
   */
  private void checkExist(PatrolReport patrolReport) {
    QueryWrapper<PatrolReport> queryWrapper = new QueryWrapper<>();
    //todo 设置判断重复条件
    //编辑的时候存在id
    Optional.ofNullable(patrolReport.getId()).ifPresent(id -> queryWrapper.ne("id_",patrolReport.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该云巡检报告已存在");
    }
  }

  /**
   * 设置基本属性
   * @param patrolReport
   */
  private void setBase(PatrolReport patrolReport) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    patrolReport.setModifyTime(new Date());
    patrolReport.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == patrolReport.getId()){
      patrolReport.setCreatorId(userId);
      patrolReport.setCreateTime(new Date());
    }
  }
}
