package com.smartPark.business.patrol.util;

import cn.hutool.json.JSONObject;
import com.smartPark.business.patrol.entity.vo.PatrolPlanVo;
import com.smartPark.common.cronHelp.JobGroupConstant;
import com.smartPark.common.job.JobEntity;
import com.smartPark.common.job.constant.CronConstant;
import com.smartPark.common.job.entity.JobDTO;
import com.smartPark.common.job.util.JobUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Description 巡查计划任务
 * <AUTHOR> <PERSON>uan<PERSON>
 * @Date 2023/4/11 10:05
 */
public class PatrolJobUtils {
    /**
     * 创建任务
     */
    public static void addJob(PatrolPlanVo patrolPlanVo){
        String actionTime = patrolPlanVo.getActionTime();
        String timeValues = patrolPlanVo.getTimeValues();
        if (StringUtils.isBlank(actionTime) || StringUtils.isBlank(timeValues)){
            return;
        }
        //时间分隔
        String[] split = actionTime.split(",");
        int j = 1;
        boolean isWeekAndMon = true;
        //按星期和按月转换一下
        if (Integer.valueOf(3).equals(patrolPlanVo.getTimeType())){
            patrolPlanVo.setTimeType(CronConstant.TIME_TYPE_MONTH);
        }else if (Integer.valueOf(2).equals(patrolPlanVo.getTimeType())){
            patrolPlanVo.setTimeType(CronConstant.TIME_TYPE_WEEK);
        }else {
            j = split.length;
            isWeekAndMon = false;
        }
        for (int i = 0; i < j; i++) {
            String jobName = "";
            //组装任务
            JobDTO jobDTO = new JobDTO();
            List<String> list = new ArrayList<>();
            if (isWeekAndMon){
                list = new ArrayList<>(Arrays.asList(split));
                jobDTO.setTimeValues(timeValues);
            }else {
                String startTime = split[i];
                list.add(startTime);
                jobName = "_"+startTime+"_"+i;
            }
            jobDTO.setStartTime(list);
            //回调
            jobDTO.setCallBack("/openapi/task/callback/patrolControl");
            //参数处理
            JSONObject jsonObject = new JSONObject();
            jsonObject.putOpt("timeType",patrolPlanVo.getTimeType());
            jsonObject.putOpt("planId",patrolPlanVo.getId());
            jobDTO.setModelGroupNameEnum(JobGroupConstant.ModelGroupNameEnum.PATROL_PLAN);
            jobDTO.setParam(jsonObject);
            JobEntity jobEntity = JobUtils.initTaskJobEntity(jobDTO);
            //重新组装名称
            jobEntity.setJobGroup(jobEntity.getJobGroup()+"_"+patrolPlanVo.getId());
            jobEntity.setJobName(jobEntity.getJobName()+"_"+patrolPlanVo.getPlanNo()+jobName);
            JobUtils.requestTaskApi(JobGroupConstant.JobInterfaceEnum.ADD_JOB,jobEntity,null);
        }
    }

    /**
     * 删除任务
     */
    public static void delJob(Long planId){
        //组装任务
        JobDTO jobDTO = new JobDTO();
        jobDTO.setModelGroupNameEnum(JobGroupConstant.ModelGroupNameEnum.PATROL_PLAN);
        JobEntity jobEntity = JobUtils.initTaskJobEntity(jobDTO);
        //重新组装名称
        jobEntity.setJobGroup(jobEntity.getJobGroup()+"_"+planId);
        JobUtils.requestTaskApi(JobGroupConstant.JobInterfaceEnum.DEL_JOB,jobEntity,null);
    }
}
