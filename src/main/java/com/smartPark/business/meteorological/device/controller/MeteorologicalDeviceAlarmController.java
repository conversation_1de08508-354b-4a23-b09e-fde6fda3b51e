package com.smartPark.business.meteorological.device.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.meteorological.device.entity.vo.MeteorologicalDeviceAlarmVo;
import com.smartPark.business.meteorological.device.service.MeteorologicalDeviceAlarmService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 气象环境设备/气象环境设备告警
 * @Description 气象环境设备/气象环境设备告警
 * <AUTHOR>
 * @Date 2023/3/23 17:22
 */
@RestController
@RequestMapping("meteorologicalDeviceAlarm")
@Api(tags = "气象环境设备告警")
public class MeteorologicalDeviceAlarmController {
    @Autowired
    private MeteorologicalDeviceAlarmService meteorologicalDeviceAlarmService;

    /**
     * @Description: 根据条件，分页(不分页)查询
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @PostMapping("list")
    @ApiOperation("根据条件，分页(不分页)查询")
    public RestMessage queryListByPage(@RequestBody RequestModel<MeteorologicalDeviceAlarmVo> requestModel){
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<MeteorologicalDeviceAlarmVo> record =  meteorologicalDeviceAlarmService.queryListByPage(requestModel);
        return RestBuilders.successBuilder().data(record).build();
    }

    /**
     * @Description: 根据id查询单条设备告警详情
     * <AUTHOR>
     * @date 2020/11/04 11:42
     */
    @GetMapping("{id}")
    @ApiOperation("根据id查询单条设备告警详情")
    public RestMessage findById(@PathVariable("id") Long id){
        Assert.notNull(id, "id 不能为空");
        MeteorologicalDeviceAlarmVo meteorologicalDeviceAlarmVo =  meteorologicalDeviceAlarmService.findById(id);
        return RestBuilders.successBuilder().data(meteorologicalDeviceAlarmVo).build();
    }

}
