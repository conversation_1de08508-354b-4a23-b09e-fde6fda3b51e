package com.smartPark.business.waterquality.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.smartPark.business.waterquality.device.entity.WaterQualityDevice;
import com.smartPark.business.waterquality.device.entity.vo.WaterQualityDeviceDeviceDTO;
import com.smartPark.business.waterquality.device.entity.vo.WaterQualityDeviceVo;
import com.smartPark.business.waterquality.device.mapper.WaterQualityDeviceMapper;
import com.smartPark.business.waterquality.device.service.WaterQualityDeviceService;
import com.smartPark.common.annotation.LogHelper;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.BaseApplicationConstant;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.device.dto.DeviceStatusDTO;
import com.smartPark.common.device.mapper.DeviceExtendInfoMapper;
import com.smartPark.common.device.mapper.DeviceMapper;
import com.smartPark.common.device.mapper.DeviceStatusMapper;
import com.smartPark.common.device.mapper.ObjInfoMapper;
import com.smartPark.common.device.util.DeviceUtils;
import com.smartPark.common.entity.BaseApplication;
import com.smartPark.common.entity.device.*;
import com.smartPark.common.entity.device.dto.DeviceObjInfoDevicePropertyStatusListInfo;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.rpc.RpcEnum;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import site.morn.rest.RestBuilder;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 园林土壤设备表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
@Slf4j
public class WaterQualityDeviceServiceImpl extends ServiceImpl<WaterQualityDeviceMapper, WaterQualityDevice> implements WaterQualityDeviceService {
  private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(WaterQualityDeviceServiceImpl.class);

  @Autowired
  private BaseUserContextProducer baseUserContextProducer;
  @Autowired
  private DeviceExtendInfoMapper deviceExtendInfoMapper;
  @Autowired
  private ObjInfoMapper objInfoMapper;
  @Autowired
  private DeviceMapper deviceMapper;
  @Autowired
  private RedisUtil redisUtil;
  @Autowired
  private DeviceStatusMapper deviceStatusMapper;

  /**
   * 增加
   *
   * @param devices
   */
  @Override
  @Transactional
  public void insert(List<WaterQualityDevice> devices) {
    StringJoiner sj = new StringJoiner("，");
    devices.forEach((device) -> {
      insert(device);
      sj.add(device.getDeviceCode());
    });
    LogHelper.setLogInfo("", devices.toString(), null, null,"添加设备，设备编码："+sj);
  }

    /**
     * 增加
     * @param gardenSoilDevice
     */
    @Transactional
    public void insert(WaterQualityDevice gardenSoilDevice) {
        Assert.hasLength(gardenSoilDevice.getDeviceCode(), "设备id不能为空");
        /**
         * 验证重复
         */
        if (gardenSoilDevice.getQualityGrade() == null) {
          gardenSoilDevice.setQualityGrade(5);
        }
        this.checkExist(gardenSoilDevice);
        //验证设备是否符合
        this.getDeviceExtendInfos(gardenSoilDevice.getDeviceCode(), true);
        //设置基本属性
        this.setBase(gardenSoilDevice);
        this.save(gardenSoilDevice);
        //保存base一份
        //获取应用名，应用id
        DeviceApplicationModelRef device = getDeviceApplicationModelRef();
        device.setDeviceCode(gardenSoilDevice.getDeviceCode());
        //远程保存
        // todo  绑定设备 待检查

      try {
        DeviceApplicationModelRef deviceApplicationModelRef = new DeviceApplicationModelRef();
        deviceApplicationModelRef.setDeviceCode(gardenSoilDevice.getDeviceCode());
        deviceApplicationModelRef.setModelId(DeviceModelConstant.WATER_QUALITY_MONITORING);
        deviceApplicationModelRef.setType(0);
        deviceApplicationModelRef.setApplicationId(3L);
        com.smartPark.common.utils.EventUtil.publishRefEvent(deviceApplicationModelRef);
      } catch (Exception e) {
        e.printStackTrace();
      }
//    JSONObject jsonObject = RpcEnum.BASE.postForObject("/sanzhiapi/deviceApplication/add", device, JSONObject.class);
//    if (!RestBuilder.properties().getSuccessCode().equals(jsonObject.getString("code"))){
//      LOGGER.info("新增失败===="+jsonObject.getString("message"));
//      throw new BusinessException("新增失败");
//    }

  }

  /**
   * 或者保存设备关联的实体
   * @return
   */
  private DeviceApplicationModelRef getDeviceApplicationModelRef() {
    BaseApplication baseApplication = (BaseApplication)redisUtil.hget(RedisConstant.APPLICATION, BaseApplicationConstant.LIVABLE);
    DeviceApplicationModelRef device = DeviceApplicationModelRef.getDevice(baseApplication);
    device.setModelId(DeviceModelConstant.WATER_QUALITY_MONITORING);
    return device;
  }

  /**
   * 根据id编辑
   * @param device
   */
  @Override
  public void updateOne(WaterQualityDevice device) {
    /**
     * 验证重复
     */
    this.checkExist(device);
    //设置基本属性
    this.setBase(device);
    this.updateById(device);
    WaterQualityDevice byId = baseMapper.selectById(device.getId());
    LogHelper.setLogInfo("", device.toString(), null, null,"编辑设备，设备编码："+byId.getDeviceCode());
  }

  /**
   * @Description: 查询设备信息
   * <AUTHOR>
   * @param flag true 验证设备码 false不验证
   * @date 2020/11/04 11:42
   */
  @Override
  public WaterQualityDeviceDeviceDTO findDeviceByDeviceId(String deviceCode, Boolean flag) {
    //校验重复
    if (flag){
      WaterQualityDevice gardenSoilDevice = new WaterQualityDevice();
      gardenSoilDevice.setDeviceCode(deviceCode);
      this.checkExist(gardenSoilDevice);
    }
    WaterQualityDeviceDeviceDTO gardenSoilDeviceDeviceDTO = new WaterQualityDeviceDeviceDTO();
    DeviceExtendInfo deviceExtendInfo = getDeviceExtendInfos(deviceCode,flag);
    gardenSoilDeviceDeviceDTO.setExtendInfo(deviceExtendInfo);
    if (null != deviceExtendInfo && StringUtils.isNotBlank(deviceExtendInfo.getDwbsm())){
        ObjInfo objInfo = objInfoMapper.findByMonitorPointBsm(deviceExtendInfo.getDwbsm());
        gardenSoilDeviceDeviceDTO.setObjInfo(objInfo);
    }
    return gardenSoilDeviceDeviceDTO;
  }

  /**
   * @Description: 删除水质监测设备（包含批量删除）
   */
  @Override
  @Transactional
  public void delBatch(Set<Long> ids) {
    //获取设备id集合
    List<WaterQualityDevice> gardenSoilDeviceList = baseMapper.selectBatchIds(ids);
    List<String> deviceIds = gardenSoilDeviceList.stream().map(m -> m.getDeviceCode()).collect(Collectors.toList());
    StringJoiner sj = new StringJoiner("，");
    deviceIds.forEach(sj::add);
    this.removeByIds(ids);
    //从base中删除
    //获取应用名，应用id
    DeviceApplicationModelRef device = getDeviceApplicationModelRef();
    device.setDeviceCodes(deviceIds);
    //远程保存
    JSONObject jsonObject = RpcEnum.BASE.postForObject("/sanzhiapi/deviceApplication/del", device, JSONObject.class);
    if (!RestBuilder.properties().getSuccessCode().equals(jsonObject.getString("code"))){
      LOGGER.info("删除错误===="+jsonObject.getString("message"));
      throw new BusinessException("删除失败");
    }
    LogHelper.setLogInfo("", ids.toString(), null, null,"删除设备，设备编码："+sj);
  }

  /**
   * @Description: 根据id查询园林土壤设备详情
   * <AUTHOR>
   * @date 2020/11/04 11:42
   */
  @Override
  public WaterQualityDeviceDeviceDTO findById(Long id) {
//    deviceMapper.selectAllByDeviceUnitId(1L);

    //设备详情
    WaterQualityDevice waterQualityDevice = baseMapper.selectById(id);
    if(waterQualityDevice==null){
      throw new BusinessException("该设备已删除，请刷新页面再进行操作");
    }
    WaterQualityDeviceDeviceDTO deviceDTO = BeanUtil.toBean(waterQualityDevice, WaterQualityDeviceDeviceDTO.class);
    //查询设备的属性
    QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("code",waterQualityDevice.getDeviceCode());
    List<Device> devices = deviceMapper.selectList(queryWrapper);
    if (CollectionUtil.isNotEmpty(devices)){
      deviceDTO.setStatus(devices.get(0).getStatus());
      deviceDTO.setAlarmState(devices.get(0).getAlarmState());
    }
//    WaterQualityDeviceDeviceDTO waterQualityDeviceDeviceDTO = findDeviceByDeviceId(waterQualityDevice.getDeviceCode(),false);
    if (null != deviceDTO){
      DeviceUtils.setDeviceDetail(deviceDTO);
//      deviceDTO.setObjInfo(waterQualityDeviceDeviceDTO.getObjInfo());
//      deviceDTO.setExtendInfo(waterQualityDeviceDeviceDTO.getExtendInfo());
//      //区域范围
//      if (StringUtils.isNotBlank(deviceDTO.getExtendInfo().getAreaPath())){
//        deviceDTO.getExtendInfo().setAreaPath(deviceDTO.getExtendInfo().getAreaPath().replace("@","/"));
//      }
    }
    //设置设备属性
    deviceDTO.setDevicePropertyStatusList(getDevicePropertyStatus(new DeviceStatusDTO().setDeviceCodes(Lists.newArrayList(waterQualityDevice.getDeviceCode()))));
    return deviceDTO;
  }
  /**
   * 设置设备属性
   *
   * @param deviceStatusDTO
   * @return
   */
  private List<DevicePropertyStatus> getDevicePropertyStatus(DeviceStatusDTO deviceStatusDTO) {
    List<DevicePropertyStatus> devicePropertyStatusList = deviceStatusMapper.listVisiblePropertyStatus(deviceStatusDTO);
    devicePropertyStatusList.forEach((item) -> {
      if (item.getUnit().equals("enum")) {
        JSONObject specsMap = item.getSpecsMap();
        if (specsMap != null && specsMap.containsKey(item.getValue())) {
          item.setValue(specsMap.get(item.getValue()).toString());
        }
      }
    });
    return devicePropertyStatusList;
  }

  /**
   * @Description: 根据id查询园林土壤设备详情
   * <AUTHOR>
   * @date 2020/11/04 11:42
   */
  @Override
  public DeviceObjInfoDevicePropertyStatusListInfo getDeviceObjInfoDevicePropertyStatusListInfo(String deviceCode) {
    //设备详情
    DeviceObjInfoDevicePropertyStatusListInfo deviceDTO = new DeviceObjInfoDevicePropertyStatusListInfo();
    //查询设备的属性
    QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("code", deviceCode);
    List<Device> devices = deviceMapper.selectList(queryWrapper);
    if (CollectionUtil.isNotEmpty(devices)) {
      deviceDTO.setDevice(devices.get(0));
    }
    DeviceExtendInfo deviceExtendInfo = getDeviceExtendInfos(deviceCode, false);
    ObjInfo objInfo = objInfoMapper.findByMonitorPointBsm(deviceExtendInfo.getDwbsm());
    deviceDTO.setObjInfo(objInfo);
    deviceDTO.setExtendInfo(deviceExtendInfo);
    //区域范围
    if (StringUtils.isNotBlank(deviceDTO.getExtendInfo().getAreaPath())) {
      deviceDTO.getExtendInfo().setAreaPath(deviceDTO.getExtendInfo().getAreaPath().replace("@", "/"));
    }
    //设置设备属性
    setDevicePropertyStatus(deviceDTO);
    return deviceDTO;
  }

  /**
   * 设置设备属性
   * @param deviceDTO
   */
  private void setDevicePropertyStatus(DeviceObjInfoDevicePropertyStatusListInfo deviceDTO) {
    //todo 设备属性先写死
    List<DevicePropertyStatus> devicePropertyStatusList = new ArrayList<>();
    //园林土壤设备状态
    DevicePropertyStatus devicePropertyStatus1 = new DevicePropertyStatus();
    devicePropertyStatus1.setProp("status");
    devicePropertyStatus1.setPropName("园林土壤设备状态");
    devicePropertyStatus1.setValue("正常");
    devicePropertyStatus1.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus1);
    //倾斜角度
    DevicePropertyStatus devicePropertyStatus2 = new DevicePropertyStatus();
    devicePropertyStatus2.setProp("angle");
    devicePropertyStatus2.setPropName("倾斜角度");
    devicePropertyStatus2.setValue("30°");
    devicePropertyStatus2.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus2);
    //电池电量
    DevicePropertyStatus devicePropertyStatus3 = new DevicePropertyStatus();
    devicePropertyStatus3.setProp("cell");
    devicePropertyStatus3.setPropName("电池电量");
    devicePropertyStatus3.setValue("100%");
    devicePropertyStatus3.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus3);
    deviceDTO.setDevicePropertyStatusList(devicePropertyStatusList);
  }

  /**
   * 根据设备id获取设备部件码信息
   * @param deviceCode
   * @return
   */
  @Override
  public DeviceExtendInfo getDeviceExtendInfos(String deviceCode, Boolean flag) {
    //根据id查询设备部件码
    QueryWrapper<DeviceExtendInfo> example = new QueryWrapper<>();
    example.eq("device_id", deviceCode);
    List<DeviceExtendInfo> extendInfoList = deviceExtendInfoMapper.selectList(example);
    if (CollectionUtil.isNotEmpty(extendInfoList)){
      DeviceExtendInfo extendInfo = extendInfoList.get(0);
      if (flag){
        checkBsm(extendInfo.getBsm());
      }
      return extendInfo;
    }else if (flag){
      log.error("设备编码错误,校验不通过");
      throw new BusinessException("设备编码错误,校验不通过");
    }
    return null;
  }

  /**
   * 设置设备属性
   * @param deviceDTO
   */
  private void setDevicePropertyStatus(WaterQualityDeviceDeviceDTO deviceDTO) {
    //todo 设备属性先写死
    List<DevicePropertyStatus> devicePropertyStatusList = new ArrayList<>();
    //园林土壤设备状态
    DevicePropertyStatus devicePropertyStatus1 = new DevicePropertyStatus();
    devicePropertyStatus1.setProp("O2");
    devicePropertyStatus1.setPropName("溶解氧");
    devicePropertyStatus1.setValue("20mg/l");
    devicePropertyStatus1.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus1);
    //倾斜角度
    DevicePropertyStatus devicePropertyStatus2 = new DevicePropertyStatus();
    devicePropertyStatus2.setProp("diandaolv");
    devicePropertyStatus2.setPropName("电导率");
    devicePropertyStatus2.setValue("30mg/l");
    devicePropertyStatus2.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus2);
    //电池电量
    DevicePropertyStatus devicePropertyStatus3 = new DevicePropertyStatus();
    devicePropertyStatus3.setProp("pH");
    devicePropertyStatus3.setPropName("pH");
    devicePropertyStatus3.setValue("5");
    devicePropertyStatus3.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus3);

    DevicePropertyStatus devicePropertyStatus4 = new DevicePropertyStatus();
    devicePropertyStatus4.setProp("zhuodu");
    devicePropertyStatus4.setPropName("浊度");
    devicePropertyStatus4.setValue("5NTU");
    devicePropertyStatus4.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus4);

    DevicePropertyStatus devicePropertyStatus5 = new DevicePropertyStatus();
    devicePropertyStatus5.setProp("wendu");
    devicePropertyStatus5.setPropName("温度");
    devicePropertyStatus5.setValue("30℃");
    devicePropertyStatus5.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus5);

    DevicePropertyStatus devicePropertyStatus6 = new DevicePropertyStatus();
    devicePropertyStatus6.setProp("COD");
    devicePropertyStatus6.setPropName("COD");
    devicePropertyStatus6.setValue("5mg/l");
    devicePropertyStatus6.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus6);

    DevicePropertyStatus devicePropertyStatus7 = new DevicePropertyStatus();
    devicePropertyStatus7.setProp("andan");
    devicePropertyStatus7.setPropName("氨氮");
    devicePropertyStatus7.setValue("5mg/l");
    devicePropertyStatus7.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus7);

    DevicePropertyStatus devicePropertyStatus8 = new DevicePropertyStatus();
    devicePropertyStatus8.setProp("zongling");
    devicePropertyStatus8.setPropName("总磷");
    devicePropertyStatus8.setValue("5mg/l");
    devicePropertyStatus8.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus8);

    DevicePropertyStatus devicePropertyStatus9 = new DevicePropertyStatus();
    devicePropertyStatus9.setProp("zongdan");
    devicePropertyStatus9.setPropName("总氮");
    devicePropertyStatus9.setValue("5mg/l");
    devicePropertyStatus9.setModifyTime(new Timestamp(System.currentTimeMillis()));
    devicePropertyStatusList.add(devicePropertyStatus9);

    deviceDTO.setDevicePropertyStatusList(devicePropertyStatusList);
  }

  @Override
  public IPage<WaterQualityDeviceVo> queryListByPage(RequestModel<WaterQualityDeviceVo> requestModel) {
    Page page = requestModel.getPage();
    WaterQualityDeviceVo gardenSoilDeviceVo = requestModel.getCustomQueryParams();
    IPage<WaterQualityDeviceVo> gardenSoilDeviceIPage = baseMapper.queryListByPage(page, gardenSoilDeviceVo);
    gardenSoilDeviceIPage.getRecords().forEach(m -> {
      //区域范围
      if (StringUtils.isNotBlank(m.getAreaPath())) {
        m.setAreaPath(m.getAreaPath().replace("@", "/"));
      }
    });
    return gardenSoilDeviceIPage;
  }

  /**
   * 验证重复
   */
  private void checkExist(WaterQualityDevice gardenSoilDevice) {
    QueryWrapper<WaterQualityDevice> queryWrapper = new QueryWrapper<>();
    //设置判断重复条件
    queryWrapper.eq("device_code_",gardenSoilDevice.getDeviceCode())
            .eq("deleted_",0);
    //编辑的时候存在id
    Optional.ofNullable(gardenSoilDevice.getId()).ifPresent(id -> queryWrapper.ne("id_",gardenSoilDevice.getId()));
    Integer integer = baseMapper.selectCount(queryWrapper);
    if (integer>0){
      throw new BusinessException("该园林土壤设备已存在");
    }
  }

  /**
   * 设置基本属性
   * @param gardenSoilDevice
   */
  private void setBase(WaterQualityDevice gardenSoilDevice) {
    Long userId = null;
    if(null != baseUserContextProducer.getCurrent()){
      userId = baseUserContextProducer.getCurrent().getId();
    }
    gardenSoilDevice.setModifyTime(new Date());
    gardenSoilDevice.setModifyId(userId);
    //没有id就是新增,有就是编辑
    if (null == gardenSoilDevice.getId()){
        gardenSoilDevice.setCreatorId(userId);
        gardenSoilDevice.setCreateTime(new Date());
    }
  }

  /**
   * 验证设备是否符合
   * @param bsm
   */
  private void checkBsm(String bsm) {
    //验证设备标识码是不是20位
//    if (bsm.length() != 20){
//      throw new BusinessException("设备编码错误,校验不通过");
//    }
//    String code = bsm.substring(12, 16);
//    if (!"0308".equals(code)){
//      throw new BusinessException("设备编码错误,校验不通过");
//    }
  }
}
