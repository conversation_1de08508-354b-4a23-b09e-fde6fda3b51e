package com.smartPark.business.waterquality.device.entity.vo;

import com.smartPark.business.waterquality.device.entity.WaterQualityDevice;
import com.smartPark.common.entity.device.DeviceExtendInfo;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.entity.device.MonitorPoint;
import com.smartPark.common.entity.device.ObjInfo;
import lombok.Data;

import java.util.List;

/**
 * @Description 园林土壤设备设备的信息详情
 * <AUTHOR>
 * @Date 2023/3/21 13:46
 */
@Data
public class WaterQualityDeviceDeviceDTO extends WaterQualityDeviceVo {
    /**
     * 设备基础信息
     */
    private DeviceExtendInfo extendInfo;

    /**
     * 监测点位信息
     */
    private MonitorPoint monitorPoint;

    /**
     *部件码基本信息
     */
    private ObjInfo objInfo;

    /**
     * 设备属性
     */
    private List<DevicePropertyStatus> devicePropertyStatusList;
}
