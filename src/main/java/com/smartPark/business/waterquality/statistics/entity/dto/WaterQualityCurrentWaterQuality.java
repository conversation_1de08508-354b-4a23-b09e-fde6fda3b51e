package com.smartPark.business.waterquality.statistics.entity.dto;

import lombok.Data;

/**
 * 3.1. 当前水质
 */
@Data
public class WaterQualityCurrentWaterQuality {
    /**
     * 属性identifier
     */
    private String identifier;

    /**
     * 属性名
     */
    private String attrName;
    /**
     * 属性值
     */
    private String value;

    /**
     * 单位
     */
    private String unit;


    /**
     * 等级
     */
    private Integer grade;

    public WaterQualityCurrentWaterQuality() {
    }

    public WaterQualityCurrentWaterQuality(String identifier, String attrName, String value, String unit, Integer grade) {
        this.identifier = identifier;
        this.attrName = attrName;
        this.value = value;
        this.unit = unit;
        this.grade = grade;
    }
}
