package com.smartPark.business.waterquality.statistics.service.impl;

import cn.hutool.core.date.DateUnit;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.smartPark.business.waterquality.device.entity.WaterQualityDevice;
import com.smartPark.business.waterquality.device.service.WaterQualityDeviceService;
import com.smartPark.business.waterquality.statistics.entity.WaterQualityGradeUpToStandard;
import com.smartPark.business.waterquality.statistics.entity.dto.*;
import com.smartPark.business.waterquality.statistics.service.WaterQualityEnvAnalysisService;
import com.smartPark.business.waterquality.statistics.service.WaterQualityGradeLineChartService;
import com.smartPark.business.waterquality.statistics.service.WaterQualityRankItemsOverPollutantService;
import com.smartPark.business.waterquality.statistics.util.QualityGradeUtil;
import com.smartPark.common.device.service.DeviceStatusService;
import com.smartPark.common.device.service.DeviceUnitPropertyService;
import com.smartPark.common.entity.device.DeviceStatus;
import com.smartPark.common.entity.device.DeviceUnitProperty;
import com.smartPark.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.smartPark.business.waterquality.statistics.util.QualityGradeUtil.getQualityGradeByItem;

@Service
@Slf4j
public class WaterQualityEnvAnalysisServiceImpl implements WaterQualityEnvAnalysisService {

    @Resource
    private DeviceStatusService deviceStatusService;
    @Resource
    private DeviceUnitPropertyService deviceUnitPropertyService;
    @Resource
    private WaterQualityGradeLineChartService waterQualityGradeLineChartService;
    @Resource
    private WaterQualityDeviceService waterQualityDeviceService;
    @Resource
    private WaterQualityGradeUpToStandardServiceImpl waterQualityGradeUpToStandardService;
    @Resource(name = "livableWaterQualityRankItemsOverPollutantService")
    private WaterQualityRankItemsOverPollutantService waterQualityRankItemsOverPollutantService;

    @Override
    public WaterQualityEnvCoreMetricDTO queryCoreMetrics(WaterQualityEnvQueryDTO waterQualityEnvQueryDTO) {
        //获取日期本月第一天
        Calendar calendar1 = Calendar.getInstance();
        calendar1.set(Calendar.DAY_OF_MONTH, 1);
        calendar1.set(Calendar.HOUR_OF_DAY, 0);
        calendar1.set(Calendar.MINUTE, 0);
        calendar1.set(Calendar.SECOND, 0);
        calendar1.set(Calendar.MILLISECOND, 0);
        Date start = calendar1.getTime();
        // 查询本月达标条数
        waterQualityEnvQueryDTO.setQueryStartTime(DateUtil.getYYYYMMDDHHMMSSDate(start));
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.set(Calendar.HOUR_OF_DAY, 0);
        calendarEnd.set(Calendar.MINUTE, 0);
        calendarEnd.set(Calendar.SECOND, 0);
        calendarEnd.set(Calendar.MILLISECOND, 0);
        Date end = calendarEnd.getTime();
        waterQualityEnvQueryDTO.setQueryEndTime(DateUtil.getYYYYMMDDHHMMSSDate(end));
        long upToStandardCount = waterQualityGradeUpToStandardService.queryUpToStandardCount(waterQualityEnvQueryDTO);
        //hutool 获取本月的总天数
        long monthDays = cn.hutool.core.date.DateUtil.between(start, end, DateUnit.DAY);
        // 本月达标率 保留4位小数
        double upToStandardRate = BigDecimal.valueOf(1.0 * upToStandardCount).divide(BigDecimal.valueOf(monthDays), 4, RoundingMode.HALF_UP).doubleValue();
        // 如法炮制计算本年的达标率
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND,0);
        start = calendar.getTime(); // 本年第一天
        waterQualityEnvQueryDTO.setQueryStartTime(DateUtil.getYYYYMMDDHHMMSSDate(start));
        calendar.add(Calendar.YEAR, 1);
        waterQualityEnvQueryDTO.setQueryEndTime(DateUtil.getYYYYMMDDHHMMSSDate(end));
        long upToStandardCountYear = waterQualityGradeUpToStandardService.queryUpToStandardCount(waterQualityEnvQueryDTO);
        long yearDays = cn.hutool.core.date.DateUtil.between(start, end, DateUnit.DAY);
        double upToStandardRateYear = BigDecimal.valueOf(1.0 * upToStandardCountYear).divide(BigDecimal.valueOf(yearDays), 4, RoundingMode.HALF_UP).doubleValue();

        List<WaterQualityCurrentWaterQuality> currentList = queryCurrentWaterQuality(waterQualityEnvQueryDTO);
        Integer grade = QualityGradeUtil.getGrade(currentList);
        return new WaterQualityEnvCoreMetricDTO(grade, upToStandardCount, upToStandardRate, upToStandardCountYear, upToStandardRateYear,isUpStandard(grade,waterQualityEnvQueryDTO.getDeviceCode()));
    }

    /**
     * 根据设备以及等级，判断是否达标
     * @param grade
     * @param deviceCode
     * @return
     */
    private Boolean isUpStandard(Integer grade,String deviceCode) {
        WaterQualityDevice waterQualityDevice = waterQualityDeviceService.getOne(new QueryWrapper<WaterQualityDevice>().eq("device_code_", deviceCode));
        if (waterQualityDevice != null && waterQualityDevice.getQualityGrade() != null) {
            if (waterQualityDevice.getQualityGrade() < grade) {
                return false;
            } else {
                return true;
            }
        }
        return true;
    }


    //    COD，氨氮，总磷，总氮，溶解氧
    @Override
    public List<WaterQualityCurrentWaterQuality> queryCurrentWaterQuality(WaterQualityEnvQueryDTO waterQualityEnvQueryDTO) {
        Map<String, List<DeviceUnitProperty>> map = getPropCode2DeviceUnitPropertyListMap(waterQualityEnvQueryDTO);
        List<DeviceStatus> deviceStatus = deviceStatusService.queryDeviceStatus(waterQualityEnvQueryDTO.getDeviceCode());

        List<WaterQualityCurrentWaterQuality> list = new ArrayList<>();
        for (DeviceStatus status : deviceStatus) {
            String propCode = status.getProp();
            String value = status.getValue();
            Integer grade = null;
            try {
                grade = getQualityGradeByItem(Double.parseDouble(value), propCode);
            } catch (Exception e) {
                log.error("水质环境分析，获取水质等级失败，propCode:{}, value:{}", propCode, value);
            }
            String attrName = null;
            String unit = null;
            if (map != null && map.get(propCode) != null && map.get(propCode).size() > 0) {
                attrName = map.get(propCode).get(0).getPropName();
            }
            if (map != null && map.get(propCode) != null && map.get(propCode).size() > 0) {
                unit = map.get(propCode).get(0).getValueUnit();
            }
            list.add(new WaterQualityCurrentWaterQuality(propCode, attrName, value, unit, grade));
        }
        return list;
    }

    private Map<String, List<DeviceUnitProperty>> getPropCode2DeviceUnitPropertyListMap(WaterQualityEnvQueryDTO waterQualityEnvQueryDTO) {
        List<DeviceUnitProperty> pros = deviceUnitPropertyService.queryProperty(waterQualityEnvQueryDTO.getDeviceCode());
        //根据 propCode 转map
        Map<String, List<DeviceUnitProperty>> map = null;
        if (pros != null && pros.size() > 0) {
            map = pros.stream().collect(Collectors.groupingBy(DeviceUnitProperty::getPropCode));
        }
        return map;
    }

    @Override
    public List<WaterQualityRankItemsOverPollutantDTO> queryRankItemsOverPollutantStandard(WaterQualityEnvQueryDTO waterQualityEnvQueryDTO) {
        List<WaterQualityRankItemsOverPollutantDTO> res = waterQualityRankItemsOverPollutantService.queryRankItemsOverPollutantStandard(waterQualityEnvQueryDTO);
        Map<String, List<DeviceUnitProperty>> map = getPropCode2DeviceUnitPropertyListMap(waterQualityEnvQueryDTO);
        for (WaterQualityRankItemsOverPollutantDTO re : res) {
            String propCode = re.getAttrName();
            if (map != null && map.get(propCode) != null && map.get(propCode).size() > 0) {
                String attrName = map.get(propCode).get(0).getPropName();
                re.setAttrName(attrName);
            }
        }
        return res;
    }

    @Override
    public List<WaterQualityGradeLineChartDTO> queryWaterQualityGradeLineChart(WaterQualityEnvQueryDTO waterQualityEnvQueryDTO) {

        List<WaterQualityGradeLineChartDTO> list = waterQualityGradeLineChartService.queryWaterQualityGradeLineChart(waterQualityEnvQueryDTO);
        Map<String, List<DeviceUnitProperty>> map = getPropCode2DeviceUnitPropertyListMap(waterQualityEnvQueryDTO);

        if (list.size() > 0) {
            for (WaterQualityGradeLineChartDTO waterQualityGradeLineChartDTO : list) {
                List<WaterQualityCurrentWaterQuality> details = waterQualityGradeLineChartDTO.getDetails();
                if (details == null || details.size() == 0) {
                    continue;
                }
                for (WaterQualityCurrentWaterQuality detail : details) {
                    if (detail == null) {
                        continue;
                    }
                    String propCode = detail.getIdentifier();
                    if (map != null && map.get(propCode) != null && map.get(propCode).size() > 0) {
                        String attrName = map.get(propCode).get(0).getPropName();
                        detail.setAttrName(attrName);
                    }
                    if (map != null && map.get(propCode) != null && map.get(propCode).size() > 0) {
                        String unit = map.get(propCode).get(0).getValueUnit();
                        detail.setUnit(unit);
                    }
                }
            }
        }
        return list;

    }

    @Override
    public List<List<WaterQualityGradeDistributionDTO>> queryWaterQualityGradeDistribution(WaterQualityEnvQueryDTO waterQualityEnvQueryDTO) {
        //传参年份
        String yyyy = waterQualityEnvQueryDTO.getQueryEndTime();
        String before = String.valueOf(Integer.parseInt(yyyy) - 1);

        Date start = DateUtil.getDateFromFormat(yyyy, "yyyy");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        //往前推一年
        calendar.add(Calendar.YEAR, -1);
        //设置 十分秒 为0
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        start = calendar.getTime();
        //往后推2年
        calendar.add(Calendar.YEAR, 2);
        Date end = calendar.getTime();

        Map<String, List<Distribution>> map = new LinkedHashMap<>();
        Date start1 = new Date(start.getTime());
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(start1);
        Date end1 = new Date(end.getTime());
        // 将两年的日期顺序放入map2
        while (start1.getTime() < end1.getTime()) {
            String dateStr = DateUtil.getDateFromFormatStr(start1, "yyyy-MM");
            map.put(dateStr, Collections.emptyList());
            calendar2.add(Calendar.MONTH, 1);
            start1 = calendar2.getTime();
        }

        waterQualityEnvQueryDTO.setQueryStartTime(DateUtil.getDateFromFormatStr(start, "yyyy-MM-dd HH:mm:ss"));
        waterQualityEnvQueryDTO.setQueryEndTime(DateUtil.getDateFromFormatStr(end, "yyyy-MM-dd HH:mm:ss"));
        List<Distribution> list2 = waterQualityGradeLineChartService.queryWaterQualityGradeDistribution(waterQualityEnvQueryDTO);
        // 按照日期分组
        Map<String, List<Distribution>> map2 = list2.stream().collect(Collectors.groupingBy(Distribution::getDatetime));
        // 将map2的值放入map
        map2.forEach((k, v) -> {
            map.put(k, v);
        });

        List<WaterQualityGradeDistributionDTO> list0 = new ArrayList<>();
        List<WaterQualityGradeDistributionDTO> list1 = new ArrayList<>();
        map.forEach((k, v) -> {
            if (k.startsWith(before)) {
                list0.add(new WaterQualityGradeDistributionDTO(v, k));
            } else {
                list1.add(new WaterQualityGradeDistributionDTO(v, k));
            }
        });
        List<List<WaterQualityGradeDistributionDTO>> list = new ArrayList<>();
        list.add(list0);
        list.add(list1);
        return list;
    }


}
