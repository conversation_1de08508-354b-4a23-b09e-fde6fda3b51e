package com.smartPark.business.intelligentparking.device.service.impl;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.intelligentparking.device.entity.vo.MagneticDeviceAlarmVo;
import com.smartPark.business.intelligentparking.device.entity.vo.MagneticDeviceVo;
import com.smartPark.business.intelligentparking.device.excel.handler.MagneticDeviceAlarmHandler;
import com.smartPark.business.intelligentparking.device.mapper.MagneticDeviceAlarmMapper;
import com.smartPark.business.intelligentparking.device.service.MagneticDeviceAlarmService;
import com.smartPark.business.intelligentparking.device.service.MagneticDeviceService;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.constant.DeviceModelConstant;
import com.smartPark.common.security.context.BaseUserContextProducer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 地磁告警服务
 *
 * @since
 */
@Service
public class MagneticDeviceAlarmServiceImpl implements MagneticDeviceAlarmService {
    @Autowired
    private MagneticDeviceAlarmMapper magneticDeviceAlarmMapper;
    @Autowired
    private MagneticDeviceService magneticDeviceService;
    @Autowired
    private MagneticDeviceAlarmService magneticDeviceAlarmService;
    @Autowired
    private BaseUserContextProducer baseUserContextProducer;
    @Autowired
    private ExcelService excelService;

    /**
     * 根据区域查询告警情况
     *
     * @since
     */
    @Override
    public Map<String, Object> findAlarmStatistics(MagneticDeviceVo magneticDeviceVo) {
        Map<String, Object> map = new HashMap<>();
        //查询实时告警数目
        List<MagneticDeviceVo> magneticDeviceVos = this.getMagneticDeviceVos(magneticDeviceVo);
        long count = magneticDeviceVos.stream().filter(m -> Integer.valueOf(1).equals(m.getAlarmState())).count();
        map.put("alarm", count);
        RequestModel<MagneticDeviceAlarmVo> requestModel = new RequestModel<>();
        MagneticDeviceAlarmVo magneticDeviceAlarmVo = new MagneticDeviceAlarmVo();
        magneticDeviceAlarmVo.setAreaPaths(magneticDeviceVo.getAreaPaths());
        requestModel.setCustomQueryParams(magneticDeviceAlarmVo);
        requestModel.setPage(new Page(1, 5));
        IPage<MagneticDeviceAlarmVo> alarmVoIPage = magneticDeviceAlarmService.queryListByPage(requestModel);
        //查询告警总数量  20230407 修改为正常设备数
        long total = magneticDeviceVos.size();
        map.put("alarmTotal", total - count);
        //查询告警列表
        map.put("alarmList", alarmVoIPage.getRecords());
        return map;
    }

    /**
     * 根据条件，分页(不分页)查询
     *
     * @since
     */
    @Override
    public IPage<MagneticDeviceAlarmVo> queryListByPage(RequestModel<MagneticDeviceAlarmVo> requestModel) {
        Page page = requestModel.getPage();
        MagneticDeviceAlarmVo magneticDeviceAlarmVo = requestModel.getCustomQueryParams();
        //todo 暂定地磁告警为29
        magneticDeviceAlarmVo.setModel(String.valueOf(DeviceModelConstant.MAGNETIC_DEVICE));
        IPage<MagneticDeviceAlarmVo> magneticDeviceList = magneticDeviceAlarmMapper.findMagneticDeviceList(page, magneticDeviceAlarmVo);
        magneticDeviceList.getRecords().forEach(m -> {
            //区域范围
            if (StringUtils.isNotBlank(m.getAreaPath())) {
                m.setAreaPath(m.getAreaPath().replace("@", "/"));
            }
        });
        return magneticDeviceList;
    }

    /**
     * 根据设备编码查询地磁告警日志
     *
     * @since
     */
    @Override
    public List<MagneticDeviceAlarmVo> findAlarmByDeviceCode(String deviceCode) {
        List<MagneticDeviceAlarmVo> records = getMagneticDeviceAlarmVos((vo) -> vo.setDeviceCode(deviceCode));
        return records;
    }

    /**
     * 根据id查询单条设备告警详情
     *
     * @since
     */
    @Override
    public MagneticDeviceAlarmVo findById(Long id) {
        List<MagneticDeviceAlarmVo> records = getMagneticDeviceAlarmVos((vo) -> vo.setId(id));
        MagneticDeviceAlarmVo magneticDeviceAlarmVo = records.get(0);
        return magneticDeviceAlarmVo;
    }

    /**
     * 根据条件导出
     *
     * @since
     */
    @Override
    public Long export(MagneticDeviceAlarmVo magneticDeviceAlarmVo, HttpServletRequest request, HttpServletResponse response) {
        Long userId = baseUserContextProducer.getCurrent().getId();
        DataExportParam dataExportParam = new DataExportParam();
        dataExportParam.setParam(magneticDeviceAlarmVo);
        dataExportParam.setExportFileName("地磁告警");
        dataExportParam.setTenantCode("traffic");
        dataExportParam.setBusinessCode("magneticDeviceAlarm");
        dataExportParam.setCreateUserCode(userId.toString());
        Long taskId = excelService.doExport(dataExportParam, MagneticDeviceAlarmHandler.class);
        return taskId;
    }

    /**
     * 查询地磁设备
     *
     * @param magneticDeviceVo
     * @return
     */
    private List<MagneticDeviceVo> getMagneticDeviceVos(MagneticDeviceVo magneticDeviceVo) {
        //查询符合条件的设备
        RequestModel<MagneticDeviceVo> requestModel = new RequestModel<>();
        requestModel.setCustomQueryParams(magneticDeviceVo);
        requestModel.setPage(new Page(1, -1));
        IPage<MagneticDeviceVo> iPage = magneticDeviceService.queryListByPage(requestModel);
        List<MagneticDeviceVo> records = iPage.getRecords();
        return records;
    }

    /**
     * 不分页查询告警集合
     *
     * @param consumer 调用的地方自己设置值
     * @return
     */
    private List<MagneticDeviceAlarmVo> getMagneticDeviceAlarmVos(Consumer<MagneticDeviceAlarmVo> consumer) {
        RequestModel<MagneticDeviceAlarmVo> requestModel = new RequestModel<>();
        MagneticDeviceAlarmVo magneticDeviceAlarmVo = new MagneticDeviceAlarmVo();
        consumer.accept(magneticDeviceAlarmVo);
        requestModel.setCustomQueryParams(magneticDeviceAlarmVo);
        requestModel.setPage(new Page(0, -1));
        IPage<MagneticDeviceAlarmVo> alarmVoIPage = queryListByPage(requestModel);
        List<MagneticDeviceAlarmVo> records = alarmVoIPage.getRecords();
        return records;
    }
}
