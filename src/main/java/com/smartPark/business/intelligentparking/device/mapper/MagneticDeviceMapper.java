package com.smartPark.business.intelligentparking.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.intelligentparking.device.entity.MagneticDevice;
import com.smartPark.business.intelligentparking.device.entity.vo.MagneticDeviceVo;
import com.smartPark.business.intelligentparking.device.excel.model.MagneticDeviceExportModelDTO;
import com.smartPark.business.intelligentparking.lot.entity.ParkingLotFloor;
import com.smartPark.common.entity.deviceArea.DeviceArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 地磁表 Mapper 接口
 * </p>
 *
 * 
 *
 */
public interface MagneticDeviceMapper extends BaseMapper<MagneticDevice> {

    IPage<MagneticDeviceVo> queryListByPage(Page page, @Param("magneticDeviceVo") MagneticDeviceVo magneticDeviceVo);

    List<DeviceArea> getAreas();

    List<MagneticDevice> selectOnline();

    /**
     * 查询导出excel数据
     * @param magneticDeviceVo
     * @return
     */
    List<MagneticDeviceExportModelDTO> queryList4Export(@Param("magneticDeviceVo") MagneticDeviceVo magneticDeviceVo);

    IPage<MagneticDeviceVo> listForRelate(Page page, @Param("magneticDeviceVo") MagneticDeviceVo magneticDeviceVo);

    Set<ParkingLotFloor> relatedFloors();
}
