package com.smartPark.business.intelligentparking.overview.entity.dto;

import com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUsePeriod;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ParkingSpaceUsePeriod实体类DTO
 *
 * <AUTHOR>
 * @date 2023/05/11
 */

@Data
@Accessors(chain = true)
public class ParkingSpaceUsePeriodDTO extends ParkingSpaceUsePeriod {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public ParkingSpaceUsePeriodDTO(ParkingSpaceUsePeriod trafficParkingSpaceUsePeriod) {
        //this.setName(trafficParkingSpaceUsePeriod.getName());
    }
}
