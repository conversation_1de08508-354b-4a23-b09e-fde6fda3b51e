package com.smartPark.business.intelligentparking.overview.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.smartPark.common.exceptions.BusinessException;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.security.context.BaseUserContextProducer;


import com.smartPark.business.intelligentparking.overview.mapper.ParkingSpaceUseRankMapper;
import com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUseRank;
import com.smartPark.business.intelligentparking.overview.entity.dto.ParkingSpaceUseRankDTO;
import com.smartPark.business.intelligentparking.overview.service.ParkingSpaceUseRankService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * ParkingSpaceUseRank表服务实现类
 *
 * <AUTHOR>
 * @date 2023/05/11
 */
@Slf4j
@Service("trafficParkingSpaceUseRankService")
public class ParkingSpaceUseRankServiceImpl extends ServiceImpl
        <ParkingSpaceUseRankMapper, ParkingSpaceUseRank> implements ParkingSpaceUseRankService {
    @Resource
    private CommonService commonService;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(ParkingSpaceUseRank::getId, idList).update();
        return true;
    }


    @Override
    public boolean saveOne(ParkingSpaceUseRank trafficParkingSpaceUseRank) {
        commonService.setCreateAndModifyInfo(trafficParkingSpaceUseRank);

        validParamRequired(trafficParkingSpaceUseRank);
        validRepeat(trafficParkingSpaceUseRank);
        validParamFormat(trafficParkingSpaceUseRank);
        return save(trafficParkingSpaceUseRank);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(ParkingSpaceUseRank trafficParkingSpaceUseRank) {
        Assert.notNull(trafficParkingSpaceUseRank.getId(), "id不能为空");
        commonService.setModifyInfo(trafficParkingSpaceUseRank);

        validRepeat(trafficParkingSpaceUseRank);
        validParamFormat(trafficParkingSpaceUseRank);
        return updateById(trafficParkingSpaceUseRank);
    }

    @Override
    public IPage<ParkingSpaceUseRank> selectPage(Page page, ParkingSpaceUseRank trafficParkingSpaceUseRank) {
        return baseMapper.selectPage(page, trafficParkingSpaceUseRank);
    }

    @Override
    public void export(ParkingSpaceUseRank trafficParkingSpaceUseRank, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public List<ParkingSpaceUseRank> selectParkingSpaceUseRank(ParkingSpaceUseRank parkingSpaceUseRank) {
//        List<ParkingSpaceUseRank> list = new ArrayList<>();
//        for (int i = 0; i < 5; i++) {
//            list.add(new ParkingSpaceUseRank("停车场"+i,i*0.1));
//        }
        List<ParkingSpaceUseRank> list = baseMapper.selectParkingSpaceUseRank(parkingSpaceUseRank);
        return list;
    }

    @Override
    public ParkingSpaceUseRank getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(ParkingSpaceUseRank trafficParkingSpaceUseRank) {

        /* List<ParkingSpaceUseRank> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(ParkingSpaceUseRank::getDeviceCode, trafficParkingSpaceUseRank.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(trafficParkingSpaceUseRank.getId()) || !trafficParkingSpaceUseRank.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("名称重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(ParkingSpaceUseRank trafficParkingSpaceUseRank) {
        //Assert.notNull(trafficParkingSpaceUseRank, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(trafficParkingSpaceUseRank.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(ParkingSpaceUseRank trafficParkingSpaceUseRank) {
        //Assert.isTrue(trafficParkingSpaceUseRank.getName() == null || trafficParkingSpaceUseRank.getName().length() <= 50,
        //        "名称超长");
    }
}

