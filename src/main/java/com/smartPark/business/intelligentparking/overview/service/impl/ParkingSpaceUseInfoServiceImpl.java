package com.smartPark.business.intelligentparking.overview.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.intelligentparking.lot.api.ParkingServerApi;
import com.smartPark.business.intelligentparking.lot.api.entity.ParkingLotIInfoDTO;
import com.smartPark.business.intelligentparking.lot.api.entity.ParkingLotInfoDetail;
import com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUseInfo;
import com.smartPark.business.intelligentparking.overview.mapper.ParkingSpaceUseInfoMapper;
import com.smartPark.business.intelligentparking.overview.service.ParkingSpaceUseInfoService;
import com.smartPark.common.base.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * ParkingSpaceUseInfo表服务实现类
 *
 * <AUTHOR>
 * @date 2023/05/11
 */
@Slf4j
@Service("trafficParkingSpaceUseInfoService")
public class ParkingSpaceUseInfoServiceImpl extends ServiceImpl
        <ParkingSpaceUseInfoMapper, ParkingSpaceUseInfo> implements ParkingSpaceUseInfoService {
    @Resource
    private CommonService commonService;
    @Resource
    private ParkingServerApi parkingServerApi;

    @Value("${isTest:false}")
    private Boolean isTest;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(ParkingSpaceUseInfo::getId, idList).update();
        return true;
    }


    @Override
    public boolean saveOne(ParkingSpaceUseInfo trafficParkingSpaceUseInfo) {
        commonService.setCreateAndModifyInfo(trafficParkingSpaceUseInfo);

        validParamRequired(trafficParkingSpaceUseInfo);
        validRepeat(trafficParkingSpaceUseInfo);
        validParamFormat(trafficParkingSpaceUseInfo);
        return save(trafficParkingSpaceUseInfo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(ParkingSpaceUseInfo trafficParkingSpaceUseInfo) {
        Assert.notNull(trafficParkingSpaceUseInfo.getId(), "id不能为空");
        commonService.setModifyInfo(trafficParkingSpaceUseInfo);

        validRepeat(trafficParkingSpaceUseInfo);
        validParamFormat(trafficParkingSpaceUseInfo);
        return updateById(trafficParkingSpaceUseInfo);
    }

    @Override
    public IPage<ParkingSpaceUseInfo> selectPage(Page page, ParkingSpaceUseInfo trafficParkingSpaceUseInfo) {
        return baseMapper.selectPage(page, trafficParkingSpaceUseInfo);
    }

    @Override
    public void export(ParkingSpaceUseInfo trafficParkingSpaceUseInfo, HttpServletRequest request, HttpServletResponse
            response) {

    }

    @Override
    public ParkingSpaceUseInfo getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(ParkingSpaceUseInfo trafficParkingSpaceUseInfo) {

        /* List<ParkingSpaceUseInfo> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(ParkingSpaceUseInfo::getDeviceCode, trafficParkingSpaceUseInfo.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(trafficParkingSpaceUseInfo.getId()) || !trafficParkingSpaceUseInfo.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("名称重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(ParkingSpaceUseInfo trafficParkingSpaceUseInfo) {
        //Assert.notNull(trafficParkingSpaceUseInfo, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(trafficParkingSpaceUseInfo.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(ParkingSpaceUseInfo trafficParkingSpaceUseInfo) {
        //Assert.isTrue(trafficParkingSpaceUseInfo.getName() == null || trafficParkingSpaceUseInfo.getName().length() <= 50,
        //        "名称超长");
    }

    @Override
    public ParkingSpaceUseInfo selectParkingSpaceUseInfo(ParkingSpaceUseInfo parkingSpaceUseInfo) {
        //为了测试环境不报错，这里暂时加个开关，如果是测试环境，这里给个假数据
        if(Boolean.TRUE.equals(isTest)){
            if (parkingSpaceUseInfo.getParkingNum()==null){
                parkingSpaceUseInfo.setParkingNum(0);
            }
            if (parkingSpaceUseInfo.getParkingNum()==0){
                parkingSpaceUseInfo.setUsedNum(0);
            }else {
                Random random = new Random();
                parkingSpaceUseInfo.setUsedNum(parkingSpaceUseInfo.getParkingNum()- random.nextInt(parkingSpaceUseInfo.getParkingNum()));
            }
            return parkingSpaceUseInfo;
        }
        JSONObject parkingLotInfo = parkingServerApi.getParkingLotInfo();
        ParkingLotIInfoDTO res = JSONObject.toJavaObject(parkingLotInfo, ParkingLotIInfoDTO.class);
        Integer parkingNum = res.getParkingLotInfo().stream().mapToInt(ParkingLotInfoDetail::getTotalNum).sum();
        Integer usedNum = res.getParkingLotInfo().stream().mapToInt(ParkingLotInfoDetail::getTotalStopNum).sum();
        parkingSpaceUseInfo.setParkingNum(parkingNum);
        parkingSpaceUseInfo.setUsedNum(usedNum);
        return parkingSpaceUseInfo;
    }

    @Override
    public List<ParkingSpaceUseInfo> selectParkingSpaceUseInfos(List<Long> ids) {
        List<ParkingSpaceUseInfo> parkingSpaceUseInfoList = new ArrayList<>();
        for (Long id : ids) {
            ParkingSpaceUseInfo parkingSpaceUseInfo = new ParkingSpaceUseInfo(0,0, id);
            parkingSpaceUseInfo = selectParkingSpaceUseInfo(parkingSpaceUseInfo);
            parkingSpaceUseInfoList.add(parkingSpaceUseInfo);
        }
        return parkingSpaceUseInfoList;
    }
}

