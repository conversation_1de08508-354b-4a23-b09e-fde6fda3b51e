package com.smartPark.business.intelligentparking.overview.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.intelligentparking.lot.entity.CarInOutRecord;
import com.smartPark.business.intelligentparking.lot.entity.ParkingLot;
import com.smartPark.business.intelligentparking.lot.mapper.CarInOutRecordMapper;
import com.smartPark.business.intelligentparking.lot.service.ParkingLotService;
import com.smartPark.business.intelligentparking.overview.entity.dto.*;
import com.smartPark.business.intelligentparking.overview.mapper.ParkingSpaceUsePeriodMapper;
import com.smartPark.business.intelligentparking.overview.service.ParkingSpaceUsePeriodService;
import com.smartPark.business.intelligentparking.overview.service.ParkingStatisticsService;
import com.smartPark.common.async.AsyncUtil;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.utils.energy.EnergyDate;
import com.smartPark.common.utils.energy.EnergyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 停车统计分析
 */
@Service
public class ParkingStatisticsServiceImpl implements ParkingStatisticsService {
    @Autowired
    private ParkingLotService parkingLotService;
    @Autowired
    private CarInOutRecordMapper carInOutRecordMapper;
    @Autowired
    private ParkingSpaceUsePeriodMapper parkingSpaceUsePeriodMapper;
    @Autowired
    private ParkingSpaceUsePeriodService parkingSpaceUsePeriodService;

    /**
     * 汇总统计
     */
    @Override
    public List<Map<String, Object>> getSummary(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<Long> lotIds = parkingStatisticsQueryDTO.getLotIds();
        ParkingLot parkingLot = new ParkingLot();
        parkingLot.setIds(lotIds);
        parkingLot.setQueryParkingSpace(true);
        List<ParkingLot> parkingLotList = parkingLotService.selectPage(new Page<>(1, -1), parkingLot).getRecords();
        //停车场数
        Map<String,Object> lotMap = new HashMap<>();
        lotMap.put("sum",parkingLotList.size());
        int in = parkingLotList.stream().mapToInt(p -> null == p.getEntranceNum()?0:p.getEntranceNum()).sum();
        lotMap.put("in",in);
        int out = parkingLotList.stream().mapToInt(p -> null == p.getExitNum()?0:p.getExitNum()).sum();
        lotMap.put("out",out);
        mapList.add(lotMap);
        //车位总数
        Map<String,Object> carMap = new HashMap<>();
        int carSum = parkingLotList.stream().mapToInt(p -> null == p.getParkingNum()?0:p.getParkingNum()).sum();
        carMap.put("sum",carSum);
        Integer free = parkingLotList.stream().mapToInt(p -> null == p.getFreeParkingNum()?0:p.getFreeParkingNum()).sum();
        Integer occupyNum = carSum - free;
        carMap.put("occupy",occupyNum);
        carMap.put("free",free);
        carMap.put("part", EnergyUtils.partToString(EnergyUtils.calPart(occupyNum,carSum)));
        mapList.add(carMap);

        //查询去年和今年的停车数据
        Date today = new Date();
        Date yearTime = DateUtil.offset(today, DateField.YEAR,-1);
        Date begin = DateUtil.beginOfYear(yearTime);
        LambdaQueryWrapper<CarInOutRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CollectionUtil.isNotEmpty(lotIds),CarInOutRecord::getParkingLotId,lotIds);
        lambdaQueryWrapper.and(w->w.and(w1-> w1.gt(CarInOutRecord::getInTime, begin).le(CarInOutRecord::getInTime, today))
                        .or(w2 ->w2.gt(CarInOutRecord::getOutTime, begin).le(CarInOutRecord::getOutTime, today)));
        List<CarInOutRecord> carInOutRecords = carInOutRecordMapper.selectList(lambdaQueryWrapper);
        //今日车流量
        mapList.add(getCarFlow(1,carInOutRecords,today));
        //本月车流量
        mapList.add(getCarFlow(2,carInOutRecords,today));
        //今年车流量
        mapList.add(getCarFlow(3,carInOutRecords,today));
        //扩展道闸，进场车辆数、出场车辆数
        mapList.add(getParkInoutStatic(parkingLotList,carInOutRecords));
        return mapList;
    }

    private Map<String, Object> getParkInoutStatic(List<ParkingLot> parkingLotList, List<CarInOutRecord> carInOutRecords) {
        //查询道闸出入口
        Map<String, Object> map = new HashMap<>();
        //将停车场进口数与出口数之和相加作为道闸出入口数
        int sum = parkingLotList.stream().mapToInt(p -> {
            if (null == p.getEntranceNum()){
                p.setEntranceNum(0);
            }
            if (null == p.getExitNum()){
                p.setExitNum(0);
            }
            return p.getEntranceNum() + p.getExitNum();
        }).sum();
        map.put("gateSum",sum);
        //查询今日车辆进场数与车辆出场数
        Date startTime = DateUtil.beginOfDay(new Date());
        Date endTime = DateUtil.endOfDay(new Date());
        long in = carInOutRecords.stream().filter(c -> null != c.getInTime() && startTime.compareTo(c.getInTime()) < 1 && endTime.compareTo(c.getInTime()) > -1)
                .count();
        map.put("in",in);
        long out = carInOutRecords.stream().filter(c -> null != c.getOutTime() && startTime.compareTo(c.getOutTime()) < 1 && endTime.compareTo(c.getOutTime()) > -1)
                .count();
        map.put("out",out);
        return map;
    }

    /**
     * 停车位曲线
     */
    @Override
    public List<ParkingStatisticsDTO> parkingSpace(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        Integer dateType = parkingStatisticsQueryDTO.getDateType();
        Date startTime = parkingStatisticsQueryDTO.getStartTime();
        Date endTime = parkingStatisticsQueryDTO.getEndTime();
//        todo 暂时不实时计算
//        //当天的实时计算
//        if (Integer.valueOf(1).equals(dataType) && DateUtil.isSameDay(new Date(), endTime)){
//            //今天的
//        }else {
//            //按时间分组统计停车位使用情况
//            List<ParkingStatisticsDTO> parkingStatisticsDTOS = parkingSpaceUsePeriodMapper.countGroupByTime(parkingStatisticsQueryDTO);
//        }
        List<ParkingStatisticsDTO> parkingStatisticsDTOS = parkingSpaceUsePeriodMapper.countGroupByTime(parkingStatisticsQueryDTO);
        List<DateTime> dateTimes = EnergyUtils.rangeBqToList(startTime, endTime, dateType);
        List<ParkingStatisticsDTO> newList = new ArrayList<>();
        dateTimes.forEach(d ->{
            String timeToStr = EnergyUtils.timeToStr(d, dateType);
            ParkingStatisticsDTO statisticsDTO = parkingStatisticsDTOS.stream().filter(p -> timeToStr.equals(p.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            statisticsDTO.setTimeStr(timeToStr);
            newList.add(statisticsDTO);
        });
        return newList;
    }

    /**
     * 停车率排名
     */
    @Override
    public List<ParkingStatisticsRankDTO> parkingRank(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        List<ParkingStatisticsRankDTO> rankDTOList = parkingSpaceUsePeriodMapper.parkingRank(parkingStatisticsQueryDTO);
        return rankDTOList;
    }

    /**
     * 车流量曲线
     */
    @Override
    public List<ParkingStatisticsDTO> carFlow(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        Date startTime = parkingStatisticsQueryDTO.getStartTime();
        Date endTime = parkingStatisticsQueryDTO.getEndTime();
        Integer dateType = parkingStatisticsQueryDTO.getDateType();
        //进入车流量
        List<ParkingStatisticsDTO> inCar = carInOutRecordMapper.inCarFlow(parkingStatisticsQueryDTO);
        //出车流量
        List<ParkingStatisticsDTO> outCar = carInOutRecordMapper.outCarFlow(parkingStatisticsQueryDTO);
        List<DateTime> dateTimes = EnergyUtils.rangeBqToList(startTime, endTime, dateType);
        List<ParkingStatisticsDTO> newList = new ArrayList<>();
        dateTimes.forEach(d ->{
            ParkingStatisticsDTO statisticsDTO = new ParkingStatisticsDTO();
            String timeToStr = EnergyUtils.timeToStr(d, dateType);
            ParkingStatisticsDTO in = inCar.stream().filter(p -> timeToStr.equals(p.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            ParkingStatisticsDTO out = outCar.stream().filter(p -> timeToStr.equals(p.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            statisticsDTO.setTimeStr(timeToStr);
            statisticsDTO.setInCarNum(in.getInCarNum());
            statisticsDTO.setOutCarNum(out.getOutCarNum());
            newList.add(statisticsDTO);
        });
        return newList;
    }

    /**
     * 车流量排名
     */
    @Override
    public List<ParkingStatisticsRankDTO> carFlowRank(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        List<ParkingStatisticsRankDTO> inCar = carInOutRecordMapper.inCarFlowByLot(parkingStatisticsQueryDTO);
        List<ParkingStatisticsRankDTO> outCar = carInOutRecordMapper.outCarFlowByLot(parkingStatisticsQueryDTO);
        //合并
        inCar.forEach(in ->{
            ParkingStatisticsRankDTO out = outCar.stream().filter(o -> in.getLotId().equals(o.getLotId())).findFirst().orElse(null);
            if (null != out){
                in.setCarNum(in.getCarNum()+out.getCarNum());
            }
        });
        List<Long> lotIds = inCar.stream().map(in -> in.getLotId()).collect(Collectors.toList());
        //过滤进无，出有的
        outCar.stream().filter(o->!lotIds.contains(o.getLotId())).forEach(o ->{
            inCar.add(o);
        });
        //排序
        List<ParkingStatisticsRankDTO> rankDTOList = inCar.stream().sorted(Comparator.comparing(ParkingStatisticsRankDTO::getCarNum).reversed()).collect(Collectors.toList());
        return rankDTOList.size()>5?rankDTOList.subList(0,5):rankDTOList;
    }

    /**
     * 停车时长分布
     */
    @Override
    public List<PartCountDTO> parkingTime(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        List<PartCountDTO> parkingList = PartCountDTO.getParkingList();
        //按条件查询停车数据
//        // 这个是每小时停留时间
//        List<PartCountDTO> partCountDTOS = carInOutRecordMapper.computeTimeGroupByCar(parkingStatisticsQueryDTO);

        // 这个是出库时间在查询范围
        List<PartCountDTO> partCountDTOS = carInOutRecordMapper.computeTimeGroupByTime(parkingStatisticsQueryDTO);
        int sum = partCountDTOS.size();
        //小于30分钟
        long num1 = partCountDTOS.stream().filter(p -> null != p.getPart() && 0.5 > p.getPart()).count();
        parkingList.get(0).setNum((int)num1);
        parkingList.get(0).setPart(EnergyUtils.calPart((int)num1,sum));
        //30-60min
        long num2 = partCountDTOS.stream().filter(p ->null != p.getPart() &&  0.5 <= p.getPart() && 1>p.getPart()).count();
        parkingList.get(1).setNum((int)num2);
        parkingList.get(1).setPart(EnergyUtils.calPart((int)num2,sum));
        //1-3h
        long num3 = partCountDTOS.stream().filter(p ->null != p.getPart() &&  1 <= p.getPart() && 3>p.getPart()).count();
        parkingList.get(2).setNum((int)num3);
        parkingList.get(2).setPart(EnergyUtils.calPart((int)num3,sum));
        //3-6h
        long num4 = partCountDTOS.stream().filter(p ->null != p.getPart() &&  3 <= p.getPart() && 6>p.getPart()).count();
        parkingList.get(3).setNum((int)num4);
        parkingList.get(3).setPart(EnergyUtils.calPart((int)num4,sum));
        //6-10h
        long num5 = partCountDTOS.stream().filter(p ->null != p.getPart() &&  6 <= p.getPart() && 10>p.getPart()).count();
        parkingList.get(4).setNum((int)num5);
        parkingList.get(4).setPart(EnergyUtils.calPart((int)num5,sum));
        //>10h
        long num6 = partCountDTOS.stream().filter(p ->null != p.getPart() &&  10 <= p.getPart()).count();
        parkingList.get(5).setNum((int)num6);
        parkingList.get(5).setPart(EnergyUtils.calPart((int)num6,sum));
        return parkingList;
    }

    /**
     * 停留时长曲线(出库时间)
     * @param parkingStatisticsQueryDTO
     * @return
     */
    @Override
    public List<ParkingStatisticsDTO> avgParkingTime(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        Date startTime = parkingStatisticsQueryDTO.getStartTime();
        Date endTime = parkingStatisticsQueryDTO.getEndTime();
        Integer dateType = parkingStatisticsQueryDTO.getDateType();
        List<CarInOutRecord> carInOutRecords = carInOutRecordMapper.computeTimeByTime(parkingStatisticsQueryDTO);
        List<DateTime> dateTimes = EnergyUtils.rangeBqToList(startTime, endTime, dateType);
        List<ParkingStatisticsDTO> parkingStatisticsDTOS = new ArrayList<>();
        dateTimes.forEach(d->{
            ParkingStatisticsDTO parkingStatisticsDTO = new ParkingStatisticsDTO();
            String timeToStr = EnergyUtils.timeToStr(d, dateType);
            parkingStatisticsDTO.setTimeStr(EnergyUtils.timeToStr(d,dateType));
            List<CarInOutRecord> records = carInOutRecords.stream().filter(c -> timeToStr.equals(EnergyUtils.timeToStr(c.getOutTime(), dateType)))
                    .collect(Collectors.toList());
            //计算停车时长
            Double aDouble = getAvgParkHour(records);
            parkingStatisticsDTO.setParkingHour(aDouble);
            parkingStatisticsDTOS.add(parkingStatisticsDTO);
        });
        return parkingStatisticsDTOS;
    }

    /**
     * 计算平均停车时长
     * @param records
     * @return
     */
    private static Double getAvgParkHour(List<CarInOutRecord> records) {
        Double aDouble = 0d;
        if (CollectionUtil.isNotEmpty(records)){
            records.forEach(r->{
                r.setHour(Double.valueOf(DateUtil.between(r.getInTime(),r.getOutTime(),DateUnit.SECOND))/3600);
            });
            //求平均时长
            double sum = records.stream().mapToDouble(r -> r.getHour()).sum();
            aDouble = EnergyUtils.formatDouble(sum / Double.valueOf(records.size()));
        }
        return aDouble;
    }

//    /**
//     * 平均停车时长曲线（停留时间）
//     */
//    @Override
//    public List<ParkingStatisticsDTO> avgParkingTime(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
//        Date startTime = parkingStatisticsQueryDTO.getStartTime();
//        Date endTime = parkingStatisticsQueryDTO.getEndTime();
//        Integer dataType = parkingStatisticsQueryDTO.getDataType();
//        List<CarInOutRecord> carInOutRecords = carInOutRecordMapper.computeTime(parkingStatisticsQueryDTO);
//        List<EnergyDate> energyDateList = EnergyUtils.rangeToList(startTime, endTime, dataType);
//        List<ParkingStatisticsDTO> parkingStatisticsDTOS = new ArrayList<>();
//        energyDateList.forEach(e->{
//            ParkingStatisticsDTO parkingStatisticsDTO = new ParkingStatisticsDTO();
//            parkingStatisticsDTO.setTimeStr(EnergyUtils.timeToStr(e.getBqStartTime(),dataType));
//            List<CarInOutRecord> records = carInOutRecords.stream().filter(c -> !(c.getOutTime().before(e.getBqStartTime()) || c.getInTime().after(e.getBqEndTime())))
//                    .collect(Collectors.toList());
//            //计算停车时长
//            if (CollectionUtil.isNotEmpty(records)){
//                records.forEach(r->{
//                    if (r.getInTime().before(e.getBqStartTime())){
//                        r.setInTime(e.getBqStartTime());
//                    }
//                    if (r.getOutTime().after(e.getBqEndTime())){
//                        r.setOutTime(e.getBqEndTime());
//                    }
//                    r.setHour(Double.valueOf(DateUtil.between(r.getInTime(),r.getOutTime(),DateUnit.SECOND))/3600);
//                });
//                //求平均时长
//                int size = records.stream().map(r -> r.getCarNo()).collect(Collectors.toSet()).size();
//                double sum = records.stream().mapToDouble(r -> r.getHour()).sum();
//                parkingStatisticsDTO.setParkingHour(EnergyUtils.formatDouble(sum/Double.valueOf(size)));
//                parkingStatisticsDTOS.add(parkingStatisticsDTO);
//            }
//        });
//        return parkingStatisticsDTOS;
//    }

    /**
     * 车流量明细
     */
    @Override
    public ParkingCountPageDTO countTable(RequestModel<ParkingStatisticsQueryDTO> requestModel) {
        Page page = requestModel.getPage();
        ParkingStatisticsQueryDTO parkingStatisticsQueryDTO = requestModel.getCustomQueryParams();
        Integer dateType = parkingStatisticsQueryDTO.getDateType();
        Date startTime = parkingStatisticsQueryDTO.getStartTime();
        Date endTime = parkingStatisticsQueryDTO.getEndTime();

        EnergyDate energyDate = new EnergyDate(startTime,endTime);
        //查询数据
        //本期
        //车流量
        List<ParkingStatisticsDTO> bqCarFlow = carFlow(parkingStatisticsQueryDTO);
        //停车率
        List<ParkingStatisticsDTO> bqParkingStatisticsDTOS = parkingSpace(parkingStatisticsQueryDTO);
        //停车时长
        List<ParkingStatisticsDTO> bqParkHourDTOS = avgParkingTime(parkingStatisticsQueryDTO);

        //同期
        ParkingStatisticsQueryDTO tqQueryDTO = BeanUtil.toBean(parkingStatisticsQueryDTO, ParkingStatisticsQueryDTO.class);
        tqQueryDTO.setStartTime(energyDate.getTqStartTime());
        tqQueryDTO.setEndTime(energyDate.getTqEndTime());
        //车流量
        List<ParkingStatisticsDTO> tqCarFlow = carFlow(tqQueryDTO);
        //停车率
        List<ParkingStatisticsDTO> tqParkingStatisticsDTOS = parkingSpace(tqQueryDTO);
        //停车时长
        List<ParkingStatisticsDTO> tqParkHourDTOS = avgParkingTime(tqQueryDTO);

        //汇总
        ParkingStatisticsCompDTO statisticsCompDTO = new ParkingStatisticsCompDTO();
        statisticsCompDTO.setBqInCarNum(bqCarFlow.stream().mapToLong(p -> p.getInCarNum()).sum());
        statisticsCompDTO.setBqOutCarNum(bqCarFlow.stream().mapToLong(p -> p.getOutCarNum()).sum());
        statisticsCompDTO.setTqInCarNum(tqCarFlow.stream().mapToLong(p -> p.getInCarNum()).sum());
        statisticsCompDTO.setTqOutCarNum(tqCarFlow.stream().mapToLong(p -> p.getOutCarNum()).sum());

        //停车率平均值
        Double bqParkRadio = parkingSpaceUsePeriodMapper.countByTime(parkingStatisticsQueryDTO);
        statisticsCompDTO.setBqParkRadio(EnergyUtils.formatDouble(null != bqParkRadio?bqParkRadio:0));
        Double tqParkRadio = parkingSpaceUsePeriodMapper.countByTime(tqQueryDTO);
        statisticsCompDTO.setTqParkRadio(EnergyUtils.formatDouble(null != tqParkRadio?tqParkRadio:0));

        //停车时长平均值
        List<PartCountDTO> bqPartCountDTOS = carInOutRecordMapper.computeTimeGroupByTime(parkingStatisticsQueryDTO);
        bqPartCountDTOS.stream().filter(b->null != b.getPart()).mapToDouble(b->b.getPart()).average().ifPresent(a->statisticsCompDTO.setBqParkingHour(EnergyUtils.formatDouble(a)));
        List<PartCountDTO> tqPartCountDTOS = carInOutRecordMapper.computeTimeGroupByTime(tqQueryDTO);
        tqPartCountDTOS.stream().filter(b->null != b.getPart()).mapToDouble(b->b.getPart()).average().ifPresent(a->statisticsCompDTO.setTqParkingHour(EnergyUtils.formatDouble(a)));

        //列表
        List<EnergyDate> energyDateList = EnergyUtils.rangeToList(startTime, endTime, dateType);
        Collections.reverse(energyDateList);
        IPage toPage = EnergyUtils.toPage(energyDateList, page);
        List<ParkingStatisticsCompDTO> list = new ArrayList<>();
        toPage.getRecords().forEach(en ->{
            EnergyDate t = (EnergyDate)en;
            ParkingStatisticsCompDTO compDTO = new ParkingStatisticsCompDTO();
            String bqTimeToStr = EnergyUtils.timeToStr(t.getBqStartTime(), dateType);
            String tqTimeToStr = EnergyUtils.timeToStr(t.getTqStartTime(), dateType);
            compDTO.setTimeStr(bqTimeToStr);
            //车流量
            ParkingStatisticsDTO bqStatisticsDTO = bqCarFlow.stream().filter(b -> bqTimeToStr.equals(b.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            compDTO.setBqInCarNum(bqStatisticsDTO.getInCarNum());
            compDTO.setBqOutCarNum(bqStatisticsDTO.getOutCarNum());
            ParkingStatisticsDTO tqStatisticsDTO = tqCarFlow.stream().filter(b -> tqTimeToStr.equals(b.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            compDTO.setTqInCarNum(tqStatisticsDTO.getInCarNum());
            compDTO.setTqOutCarNum(tqStatisticsDTO.getOutCarNum());
            //停车
            ParkingStatisticsDTO bqParkingStatisticsDTO = bqParkingStatisticsDTOS.stream().filter(b -> bqTimeToStr.equals(b.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            compDTO.setBqInCarNum(bqStatisticsDTO.getInCarNum());
            compDTO.setBqOutCarNum(bqStatisticsDTO.getOutCarNum());
            ParkingStatisticsDTO tqParkingStatisticsDTO = tqParkingStatisticsDTOS.stream().filter(b -> tqTimeToStr.equals(b.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            compDTO.setBqParkSum(bqParkingStatisticsDTO.getParkSum());
            compDTO.setBqParkUseNum(bqParkingStatisticsDTO.getParkUseNum());
            compDTO.setBqParkRadio(bqParkingStatisticsDTO.getParkRadio());
            compDTO.setTqParkSum(tqParkingStatisticsDTO.getParkSum());
            compDTO.setTqParkUseNum(tqParkingStatisticsDTO.getParkUseNum());
            compDTO.setTqParkRadio(tqParkingStatisticsDTO.getParkRadio());
            //时长
            ParkingStatisticsDTO bqHours = bqParkHourDTOS.stream().filter(b -> bqTimeToStr.equals(b.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            compDTO.setBqParkingHour(bqHours.getParkingHour());
            ParkingStatisticsDTO tqHours = tqParkHourDTOS.stream().filter(b -> tqTimeToStr.equals(b.getTimeStr())).findFirst().orElse(new ParkingStatisticsDTO());
            compDTO.setTqParkingHour(tqHours.getParkingHour());
            list.add(compDTO);
        });
        toPage.setRecords(list);
        ParkingCountPageDTO pageDTO = BeanUtil.toBean(toPage, ParkingCountPageDTO.class);
        pageDTO.setEnergyDate(energyDate);
        pageDTO.setCountData(statisticsCompDTO);
        return pageDTO;
    }

    @Override
    public Map<String, Object> countTable4Export(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO, String key) {
        RequestModel<ParkingStatisticsQueryDTO> requestModel = new RequestModel<>();
        requestModel.setCustomQueryParams(parkingStatisticsQueryDTO);
        requestModel.setPage(new Page<>(1,1000000000));
        ParkingCountPageDTO countPageDTO = this.countTable(requestModel);
        //假进度条
        AsyncUtil.setDone(key,30);
        EnergyDate energyDate = countPageDTO.getEnergyDate();
        Map<String,Object> map = new HashMap<>();
        ParkingStatisticsCompDTO statisticsCompDTO = (ParkingStatisticsCompDTO)countPageDTO.getCountData();
        statisticsCompDTO.setCarNumCompStr(EnergyUtils.setCom4Export(statisticsCompDTO.getCarNumCompStr()));
        statisticsCompDTO.setParkRadioCompStr(EnergyUtils.setCom4Export(statisticsCompDTO.getParkRadioCompStr()));
        statisticsCompDTO.setParkingHourCompStr(EnergyUtils.setCom4Export(statisticsCompDTO.getParkingHourCompStr()));
        countPageDTO.setCountData(statisticsCompDTO);

        //列表数据
        countPageDTO.getRecords().forEach(r ->{
            ParkingStatisticsCompDTO son = (ParkingStatisticsCompDTO)r;
            son.setCarNumCompStr(EnergyUtils.setCom4Export(son.getCarNumCompStr()));
            son.setParkRadioCompStr(EnergyUtils.setCom4Export(son.getParkRadioCompStr()));
            son.setParkingHourCompStr(EnergyUtils.setCom4Export(son.getParkingHourCompStr()));
        });
        //时间转义
        map.put("p",countPageDTO);
        getExcel(energyDate,parkingStatisticsQueryDTO,map,"停车统计分析","parking",key);
        //假进度条
        AsyncUtil.setDone(key,80);
        return map;
    }

    /**
     * 停车占用排名
     */
    @Override
    public List<ParkingStatisticsRankDTO> occupyRank() {
        List<ParkingLot> parkingLotList = parkingLotService.selectPage(new Page<>(1, -1), new ParkingLot()).getRecords();
        List<ParkingStatisticsRankDTO> list = carInOutRecordMapper.occupy4LotIds();
        List<ParkingStatisticsRankDTO> result = new ArrayList<>();
        parkingLotList.forEach(p ->{
            ParkingStatisticsRankDTO parkingStatisticsRankDTO = new ParkingStatisticsRankDTO();
            parkingStatisticsRankDTO.setLotId(p.getId());
            parkingStatisticsRankDTO.setSum(p.getParkingNum());
            parkingStatisticsRankDTO.setLotName(p.getLotName());
            //占用车位数
            ParkingStatisticsRankDTO statisticsRankDTO = list.stream().filter(l -> p.getId().equals(l.getLotId())).findFirst().orElse(null);
            parkingStatisticsRankDTO.setOccupy(null == statisticsRankDTO?0:statisticsRankDTO.getOccupy());
            parkingStatisticsRankDTO.setFree(parkingStatisticsRankDTO.getSum()-parkingStatisticsRankDTO.getOccupy());
            parkingStatisticsRankDTO.setPart(EnergyUtils.calPart(parkingStatisticsRankDTO.getOccupy(),parkingStatisticsRankDTO.getSum()));
            result.add(parkingStatisticsRankDTO);
        });
        //排序
        result.sort(Comparator.comparing(ParkingStatisticsRankDTO::getPart).reversed());
        return result;
    }

    @Override
    public List<ParkingStatisticsDTO> getDayParkingData(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        //设置开始结束时间
        if (parkingStatisticsQueryDTO.getStatisticTime()!=null){
            parkingStatisticsQueryDTO.setStartTime(DateUtil.beginOfDay(parkingStatisticsQueryDTO.getStatisticTime()));
            parkingStatisticsQueryDTO.setEndTime(DateUtil.endOfDay(parkingStatisticsQueryDTO.getStatisticTime()));
        }
        parkingStatisticsQueryDTO.setDateType(2);
        return getParkingStatisticsDTOS(parkingStatisticsQueryDTO);
    }

    @Override
    public List<ParkingStatisticsDTO> getCurrentMonthParkingData(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        //设置开始结束时间
        parkingStatisticsQueryDTO.setStartTime(DateUtil.beginOfMonth(parkingStatisticsQueryDTO.getStatisticTime()==null?new Date():parkingStatisticsQueryDTO.getStatisticTime()));
        parkingStatisticsQueryDTO.setEndTime(DateUtil.endOfMonth(parkingStatisticsQueryDTO.getStatisticTime()==null?new Date():parkingStatisticsQueryDTO.getStatisticTime()));
        parkingStatisticsQueryDTO.setDateType(3);
        return getParkingStatisticsDTOS(parkingStatisticsQueryDTO);
    }

    /**
     *
     * @param parkingStatisticsQueryDTO
     * @return
     */
    private List<ParkingStatisticsDTO> getParkingStatisticsDTOS(ParkingStatisticsQueryDTO parkingStatisticsQueryDTO) {
        List<ParkingStatisticsDTO> parkingStatisticsDTOS = parkingSpaceUsePeriodMapper.countGroupByTimeAndParkId(parkingStatisticsQueryDTO);
        //填充停车场名称
        parkingStatisticsDTOS.forEach(p ->{
            ParkingLot parkingLot = parkingLotService.getOneById(p.getParkingLotId());
            if (parkingLot!=null){
                p.setParkName(parkingLot.getLotName());
            }
            //如果parkRadio不为空，则四舍五入保留2位小数
            p.setParkRadio(null==p.getParkRadio()?null:new BigDecimal(p.getParkRadio()).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
        });
        return parkingStatisticsDTOS;
    }

    /**
     * 生成excel
     * @param key
     * @return
     */
    private void getExcel(EnergyDate energyDate,ParkingStatisticsQueryDTO dto,Map<String,Object> map,String excelName, String tempName,String key){
        //处理时间
        //本期
        Integer dateType = dto.getDateType();
        if (Integer.valueOf(1).equals(dateType)){
            dateType = 2;
        }
        map.put("bqData",EnergyUtils.timeToStr(energyDate.getBqStartTime(),dateType)+"~"+EnergyUtils.timeToStr(energyDate.getBqEndTime(),dateType));
        //同期
        map.put("tqData",EnergyUtils.timeToStr(energyDate.getTqStartTime(),dateType)+"~"+EnergyUtils.timeToStr(energyDate.getTqEndTime(),dateType));
        //上期
        map.put("sqData",EnergyUtils.timeToStr(energyDate.getSqStartTime(),dateType)+"~"+EnergyUtils.timeToStr(energyDate.getSqEndTime(),dateType));

        String[] units = {"","","天","月","年"};
        //路径
        map.put("path",tempName+".xlsx");
        //文件名
        map.put("fileName",excelName);
    }

    /**
     * 计算车流量
     * @param type 1天 2月 3年
     * @param carInOutRecords
     * @param date
     */
    private Map<String,Object> getCarFlow(int type, List<CarInOutRecord> carInOutRecords, Date date) {
        //本期
        EnergyDate energyDate = EnergyUtils.getDateByType(date, type == 1?type:type+1);
        long bq = getFlow(carInOutRecords, energyDate.getBqStartTime(), energyDate.getBqEndTime());
        long sq = getFlow(carInOutRecords, energyDate.getSqStartTime(), energyDate.getSqEndTime());
        Map<String,Object> map = new HashMap<>();
        map.put("type",type);
        map.put("bq",bq);
        map.put("sq",sq);
        map.put("radio",EnergyUtils.calComRatio(bq,sq));
        return map;
    }

    /**
     *
     * @param carInOutRecords
     * @param startTime
     * @param endTime
     * @return
     */
    private long getFlow(List<CarInOutRecord> carInOutRecords, Date startTime, Date endTime) {
        //进
        long in = carInOutRecords.stream().filter(c -> null != c.getInTime() && startTime.compareTo(c.getInTime()) < 1 && endTime.compareTo(c.getInTime()) > -1)
                .count();
        //出
        long out = carInOutRecords.stream().filter(c ->null != c.getOutTime() &&  startTime.compareTo(c.getOutTime()) < 1 && endTime.compareTo(c.getOutTime()) > -1)
                .count();
        return in+out;
    }
}
