package com.smartPark.business.intelligentparking.lot.api.entity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/10
 * @description
 */
public class ParkingLotIInfoDTO extends ParkingLotRes{
  /**
   * 总车位
   */
  private Integer totalNum;
  /**
   * 总已停车位
   */
  private Integer totalStopNum;
  /**
   * 总剩余车位
   */
  private Integer totalRemainNum;
  /**
   * 车场ID
   */
  private String parkID;
  /**
   * 车场名称
   */
  private String parkName;
  /**
   * 车场收费标准
   */
  private String chargeRuleDesc;
  /**
   * 区域余位信息
   */
  private List<ParkingLotInfoDetail> parkingLotInfo;

  public String getChargeRuleDesc() {
    return this.chargeRuleDesc;
  }

  public void setChargeRuleDesc(final String chargeRuleDesc) {
    this.chargeRuleDesc = chargeRuleDesc;
  }

  public String getParkID() {
    return this.parkID;
  }

  public void setParkID(final String parkID) {
    this.parkID = parkID;
  }

  public List<ParkingLotInfoDetail> getParkingLotInfo() {
    return this.parkingLotInfo;
  }

  public void setParkingLotInfo(
      final List<ParkingLotInfoDetail> parkingLotInfo) {
    this.parkingLotInfo = parkingLotInfo;
  }

  public String getParkName() {
    return this.parkName;
  }

  public void setParkName(final String parkName) {
    this.parkName = parkName;
  }

  public Integer getTotalNum() {
    return this.totalNum;
  }

  public void setTotalNum(final Integer totalNum) {
    this.totalNum = totalNum;
  }

  public Integer getTotalRemainNum() {
    return this.totalRemainNum;
  }

  public void setTotalRemainNum(final Integer totalRemainNum) {
    this.totalRemainNum = totalRemainNum;
  }

  public Integer getTotalStopNum() {
    return this.totalStopNum;
  }

  public void setTotalStopNum(final Integer totalStopNum) {
    this.totalStopNum = totalStopNum;
  }
}
