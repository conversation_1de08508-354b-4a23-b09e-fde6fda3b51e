package com.smartPark.business.intelligentparking.lot.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.intelligentparking.lot.entity.ParkingCost;
import com.smartPark.business.intelligentparking.lot.mapper.ParkingCostMapper;
import com.smartPark.business.intelligentparking.lot.service.ParkingCostService;
import com.smartPark.business.intelligentparking.lot.vo.ParkingCostVO;
import com.smartPark.common.base.model.RequestModel;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.security.entity.BaseappUser;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class ParkingCostServiceImpl extends ServiceImpl<ParkingCostMapper, ParkingCost> implements ParkingCostService {

    @Resource
    BaseUserContextProducer baseUserContextProducer;
    
    @Override
    public boolean addParkingCost(ParkingCost parkingCostDTO) {
        ParkingCost parkingCost = new ParkingCost();
        BeanUtils.copyProperties(parkingCostDTO, parkingCost);
        parkingCost.setCreateTime(new Date());
        BaseappUser current = baseUserContextProducer.getCurrent();
        parkingCost.setCreatorId(current.getId());
        return save(parkingCost);
    }
    
    @Override
    public Page<ParkingCostVO> queryParkingCostPage(RequestModel<ParkingCostVO> requestModel) {
        return baseMapper.queryParkingCostPage(requestModel.getPage(), requestModel.getCustomQueryParams());
    }
} 