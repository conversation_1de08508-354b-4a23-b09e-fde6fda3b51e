package com.smartPark.business.intelligentparking.lot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smartPark.business.intelligentparking.lot.entity.ParkingCost;
import com.smartPark.business.intelligentparking.lot.vo.ParkingCostVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ParkingCostMapper extends BaseMapper<ParkingCost> {
    
    Page<ParkingCostVO> queryParkingCostPage(Page page, @Param("param") ParkingCostVO param);
} 