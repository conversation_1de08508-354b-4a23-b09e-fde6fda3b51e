package com.smartPark.business.intelligentparking.lot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.smartPark.business.intelligentparking.lot.entity.ParkingIllegalRecord;
import com.smartPark.business.intelligentparking.lot.entity.vo.ParkingIllegalRecordVo;
import com.smartPark.business.intelligentparking.lot.service.ParkingIllegalRecordService;
import com.smartPark.common.base.model.RequestModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

/**
 * 智慧停车/违停记录管理
 * @author: kan yuanfeng
 * @Date: 2023/04/04 11:42
 * @Description: 违停记录管理
 */
@RestController
@RequestMapping("parkingIllegalRecord")
@Api(tags = "智慧停车/违停记录管理")
public class ParkingIllegalRecordController {
  
  @Autowired
  private ParkingIllegalRecordService parkingIllegalRecordService;

  /**
   * @Description: 增加违停记录
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @PostMapping
  @ApiOperation("增加违停记录")
//  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.ADD,menuCode = "parkPatrol:category:add",desc = "新增违停记录")
  public RestMessage insert(@RequestBody ParkingIllegalRecord parkingIllegalRecord){
    //todo 参数验证
    parkingIllegalRecordService.insert(parkingIllegalRecord);
//    LogHelper.setLogMemo("新增违停记录,违停记录名称:"+parkingIllegalRecord.getName());
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 编辑违停记录
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @PutMapping
  @ApiOperation("编辑违停记录")
//  @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.EDIT,menuCode = "parkPatrol:category:edit",desc = "修改违停记录")
  public RestMessage updateById(@RequestBody ParkingIllegalRecord parkingIllegalRecord){
    Assert.notNull(parkingIllegalRecord.getId(),"id不能为空");
    parkingIllegalRecordService.updateOne(parkingIllegalRecord);
    return RestBuilders.successBuilder().build();
  }

  /**
   * @Description: 根据id查询违停记录详情
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @GetMapping("{id}")
  @ApiOperation("根据id查询违停记录详情")
  public RestMessage findById(@PathVariable("id")Long id) {
    Assert.notNull(id,"id不能为空");
    ParkingIllegalRecordVo parkingIllegalRecordVo = parkingIllegalRecordService.findById(id);
    return RestBuilders.successBuilder().data(parkingIllegalRecordVo).build();
  }

  /**
   * @Description: 根据条件，分页(不分页)查询
   * <AUTHOR> yuanfeng
   * @date 2023/04/04 11:42
   */
  @PostMapping("list")
  @ApiOperation("根据条件，分页(不分页)查询")
  public RestMessage queryListByPage(@RequestBody RequestModel<ParkingIllegalRecordVo> requestModel){
    Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
    Assert.notNull(requestModel.getPage(), "page 不能为空");
    IPage<ParkingIllegalRecordVo> record =  parkingIllegalRecordService.queryListByPage(requestModel);
    return RestBuilders.successBuilder().data(record).build();
  }
}

