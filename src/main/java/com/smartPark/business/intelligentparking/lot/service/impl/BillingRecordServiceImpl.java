package com.smartPark.business.intelligentparking.lot.service.impl;

import com.asyncexcel.core.exporter.DataExportParam;
import com.asyncexcel.springboot.ExcelService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smartPark.business.intelligentparking.lot.entity.BillingRecord;
import com.smartPark.business.intelligentparking.lot.entity.OpenBillingRecord;
import com.smartPark.business.intelligentparking.lot.entity.ParkingLot;
import com.smartPark.business.intelligentparking.lot.excel.handler.BillingRecordExportHandler;
import com.smartPark.business.intelligentparking.lot.mapper.BillingRecordMapper;
import com.smartPark.business.intelligentparking.lot.service.BillingMonitoringService;
import com.smartPark.business.intelligentparking.lot.service.BillingRecordService;
import com.smartPark.business.intelligentparking.lot.service.ParkingLotService;
import com.smartPark.common.base.service.CommonService;
import com.smartPark.common.security.context.BaseUserContextProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * BillingRecord表服务实现类
 *
 * <AUTHOR>
 * @since 2023/05/10
 */
@Slf4j
@Service("trafficBillingRecordService")
public class BillingRecordServiceImpl extends ServiceImpl
        <BillingRecordMapper, BillingRecord> implements BillingRecordService {
    @Resource
    private CommonService commonService;

    @Resource
    private ParkingLotService parkingLotService;

    @Autowired
    private BillingMonitoringService billingMonitoringService;

    @Override
    public boolean removeById(Serializable id) {
        return super.update().set("deleted_", id).eq("id_", id).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> idList) {
        // 将删除状态改为主键值
        new LambdaUpdateChainWrapper<>(getBaseMapper()).setSql("deleted_ = id_").in(BillingRecord::getId, idList).update();
        return true;
    }


    @Override
    public boolean saveOne(BillingRecord trafficBillingRecord) {
        commonService.setCreateAndModifyInfo(trafficBillingRecord);

        validParamRequired(trafficBillingRecord);
        validRepeat(trafficBillingRecord);
        validParamFormat(trafficBillingRecord);
        return save(trafficBillingRecord);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOne(BillingRecord trafficBillingRecord) {
        Assert.notNull(trafficBillingRecord.getId(), "id不能为空");
        commonService.setModifyInfo(trafficBillingRecord);

        validRepeat(trafficBillingRecord);
        validParamFormat(trafficBillingRecord);
        return updateById(trafficBillingRecord);
    }

    @Override
    public IPage<BillingRecord> selectPage(Page page, BillingRecord trafficBillingRecord) {
        return baseMapper.selectPage(page, trafficBillingRecord);
    }

    @Resource
    BaseUserContextProducer baseUserContextProducer;
    @Resource
    private ExcelService excelService;

    @Override
    public Long export(BillingRecord billingRecord, HttpServletRequest request, HttpServletResponse
            response) {

        Long userId = baseUserContextProducer.getCurrent().getId();
        DataExportParam dataExportParam = new DataExportParam();
        dataExportParam.setParam(billingRecord);
        dataExportParam.setExportFileName("收费记录信息");
        dataExportParam.setTenantCode("traffic");
        dataExportParam.setBusinessCode("billingRecord");
        dataExportParam.setCreateUserCode(userId.toString());
        Long taskId = excelService.doExport(dataExportParam, BillingRecordExportHandler.class);

        String msg = "导出收费记录，任务id：" + taskId;
        // LogHelper.setLogInfo("intelligentParking:fee:export", msg, null, msg, msg);
        return taskId;
    }

    @Override
    public BillingRecord getOneById(Serializable id) {
        return baseMapper.getOneById(id);
    }

    /**
     * 校验重复
     */
    private void validRepeat(BillingRecord trafficBillingRecord) {

        /* List<BillingRecord> list = new LambdaQueryChainWrapper<>(baseMapper)
            .eq(BillingRecord::getDeviceCode, trafficBillingRecord.getDeviceCode())
            .list();
            if (list.size() > 0 && (list.size() > 1 || ObjectUtils.isEmpty(trafficBillingRecord.getId()) || !trafficBillingRecord.getId().equals(list.get(0).getId()))) {
                throw new BusinessException("名称重复");
            }
        */


    }


    /**
     * 校验参数必填
     */
    private void validParamRequired(BillingRecord trafficBillingRecord) {
        //Assert.notNull(trafficBillingRecord, "参数为空");
        //Assert.isTrue(StringUtils.isNotBlank(trafficBillingRecord.getName()), "名称为空");
    }

    /**
     * 校验参数格式
     */
    private void validParamFormat(BillingRecord trafficBillingRecord) {
        //Assert.isTrue(trafficBillingRecord.getName() == null || trafficBillingRecord.getName().length() <= 50,
        //        "名称超长");
    }

    /**
     * 接收收费记录接口
     * @param openBillingRecord
     * @return
     */
    @Override
    public boolean receiveBillingRecord(OpenBillingRecord openBillingRecord) {
        BillingRecord transfer = openBillingRecord.transfer();
        fillBillingRecord(transfer);
        billingMonitoringService.simulation(transfer);
        return saveOne(transfer);
    }

    private void fillBillingRecord(BillingRecord billingRecord) {
        ParkingLot one = parkingLotService.findByParkId(billingRecord.getParkId());
        if (one != null){
            billingRecord.setParkingLotId(one.getId());
            billingRecord.setParkingLotName(one.getLotName());
        }
    }
}

