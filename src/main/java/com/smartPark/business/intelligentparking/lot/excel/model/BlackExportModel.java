package com.smartPark.business.intelligentparking.lot.excel.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.asyncexcel.core.ExportRow;
import lombok.Data;

import java.util.Date;

/**
 * 地磁告警
 *
 * @since 2023
 */
@Data
@ContentFontStyle(fontName = "宋体 (正文)", fontHeightInPoints = 11)
@HeadFontStyle(fontName = "宋体 (标题)", fontHeightInPoints = 11, bold = BooleanEnum.TRUE)
public class BlackExportModel extends ExportRow {

//  车牌号码、车辆颜色、联系人、联系电话、生效范围、生效日期、失效日期、备注；

    @ExcelProperty(value = "车牌号码")
    private String carNo;

    //     1-白色、2-黑色、3-银色、4-红色、5-蓝色、6-金色、7-灰色、8-绿色、9-棕色、10-粉色、11-其它
    @ExcelProperty(value = "车辆颜色")
    private String carColor;

    @ExcelProperty(value = "联系人")
    private String applicantName;

    @ExcelProperty(value = "联系电话")
    private String phone;

    //     全部 ，多选的值 逗号分割
    @ExcelProperty(value = "生效范围")
    private String parkingLotName;


    @ExcelProperty(value = "生效日期")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(value = 30)
    private Date effectiveDate;

    @ExcelProperty(value = "失效日期")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ColumnWidth(value = 30)
    private Date invalidDate;

    @ExcelProperty(value = "备注")
    private String remark;

}
