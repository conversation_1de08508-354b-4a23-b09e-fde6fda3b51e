package com.smartPark.sanzhi.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 用户存放redis验证数据
 * <AUTHOR>
 * @Date 2023/6/6 10:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserToken implements Serializable {
    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;
}
