package com.smartPark.sanzhi.util;

import com.smartPark.common.security.entity.Privilege;

import java.util.*;

public class PrivilegeUtils {

    /**
     * 根据列表规模选择算法构建树
     */
    public static List<Privilege> buildTree(Collection<Privilege> nodeList) {
        //构建map、通过key找value耗时不小，列数少时没必要用动态规划
        if(nodeList.size()>1000) {
            return buildTreeByDP(nodeList);
        } else {
            return buildTreeByRecursion(nodeList);
        }
    }

    /**
     * 动态规划构建树形结构
     */
    public static List<Privilege> buildTreeByDP(Collection<Privilege> nodeList) {
        // 创建集合封装最终数据
        List<Privilege> trees = new ArrayList<>();
        Map<Long,Privilege> nodeMap=new HashMap<>(nodeList.size());
        for (Privilege node : nodeList)
        {
            //用id作key
            nodeMap.put(node.getId(),node);
        }
        for (Privilege node : nodeMap.values())
        {
            //子节点仅对应一个父节点
            Privilege ParentNode=nodeMap.get(node.getParentId());
            if (ParentNode!=null) {
                if(ParentNode.getChildren()==null) {
                    ParentNode.setChildren(new ArrayList<>());
                }
                //父节点添加子节点
                ParentNode.getChildren().add(node);
            } else {
                //找到树根，parentId=0
                trees.add(node);
            }
        }
        return trees;
    }



    /**
     * 递归算法
     */
    public static List<Privilege> buildTreeByRecursion(Collection<Privilege> nodeList) {
        // 创建集合封装最终数据
        List<Privilege> trees = new ArrayList<>();
        // 遍历所有菜单集合
        for (Privilege privilege:nodeList) {
            // 每个parentId=null需要进行一次递归
            if(privilege.getParentId()==null) {
                trees.add(findChildren(privilege,nodeList));
            }
        }
        return trees;
    }

    /**
     * 从根节点进行递归查询，查询子节点
     */
    private static Privilege findChildren(Privilege privilege, Collection<Privilege> treeNodes) {
        // 数据初始化
        privilege.setChildren(new ArrayList<>());
        // 遍历递归查找
        for (Privilege node:treeNodes) {
            // 为当前菜单添加子节点
            if(privilege.getId().equals(node.getParentId())) {
                if(privilege.getChildren()==null) {
                    privilege.setChildren(new ArrayList<>());
                }
                privilege.getChildren().add(findChildren(node,treeNodes));
            }
        }
        return privilege;
    }

}