package com.smartPark.sanzhi.controller;

import com.alibaba.fastjson.JSONObject;
import com.smartPark.common.rpc.RpcEnum;
import com.smartPark.common.security.context.BaseUserContextProducer;
import com.smartPark.common.security.entity.BaseappUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限管理
 *
 * <AUTHOR>
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping("privilege")
public class PrivilegeController {
    @Autowired
    private BaseUserContextProducer baseUserContextProducer;

    /**
     * 根据类型获取权限树、或者小程序的权限树
     *
     * @param type          添加type   兼容小程序 权限 0:PC， 1:小程序
     * @param applicationId 对应的三智的id
     *                      1	三智管理后台
     *                      2	园区畅行
     *                      3	生态宜居
     *                      4	安全守护
     * @return
     */
    @ApiOperation("根据类型获取权限树、或者小程序的权限树")
    @GetMapping("/selectPrivilegeByUser")
    public JSONObject selectPrivilegeByUser(@RequestParam(value = "type", required = false) Integer type, @RequestParam(value = "applicationId", required = false) Integer applicationId,@RequestParam(value = "dependUser", required = false) Integer dependUser) {
        String username = null;
        if(dependUser == null || dependUser.intValue() == 0){
            BaseappUser current = baseUserContextProducer.getCurrent();
            username = current.getUsername();
        }else {
            //不依赖用户就查所有，也就是默认超管账号
            username = "admin";
        }

        //调用base服务的菜单
        String url = "sanzhiapi/privilegeController/selectPrivilegeByUser?username="+username;
        if (null != type){
            url += "&type="+type;
        }
        if (null != applicationId){
            url += "&applicationId="+applicationId;
        }
        JSONObject jsonObject = RpcEnum.BASE.getForObject(url, JSONObject.class);
        return jsonObject;
    }


//    @ApiOperation("获取当前用户有权限的应用id列表")
//    @GetMapping("/selectApplicationIds")
//    public JSONObject selectApplicationIds() {
//        //调用base服务的菜单
//        String url = "sanzhiapi/privilegeController/selectApplicationIds?userId=" + baseUserContextProducer.getCurrent().getId();
//        JSONObject jsonObject = RpcEnum.BASE.getForObject(url, JSONObject.class);
//        return jsonObject;
//    }

}
