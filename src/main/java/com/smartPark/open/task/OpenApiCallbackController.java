package com.smartPark.open.task;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smartPark.business.airquality.device.entity.vo.AirQualityStatisticsVo;
import com.smartPark.business.airquality.device.service.AirEnvAnalysisService;
import com.smartPark.business.autoIrrigate.entity.IrrigateDevice;
import com.smartPark.business.autoIrrigate.entity.IrrigateWaterStatistics;
import com.smartPark.business.autoIrrigate.mapper.IrrigateDeviceMapper;
import com.smartPark.business.autoIrrigate.mapper.IrrigateWaterStatisticsMapper;
import com.smartPark.business.autoIrrigate.service.IrrigateAlarmConfigService;
import com.smartPark.business.autoIrrigate.service.IrrigateControlRecordService;
import com.smartPark.business.autoIrrigate.service.IrrigateTimeStatisticsService;
import com.smartPark.business.autoIrrigate.service.impl.IrrigateDeviceFlowProcess;
import com.smartPark.business.garbage.service.GarbagePlanTaskService;
import com.smartPark.business.hazardousMonitoring.service.HazardousStatisticsService;
import com.smartPark.business.seat.service.SeatInfoPlanService;
import com.smartPark.business.toilet.service.LivableToiletStageStandardStatService;
import com.smartPark.business.toilet.service.ToiletDeviceService;
import com.smartPark.business.toilet.service.ToiletTodayStatisticsService;
import com.smartPark.common.annotation.BusinessLogAnnotate;
import com.smartPark.common.constant.LogConstant;
import com.smartPark.common.device.dto.FlowPushData;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * 任务回调
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/openapi/task/callback")
public class OpenApiCallbackController {

    @Resource
    private IrrigateControlRecordService irrigateControlRecordService;
    @Resource
    private GarbagePlanTaskService garbagePlanTaskService;

    @Resource
    private ToiletDeviceService toiletDeviceService;

    @Resource
    private AirEnvAnalysisService airEnvAnalysisService;

    @Resource
    private IrrigateTimeStatisticsService irrigateTimeStatisticsService;

    @Resource
    private HazardousStatisticsService hazardousStatisticsService;
    @Resource
    private IrrigateDeviceFlowProcess irrigateDeviceFlowProcess;
    @Resource
    private IrrigateDeviceMapper irrigateDeviceMapper;
    @Resource
    private IrrigateWaterStatisticsMapper irrigateWaterStatisticsMapper;
    @Resource
    private ToiletTodayStatisticsService toiletTodayStatisticsService;
    @Resource
    private LivableToiletStageStandardStatService toiletStageStandardStatService;
    @Resource
    private SeatInfoPlanService seatInfoPlanService;
    @Resource
    private IrrigateAlarmConfigService irrigateAlarmConfigService;
    /**
     * 灌溉计划控制
     *
     * @param paramObj 传参param字符串(创建任务时设置的参数)
     * @return 新增结果
     */
    @PostMapping("/taskIrrigateControl")
    @ApiOperation("灌溉计划控制")
    public RestMessage taskIrrigateControl(@RequestBody Object paramObj) {
        return irrigateControlRecordService.taskIrrigateControl(JSONUtil.toJsonStr(paramObj));
    }

    /**
     * 垃圾收运计划触发
     *
     * @return 新增结果
     */
    @PostMapping("garbageTask")
    @ApiOperation("垃圾收运计划")
    public RestMessage toAddGarbageTask(@RequestBody Object paramObj) {
        //触发生成垃圾收运任务
        garbagePlanTaskService.toAddGarbageTask();
        return RestBuilders.successBuilder().build();
    }

    /**
     * 公厕联动消杀任务
     *
     * @param paramObj 传参paramObj（json）
     * @return 新增结果
     */
    @PostMapping("/taskToiletKillControl")
    @ApiOperation("公厕设备控制")
    @BusinessLogAnnotate(actionType = LogConstant.LogOperateActionType.DEVICE_PLAN, menuCode = "smartPublicToilet:smartServe:linkageKill:clockJob", desc = "控制设备")
    public RestMessage taskToiletKillControl(@RequestBody Object paramObj) {
        JSONObject paramJson = JSONUtil.createObj();
        if(paramObj != null){
            paramJson = JSONUtil.parseObj(paramObj);
        }
        String param = JSONUtil.toJsonStr(paramJson);
        return toiletDeviceService.taskToiletKillControl(param);
    }

    /**
     * 空气质量趋势分析
     * 每小时01分执行一次
     * @param paramObj 传参paramObj（json）
     */
    @PostMapping("/taskAirQualityAnalysis")
    @ApiOperation("空气质量趋势分析")
    public RestMessage taskAirQualityAnalysis(@RequestBody Object paramObj) {
        try {
            JSONObject paramJson = JSONUtil.createObj();
            if(paramObj != null){
                paramJson = JSONUtil.parseObj(paramObj);
            }
            String param = JSONUtil.toJsonStr(paramJson);
            airEnvAnalysisService.taskAirQualityAnalysis(param);
            //污染源空气质量
            hazardousStatisticsService.calAirStatistics();
        } catch (Exception e) {
            log.error("统计失败");
        }
        //将浇水的任务也放这里不想再新建任务了
        try {
            irrigateAlarmConfigService.triggerAlarm();
        } catch (Exception e) {
            log.error("灌溉告警触发失败");
        }
        return RestBuilders.successBuilder().build();
    }

    /**
     * 空气质量补充计算
     * @param airQualityStatisticsVo 空气质量统计vo
     * @return 统一出参
     */
    @PostMapping("/airQualityAddCalculation")
    @ApiOperation("空气质量补充计算")
    public RestMessage airQualityAddCalculation(@RequestBody AirQualityStatisticsVo airQualityStatisticsVo) {
        Assert.isTrue(StringUtils.isNotBlank(airQualityStatisticsVo.getQueryType()), "统计类型不能为空");
        return airEnvAnalysisService.airQualityAddCalculation(airQualityStatisticsVo);
    }

    /**
     * 灌溉用时统计
     * @param paramObj 传参paramObj（json）
     * @return 统一出参
     */
    @PostMapping("/taskIrrigateUseTimeCount")
    public RestMessage taskIrrigateUseTimeCount(@RequestBody Object paramObj) {
        return irrigateTimeStatisticsService.taskIrrigateUseTimeCount(JSONUtil.toJsonStr(paramObj));
    }


    /**
     * 灌溉用时统计
     * @param paramObj 传参paramObj（json）
     * @return 统一出参
     */
    @PostMapping("createIrrigateData")
    public RestMessage createIrrigateData(@RequestBody Object paramObj) {
        //查询所有的传感器
        LambdaQueryWrapper<IrrigateDevice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IrrigateDevice::getType,3);
        wrapper.eq(IrrigateDevice::getDeleted,0);
        List<IrrigateDevice> devices = irrigateDeviceMapper.selectList(wrapper);
        devices.forEach(r ->{
            String deviceCode = r.getDeviceCode();
            //查询最新的数据
            LambdaQueryWrapper<IrrigateWaterStatistics> qw = new LambdaQueryWrapper<>();
            qw.eq(IrrigateWaterStatistics::getDeviceCode, deviceCode);
            qw.orderByDesc(IrrigateWaterStatistics::getCreateTime);
            qw.last("limit 1");
            IrrigateWaterStatistics lastRecord = irrigateWaterStatisticsMapper.selectOne(qw);
            FlowPushData flowPushData = new FlowPushData();
            flowPushData.setDevice_id(deviceCode);
            com.alibaba.fastjson.JSONObject flowData = new com.alibaba.fastjson.JSONObject();
            //0到0.007的double随机数
            double v = Math.random() * 0.0075;
            if (lastRecord != null) {
                //加上上一次的数据
                v += (null != lastRecord.getLastEndDosage()?lastRecord.getLastEndDosage():0);
            }
            flowData.put("water_flux",v);
            flowPushData.setData(flowData);
            flowPushData.setTimestamp(System.currentTimeMillis());
            irrigateDeviceFlowProcess.flowDataProcess(flowPushData);
        });
        return RestBuilders.successBuilder().build();
    }

    /**
     * 智慧公厕统计分析
     * @param paramObj 传参param字符串(创建任务时设置的参数)
     * @return 新增结果
     */
    @PostMapping("/toiletFlowStatTask")
    @ApiOperation("智慧公厕统计分析")
    public RestMessage toiletFlowStatTask(@RequestBody Object paramObj) {
        String currentDate = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
        toiletTodayStatisticsService.livableToiletEnvStat(currentDate);
        return RestBuilders.successBuilder().build();
    }

    /**
     * 智慧公厕-坑位空闲统计分析
     * @param paramObj 传参param字符串(创建任务时设置的参数)
     * @return 新增结果
     */
    @PostMapping("/toiletFlowStatTask/idle")
    @ApiOperation("智慧公厕-坑位空闲统计分析")
    public RestMessage toiletFlowStatTaskIdle(@RequestBody Object paramObj) {
        String currentDate = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
        toiletTodayStatisticsService.toiletIdleAnalysis(currentDate);
        return RestBuilders.successBuilder().build();
    }

    /**
     * 公厕达标分析
     */
    @PostMapping("/stageStandardStat")
    public RestMessage stageStandardStat(@RequestBody Object date) {
        toiletStageStandardStatService.stageStandardStat(ObjectUtils.isEmpty(date) ? null :date.toString());
        return RestBuilders.successBuilder().data("").build();
    }

    /**
     * 座椅控制回调
     *
     * @param jsonObject 传参param字符串(创建任务时设置的参数)
     * @return 新增结果
     */
    @PostMapping("taskSeatInfoControl")
    @ApiOperation("座椅控制回调")
    public RestMessage taskStreetlightCallback(@RequestBody com.alibaba.fastjson.JSONObject jsonObject) throws ParseException {
        seatInfoPlanService.taskCallback(jsonObject);
        return RestBuilders.successBuilder().build();
    }
}
