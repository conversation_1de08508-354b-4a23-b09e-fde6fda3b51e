package com.smartPark.open.foreign.queue.impl;

import com.smartPark.common.device.dto.FlowPushData;
import com.smartPark.open.foreign.queue.IDeviceEventQueue;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/25
 * @description
 */
public class DeviceEventMemQueue implements IDeviceEventQueue {

  // 流水数据源队列
  final static BlockingQueue<FlowPushData> deviceEventQueue = new ArrayBlockingQueue<>(10000);

  @Override
  public int getDeviceEventSize() {
    return deviceEventQueue.size();
  }

  @Override
  public void addDeviceEventQueue(FlowPushData obj) {
    Assert.notNull(obj, "data not be null");
    deviceEventQueue.add(obj);
  }

  @Override
  public FlowPushData takeDeviceEvent() throws InterruptedException {
    return deviceEventQueue.take();
  }

}
