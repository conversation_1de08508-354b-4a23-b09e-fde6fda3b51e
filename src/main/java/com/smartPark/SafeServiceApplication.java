package com.smartPark;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.asyncexcel.springboot.EnableAsyncExcel;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import site.morn.boot.data.jpa.JpaRepositoryFactoryProducer;

@MapperScan(basePackages = {"com.smartPark.**.mapper","com.smartPark.**.dao","com.asyncexcel.springboot.context.mapper"})
@ServletComponentScan
@EnableAsync
@EnableCaching

@EnableTransactionManagement

@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.smartPark"})
@EnableJpaRepositories(
    repositoryFactoryBeanClass = JpaRepositoryFactoryProducer.class,
    basePackages = {FRAMEWORK_BASE_PACKAGES, "com.smartPark"})
@SpringBootApplication(scanBasePackages = {FRAMEWORK_BASE_PACKAGES, "com.smartPark"}, exclude = DruidDataSourceAutoConfigure.class)

//新加配置两条 来之 umbp 可能不需要
@EnableJpaAuditing
//启用属性加密
//@EnableEncryptableProperties
@EnableScheduling
@EnableAsyncExcel
public class SafeServiceApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(SafeServiceApplication.class);
    }

    public static void main(String[] args) {
        StopWatch sw = DateUtil.createStopWatch();
        sw.start();
        SpringApplication app = new SpringApplication(SafeServiceApplication.class);
        app.setBannerMode(Banner.Mode.OFF);
        app.run(args);
        sw.stop();
        System.out.println("---三智安全守护服务启动成功---耗时：" + sw.getTotalTimeMillis() + "ms");
    }

}
