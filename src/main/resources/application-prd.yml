profile: dev
# 本地起前端服务用
#server:
#  servlet:
#    context-path: /api

spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 500MB
  excel:
    datasource:
      url: ******************************************************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: base
      password: UJHYHex4cBUXvyyGVHui
  #  excel:
  #    datasource:
  #      url: ******************************************************************************************************************************************
  #      driver-class-name: com.mysql.cj.jdbc.Driver
  #      username: base
  #      password: UJHYHex4cBUXvyyGVHui
  datasource:
    publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIeph81RzxMYHOCS4fYoqRY6d7zrL1S4dWhU75VVNDg5DVoU/bf2YDME8m0++V9Pyi0Z5THetMZmCnd2fYE01bkCAwEAAQ==
    #  dev 数据库配置
    druid:
      url: *****************************************************************************************************************************************************************
      username: base
      #      UJHYHex4cBUXvyyGVHui
      password: Pipapsw6I4SnOL348LNZqV+sOU5jVdoDhjE0HHeS6vVRHLuFcj34DnG0SELUTY6L4cpd3dvo4bb6I4lCC2hISw==
  # 整合thymeleaf 测试 websocket start ---
  #  mvc:
  #    view:
  #      prefix: classpath:/templates/
  #      suffix: .html
  #    static-path-pattern: /static/**
  #  #关闭thymeleaf的缓存，不然在开发过程中修改页面不会立刻生效需要重启，生产可配置为true
  #  thymeleaf:
  #    cache: false
  #    prefix: classpath:/templates/
  #    suffix: .html
  #    mode: HTML5
  #    encoding: UTF-8
  #  resources:
  #    chain:
  #      strategy:
  #        content:
  #          enabled: true
  #          paths: /**
  # 整合thymeleaf 测试 websocket end ---
  #kafka配置
  kafka:
    bootstrap-servers: *********:9092
    client-id: dc-device-flow-analyze
    consumer:
      group-id: dc-device-flow-analyze-consumer-group
      max-poll-records: 10
      #Kafka中没有初始偏移或如果当前偏移在服务器上不再存在时,默认区最新 ，有三个选项 【latest, earliest, none】
      auto-offset-reset: earliest
      #是否开启自动提交
      enable-auto-commit: false
      #自动提交的时间间隔
      auto-commit-interval: 1000
    producer:
      acks: 1
      batch-size: 4096
      buffer-memory: 40960000
      client-id: dc-device-flow-analyze-producer
      #compression-type: zstd  #压缩类型
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      retries: 3
      properties:
        spring.json.add.type.headers: false
        max.request.size: 126951500
    listener:
      ack-mode: MANUAL_IMMEDIATE
      concurrency: 1  #推荐设置为topic的分区数
      type: BATCH #开启批量监听

  redis:
    database: 1
    host: *********
    port: 15052
    password: QOooooOQ_linkthings

  #默认dohealthcheck为localhost
  elasticsearch:
    rest:
      uris: ["${es.host}:${es.port}"]
#  kafka:
#    producer:
#      bootstrap-servers: **************:9092
#      batch-size: 16384
#      retries: 0
#      buffer-memory: 33554432
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#
#    consumer:
#      enable-auto-commit: true
#      group-id: gridMonitorGroup
#      auto-commit-interval: 1000
#      auto-offset-reset: latest
#      bootstrap-servers: **************:9092
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      concurrency: 3

es:
  isUseOnlineEs: false
  #  host: linkthings-es-in.db.dev.easylinkin.net
  # host: linkthings-es-in.db.prd.easylinkin.net
  host: *********

linkapp:
  url: https://linkapp-out-dev-service.easylinkin.com
  defaultPassword: easylinkin@2020
  isEnableVerificationCode: false
  #   是否启用登录密码错误次数超限锁住用户机制
  enableLoginCountCheck: false
  configDeviceExecuteConditionCommand: -10
  # 空调模块批量控制延时下发指令，单位：毫秒
  airControllerDelay: 3000
  #   是否监听，ws实时推送
  openReceiving: true


linkthing:
  #emp同步
  clientId: a6742665981a4bc9b37dc6006546d824
  clientSecret: 6CBF9B2146C3512FBCBB078A8894E45732E0918E9B4F0AD31F7A2D05B0EDC354
  #saas账号
  saasId: a6742665981a4bc9b37dc6006546d824
  saasSecret: 6CBF9B2146C3512FBCBB078A8894E45732E0918E9B4F0AD31F7A2D05B0EDC354
  #数据下行
  keyId: 093bf019bf974481a0b4be7f25eeaccb
  appSecret: 22edd3274a7c4fdebcbc99bcd4cd6a73
  tenantId: 20000592
  projectId: 8
  url:
    auth_url: http://linkos-auth-center-in.dev.service.easylinkin.net
    api_url: http://linkthings-api-in.dev.service.easylinkin.net
    find_active_app_list: http://linkthings-api-in.dev.service.easylinkin.net/api/appBuyList/findActiveAppList


message:
  #手机短信阿里云的参数
  product: Dysmsapi
  domain: dysmsapi.aliyuncs.com
  accessKeyId: LTAI4GJY8uMYC2XRw92YpbN7
  accessKeySecret: ******************************
  regingId: cn-hangzhou
  signName: 武汉慧联无限科技有限公司
  #腾讯邮件服务参数
  host: smtp.exmail.qq.com
  username: <EMAIL>
  password: AomruakoTSsDQQBL1
  subject: 设备告警
  port: 465


oss:
  # 判断使用的oss：minio；其他都是aliyun
  env: minio
  # 根文件夹
  dir: safe

  # 阿里云oss 的参数
  accessKeyId: LTAI4GJY8uMYC2XRw92YpbN7
  accessKeySecret: ******************************
  endPoint: oss-cn-hangzhou.aliyuncs.com
  endPoint-internal: oss-cn-hangzhou-internal.aliyuncs.com
  bucket: linksaas-not-prd
  domain: https://puboss2-not-prd.easylinkin.com
# minio 的参数
minio:
  url: https://miniohlhsz.net4iot.com:16005
  accessKey: nini_linkthings
  secretKey: ncYYn8PMtY93g6tuOtLPiVpz4ZncfaBD
  bucket: sanzhi
  uploadHost: api/oss/uploadMinio
  accessHost: http://minioprd.net4iot.com:16005

nb:
  nbVideoUrl: https://nb-video-dev.oss-cn-hangzhou.aliyuncs.com

ai:
  face:
    platform: baidu
    #    koala:
    #      username: <EMAIL>
    #      password: 123456
    #      token: 06585230-a123-4a48-a32b-f207e698b55c
    #      management: http://*************/
    #      recognize: http://*************:8866/
    baidu:
      ak: VHElOS0CgiqAorEW8iX4BrtY
      sk: NobqG3i3cbY6SMwVBqzw4xzrMP86mqep
      management: https://aip.baidubce.com/
      group: easylinkin


#高德地图API调用信息
gaode:
  api:
    #公司--key
    #key: ce9857bf787d8aa6fc4b5ca9f7426ce7

    #lqh--key
    key: 8d3b95d6435c4fe8dcd5582986af204e
    sid: 377157
    #逆地理编码api接口地址
    wep_api_url: https://restapi.amap.com/v3/geocode/regeo?
    #坐标系转换api接口地址
    location_convert_url: https://restapi.amap.com/v3/assistant/coordinate/convert?
    #查询指定坐标与围栏关系
    track_location_url: https://tsapi.amap.com/v1/track/geofence/status/location?
    #创建查询电子围栏地址
    fenceUrl: https://tsapi.amap.com/v1/track/geofence


wechat:
  pay:
    third_url: http://linkos-pay-out-dev-service.easylinkin.com
    attach: 用户充值
    #支付完成后跳转到业务服务的链接
    backUrl: https://linkapp-out-dev-service.easylinkin.com/h5.html#/feesResult
    body: 水电表充值服务
    orgId: aep
    subjectId: 1
    sign: 35dd67140d34aba8528c313fdce51201
    #token: aj1UmMaIUQH87jhHJBAAka
    token: ffssswwewe
    #业务服务接受第三方支付结果回调链接
    biz_notify_url_: http://linkapp-out.dev.service.easylinkin.net/api/recharge/thirdAsynNotify
    #预支付跳转第三方链接
    jump_url: http://linkos-pay-out-dev-service.easylinkin.com/weixin?outTradeNo=
    #订单结果轮询链接
    ask_order_url: http://linkos-pay-out-dev-service.easylinkin.com/weixin/transactions

ipservice:
  #  url: http://*************:40759
  url: http://
  receiveImage: recv

#base接口开头
base_service:
  interface:
    profix: /baseOpenApi

#三智应用请求url 先从环境变量读，再从数据库读
#园区畅行
traffic_service:
#生态宜居
livable_service:
#livable_service: http://127.0.0.1:5044
#安全守护
safe_service:
#物联网平台
iot_api_plantform:  http://test.easylinkin.net:15012
#运营平台业务中枢
operation_platform: http://*********:50080


#智慧出行redis配置
travel:
  redis:
    host: server.natappfree.cc
    port: 42866
    password: aaa@bbb@123456
    timeout: 20000

3zhi:
  authCenter:  http://*********:80/unauth-api

#运营中心kafka配置
operation:
  kafka:
    bootstrap-servers: *********:9092
#    username:
#    password: