-- 设备属性状态表
DROP TABLE IF EXISTS `base_device_status`;
CREATE TABLE `base_device_status` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `device_code_` varchar(32) NOT NULL COMMENT '设备编码',
  `prop_` varchar(32) NOT NULL DEFAULT '' COMMENT '设备属性',
  `value_` text COMMENT '设备属性对应的值',
  `modify_time_` datetime DEFAULT NULL COMMENT '属性更新时间',
  PRIMARY KEY (`id_`),
  UNIQUE KEY `code_prop_` (`device_code_`,`prop_`)
) ENGINE=InnoDB COMMENT='设备属性状态表';

-- 应用模块型号配置表
DROP TABLE IF EXISTS `base_application_model_device_unit_config`;
CREATE TABLE `base_application_model_device_unit_config` (
 `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
 `application_id_` bigint(20) NOT NULL COMMENT '应用id',
 `model_id_` int(3) NOT NULL COMMENT '模块id',
 `device_type_id_` varchar(255) DEFAULT NULL COMMENT '类型id配置集合',
 `device_unit_id_` varchar(255) NOT NULL COMMENT '型号id配置集合',
 PRIMARY KEY (`id_`)
) ENGINE=InnoDB COMMENT='应用模块型号配置表';

-- ---------------------------------
-- 设备与应用，模块关联表
-- ---------------------------------
DROP TABLE IF EXISTS `base_device_application_model_ref`;
CREATE TABLE `base_device_application_model_ref` (
     `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
     `obj_id_` varchar(20) DEFAULT NULL COMMENT '部件码id',
     `device_code_` varchar(32) DEFAULT NULL COMMENT '设备id',
     `type_` int(2) NOT NULL COMMENT '0为设备，1为设施',
     `application_id_` bigint(20) NOT NULL COMMENT '关联应用id，与base_application中id保持一致',
     `model_id_` int(4) NOT NULL COMMENT '关联应用下的模块id，',
     `data_source_` int(4) NOT NULL COMMENT '添加来源，与base_application中id保持一致',
     `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
     `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
     `delete_status_` int(2) DEFAULT NULL COMMENT '删除状态，0未删除，1已删除',
     PRIMARY KEY (`id_`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备与应用模块的关联表';