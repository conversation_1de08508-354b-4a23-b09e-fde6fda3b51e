-- 互联网日志查询菜单
INSERT INTO basedb.base_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_, application_id_, access_type_, service_code_, deleted_)
VALUES('20806', '208', '互联网导航平台日志', 'smartTravel:internetguid:log', '互联网导航平台日志', 2, NULL, NULL, 20806.00, 1, '/information/internetGuid', NULL, NULL, NULL, NULL, NULL, NULL, 0, 2, 2, NULL, 0);


-- 插入新字典值已执行
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('0493', '公园桥梁', NULL, NULL, 'obj_category_04', '园林绿化设施', '园林绿化设施小类', 93.0, '2023-04-06 16:43:49', '2023-04-06 16:43:49', 10726, 10726, 0);

-- 插入新的桥梁配置已执行
INSERT INTO basedb.base_application_model_obj_category_config
(application_id_, model_id_, obj_category1, obj_category2)
VALUES(2, 3, '04', '0493');