ALTER TABLE `safe_emergency_event`
    ADD INDEX `idx_safe_emergency_event_source_code_type` (`event_source_`, `event_code_`, `event_type_`);
ALTER TABLE `base_alarm`
    ADD INDEX `idx_base_alarm_code_type` (`code_`,  `alarm_type_`);

# 应急处置管理-事件查询接口
INSERT INTO base_interface_info (interfact_name, interface_url, application_id, application_code, controller_name, method_name, method_type, business_type, interface_state, memo, open_time, close_time, limit_rate, check_auth, check_flow, check_black_list, check_idempotent, check_safe, creator, create_time, modifier, modify_time, is_deleted) VALUES('应急处置管理-事件查询', '/baseOpenApi/safe/openapi/safeEmergencyEvent/selectEventDtoPage', 4, 'safe_service', 'EmergencyOpenApiController', 'selectEventDtoPage', 'POST', 4, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1);
