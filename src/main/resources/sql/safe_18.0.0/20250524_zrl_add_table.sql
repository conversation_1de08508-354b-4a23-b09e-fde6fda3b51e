-- 创建部门资源关联表
CREATE TABLE `safe_department_resources_ref` (
    `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `department_id_` bigint(20) NOT NULL COMMENT '部门id',
    `type_` tinyint(4) NOT NULL COMMENT '资源关联类型 1-关联物资 2-关联部门',
    `ref_supply_id_` bigint(20)  COMMENT '关联资源id',
    `ref_department_id_` bigint(20) DEFAULT NULL COMMENT '关联部门id',
    `quantity_` int(11) NOT NULL DEFAULT '0' COMMENT '资源数量',
    PRIMARY KEY (`id_`)
) COMMENT ='部门资源关联表';