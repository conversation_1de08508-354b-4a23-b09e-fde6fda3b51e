-- --------------------------
-- 新增座椅控制表
-- --------------------------
CREATE TABLE livable_seat_info_plan (
    id_ BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
    plan_name_ VARCHAR(255) COMMENT '计划名称',
    plan_no_ VARCHAR(255) COMMENT '计划编号',
    note_ TEXT COMMENT '备注说明',
    time_type_ INT COMMENT '时间类型，1季节，2时间区间-日期',
    time_values_ TEXT COMMENT '时间值，多个，隔开',
    control_type_ INT COMMENT '控制设备类型，1分组，2设备',
    plan_status_ INT COMMENT '计划状态，1启用，0停用',
    creator_id_ BIGINT COMMENT '创建人',
    create_time_ DATETIME COMMENT '创建时间',
    modify_id_ BIGINT COMMENT '修改人',
    modify_time_ DATETIME COMMENT '修改时间',
    remark_ TEXT COMMENT '备注',
    deleted_ INT DEFAULT 0 COMMENT '是否删除，1删除，0存在'
) COMMENT='座椅控制计划表';

-- --------------------------
-- 新增座椅控制参数表
-- --------------------------
drop table if exists livable_seat_info_plan_param;
CREATE TABLE livable_seat_info_plan_param (
  id_ BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
  plan_id_ BIGINT COMMENT '计划id',
  start_time_ VARCHAR(255) COMMENT '开始时间(23:21)',
  control_type_ INT COMMENT '控制类型(0断电，1通电)',
  creator_id_ BIGINT COMMENT '创建人',
  create_time_ DATETIME COMMENT '创建时间',
  modify_id_ BIGINT COMMENT '修改人',
  modify_time_ DATETIME COMMENT '修改时间',
  remark_ TEXT COMMENT '备注',
  deleted_ INT DEFAULT 0 COMMENT '是否删除，1删除，0存在'
) COMMENT='座椅控制计划参数';

-- 创建路灯控制计划关系表
drop table if exists livable_seat_info_plan_ref;
CREATE TABLE livable_seat_info_plan_ref (
    id_ BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
    plan_id_ BIGINT NOT NULL COMMENT '计划id',
    ref_type_ INT COMMENT '关系类型，与计划控制类型一致，1分组，2 设备',
    ref_id_ BIGINT COMMENT '关系id',
    creator_id_ BIGINT COMMENT '创建人',
    create_time_ DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modify_id_ BIGINT COMMENT '修改人',
    modify_time_ DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    remark_ VARCHAR(255) COMMENT '备注',
    deleted_ INT DEFAULT 0 COMMENT '是否删除，1删除，0存在'
) COMMENT='座椅控制计划关系表';


-- 座椅小时用电统计表
CREATE TABLE `livable_seat_info_electricity_statistics`
(
    `id_`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `record_time_`  datetime DEFAULT NULL COMMENT '统计日期',
    `seat_code_`   varchar(32) NOT NULL COMMENT '座椅编码',
    `device_code`   varchar(32) NOT NULL COMMENT '设备编码',
    `start_reading` double(20, 2) DEFAULT NULL COMMENT '期初电量',
    `electricy_increment` double(20,2) DEFAULT NULL COMMENT '用电量',
    `end_reading` double(20,2) DEFAULT NULL COMMENT '期末电量',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`) USING BTREE,
    KEY `device_code_idx` (`device_code`)
) COMMENT='座椅小时用电统计表';


-- 座椅告警类型字段
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('1', '低电量', NULL, NULL, 'livable_seat_alarm_type', '智能座椅告警类型', '智能座椅告警类型', 1.0, NULL, NULL, NULL, NULL, 0);
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('2', '灯光异常', NULL, NULL, 'livable_seat_alarm_type', '智能座椅告警类型', '智能座椅告警类型', 2.0, NULL, NULL, NULL, NULL, 0);
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('3', '电压异常', NULL, NULL, 'livable_seat_alarm_type', '智能座椅告警类型', '智能座椅告警类型', 3.0, NULL, NULL, NULL, NULL, 0);
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('4', '制冷异常', NULL, NULL, 'livable_seat_alarm_type', '智能座椅告警类型', '智能座椅告警类型', 4.0, NULL, NULL, NULL, NULL, 0);
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('5', '加热异常', NULL, NULL, 'livable_seat_alarm_type', '智能座椅告警类型', '智能座椅告警类型', 5.0, NULL, NULL, NULL, NULL, 0);
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('6', '音响异常', NULL, NULL, 'livable_seat_alarm_type', '智能座椅告警类型', '智能座椅告警类型', 6.0, NULL, NULL, NULL, NULL, 0);

-- 座椅告警实体类
CREATE TABLE livable_seat_info_alarm (
     id_ BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键，自增',
     seat_code_ varchar(32) NOT NULL COMMENT '座椅编码，冗余',
     device_code_ varchar(32) NOT NULL COMMENT '设备名称',
     alarm_type_ INT COMMENT '报警类型，参考字典 livable_seat_alarm_type',
     record_time_ DATETIME NOT NULL COMMENT '统计日期'
) COMMENT='座椅信息报警表';