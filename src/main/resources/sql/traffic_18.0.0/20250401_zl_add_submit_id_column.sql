-- 新增submit_id字段
ALTER TABLE traffic_smarttravel_release ADD submit_id_ bigint(20) NULL COMMENT '上报信息id';
ALTER TABLE traffic_smarttravel_submit ADD longitude_ varchar(20) NULL COMMENT '经度';
ALTER TABLE traffic_smarttravel_submit ADD latitude_ varchar(20) NULL COMMENT '维度';
ALTER TABLE traffic_smarttravel_submit ADD address_ varchar(255) NULL COMMENT '上报地点';
ALTER TABLE traffic_smarttravel_submit ADD source_ int(2) default 0 COMMENT '上报信息来源';

INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('0', '人工上报', NULL, NULL, 'traffic_travel_source', '智慧出行-上报信息来源', '智慧出行-上报信息来源', 1.0, NULL, NULL, NULL, NULL, 0);
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('1', '设备上报', NULL, NULL, 'traffic_travel_source', '智慧出行-上报信息来源', '智慧出行-上报信息来源', 2.0, NULL, NULL, NULL, NULL, 0);
INSERT INTO basedb.base_dictionary
(dict_code_, dict_desc_, application_id_, model_, category_code_, category_name_, category_desc_, sort_no_, create_time_, modify_time_, creator_id_, modify_id_, deleted_)
VALUES('2', '其他平台上报', NULL, NULL, 'traffic_travel_source', '智慧出行-上报信息来源', '智慧出行-上报信息来源', 3.0, NULL, NULL, NULL, NULL, 0);