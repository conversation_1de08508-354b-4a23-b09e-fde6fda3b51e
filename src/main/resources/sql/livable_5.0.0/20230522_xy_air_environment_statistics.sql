-- 空气环境小时统计表
CREATE TABLE `livable_air_environment_hour_statistics` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_time_` datetime DEFAULT NULL COMMENT '统计日期',
  `o3_` double(20,2) DEFAULT NULL COMMENT 'O3平均浓度',
  `no2_` double(20,2) DEFAULT NULL COMMENT 'NO2平均浓度',
  `so2_` double(20,2) DEFAULT NULL COMMENT 'SO2平均浓度',
  `co_` double(20,2) DEFAULT NULL COMMENT 'CO平均浓度',
  `pm_10_` double(20,2) DEFAULT NULL COMMENT 'PM10平均浓度',
  `pm_25_` double(20,2) DEFAULT NULL COMMENT 'PM2.5平均浓度',
  `o3_8_` double(20,2) DEFAULT NULL COMMENT 'o3 8小时滑动平均浓度',
  `aqi_` double(20,2) DEFAULT NULL COMMENT 'AQI',
  `flow_count_` bigint(20) DEFAULT NULL COMMENT '上报次数',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `remark_` text COMMENT '备注',
  PRIMARY KEY (`id_`) USING BTREE
) COMMENT='空气环境小时统计表';

-- 空气环境日统计表
CREATE TABLE `livable_air_environment_day_statistics` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_time_` datetime DEFAULT NULL COMMENT '统计日期',
  `o3_one_hour_max` double(20,2) DEFAULT NULL COMMENT 'O3 最大1小时滑动平均浓度',
  `no2_` double(20,2) DEFAULT NULL COMMENT 'NO2 24小时平均浓度',
  `so2_` double(20,2) DEFAULT NULL COMMENT 'SO2 24小时平均浓度',
  `co_` double(20,2) DEFAULT NULL COMMENT 'CO 24小时平均浓度',
  `pm_10_` double(20,2) DEFAULT NULL COMMENT 'PM10 24小时平均浓度',
  `pm_25_` double(20,2) DEFAULT NULL COMMENT 'PM2.5 24小时平均浓度',
  `o3_8_max` double(20,2) DEFAULT NULL COMMENT 'o3 日8小时最大滑动平均浓度',
  `aqi_` double(20,2) DEFAULT NULL COMMENT 'AQI',
  `flow_count_` bigint(20) DEFAULT NULL COMMENT '上报次数',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `remark_` text COMMENT '备注',
  PRIMARY KEY (`id_`) USING BTREE
) COMMENT='空气环境日统计表';

-- pm统计表
CREATE TABLE `livable_pm_statistics` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_time_` datetime DEFAULT NULL COMMENT '统计日期',
  `pm_10_` double(20,2) DEFAULT NULL COMMENT 'PM10平均浓度',
  `pm_25_` double(20,2) DEFAULT NULL COMMENT 'PM2.5平均浓度',
  `pm_10_max_` double(20,2) DEFAULT NULL COMMENT 'PM10最大浓度',
  `pm_25_max_` double(20,2) DEFAULT NULL COMMENT 'PM2.5最大浓度',
  `flow_count_` bigint(20) DEFAULT NULL COMMENT '上报次数',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `remark_` text COMMENT '备注',
  PRIMARY KEY (`id_`) USING BTREE
) COMMENT='pm统计表';

-- 噪声统计表
CREATE TABLE `livable_noise_statistics` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `record_time_` datetime DEFAULT NULL COMMENT '统计日期',
  `noise_` double(20,2) DEFAULT NULL COMMENT '平均噪声',
  `noise_max_` double(20,2) DEFAULT NULL COMMENT '最大噪声',
  `flow_count_` bigint(20) DEFAULT NULL COMMENT '上报次数',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `remark_` text COMMENT '备注',
  PRIMARY KEY (`id_`) USING BTREE
) COMMENT='噪声统计表';
