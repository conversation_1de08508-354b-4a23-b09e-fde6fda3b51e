ALTER TABLE `base_message`
    ADD COLUMN `type_` int(1) DEFAULT '0' COMMENT '消息类型 0告警事件、1工作任务、2消息提醒';

CREATE TABLE `base_message_template`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键(自增)',
    `name_`        varchar(64)  DEFAULT NULL COMMENT '模板名称',
    `code_`        varchar(32)  DEFAULT NULL COMMENT '编码(内部使用)',
    `type_`        int(1) DEFAULT '0' COMMENT '消息类型 0告警事件、1工作任务、2消息提醒',
    `model_code_`  varchar(32) DEFAULT NULL COMMENT '大类code 如0801 智慧畅行（园区畅行）来自 事件类型表小类代码',
    `is_open_`     int(10) DEFAULT NULL COMMENT '是否启用(1启用0停用)',
    `title_`       varchar(128) DEFAULT NULL COMMENT '消息标题',
    `send_rule_`   varchar(255) DEFAULT NULL COMMENT '发送规则',
    `content_`     text COMMENT '消息内容',
    `url_`         varchar(500) DEFAULT NULL COMMENT '站内信跳转连接',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime     DEFAULT NULL COMMENT '修改时间',
    `remark_`      text COMMENT '备注',
    `deleted_`     int(1) DEFAULT '0' COMMENT '是否删除，1删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT='消息模板';

INSERT INTO `base_message_template` (`id_`, `name_`, `code_`, `type_`, `model_code_`, `is_open_`, `send_rule_`, `content_`, `url_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_`, `deleted_`, `title_`)
VALUES
    (1, '告警事件消息通知', '001', 0, NULL, 1, '规则告警中配置消息通知，消息通知会配置通知人', '${deviceName}（${deviceCode}）发生 ${eventName} 事件', NULL, NULL, NULL, NULL, NULL, NULL, 0, '${eventName}'),
    (2, '餐厨垃圾管理收运计划工作任务通知', '002', 0, '0902', 1, '当收运计划生成收运任务时，则给收运人员发送一条消息', '${taskName}，包含${pointNum}个收运点，具体为 ${pointNames}，需要及时收运', NULL, NULL, NULL, NULL, NULL, NULL, 0, '${taskName}'),
    (3, '垃圾运收管理收运计划工作任务通知', '003', 0, '0903', 1, '当收运计划生成收运任务时，则给收运人员发送一条消息', '${taskName}，包含${pointNum}个收运点，具体为 ${pointNames}，需要及时收运', NULL, NULL, NULL, NULL, NULL, NULL, 0, '${taskName}'),
    (4, '排水户档案-许可证过期通知', '004', 0, '1005', 1, '排水户档案-许可证过期，消息通知给所有具有智慧管网权限的用户', '${archivesName}排水许可证过期，请更新排水许可证！', NULL, NULL, NULL, NULL, NULL, NULL, 0, '排水许可证过期');
