-- 告警规则关联账号或角色
ALTER TABLE `base_rule_engine`
    ADD COLUMN `message_type` varchar(32) DEFAULT NULL COMMENT '消息类型 1站内消息 2短信 支持多选 逗号隔开',
    ADD COLUMN `notification_type` int(11) DEFAULT NULL COMMENT '通知人类型 0按角色 1按用户';
CREATE TABLE `base_alarm_notification`
(
    `id_` bigint(20) NOT NULL AUTO_INCREMENT,
    `rule_engine_id` bigint(20) DEFAULT NULL COMMENT '告警规则配置id',
    `notification_type` int(11) DEFAULT '0' COMMENT '通知人类型 0按角色 1按用户',
    `relation_id` varchar(32) DEFAULT NULL COMMENT '关联实体id 用户id 或 角色id',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
    `deleted_` int(11) DEFAULT '0' COMMENT '是否删除，0否，1删除',
    PRIMARY KEY (`id_`)
) COMMENT='告警关联通知人';