CREATE TABLE `base_event_type`
(
    `code_`           varchar(32) NOT NULL,
    `p_code_`         varchar(32) NOT NULL,
    `type_`           tinyint(1) NOT NULL COMMENT '0 大类代码  1 小类代码  2 细分类代码  深度',
    `name_`           varchar(50)  DEFAULT NULL,
    `full_path_name_` varchar(256) DEFAULT NULL COMMENT '全路径，“/”分割',
    UNIQUE KEY `uk_code_` (`code_`)
) COMMENT '事件类型表';

CREATE TABLE `base_rule_engine`
(
    `id_`          bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code_`        varchar(50) NOT NULL COMMENT '规则编号 系统生成的唯一标识码，系统自动生成，GZGJ+8位序号',
    `name_`        varchar(50) NOT NULL COMMENT '规则名称',
    `model_code_`  varchar(32) DEFAULT NULL COMMENT '大类code 如0801 智慧畅行（园区畅行）来自 事件类型表小类代码',
    `event_code_`  varchar(32) DEFAULT NULL COMMENT '细分小类code 如080101 违规使用远光灯告警 来自 事件类型表细分小类代码',
    `alarm_level_` int(2) DEFAULT NULL COMMENT '告警等级 1-紧急告警 2-重要告警 3-次要告警 4-提示告警',
    `status_`      int(2) DEFAULT '0' COMMENT '状态 0-停用 1-启用',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime    DEFAULT NULL COMMENT '创建时间',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`      text COMMENT '备注',
    PRIMARY KEY (`id_`),
    UNIQUE KEY `uk_code_`(`code_`),
    KEY            `idx_model_code_` (`model_code_`)
) COMMENT='规则引擎';

CREATE TABLE `base_rule_trigger`
(
    `id_`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `algorithm_type` int(2) DEFAULT NULL COMMENT '算法类型：1阈值',
    `rule_engine_id` bigint(20) DEFAULT NULL  COMMENT '规则引擎id',
    `sort_flag`      varchar(20) DEFAULT NULL COMMENT '排序标记',
    `device_unit_id` varchar(32) DEFAULT NULL COMMENT '设备型号id',
    `device_scope_`  int(1) DEFAULT '0' COMMENT '设备范围 0全部设备 1自定义设备',
    `creator_id_`    bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime    DEFAULT NULL COMMENT '创建时间',
    `modify_id_`     bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime    DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id_`)
) COMMENT='规则触发器';

CREATE TABLE `baes_rule_trigger_ref_device`
(
    `id_`             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `rule_trigger_id` bigint(20) NOT NULL COMMENT '触发器id',
    `device_code`     varchar(32) NOT NULL COMMENT '设备编号',
    PRIMARY KEY (`id_`),
    UNIQUE KEY `rule_trigger_id_device_code_index` (`rule_trigger_id`,`device_code`) USING BTREE
) COMMENT='规则联动配置关联设备表';

CREATE TABLE `baes_intelligent_rule_expression`
(
    `id_`                        bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `rule_trigger_id`            bigint(20) NOT NULL COMMENT '触发器id',
    `device_attribute_id`        varchar(32)   DEFAULT NULL COMMENT '设备属性id',
    `device_attribute_parent_id` varchar(32)   DEFAULT NULL COMMENT '父属性id',
    `calculate_sign`             varchar(10)   DEFAULT NULL COMMENT '算术符',
    `expression`                 varchar(1024) DEFAULT NULL COMMENT '表达式',
    `value`                      varchar(50)   DEFAULT NULL COMMENT '数值',
    `sort_no`                    int(8) DEFAULT NULL COMMENT '排序',
    `logic_code`                 varchar(50)   DEFAULT NULL COMMENT '逻辑运算符（与、或）',
    PRIMARY KEY (`id_`)
) COMMENT='触发器对应的表达式';

-- 设备型号属性
alter table `base_device_unit_property`
    add column  `unit_` varchar(50) DEFAULT NULL COMMENT '单位',
    add column  `specs_` varchar(3000) DEFAULT NULL COMMENT '规范';

-- 支持算法类型，所属业务模块id
ALTER TABLE `base_event_type` ADD COLUMN `algorithm_types_` varchar(32) DEFAULT NULL COMMENT '支持算法类型 如 1,2,3 ',
    ADD COLUMN `model_id_` int(2) DEFAULT NULL COMMENT '所属业务模块id';
