# drop table if exists base_dictionary;
CREATE TABLE `base_dictionary`
(
    `id_`            BIGINT(20)    NOT NULL auto_increment COMMENT '自增ID',
    `dict_code_`     VARCHAR(16)   NOT NULL COMMENT '编码',
    `dict_desc_`     VARCHAR(64)   NOT NULL COMMENT '名称',
    `category_name_` VARCHAR(64)   NOT NULL COMMENT '分类名称',
    `category_desc_` VARCHAR(64)   NULL COMMENT '分类说明',
    `sort_no_`       double(10, 2) NULL COMMENT '排序编号',
    `create_time_`   datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time_`   datetime DEFAULT CURRENT_TIMESTAMP ON UPdate CURRENT_TIMESTAMP COMMENT '修改时间',
    `creator_id_`    bigint(20) COMMENT '创建人',
    `modify_id_`     bigint(20) COMMENT '修改人',
    `deleted_`       bigint(20)    NOT NULL COMMENT '是否删除字段 0:未删除; 其他-删除',
    PRIMARY KEY (`id_`),
    UNIQUE INDEX `dict_code_category_code` (`dict_code_`, `category_name_`, `deleted_`)
) COMMENT ='系统数据字典' ENGINE = InnoDB;

# INSERT INTO base_dictionary (dict_code_, dict_desc_, category_name_, category_desc_, sort_no_, create_time_,
#                              modify_time_, creator_id_, modify_id_)
# VALUES ('1', '快速路', '道路等级', '道路等级的4类', 1.1, '2023-03-22 14:34:30.406', '2023-03-22 14:34:30.406', 10726, 10726);