INSERT INTO base_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_, application_id_, access_type_, service_code_, deleted_)
VALUES('4080203', '40802', '任务方案管理', 'emergencyResponse:emergencyPlan:schemeManage', NULL, 3, NULL, NULL, 40802.30, 1, '/emergencyPlan/taskPlanManagement', NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1, NULL, 0);

INSERT INTO basedb.base_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_, application_id_, access_type_, service_code_, deleted_)
VALUES('408020301', '4080203', '新增', 'emergencyResponse:emergencyPlan:schemeManage:add', NULL, 3, NULL, NULL, 4080203.10, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1, NULL, 0);

INSERT INTO basedb.base_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_, application_id_, access_type_, service_code_, deleted_)
VALUES('408020302', '4080203', '编辑', 'emergencyResponse:emergencyPlan:schemeManage:edit', NULL, 3, NULL, NULL, 4080203.20, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1, NULL, 0);

INSERT INTO basedb.base_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_, application_id_, access_type_, service_code_, deleted_)
VALUES('408020303', '4080203', '删除', 'emergencyResponse:emergencyPlan:schemeManage:batchDel', NULL, 3, NULL, NULL, 4080203.30, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 4, 1, NULL, 0);