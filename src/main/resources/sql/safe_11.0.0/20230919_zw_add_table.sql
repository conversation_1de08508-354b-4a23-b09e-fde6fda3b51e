CREATE TABLE `safe_emergency_task_scheme`
(
    `id_`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键,自增',
    `scheme_name_`   varchar(300) DEFAULT NULL COMMENT '方案名称',
    `task_num_`      int(11) DEFAULT NULL COMMENT '任务数量',
    `executor_type_` int(11) DEFAULT NULL COMMENT '执行模式,1简单模式,2流程模式',
    `remark_`        varchar(500) DEFAULT NULL COMMENT '备注',
    `creator_id_`    bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`   datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id_`     bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`   datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`       int(11) DEFAULT '0' COMMENT '是否删除,0否,非0是',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB COMMENT='应急任务方案';

CREATE TABLE `safe_emergency_task`
(
    `id_`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键,自增',
    `scheme_id_`         bigint(20) DEFAULT NULL COMMENT '应急方案id',
    `task_name_`         varchar(200)  DEFAULT NULL COMMENT '任务名称',
    `task_type_`         bigint(20) DEFAULT NULL COMMENT '任务类型',
    `task_duration_` double DEFAULT NULL COMMENT '任务执行时长,单位小时',
    `step_no_`           int(11) DEFAULT NULL COMMENT '任务步骤编号,从1开始',
    `task_content_`      varchar(400)  DEFAULT NULL COMMENT '任务描述',
    `executor_type_`     int(11) DEFAULT NULL COMMENT '应急执行人类型,1应急人员,2应急队伍',
    `szjd_`              varchar(200)  DEFAULT NULL COMMENT '所在街道',
    `szjd_code_`         varchar(50)   DEFAULT NULL COMMENT '所在街道编码',
    `szsq_`              varchar(200)  DEFAULT NULL COMMENT '所在社区',
    `szsq_code_`         varchar(100)  DEFAULT NULL COMMENT '所在社区编码',
    `szdywg_`            varchar(200)  DEFAULT NULL COMMENT '所在单元网格',
    `szdywg_code_`       varchar(100)  DEFAULT NULL COMMENT '所在单元网格编码',
    `area_path_`         varchar(600)  DEFAULT NULL COMMENT '区域全路径',
    `area_points_`       varchar(100)  DEFAULT NULL COMMENT '任务具体位置,格式[经度,维度],[]...',
    `before_task_names_` varchar(4000) DEFAULT NULL COMMENT '前置任务名称,逗号隔开',
    `before_task_ids_`   varchar(2000) DEFAULT NULL COMMENT '前置任务ids,逗号隔开',
    `remark_`            varchar(500)  DEFAULT NULL COMMENT '备注',
    `creator_id_`        bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`       datetime      DEFAULT NULL COMMENT '创建时间',
    `modify_id_`         bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`       datetime      DEFAULT NULL COMMENT '修改时间',
    `deleted_`           int(11) DEFAULT '0' COMMENT '是否删除,0否,非0是',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB COMMENT='应急任务';

CREATE TABLE `safe_emergency_task_executor`
(
    `id_`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键,自增',
    `executor_id_`           bigint(20) DEFAULT NULL COMMENT '执行人id',
    `executor_name_`         varchar(200) DEFAULT NULL COMMENT '执行人名称,冗余',
    `emergency_people_type_` int(1) DEFAULT NULL COMMENT '应急执行人类型,1应急人,2应急队伍',
    `executor_team_id_`      bigint(20) DEFAULT NULL COMMENT '应急队伍id,应急队伍时非空',
    `task_id_`               bigint(20) DEFAULT NULL COMMENT '应急任务id',
    `creator_id_`            bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_`           datetime     DEFAULT NULL COMMENT '创建时间',
    `modify_id_`             bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_`           datetime     DEFAULT NULL COMMENT '修改时间',
    `deleted_`               int(11) DEFAULT '0' COMMENT '是否删除,0否,非0删除',
    PRIMARY KEY (`id_`)
) ENGINE=InnoDB COMMENT='应急任务执行人';