-- ----------------------------
-- 人员表
-- ----------------------------
CREATE TABLE `livable_people`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `mobile_`      varchar(32) DEFAULT NULL COMMENT '手机号',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime    DEFAULT NULL COMMENT '创建时间',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`      varchar(32) default NULL COMMENT '备注',
    `deleted_`     int(1) DEFAULT '0' COMMENT '是否删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE,
    KEY `mobile_` (`mobile_`) USING BTREE
) COMMENT='人员表';

-- 人员定位设备表
CREATE TABLE `livable_people_position_device`
(
    `id_`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `device_code_` varchar(32) NOT NULL COMMENT '物联网平台设备编码',
    `use_status_`  int(1) DEFAULT '1' COMMENT '使用状态(1启用0禁用)',
    `creator_id_`  bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime    DEFAULT NULL COMMENT '创建时间',
    `modify_id_`   bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime    DEFAULT NULL COMMENT '修改时间',
    `remark_`      varchar(32) default NULL COMMENT '备注',
    `deleted_`     int(1) DEFAULT '0' COMMENT '是否删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE,
    KEY `device_code_` (`device_code_`) USING BTREE
) COMMENT='人员定位设备表';

-- 权限
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('3060105', '306', '人员管理', 'ef_basic:peopleManagement', '人员管理', 2, NULL, NULL,
        30601.50, 1, '/electronicFenceManagement/peopleManagement', NULL, NULL, NULL, NULL, NULL,
        NULL, 0, 3, 1, NULL, 0);


INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('3060106', '306', '人员工牌设备管理', 'ef_basic:peoplePositionDeviceManagement',
        '人员工牌设备管理', 2, NULL, NULL, 30601.60, 1,
        '/electronicFenceManagement/peoplePositionDeviceManagement', NULL, NULL, NULL, NULL, NULL,
        NULL, 0, 3, 1, NULL, 0);


INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('3060107', '306', '人员轨迹查看', 'ef_basic:peopleTrack', '人员轨迹查看', 2, NULL, NULL,
        30601.70, 1, '/electronicFenceManagement/peopleTrack', NULL, NULL, NULL, NULL, NULL, NULL,
        0, 3, 1, NULL, 0);

-- 按钮权限 关联人员
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('306010501', '3060105', '关联人员', 'ef_basic:peopleManagement:relation', '关联人员', 3, NULL,
        NULL, 30601.501, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 3, 1, NULL, 0);
-- 删除人员
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('306010502', '3060105', '删除人员', 'ef_basic:peopleManagement:delete', '删除人员', 3, NULL,
        NULL, 30601.502, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 3, 1, NULL, 0);

-- 按钮权限 人员工牌设备
-- 关联设备
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('306010601', '3060106', '关联设备', 'ef_basic:peoplePositionDeviceManagement:relation',
        '关联设备', 3, NULL, NULL, 30601.601, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 3,
        1, NULL, 0);
-- 删除人员工牌设备
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('306010602', '3060106', '删除设备', 'ef_basic:peoplePositionDeviceManagement:delete',
        '删除设备', 3, NULL, NULL, 30601.602, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 3,
        1, NULL, 0);
-- 启用/停用 人员工牌设备
INSERT INTO `base_privilege` (`id_`, `parent_id_`, `name_`, `privilege_code_`, `description_`,
                              `level_`, `target`, `search_code_`, `seq_`, `type_`, `url_`,
                              `is_log_`, `create_time_`, `creator_`, `modifier_`, `modify_time_`,
                              `icon_name`, `flag_`, `application_id_`, `access_type_`,
                              `service_code_`, `deleted_`)
VALUES ('306010603', '3060106', '启用/停用', 'ef_basic:peoplePositionDeviceManagement:enable',
        '启用/停用', 3, NULL, NULL, 30601.603, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 3,
        1, NULL, 0);

-- 电子围栏-人员定位设备
INSERT INTO `base_dictionary` (`dict_code_`, `dict_desc_`, `application_id_`, `model_`,
                               `category_code_`, `category_name_`, `category_desc_`, `sort_no_`,
                               `create_time_`, `modify_time_`, `creator_id_`, `modify_id_`,
                               `deleted_`)
VALUES ('37', '电子围栏-人员定位设备', 3, '0906', 'application_model', '设备模块', '设备模块', 1.00,
        '2023-05-11 16:35:31', '2023-06-12 10:30:19', NULL, NULL, 0);




