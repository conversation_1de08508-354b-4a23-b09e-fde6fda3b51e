-- 构造火情告警数据
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1001-alarm', NULL, '34', 'hqjc-1001-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500","https://n.sinaimg.cn/sinakd20201028s/512/w750h562/20201028/8c4a-kcaeqzx7193168.jpg","https://rexian.cnwest.com/data/img/2018/06/30/0jY9KZzTUfNDZpiAfyDC.png","https://p7.itc.cn/q_70/images03/20201118/c34ffc97c0584a7abd2513538d15ea58.png"]}', '2023-10-06 10:26:52', NULL, '2023-10-06 14:26:58', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1002-alarm', NULL, '34', 'hqjc-1001-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-06 10:26:52', NULL, '2023-10-07 14:26:58', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1003-alarm', NULL, '34', 'hqjc-1001-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-07 10:26:52', NULL, '2023-10-08 10:26:57', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1004-alarm', NULL, '34', 'hqjc-1001-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-08 10:26:52', NULL, '2023-10-08 10:26:58', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1005-alarm', NULL, '34', 'hqjc-1002-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-08 10:26:52', NULL, '2023-10-08 10:26:58', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1006-alarm', NULL, '34', 'hqjc-1003-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-07 10:26:52', NULL, '2023-10-08 10:26:58', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1007-alarm', NULL, '34', 'hqjc-1004-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-06 10:26:52', NULL, '2023-10-08 10:26:58', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1008-alarm', NULL, '34', 'hqjc-1004-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-04 10:26:52', NULL, '2023-10-04 10:26:52', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1009-alarm', NULL, '34', 'hqjc-1006-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-05 10:26:52', NULL, '2023-10-05 10:26:52', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1010-alarm', NULL, '34', 'hqjc-1007-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-08 10:26:52', NULL, '2023-10-08 10:26:52', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1011-alarm', NULL, '34', 'hqjc-1007-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-09 10:26:52', NULL, '2023-10-09 10:26:52', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1012-alarm', NULL, '34', 'hqjc-1009-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-09 10:36:52', NULL, '2023-10-09 10:36:52', '2023-10-06 14:35:01', NULL);
INSERT INTO basedb.base_alarm
(code_, rule_engine_id_, model_, device_code_, alarm_type_, push_status_, work_no_, status_, level_, content_, source_json_, alarm_time_, alarm_data_, create_time_, modify_time_, remark_)
VALUES('hqjc-1013-alarm', NULL, '34', 'hqjc-1010-device', '090918', 0, NULL, 1, 1, '告警内容', '{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', '2023-10-09 10:46:52', NULL, '2023-10-09 10:46:52', '2023-10-06 14:35:01', NULL);


INSERT INTO base_device_extend_info
    (bsm,device_id,device_first_type_name,device_second_type_name,obj_id,sbly,sbmc,sbxh,sbzt,szjd,szsq,szdywg,area_path,sync_time,azsj,azwz,cjpl,create_time,creator,device_bind,dwbsm,gdfs,jcsblx,sblx,jcx,platform_type,sbcsxx,scpl,szjd_code,szsq_code,szwg_code,update_time,use_type,zbX,zbY,zcsxsj)
    VALUES
('hqjc-1001-bsm','hqjc-1001-device','火情摄像头一级类型1','火情摄像头二级类型1','hqjc-1001-obj',NULL,'火情监测设备1','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1002-bsm','hqjc-1002-device','火情摄像头一级类型1','火情摄像头二级类型2','hqjc-1002-obj',NULL,'火情监测设备2','火情监测设备型号1',1,'洪山街1','软件园社区','三单元','洪山街1@软件园社区@三单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1003-bsm','hqjc-1003-device','火情摄像头一级类型2','火情摄像头二级类型1','hqjc-1003-obj',NULL,'火情监测设备3','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1004-bsm','hqjc-1004-device','火情摄像头一级类型2','火情摄像头二级类型2','hqjc-1004-obj',NULL,'火情监测设备4','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1005-bsm','hqjc-1005-device','火情摄像头一级类型3','火情摄像头二级类型1','hqjc-1005-obj',NULL,'火情监测设备5','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1006-bsm','hqjc-1006-device','火情摄像头一级类型3','火情摄像头二级类型2','hqjc-1006-obj',NULL,'火情监测设备6','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1007-bsm','hqjc-1007-device','火情摄像头一级类型4','火情摄像头二级类型1','hqjc-1007-obj',NULL,'火情监测设备7','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1008-bsm','hqjc-1008-device','火情摄像头一级类型4','火情摄像头二级类型2','hqjc-1008-obj',NULL,'火情监测设备8','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1009-bsm','hqjc-1009-device','火情摄像头一级类型1','火情摄像头二级类型3','hqjc-1009-obj',NULL,'火情监测设备9','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1010-bsm','hqjc-1010-device','火情摄像头一级类型1','火情摄像头二级类型4','hqjc-1010-obj',NULL,'火情监测设备10','火情监测设备型号1',1,'洪山街1','软件园社区','一单元','洪山街1@软件园社区@一单元',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);


INSERT INTO basedb.base_obj_info (obj_id,obj_name,init_date,modify_date,contact_person,contact_phone,obj_state,data_source,dept_name,first_obj_category_name,second_obj_category_name,note,objX,objY,op_enterprise_name,owner_enterprise_name,szjd,szsq,szdywg,area_path,remark,bg_id,create_time,creator,dept_code,detail_address,first_obj_category,obj_qr_code,op_enterprise_code,owner_enterprise_code,second_obj_category,szjd_code,szsq_code,szwg_code,update_time,sync_time)
VALUES
('hqjc-1003-obj','火情监测部件3','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道4','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1004-obj','火情监测部件4','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道14','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1005-obj','火情监测部件5','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道14','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1006-obj','火情监测部件6','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道14','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1007-obj','火情监测部件7','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道14','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1008-obj','火情监测部件8','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道14','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1009-obj','火情监测部件9','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道14','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),
('hqjc-1010-obj','火情监测部件10','2023-04-10 19:03:12','2023-04-10 19:03:14','阚架构','22323',1,0,'主管部门1','类型15','类型14','测试',114.30671,35.20266,'养护','权属','所在街道14','所在社区14','所在单元网格14','所在街道14@所在社区14@所在单元网格14','火情监测',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);


-- 火情监测设备

CREATE TABLE `livable_fire_monitoring_device` (
  `id_` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `device_code_` varchar(32) NOT NULL COMMENT '物联网平台设备编码',
  `use_status_` int(1) DEFAULT '1' COMMENT '使用状态(1启用0禁用)',
  `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time_` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
  `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
  `remark_` varchar(32) default NULL COMMENT '备注',
  `deleted_` int(1) DEFAULT '0' COMMENT '是否删除，0存在',
  PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='火情监控设备';


INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1001-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1002-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1003-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1004-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1005-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1006-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1007-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1008-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1009-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);
INSERT INTO livable_fire_monitoring_device (device_code_, use_status_, creator_id_, create_time_, modify_id_, modify_time_, deleted_) VALUES('hqjc-1010-device', 1, 10726, '2023-04-18 14:32:43', 10726, '2023-04-18 20:22:40', 0);

UPDATE basedb.base_obj_info
SET obj_name='火情监测部件3', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30645, objY=35.20245, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道4', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1003-obj';
UPDATE basedb.base_obj_info
SET obj_name='火情监测部件4', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30646, objY=35.20236, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道14', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1004-obj';
UPDATE basedb.base_obj_info
SET obj_name='火情监测部件5', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30647, objY=35.20227, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道14', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1005-obj';
UPDATE basedb.base_obj_info
SET obj_name='火情监测部件6', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30648, objY=35.20288, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道14', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1006-obj';
UPDATE basedb.base_obj_info
SET obj_name='火情监测部件7', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30649, objY=35.20269, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道14', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1007-obj';
UPDATE basedb.base_obj_info
SET obj_name='火情监测部件8', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.3037, objY=35.20258, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道14', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1008-obj';
UPDATE basedb.base_obj_info
SET obj_name='火情监测部件9', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30631, objY=35.20241, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道14', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1009-obj';
UPDATE basedb.base_obj_info
SET obj_name='火情监测部件10', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30632, objY=35.20292, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道14', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道14@所在社区14@所在单元网格14', remark='火情监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='hqjc-1010-obj';
UPDATE basedb.base_obj_info
SET obj_name='湖渠监测部件1', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.30633, objY=35.20243, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道4', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道4@所在社区14@所在单元网格14', remark='湖渠监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='OBJ-HQJC-1';
UPDATE basedb.base_obj_info
SET obj_name='湖渠监测部件2', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.40634, objY=35.10254, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道4', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道4@所在社区14@所在单元网格14', remark='湖渠监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='OBJ-HQJC-2';
UPDATE basedb.base_obj_info
SET obj_name='湖渠监测部件3', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.50635, objY=35.30255, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道4', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道4@所在社区14@所在单元网格14', remark='湖渠监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='OBJ-HQJC-3';
UPDATE basedb.base_obj_info
SET obj_name='湖渠监测部件4', init_date='2023-04-10 19:03:12', modify_date='2023-04-10 19:03:14', contact_person='阚架构', contact_phone='22323', obj_state=1, data_source=0, dept_name='主管部门1', first_obj_category_name='类型15', second_obj_category_name='类型14', note='测试', objX=114.20636, objY=35.50266, op_enterprise_name='养护', owner_enterprise_name='权属', szjd='所在街道4', szsq='所在社区14', szdywg='所在单元网格14', area_path='所在街道4@所在社区14@所在单元网格14', remark='湖渠监测', bg_id=NULL, create_time=NULL, creator=NULL, dept_code=NULL, detail_address=NULL, first_obj_category=NULL, obj_qr_code=NULL, op_enterprise_code=NULL, owner_enterprise_code=NULL, second_obj_category=NULL, szjd_code=NULL, szsq_code=NULL, szwg_code=NULL, update_time=NULL, sync_time=NULL
WHERE obj_id='OBJ-HQJC-4';


UPDATE basedb.base_alarm
SET code_='hqjc-1001-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1001-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500","https://n.sinaimg.cn/sinakd20201028s/512/w750h562/20201028/8c4a-kcaeqzx7193168.jpg","https://rexian.cnwest.com/data/img/2018/06/30/0jY9KZzTUfNDZpiAfyDC.png","https://p7.itc.cn/q_70/images03/20201118/c34ffc97c0584a7abd2513538d15ea58.png"]}', alarm_time_='2023-10-06 10:26:52', alarm_data_=NULL, create_time_='2023-10-06 14:26:58', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=124;
UPDATE basedb.base_alarm
SET code_='hqjc-1002-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1001-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-06 10:26:52', alarm_data_=NULL, create_time_='2023-10-07 14:26:58', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=125;
UPDATE basedb.base_alarm
SET code_='hqjc-1003-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1001-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-07 10:26:52', alarm_data_=NULL, create_time_='2023-10-08 10:26:57', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=126;
UPDATE basedb.base_alarm
SET code_='hqjc-1004-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1001-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-08 10:26:52', alarm_data_=NULL, create_time_='2023-10-08 10:26:58', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=127;
UPDATE basedb.base_alarm
SET code_='hqjc-1005-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1002-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-25 10:26:52', alarm_data_=NULL, create_time_='2023-10-25 10:26:58', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=128;
UPDATE basedb.base_alarm
SET code_='hqjc-1006-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1003-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-21 10:26:52', alarm_data_=NULL, create_time_='2023-10-08 10:26:58', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=129;
UPDATE basedb.base_alarm
SET code_='hqjc-1007-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1004-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-06 10:26:52', alarm_data_=NULL, create_time_='2023-10-08 10:26:58', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=130;
UPDATE basedb.base_alarm
SET code_='hqjc-1008-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1004-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-24 10:26:52', alarm_data_=NULL, create_time_='2023-10-04 10:26:52', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=131;
UPDATE basedb.base_alarm
SET code_='hqjc-1009-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1006-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-25 10:26:52', alarm_data_=NULL, create_time_='2023-10-05 10:26:52', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=132;
UPDATE basedb.base_alarm
SET code_='hqjc-1010-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1007-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-08 10:26:52', alarm_data_=NULL, create_time_='2023-10-08 10:26:52', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=133;
UPDATE basedb.base_alarm
SET code_='hqjc-1011-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1007-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-26 10:26:52', alarm_data_=NULL, create_time_='2023-10-09 10:26:52', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=134;
UPDATE basedb.base_alarm
SET code_='hqjc-1012-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1009-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-26 10:36:52', alarm_data_=NULL, create_time_='2023-10-09 10:36:52', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=135;
UPDATE basedb.base_alarm
SET code_='hqjc-1013-alarm', rule_engine_id_=NULL, model_='34', device_code_='hqjc-1010-device', alarm_type_='090918', push_status_=0, work_no_=NULL, status_=1, level_=1, content_='告警内容', source_json_='{"alarmCaptureUrlList":["https://img1.baidu.com/it/u=2723699319,3553484640&fm=253&fmt=auto&app=138&f=JPEG?w=750&h=500"]}', alarm_time_='2023-10-26 10:46:52', alarm_data_=NULL, create_time_='2023-10-09 10:46:52', modify_time_='2023-10-06 14:35:01', remark_=NULL
WHERE id_=136;

