-- ----------------------------
-- 管道分析配置标准表
-- ----------------------------
CREATE TABLE `safe_pipeline_standard`
(
    `id_`              bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `num_`             int(20)       DEFAULT NULL COMMENT '设备告警次数',
    `week_value_`      double(4,2)   DEFAULT NULL COMMENT '周达标判断',
    `month_value_`     double(4,2)   DEFAULT NULL COMMENT '月达标判断',
    `year_value_`      double(4,2)   DEFAULT NULL COMMENT '年达标判断',
    `effect_start_time_`  datetime DEFAULT NULL COMMENT '生效开始时间',
    `effect_end_time_`    datetime DEFAULT NULL COMMENT '生效结束时间',
	`creator_id_`      bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`     datetime      DEFAULT NULL COMMENT '创建时间',
	`modify_id_`       bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`     datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`          text          COMMENT  '备注',
	`deleted_`         int(1)        DEFAULT '0' COMMENT '是否删除，1删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT ='管道分析配置标准表';
-- 默认数据
INSERT INTO `safe_pipeline_standard`(`id_`, `num_`, `week_value_`, `month_value_`, `year_value_`, `effect_start_time_`, `effect_end_time_`, `creator_id_`, `create_time_`, `modify_id_`, `modify_time_`, `remark_`, `deleted_`) VALUES (1, 10, 10.00, 10.00, 10.00, '1970-01-01 00:00:00', '2199-12-31 23:59:59', 1, '2023-09-05 13:57:55', 1, '2023-09-05 13:57:58', '默认数据', 0);

-- ----------------------------
-- 管道设备检测趋势统计表
-- ----------------------------
CREATE TABLE `safe_pipeline_trend_statistic`
(
    `id_`              bigint(20)    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `device_code_`     varchar(32)   DEFAULT null comment '设备编号',
    `identifier_`      varchar(32)   DEFAULT null comment '监测项标识',
    `record_time_`     datetime      DEFAULT NULL COMMENT '记录时间',
    `max_value_`       double(20,2)  DEFAULT NULL COMMENT '最大值',
    `max_time_`        datetime      DEFAULT NULL COMMENT '最大值的时间',
    `min_value_`       double(20,2)  DEFAULT NULL COMMENT '最小值',
    `min_time_`        datetime      DEFAULT NULL COMMENT '最小值的时间',
    `avg_value_`       double(20,2)  DEFAULT NULL COMMENT '平均值',
    `flow_count_`      int(11)       DEFAULT NULL COMMENT '上报次数',
	`creator_id_`      bigint(20)    DEFAULT NULL COMMENT '创建人id',
	`create_time_`     datetime      DEFAULT NULL COMMENT '创建时间',
	`modify_id_`       bigint(20)    DEFAULT NULL COMMENT '修改人id',
	`modify_time_`     datetime      DEFAULT NULL COMMENT '修改时间',
	`remark_`          text          COMMENT  '备注',
	`deleted_`         int(1)        DEFAULT '0' COMMENT '是否删除，1删除，0存在',
    PRIMARY KEY (`id_`) USING BTREE
) COMMENT ='管道设备检测趋势统计表';
