<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.deployManage.mapper.DeployCarMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.deployManage.entity.DeployCar">
        <id column="id_" property="id"/>
        <result column="car_no_" property="carNo"/>
        <result column="car_type_" property="carType"/>
        <result column="car_color_" property="carColor"/>
        <result column="img_url_" property="imgUrl"/>
        <result column="tag_" property="tag"/>
        <result column="remark_" property="remark"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="deleted_" property="deleted"/>
    </resultMap>
    <resultMap id="CarDtoMap" extends="BaseResultMap"
               type="com.smartPark.business.deployManage.entity.dto.DeployCarDTO">
        <result column="eff_strategy_num" property="effStrategyNum"/>
        <association property="deployRecord" column="{baseId=id_,baseType=base_type_}"
                     select="com.smartPark.business.deployManage.mapper.DeployRecordMapper.getLastRecord"/>
        <collection property="groupList" column="id_"
                    select="com.smartPark.business.deployManage.mapper.DeployGroupMapper.getCarBindGroupList"/>
    </resultMap>
    <resultMap id="CarDtoDetailMap" extends="BaseResultMap"
               type="com.smartPark.business.deployManage.entity.dto.DeployCarDTO">
        <result column="eff_strategy_num" property="effStrategyNum"/>
        <collection property="groupList" column="id_"
                    select="com.smartPark.business.deployManage.mapper.DeployGroupMapper.getCarBindGroupList"/>
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from safe_deploy_car
        where deleted_ = 0
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from safe_deploy_car
        where id_ = #{id}
    </select>

    <select id="countPersonNumByGroupId" parameterType="long" resultType="java.lang.Integer">
        select count(*)
        from safe_deploy_person_group_ref sdpgr
                 left join safe_deploy_person sdp on
            sdpgr.person_id_ = sdp.id_
        where sdpgr.deleted_ = 0
          and sdp.deleted_ = 0
          and sdpgr.group_id_ = #{groupId}
    </select>

    <select id="selectDtoPage" parameterType="com.smartPark.business.deployManage.entity.vo.DeployCarVo"
            resultMap="CarDtoMap">
        select *, 2 as base_type_
        from
        (
        select sdc.*,
        (
        select count(*)
        from safe_deploy_strategy_group_ref sdsgr
                 left join safe_deploy_strategy sds on
            sdsgr.strategy_id_ = sds.id_
                 left join safe_deploy_car_group_ref sdcgr on
            sdcgr.group_id_ = sdsgr.group_id_
        where sds.deleted_ = 0
          and sdsgr.deleted_ = 0
          and sdcgr.deleted_ = 0
          and sdcgr.car_id_ = sdc.id_
        <if test="deployCarVo.now != null">
            <![CDATA[
            and sds.start_time_ <= #{deployCarVo.now}
            and sds.end_time_ >= #{deployCarVo.now}
            ]]>
        </if>

        and sds.strategy_type_ = 2
        <if test="deployCarVo.deployGroupList != null and deployCarVo.deployGroupList.size() != 0">
            and sdsgr.group_id_ in
            <foreach collection="deployCarVo.deployGroupList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item.id}
            </foreach>
        </if>
        ) as eff_strategy_num
        from safe_deploy_car sdc
        where sdc.deleted_ = 0
        <if test="deployCarVo.carNo != null and deployCarVo.carNo != ''">
            and sdc.car_no_ like concat('%', #{deployCarVo.carNo}, '%')
        </if>
        <if test="deployCarVo.tag != null and deployCarVo.tag != ''">
            and sdc.tag_ like concat('%', #{deployCarVo.tag}, '%')
        </if>

        <if test="deployCarVo.deployGroupList != null and deployCarVo.deployGroupList.size() != 0">
            and sdc.id_ in ( select sdcgr.car_id_
                             from safe_deploy_car_group_ref sdcgr where sdcgr.deleted_ = 0
                                                                    and sdcgr.group_id_ in
            <foreach collection="deployCarVo.deployGroupList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item.id}
            </foreach>
            )
        </if>
        <if test="deployCarVo.cameraName != null and deployCarVo.cameraName != ''">
            and sdp.id_ in (SELECT DISTINCT sdpgr.person_id_
                            FROM safe_deploy_group sdg
                                     LEFT JOIN safe_deploy_person_group_ref sdpgr
                                               ON sdpgr.group_id_ = sdg.id_
                                     LEFT JOIN safe_deploy_strategy_group_ref sdsgr
                                               ON sdpgr.group_id_ = sdsgr.group_id_
                            WHERE sdsgr.strategy_id_ IN
                                  (SELECT DISTINCT sds.id_
                                   FROM safe_deploy_strategy sds
                                            LEFT JOIN safe_deploy_strategy_camera_group_ref sdsgrc
                                                      ON sds.id_ = sdsgrc.strategy_id_
                                   WHERE sdsgrc.group_id_ IN
                                         (SELECT DISTINCT sdcgr.camera_group_id_
                                          FROM safe_deploy_camera sdc
                                                   LEFT JOIN safe_deploy_camera_group_ref sdcgr
                                                             ON sdcgr.camera_id_ = sdc.id_
                                          WHERE (sdc.camera_name_ LIKE
                                                concat('%', #{deployCarVo.cameraName}, '%') or sdc.camera_no_ LIKE
                                                concat('%', #{deployCarVo.cameraName}, '%'))
                                            AND sdc.deleted_ = 0
                                            AND sdcgr.deleted_ = 0)
                                     AND sds.deleted_ = 0
                                     AND sdsgrc.deleted_ = 0
                                     AND sds.strategy_type_ = 2)
                              AND sdg.group_type_ = 2
                              AND sdg.deleted_ = 0
                              AND sdsgr.deleted_ = 0
                              AND sdpgr.deleted_ = 0)
        </if>

        <if test="deployCarVo.startTime != null">
            <![CDATA[
            and sdc.create_time_ >= #{deployCarVo.startTime}
            ]]>
        </if>
        <if test="deployCarVo.endTime != null">
            <![CDATA[
            and sdc.create_time_ <= #{deployCarVo.endTime}
            ]]>
        </if>
        ) s
        where 1 = 1
        <if test="deployCarVo.deployStatus != null">
            <if test="deployCarVo.deployStatus == 1">
                and s.eff_strategy_num = 0
            </if>
            <if test="deployCarVo.deployStatus == 2">
                and s.eff_strategy_num > 0
            </if>
        </if>

        order by s.create_time_ desc
    </select>

    <select id="selectDtoOne" parameterType="long" resultMap="CarDtoDetailMap">
        select distinct sdc.*,
               (select count(*)
                from safe_deploy_strategy_group_ref sdsgr
                         left join safe_deploy_strategy sds on
                    sdsgr.strategy_id_ = sds.id_
                         left join safe_deploy_car_group_ref sdcgr on
                    sdcgr.group_id_ = sdsgr.group_id_
                where sds.deleted_ = 0
                  and sdcgr.deleted_ = 0
                  and sdsgr.deleted_ = 0
        <![CDATA[
                  and sds.start_time_ <= now()
                  and sds.end_time_ >= now()
            ]]>
        and sds.strategy_type_ = 2
                  and sdcgr.car_id_ = sdc.id_) as eff_strategy_num
        from safe_deploy_car sdc
                 left join safe_deploy_car_group_ref sdcgr on
            sdc.id_ = sdcgr.car_id_
        where sdc.deleted_ = 0
          and sdc.id_ = #{id}
    </select>

    <select id="selectGroupCarPage" parameterType="com.smartPark.business.deployManage.entity.vo.DeployCarPageVo"
            resultMap="BaseResultMap">
        select sdc.*
        from safe_deploy_car_group_ref sdcgr
                 left join safe_deploy_car sdc on
            sdcgr.car_id_ = sdc.id_
        where sdcgr.deleted_ = 0
          and sdc.deleted_ = 0
        <if test="deployCarPageVo.groupList != null and deployCarPageVo.groupList.size() != 0">
            and sdcgr.group_id_ in
            <foreach collection="deployCarPageVo.groupList" item="item" index="index" open="(" close=")"
                     separator=",">
                #{item.id}
            </foreach>
        </if>
        <if test="deployCarPageVo.paramKey != null and deployCarPageVo.paramKey != ''">
            and (sdc.car_no_ like concat('%', #{deployCarPageVo.paramKey}, '%')
                or sdc.tag_ like concat('%', #{deployCarPageVo.paramKey}, '%'))
        </if>
        order by sdc.create_time_ desc
    </select>
</mapper>
