<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.deployManage.mapper.DeployCameraMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.deployManage.entity.DeployCamera">
        <id column="id_" property="id"/>
        <result column="camera_name_" property="cameraName"/>
        <result column="camera_no_" property="cameraNo"/>
        <result column="camera_type_" property="cameraType"/>
        <result column="device_id_" property="deviceId"/>
        <result column="obj_id_" property="objId"/>
        <result column="device_code_" property="deviceCode"/>
        <result column="remark_" property="remark"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="deleted_" property="deleted"/>
        <result column="access_channel_" property="accessChannel"/>
        <result column="status_" property="status"/>
        <result column="is_personnel_monitoring_" property="isPersonnelMonitoring"/>
        <result column="is_vehicle_monitoring_" property="isVehicleMonitoring"/>
    </resultMap>

    <resultMap id="DeployCameraDtoMap" extends="BaseResultMap" type="com.smartPark.business.deployManage.entity.dto.DeployCameraDTO">
        <association property="objInfo" column="obj_id_"  select="com.smartPark.common.device.mapper.ObjInfoMapper.findByMonitorPointBsm"/>
        <association property="deviceUnit" column="device_unit_id" select="com.smartPark.common.device.mapper.DeviceUnitMapper.selectById"/>
        <association property="deviceExtendInfo" column="device_id_" select="com.smartPark.common.device.mapper.DeviceExtendInfoMapper.findByDeviceId"/>
        <collection property="cameraGroupList" column="id_" select="com.smartPark.business.deployManage.mapper.DeployCameraGroupMapper.getCameraGroupList"/>
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from safe_deploy_camera
        where deleted_ = 0
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from safe_deploy_camera
        where id_ = #{id}
    </select>

    <select id="selectDtoPage" parameterType="com.smartPark.business.deployManage.entity.vo.DeployCameraVo" resultMap="DeployCameraDtoMap" >
        select distinct sdc.*,bd.device_unit_id
        from safe_deploy_camera sdc
                 left join base_device_extend_info bdei on
            sdc.device_id_ = bdei.device_id
                 left join safe_deploy_camera_group_ref sdcgr on sdc.id_ = sdcgr.camera_id_
        left join base_device bd on bdei.device_id = bd.code
        where sdc.deleted_ = 0
        <if test="deployCameraVo.accessChannel != null and deployCameraVo.accessChannel != ''">
            and sdc.access_channel_ = #{deployCameraVo.accessChannel}
        </if>
        <if test="deployCameraVo.isUnion">
            <choose>
                <!--    并集值为true或null            -->
                <when test="(deployCameraVo.isPersonnelMonitoring != null and deployCameraVo.isPersonnelMonitoring) or (deployCameraVo.isVehicleMonitoring != null and deployCameraVo.isVehicleMonitoring)">
                    and (sdc.is_personnel_monitoring_ = #{deployCameraVo.isPersonnelMonitoring} or sdc.is_vehicle_monitoring_ = #{deployCameraVo.isVehicleMonitoring})
                </when>
                <when test="deployCameraVo.isPersonnelMonitoring != null">
                    and sdc.is_personnel_monitoring_ = #{deployCameraVo.isPersonnelMonitoring}
                </when>
                <when test="deployCameraVo.isVehicleMonitoring != null">
                    and sdc.is_vehicle_monitoring_ = #{deployCameraVo.isVehicleMonitoring}
                </when>
            </choose>
        </if>
        <if test="!deployCameraVo.isUnion">
            <if test="deployCameraVo.isPersonnelMonitoring != null">
                and sdc.is_personnel_monitoring_ = #{deployCameraVo.isPersonnelMonitoring}
            </if>
            <if test="deployCameraVo.isVehicleMonitoring != null">
                and sdc.is_vehicle_monitoring_ = #{deployCameraVo.isVehicleMonitoring}
            </if>
        </if>
        <if test="deployCameraVo.status != null">
            and sdc.status = #{deployCameraVo.status}
        </if>
        <if test="deployCameraVo.device != null">
            <if test="deployCameraVo.device.deviceUnitId != null">
                and bd.device_unit_id = #{deployCameraVo.device.deviceUnitId}
            </if>
        </if>
        <if test="deployCameraVo.cameraNo != null and deployCameraVo.cameraNo != ''">
            and sdc.camera_no_ like concat('%', #{deployCameraVo.cameraNo}, '%')
        </if>
        <if test="deployCameraVo.deviceId != null and deployCameraVo.deviceId != ''">
            and bdei.device_id like concat('%', #{deployCameraVo.deviceId}, '%')
        </if>
        <if test="deployCameraVo.cameraName != null and deployCameraVo.cameraName != ''">
            and bdei.sbmc like concat('%', #{deployCameraVo.cameraName}, '%')
        </if>
        <if test="deployCameraVo.deployCameraGroup != null">
            <if test="deployCameraVo.deployCameraGroup.id != null">
                and sdcgr.camera_group_id_ = #{deployCameraVo.deployCameraGroup.id}
            </if>
        </if>
        <if test="deployCameraVo.deviceExtendInfo != null">
            <if test="deployCameraVo.deviceExtendInfo.deviceSecondTypeName != null and deployCameraVo.deviceExtendInfo.deviceSecondTypeName != ''">
                and bdei.device_second_type_name = #{deployCameraVo.deviceExtendInfo.deviceSecondTypeName}
            </if>
            <if test="deployCameraVo.deviceExtendInfo.szjd != null and deployCameraVo.deviceExtendInfo.szjd != ''">
                and bdei.szjd = #{deployCameraVo.deviceExtendInfo.szjd}
            </if>
            <if test="deployCameraVo.deviceExtendInfo.szsq != null and deployCameraVo.deviceExtendInfo.szsq != ''">
                and bdei.szsq = #{deployCameraVo.deviceExtendInfo.szsq}
            </if>
            <if test="deployCameraVo.deviceExtendInfo.szdywg != null and deployCameraVo.deviceExtendInfo.szdywg != ''">
                and bdei.szdywg = #{deployCameraVo.deviceExtendInfo.szdywg}
            </if>
        </if>
        order by sdc.modify_time_ desc,sdc.id_ desc
    </select>

    <select id="selectDtoOne" resultMap="DeployCameraDtoMap">
        select sdc.*
        from safe_deploy_camera sdc
                 left join base_device_extend_info bdei on
            sdc.device_id_ = bdei.device_id
        where sdc.id_ = #{id}
    </select>

    <select id="selectCameraByGroupId" parameterType="long" resultMap="BaseResultMap">
        select
            sdc.*
        from
            safe_deploy_camera_group_ref sdcgr
                left join safe_deploy_camera sdc on
                sdcgr.camera_id_ = sdc.id_
        where
            sdc.deleted_ = 0
          and sdcgr.deleted_ = 0
          and sdcgr.camera_group_id_ = #{groupId}
    </select>

    <select id="selectCameraByGroupIds" resultMap="BaseResultMap">
        select
            sdc.*
        from
            safe_deploy_camera_group_ref sdcgr
                left join safe_deploy_camera sdc on
                sdcgr.camera_id_ = sdc.id_
        where
            sdc.deleted_ = 0
          and sdcgr.deleted_ = 0
          and sdcgr.camera_group_id_ in
          <foreach collection="groupIds" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
    </select>
</mapper>
