<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.streetlight.mapper.StreetlightPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.streetlight.entity.StreetlightPlan">
        <id column="id_" property="id" />
        <result column="plan_name_" property="planName" />
        <result column="plan_no_" property="planNo" />
        <result column="note_" property="note" />
        <result column="time_type_" property="timeType" />
        <result column="time_values_" property="timeValues" />
        <result column="control_type_" property="controlType" />
        <result column="plan_status_" property="planStatus" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.smartPark.business.streetlight.entity.vo.StreetlightPlanVo" extends="BaseResultMap">

    </resultMap>

    <select id="queryListByPage" resultMap="BaseResultMapVo">
        SELECT
            a.*
        FROM
            traffic_streetlight_plan a
        WHERE
            a.deleted_ = 0
        <if test="vo.planNo != null and vo.planNo != ''">
            AND a.plan_no_ LIKE CONCAT('%',#{vo.planNo},'%')
        </if>
        <if test="vo.planName != null and vo.planName != ''">
            AND a.plan_name_ LIKE CONCAT('%',#{vo.planName},'%')
        </if>
        <if test="vo.planStatus != null">
            AND a.plan_status_ =  #{vo.planStatus}
        </if>
        ORDER BY
            a.modify_time_ DESC
    </select>

</mapper>
