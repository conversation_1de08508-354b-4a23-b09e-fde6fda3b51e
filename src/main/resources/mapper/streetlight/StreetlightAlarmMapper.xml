<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.streetlight.mapper.StreetlightAlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.common.alarm.entity.Alarm">
        <id column="id_" property="id" />
        <id column="code_" property="code" />
        <result column="rule_engine_id_" property="ruleEngineId" />
        <result column="model_" property="model" />
        <result column="device_code_" property="deviceCode" />
        <result column="alarm_type_" property="alarmType" />
        <result column="push_status_" property="pushStatus" />
        <result column="work_no_" property="workNo" />
        <result column="status_" property="status" />
        <result column="level_" property="level" />
        <result column="content_" property="content" />
        <result column="source_json_" property="sourceJson" />
        <result column="alarm_time_" property="alarmTime" />
        <result column="alarm_data_" property="alarmData" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="findStreetlightList" resultType="com.smartPark.business.streetlight.entity.vo.StreetlightAlarmVo">
        SELECT
            a.id_ id,
            a.code_ code,
            a.device_code_ deviceCode,
            c.sbmc,
            c.bsm,
            c.obj_id objId,
            c.area_path areaPath,
            a.alarm_type_ alarmType,
            a.level_ level,
            a.push_status_ pushStatus,
            c.area_path areaPath,
            a.alarm_time_ alarmTime,
            a.work_no_ workNo,
            a.content_ content
        FROM
            base_alarm a
--                 LEFT JOIN safe_manhole b ON a.device_code_ = b.device_code_
--                 AND b.deleted_ = 0
                LEFT JOIN base_device_extend_info c ON a.device_code_ = c.device_id
        WHERE
        <if test="manholeAlarmVo.model != null and manholeAlarmVo.model != ''">
            a.model_ = #{manholeAlarmVo.model}
        </if>
        <if test="manholeAlarmVo.id != null ">
            AND a.id_ = #{manholeAlarmVo.id}
        </if>
        <if test="manholeAlarmVo.deviceCodeKey != null and manholeAlarmVo.deviceCodeKey != ''">
            AND a.device_code_ LIKE CONCAT('%',#{manholeAlarmVo.deviceCodeKey},'%')
        </if>
        <if test="manholeAlarmVo.deviceCode != null and manholeAlarmVo.deviceCode != ''">
            AND a.device_code_ = #{manholeAlarmVo.deviceCode}
        </if>
        <if test="manholeAlarmVo.szjd != null and manholeAlarmVo.szjd != ''">
            AND c.szjd =  #{manholeAlarmVo.szjd}
        </if>
        <if test="manholeAlarmVo.szsq != null and manholeAlarmVo.szsq != ''">
            AND c.szsq =  #{manholeAlarmVo.szsq}
        </if>
        <if test="manholeAlarmVo.szdywg != null and manholeAlarmVo.szdywg != ''">
            AND c.szdywg =  #{manholeAlarmVo.szdywg}
        </if>
        <if test="manholeAlarmVo.areaPaths != null and manholeAlarmVo.areaPaths.size() > 0">
            AND c.area_path IN
            <foreach collection="manholeAlarmVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                #{areaPath}
            </foreach>
        </if>
        <if test="manholeAlarmVo.areaPath != null and manholeAlarmVo.areaPath != ''">
            AND c.area_path =  #{manholeAlarmVo.areaPath}
        </if>
        <if test="manholeAlarmVo.bsm != null and manholeAlarmVo.bsm != ''">
            AND c.bsm LIKE CONCAT('%',#{manholeAlarmVo.bsm},'%')
        </if>
        <if test="manholeAlarmVo.alarmType != null ">
            AND a.alarm_type_ = #{manholeAlarmVo.alarmType}
        </if>
        <if test="manholeAlarmVo.level != null ">
            AND a.level_ = #{manholeAlarmVo.level}
        </if>
        <if test="manholeAlarmVo.pushStatus != null ">
            AND a.push_status_ = #{manholeAlarmVo.pushStatus}
        </if>
        ORDER BY a.alarm_time_ DESC
    </select>

    <select id="countAlarmGroupByTime" resultType="com.smartPark.business.streetlight.entity.vo.StreetlightCountVo">
        select
        <if test="dateType != null and dateType == 1">
            DATE_FORMAT( alarm_time_, '%Y-%m-%d %H' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 2">
            DATE_FORMAT( alarm_time_, '%Y-%m-%d' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 3">
            DATE_FORMAT( alarm_time_, '%Y-%m' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 4">
            DATE_FORMAT( alarm_time_, '%Y' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 5">
            IFNULL(count(*),0) AS `alarm`
        </if>
        from `base_alarm`
        where
        1=1
        <if test="alarmStatus != null">
            and status_ = #{alarmStatus}
        </if>
        <if test="deviceCodes != null and deviceCodes.size() > 0">
            and device_code_ in
            <foreach collection="deviceCodes" index="index" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="dateType != null and dateType == 1">
            and alarm_time_ &gt;= #{startTime}
            and alarm_time_ &lt;= #{endTime}
            group by `time`
        </if>
        <if test="dateType != null and dateType == 2">
            and DATE_FORMAT(alarm_time_,'%Y-%m-%d') &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d' )
            and DATE_FORMAT(alarm_time_,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            group by `time`
        </if>
        <if test="dateType != null and dateType == 3">
            and DATE_FORMAT(alarm_time_,'%Y-%m') &gt;= DATE_FORMAT(#{startTime}, '%Y-%m' )
            and DATE_FORMAT(alarm_time_,'%Y-%m') &lt;= DATE_FORMAT(#{endTime}, '%Y-%m' )
            group by `time`
        </if>
        <if test="dateType != null and dateType == 4">
            and DATE_FORMAT(alarm_time_,'%Y') &gt;= DATE_FORMAT(#{startTime}, '%Y' )
            and DATE_FORMAT(alarm_time_,'%Y') &lt;= DATE_FORMAT(#{endTime}, '%Y' )
            group by `time`
        </if>
        <if test="dateType != null and dateType == 5">
        </if>
    </select>

    <select id="countAlarmGroupByTimeAndDeviceCode" resultType="com.smartPark.business.streetlight.entity.vo.StreetlightCountVo">
        select t.`time`, t.`name`, t.`alarm` from
        (select
        <if test="dateType != null and dateType == 1">
            DATE_FORMAT( alarm_time_, '%Y-%m-%d %H' ) AS `time`,
            device_code_ as `name`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 2">
            DATE_FORMAT( alarm_time_, '%Y-%m-%d' ) AS `time`,
            device_code_ as `name`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 3">
            DATE_FORMAT( alarm_time_, '%Y-%m' ) AS `time`,
            device_code_ as `name`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 4">
            DATE_FORMAT( alarm_time_, '%Y' ) AS `time`,
            device_code_ as `name`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 5">
            null AS `time`,
            device_code_ as `name`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        from `base_alarm`

        where
        1=1
        <if test="deviceCodes != null and deviceCodes.size() > 0">
            and device_code_ in
            <foreach collection="deviceCodes" index="index" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="dateType != null and dateType == 1">
            and alarm_time_ &gt;= #{startTime}
            and alarm_time_ &lt;= #{endTime}
            group by device_code_,`time`
        </if>
        <if test="dateType != null and dateType == 2">
            and DATE_FORMAT(alarm_time_,'%Y-%m-%d') &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d' )
            and DATE_FORMAT(alarm_time_,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            group by device_code_,`time`
        </if>
        <if test="dateType != null and dateType == 3">
            and DATE_FORMAT(alarm_time_,'%Y-%m') &gt;= DATE_FORMAT(#{startTime}, '%Y-%m' )
            and DATE_FORMAT(alarm_time_,'%Y-%m') &lt;= DATE_FORMAT(#{endTime}, '%Y-%m' )
            group by device_code_,`time`
        </if>
        <if test="dateType != null and dateType == 4">
            and DATE_FORMAT(alarm_time_,'%Y') &gt;= DATE_FORMAT(#{startTime}, '%Y' )
            and DATE_FORMAT(alarm_time_,'%Y') &lt;= DATE_FORMAT(#{endTime}, '%Y' )
            group by device_code_,`time`
        </if>
        <if test="dateType != null and dateType == 5">
            group by device_code_
        </if>) t
        where t.alarm &gt; 0
    </select>

    <select id="countEventProcessGroupByTime"
        resultType="com.smartPark.business.streetlight.entity.vo.StreetlightCountVo">
        select
        <if test="dateType != null and dateType == 1">
            DATE_FORMAT( ba.alarm_time_, '%Y-%m-%d %H' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 2">
            DATE_FORMAT( ba.alarm_time_, '%Y-%m-%d' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 3">
            DATE_FORMAT( ba.alarm_time_, '%Y-%m' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 4">
            DATE_FORMAT( ba.alarm_time_, '%Y' ) AS `time`,
            IFNULL(count(*),0) AS `alarm`
        </if>
        <if test="dateType != null and dateType == 5">
            IFNULL(count(*),0) AS `alarm`
        </if>
        from `base_alarm` ba left join `base_event_process` be on ba.code_ = be.alarm_no_
        where
        1=1
        <!--  未处理工单 EventProcessConstant      -->
        <if test="alarmStatus != null and alarmStatus == 1">
            and ( be.process_status_ is null or be.process_status_ in (0,1,2,3,4,7) )
        </if>
        <!--  已处理工单 EventProcessConstant      -->
        <if test="alarmStatus != null and alarmStatus == 2">
            and be.process_status_ in (5,6,8)
        </if>
        <if test="deviceCodes != null and deviceCodes.size() > 0">
            and ba.device_code_ in
            <foreach collection="deviceCodes" index="index" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="dateType != null and dateType == 1">
            and ba.alarm_time_ &gt;= #{startTime}
            and ba.alarm_time_ &lt;= #{endTime}
            group by `time`
        </if>
        <if test="dateType != null and dateType == 2">
            and DATE_FORMAT(ba.alarm_time_,'%Y-%m-%d') &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d' )
            and DATE_FORMAT(ba.alarm_time_,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d' )
            group by `time`
        </if>
        <if test="dateType != null and dateType == 3">
            and DATE_FORMAT(ba.alarm_time_,'%Y-%m') &gt;= DATE_FORMAT(#{startTime}, '%Y-%m' )
            and DATE_FORMAT(ba.alarm_time_,'%Y-%m') &lt;= DATE_FORMAT(#{endTime}, '%Y-%m' )
            group by `time`
        </if>
        <if test="dateType != null and dateType == 4">
            and DATE_FORMAT(ba.alarm_time_,'%Y') &gt;= DATE_FORMAT(#{startTime}, '%Y' )
            and DATE_FORMAT(ba.alarm_time_,'%Y') &lt;= DATE_FORMAT(#{endTime}, '%Y' )
            group by `time`
        </if>
        <if test="dateType != null and dateType == 5">
        </if>
    </select>
</mapper>
