<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.drainage.mapper.DrainageDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.drainage.entity.DrainageDevice">
        <id column="id_" property="id" />
        <result column="device_code_" property="deviceCode" />
        <!-- <result column="use_status_" property="useStatus" /> -->
        <result column="pump_station_obj_id_" property="pumpStationArchivesObjId" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted"/>
    </resultMap>

    <select id="queryListByPage" resultType="com.smartPark.business.drainage.entity.vo.DrainageDeviceVo">
        SELECT
            a.id_ id,
            a.device_code_ deviceCode,
            b.sbmc,
            d.status status,
            d.alarm_state alarmState,
            b.use_status useStatus,
            b.bsm,
            c.owner_enterprise_name ownerEnterpriseName,
            b.area_path areaPath,
            IFNULL(u.code,b.sbxh) deviceUnitCode,
            c.objX,
            c.objY
        FROM
            safe_drainage_device a
                LEFT JOIN base_device_extend_info b ON a.device_code_ = b.device_id
                LEFT JOIN base_monitor_point_obj_v c ON b.dwbsm = c.obj_id
                LEFT JOIN base_device d ON a.device_code_ = d.`code`
                LEFT JOIN base_device_unit u on d.device_unit_id = u.id
        WHERE
          a.deleted_ = 0
        <if test="drainageDeviceVo.deviceCode != null and drainageDeviceVo.deviceCode != ''">
            AND a.device_code_ LIKE CONCAT('%',#{drainageDeviceVo.deviceCode},'%')
        </if>
        <if test="drainageDeviceVo.sbmc != null and drainageDeviceVo.sbmc != ''">
            AND b.sbmc LIKE CONCAT('%',#{drainageDeviceVo.sbmc},'%')
        </if>
        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.deviceUnitCode)">
            AND (b.sbxh = #{drainageDeviceVo.deviceUnitCode} or u.code = #{drainageDeviceVo.deviceUnitCode})
        </if>
        <if test="drainageDeviceVo.areaPaths != null and drainageDeviceVo.areaPaths.size() > 0">
            and b.area_path in
            <foreach collection="drainageDeviceVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                #{areaPath}
            </foreach>
        </if>
        <if test="drainageDeviceVo.szjd != null and drainageDeviceVo.szjd != ''">
            AND b.szjd =  #{drainageDeviceVo.szjd}
        </if>
        <if test="drainageDeviceVo.szsq != null and drainageDeviceVo.szsq != ''">
            AND b.szsq =  #{drainageDeviceVo.szsq}
        </if>

        <if test="drainageDeviceVo.szdywg != null and drainageDeviceVo.szdywg != ''">
            AND b.szdywg =  #{drainageDeviceVo.szdywg}
        </if>
        <if test="drainageDeviceVo.areaPath != null and drainageDeviceVo.areaPath != ''">
            AND b.area_path =  #{drainageDeviceVo.areaPath}
        </if>
        <if test="drainageDeviceVo.bsm != null and drainageDeviceVo.bsm != ''">
            AND b.bsm LIKE CONCAT('%',#{drainageDeviceVo.bsm},'%')
        </if>
        <if test="drainageDeviceVo.status == 1 ">
            AND d.`status` =  1
        </if>
        <if test="drainageDeviceVo.status == 0 ">
            AND (d.`status` !=  1 or d.`status` is null)
        </if>
        <if test="drainageDeviceVo.useStatus != null">
            AND b.use_status =  #{drainageDeviceVo.useStatus}
        </if>
        <if test="drainageDeviceVo.type != null">
            AND a.type_ =  #{drainageDeviceVo.type}
        </if>
        ORDER BY a.modify_time_ DESC
    </select>

    <select id="queryListByPage2" resultType="com.smartPark.business.drainage.entity.vo.DrainageDeviceVo">
        SELECT
        a.id_ id,
        a.device_code_ deviceCode,
        b.sbmc,
        d.status status,
        d.alarm_state alarmState,
        b.use_status useStatus,
        b.bsm,
        c.owner_enterprise_name ownerEnterpriseName,
        b.area_path areaPath,
        IFNULL(u.code,b.sbxh) deviceUnitCode,
        c.objX,
        c.objY
        FROM
        safe_pipe_monitor_device a
        LEFT JOIN base_device_extend_info b ON a.device_code_ = b.device_id
        LEFT JOIN base_monitor_point_obj_v c ON b.dwbsm = c.obj_id
        LEFT JOIN base_device d ON a.device_code_ = d.`code`
        LEFT JOIN base_device_unit u on d.device_unit_id = u.id
        left join base_device_type bdt on u.deviceType_id = bdt.id
        WHERE
        a.deleted_ = 0
        <if test="drainageDeviceVo.deviceCode != null and drainageDeviceVo.deviceCode != ''">
            AND a.device_code_ LIKE CONCAT('%',#{drainageDeviceVo.deviceCode},'%')
        </if>
        <if test="drainageDeviceVo.sbmc != null and drainageDeviceVo.sbmc != ''">
            AND b.sbmc LIKE CONCAT('%',#{drainageDeviceVo.sbmc},'%')
        </if>
        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.deviceUnitCode)">
            AND (b.sbxh = #{drainageDeviceVo.deviceUnitCode} or u.code = #{drainageDeviceVo.deviceUnitCode})
        </if>
        <if test="drainageDeviceVo.areaPaths != null and drainageDeviceVo.areaPaths.size() > 0">
            and b.area_path in
            <foreach collection="drainageDeviceVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                #{areaPath}
            </foreach>
        </if>
        <if test="drainageDeviceVo.szjd != null and drainageDeviceVo.szjd != ''">
            AND b.szjd =  #{drainageDeviceVo.szjd}
        </if>
        <if test="drainageDeviceVo.szsq != null and drainageDeviceVo.szsq != ''">
            AND b.szsq =  #{drainageDeviceVo.szsq}
        </if>

        <if test="drainageDeviceVo.szdywg != null and drainageDeviceVo.szdywg != ''">
            AND b.szdywg =  #{drainageDeviceVo.szdywg}
        </if>
        <if test="drainageDeviceVo.areaPath != null and drainageDeviceVo.areaPath != ''">
            AND b.area_path =  #{drainageDeviceVo.areaPath}
        </if>
        <if test="drainageDeviceVo.bsm != null and drainageDeviceVo.bsm != ''">
            AND b.bsm LIKE CONCAT('%',#{drainageDeviceVo.bsm},'%')
        </if>
        <if test="drainageDeviceVo.status == 1 ">
            AND d.`status` =  1
        </if>
        <if test="drainageDeviceVo.status == 0 ">
            AND d.`status` !=  1
        </if>
        <if test="drainageDeviceVo.useStatus != null">
            AND b.use_status =  #{drainageDeviceVo.useStatus}
        </if>
        <if test="drainageDeviceVo.deviceTypeName != null and drainageDeviceVo.deviceTypeName!=''">
            AND bdt.name =  #{drainageDeviceVo.deviceTypeName,jdbcType=VARCHAR}
        </if>
        ORDER BY a.modify_time_ DESC

    </select>

    <select id="getAreas" resultType="com.smartPark.common.entity.deviceArea.DeviceArea">
        SELECT
            b.szjd,
            b.szsq,
            b.szdywg
        FROM
            safe_drainage_device a
                JOIN base_device_extend_info b ON a.device_code_ = b.device_id
        WHERE
            a.deleted_ = 0
    </select>

    <select id="selectOnline" resultType="com.smartPark.business.drainage.entity.DrainageDevice">
        SELECT
            a.id_ id,
            a.device_code_ deviceCode,
            d.status status
        FROM
            safe_drainage_device a
                LEFT JOIN base_device d ON a.device_code_ = d.`code`
        WHERE
            a.deleted_ = 0
            and d.status = 1
    </select>
    <select id="findAlarmList" resultType="com.smartPark.business.drainage.entity.vo.DrainageDeviceAlarmVo">
        SELECT
        a.id_ id,
        a.code_ code,
        a.device_code_ deviceCode,
<!--        IFNULL(u.code,c.sbxh) deviceUnitCode,-->
        c.sbmc,
        c.bsm,
        c.obj_id objId,
        c.area_path areaPath,
        a.alarm_type_ alarmType,
        a.level_ level,
        a.push_status_ pushStatus,
        c.area_path areaPath,
        a.alarm_time_ alarmTime,
        a.work_no_ workNo,
        a.content_ content
        FROM
        base_alarm a
        LEFT JOIN base_device_extend_info c ON a.device_code_ = c.device_id
        LEFT JOIN base_device b on a.device_code_ = b.code
<!--        LEFT JOIN base_device_unit u on b.device_unit_id = u.id-->
        LEFT JOIN safe_drainage_device s on s.device_code_ = a.device_code_ and s.deleted_=0
        LEFT JOIN base_monitor_point_obj_v o on o.obj_id = s.pump_station_obj_id_
        WHERE
        <if test="drainageDeviceAlarmVo.model != null and drainageDeviceAlarmVo.model != ''">
            a.model_ = #{drainageDeviceAlarmVo.model}
        </if>
        <if test="drainageDeviceAlarmVo.pumpStationArchivesObjId != null and drainageDeviceAlarmVo.pumpStationArchivesObjId != ''">
            AND s.pump_station_obj_id_ = #{drainageDeviceAlarmVo.pumpStationArchivesObjId}
        </if>
        <if test="drainageDeviceAlarmVo.id != null ">
            AND a.id_ = #{drainageDeviceAlarmVo.id}
        </if>
        <if test="drainageDeviceAlarmVo.deviceCodeKey != null and drainageDeviceAlarmVo.deviceCodeKey != ''">
            AND a.device_code_ LIKE CONCAT('%',#{drainageDeviceAlarmVo.deviceCodeKey},'%')
        </if>
<!--        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceAlarmVo.deviceUnitCode)">-->
<!--            AND (c.sbxh = #{drainageDeviceAlarmVo.deviceUnitCode} or u.code = #{drainageDeviceAlarmVo.deviceUnitCode})-->
<!--        </if>-->
        <if test="drainageDeviceAlarmVo.deviceCode != null and drainageDeviceAlarmVo.deviceCode != ''">
            AND a.device_code_ = #{drainageDeviceAlarmVo.deviceCode}
        </if>
        <if test="drainageDeviceAlarmVo.szjd != null and drainageDeviceAlarmVo.szjd != ''">
            AND c.szjd =  #{drainageDeviceAlarmVo.szjd}
        </if>
        <if test="drainageDeviceAlarmVo.szsq != null and drainageDeviceAlarmVo.szsq != ''">
            AND c.szsq =  #{drainageDeviceAlarmVo.szsq}
        </if>
        <if test="drainageDeviceAlarmVo.szdywg != null and drainageDeviceAlarmVo.szdywg != ''">
            AND c.szdywg =  #{drainageDeviceAlarmVo.szdywg}
        </if>
        <if test="drainageDeviceAlarmVo.objAreaPaths != null and drainageDeviceAlarmVo.objAreaPaths.size() > 0">
            AND o.area_path IN
            <foreach collection="drainageDeviceAlarmVo.objAreaPaths" index="index" item="objAreaPath" open="(" close=")" separator=",">
                #{objAreaPath}
            </foreach>
        </if>
        <if test="drainageDeviceAlarmVo.areaPaths != null and drainageDeviceAlarmVo.areaPaths.size() > 0">
            AND c.area_path IN
            <foreach collection="drainageDeviceAlarmVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                #{areaPath}
            </foreach>
        </if>
        <if test="drainageDeviceAlarmVo.areaPath != null and drainageDeviceAlarmVo.areaPath != ''">
            AND c.area_path =  #{drainageDeviceAlarmVo.areaPath}
        </if>
        <if test="drainageDeviceAlarmVo.bsm != null and drainageDeviceAlarmVo.bsm != ''">
            AND c.bsm LIKE CONCAT('%',#{drainageDeviceAlarmVo.bsm},'%')
        </if>
        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceAlarmVo.alarmType)">
            AND a.alarm_type_ = #{drainageDeviceAlarmVo.alarmType}
        </if>
        <if test="drainageDeviceAlarmVo.level != null ">
            AND a.level_ = #{drainageDeviceAlarmVo.level}
        </if>
        <if test="drainageDeviceAlarmVo.pushStatus != null ">
            AND a.push_status_ = #{drainageDeviceAlarmVo.pushStatus}
        </if>
        <if test="drainageDeviceAlarmVo.type != null ">
            AND s.type_ = #{drainageDeviceAlarmVo.type}
        </if>
        ORDER BY a.alarm_time_ DESC
    </select>

    <select id="findAlarmList2" resultType="com.smartPark.business.drainage.entity.vo.DrainageDeviceAlarmVo">
        SELECT
        a.id_ id,
        a.code_ code,
        a.device_code_ deviceCode,
        <!--        IFNULL(u.code,c.sbxh) deviceUnitCode,-->
        c.sbmc,
        c.bsm,
        c.obj_id objId,
        c.area_path areaPath,
        a.alarm_type_ alarmType,
        a.level_ level,
        a.push_status_ pushStatus,
        c.area_path areaPath,
        a.alarm_time_ alarmTime,
        a.work_no_ workNo,
        a.content_ content
        FROM
        base_alarm a
        LEFT JOIN base_device_extend_info c ON a.device_code_ = c.device_id
        LEFT JOIN base_device b on a.device_code_ = b.code
        left join base_device_unit u on u.id=b.device_unit_id
        LEFT JOIN basedb.safe_pipe_monitor_device s on s.device_code_ = a.device_code_ and s.deleted_=0
        LEFT JOIN base_monitor_point_obj_v o on o.obj_id = s.monitor_pipe_id_
        WHERE
        <if test="drainageDeviceAlarmVo.model != null and drainageDeviceAlarmVo.model != ''">
            a.model_ = #{drainageDeviceAlarmVo.model}
        </if>
        <if test="drainageDeviceAlarmVo.pumpStationArchivesObjId != null and drainageDeviceAlarmVo.pumpStationArchivesObjId != ''">
            AND s.monitor_pipe_id_ = #{drainageDeviceAlarmVo.pumpStationArchivesObjId}
        </if>
        <if test="drainageDeviceAlarmVo.id != null ">
            AND a.id_ = #{drainageDeviceAlarmVo.id}
        </if>
        <if test="drainageDeviceAlarmVo.deviceCodeKey != null and drainageDeviceAlarmVo.deviceCodeKey != ''">
            AND a.device_code_ LIKE CONCAT('%',#{drainageDeviceAlarmVo.deviceCodeKey},'%')
        </if>
        <if test="drainageDeviceAlarmVo.deviceCode != null and drainageDeviceAlarmVo.deviceCode != ''">
            AND a.device_code_ = #{drainageDeviceAlarmVo.deviceCode}
        </if>
        <if test="drainageDeviceAlarmVo.szjd != null and drainageDeviceAlarmVo.szjd != ''">
            AND c.szjd =  #{drainageDeviceAlarmVo.szjd}
        </if>
        <if test="drainageDeviceAlarmVo.szsq != null and drainageDeviceAlarmVo.szsq != ''">
            AND c.szsq =  #{drainageDeviceAlarmVo.szsq}
        </if>
        <if test="drainageDeviceAlarmVo.szdywg != null and drainageDeviceAlarmVo.szdywg != ''">
            AND c.szdywg =  #{drainageDeviceAlarmVo.szdywg}
        </if>
        <if test="drainageDeviceAlarmVo.objAreaPaths != null and drainageDeviceAlarmVo.objAreaPaths.size() > 0">
            AND o.area_path IN
            <foreach collection="drainageDeviceAlarmVo.objAreaPaths" index="index" item="objAreaPath" open="(" close=")" separator=",">
                #{objAreaPath}
            </foreach>
        </if>
        <if test="drainageDeviceAlarmVo.areaPaths != null and drainageDeviceAlarmVo.areaPaths.size() > 0">
            AND c.area_path IN
            <foreach collection="drainageDeviceAlarmVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                #{areaPath}
            </foreach>
        </if>
        <if test="drainageDeviceAlarmVo.areaPath != null and drainageDeviceAlarmVo.areaPath != ''">
            AND c.area_path =  #{drainageDeviceAlarmVo.areaPath}
        </if>
        <if test="drainageDeviceAlarmVo.bsm != null and drainageDeviceAlarmVo.bsm != ''">
            AND c.bsm LIKE CONCAT('%',#{drainageDeviceAlarmVo.bsm},'%')
        </if>
        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceAlarmVo.alarmType)">
            AND a.alarm_type_ = #{drainageDeviceAlarmVo.alarmType}
        </if>
        <if test="drainageDeviceAlarmVo.level != null ">
            AND a.level_ = #{drainageDeviceAlarmVo.level}
        </if>
        <if test="drainageDeviceAlarmVo.pushStatus != null ">
            AND a.push_status_ = #{drainageDeviceAlarmVo.pushStatus}
        </if>
        <if test="drainageDeviceAlarmVo.deviceUnitCode != null and  drainageDeviceAlarmVo.deviceUnitCode !=''">
            AND u.code = #{drainageDeviceAlarmVo.deviceUnitCode,jdbcType=VARCHAR}
        </if>
        ORDER BY a.alarm_time_ DESC
    </select>

    <select id="getRelatedOrNotDevice" resultType="com.smartPark.business.drainage.entity.vo.DrainageDeviceVo">
        SELECT
        a.id_ id,
        a.device_code_ deviceCode,
        a.pump_station_obj_id_ pumpStationArchivesObjId,
        b.sbmc,
        d.status status,
        d.alarm_state alarmState,
        b.use_status useStatus,
        b.bsm,
        c.owner_enterprise_name ownerEnterpriseName,
        b.area_path areaPath,
        IFNULL(u.code,b.sbxh) deviceUnitCode,
        c.objX,
        c.objY
        FROM
        safe_drainage_device a
        LEFT JOIN base_device_extend_info b ON a.device_code_ = b.device_id
        LEFT JOIN base_monitor_point_obj_v c ON b.dwbsm = c.obj_id
        LEFT JOIN base_device d ON a.device_code_ = d.`code`
        LEFT JOIN base_device_unit u on d.device_unit_id = u.id
        LEFT JOIN base_device_type t on t.id = u.deviceType_id
        WHERE
        a.deleted_ = 0
        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.deviceTypeName)">
            AND (b.device_second_type_name = #{drainageDeviceVo.deviceTypeName} or t.name = #{drainageDeviceVo.deviceTypeName})
        </if>
        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.pumpStationArchivesObjId)">
          <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.withNotSelectedData) and drainageDeviceVo.withNotSelectedData">
              AND (a.pump_station_obj_id_ =  #{drainageDeviceVo.pumpStationArchivesObjId} or a.pump_station_obj_id_ is null)
          </if>
          <if test="(@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.withNotSelectedData) and drainageDeviceVo.withNotSelectedData) or @com.smartPark.common.utils.MybatisUtil@isEmpty(drainageDeviceVo.withNotSelectedData)">
              AND a.pump_station_obj_id_ =  #{drainageDeviceVo.pumpStationArchivesObjId}
          </if>
        </if>
        <if test="!@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.pumpStationArchivesObjId)">
            AND a.pump_station_obj_id_ is null
        </if>
        <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceVo.type)">
            AND a.type_  = #{drainageDeviceVo.type}
        </if>
        ORDER BY a.modify_time_ DESC
    </select>

    <select id="getDeviceUnitsByDeviceCodes" resultType="com.smartPark.business.drainage.entity.vo.DrainageDeviceVo">
        SELECT
        bd.code deviceCode,
        bdu.code deviceUnitCode
        FROM
        base_device bd
        LEFT JOIN base_device_unit bdu on bd.device_unit_id = bdu.id
        <where>
            <if test="deviceCodes != null and deviceCodes.size() != 0">
                bd.code IN
                <foreach collection="deviceCodes" item="deviceCode" separator="," open="(" close=")">
                    #{deviceCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="findAlarmTop3" resultType="com.smartPark.business.drainage.entity.vo.DrainageDeviceAlarmVo">
        SELECT
        a.device_code_ deviceCode,
        c.sbmc,
        count(a.device_code_) alarmNumber
        FROM
        base_alarm a
        LEFT JOIN base_device_extend_info c ON a.device_code_ = c.device_id
        LEFT JOIN safe_pipe_monitor_device s on s.device_code_ = a.device_code_
        left join base_device d on d.code = a.device_code_
        left join base_device_unit bdu on bdu.id = d.device_unit_id
        left join base_device_type bdt on bdt.id = bdu.deviceType_id
        <where>
<!--            <if test="drainageDeviceAlarmVo.type != null ">-->
<!--                s.type_ = #{drainageDeviceAlarmVo.type}-->
<!--            </if>-->
            <if test="drainageDeviceAlarmVo.deviceTypeName != null and drainageDeviceAlarmVo.deviceTypeName!=''">
                AND bdt.name =  #{drainageDeviceAlarmVo.deviceTypeName,jdbcType=VARCHAR}
            </if>
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageDeviceAlarmVo.deviceCodes)">
                AND a.device_code_ IN
                <foreach collection="drainageDeviceAlarmVo.deviceCodes" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        Group by a.device_code_
        order by count(a.device_code_) desc
        limit 3
    </select>

    <select id="selectById2" resultMap="BaseResultMap">
        select * from safe_pipe_monitor_device where id_= #{id}
    </select>
</mapper>
