<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.emergency.information.mapper.EmergencyInformationPublishMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.emergency.information.entity.EmergencyInformationPublish">
        <id column="id_" property="id" />
        <result column="title_" property="title" />
        <result column="content_" property="content" />
        <result column="publish_way_" property="publishWay" />
        <result column="information_type_" property="informationType" />
        <result column="event_no_" property="eventNo" />
        <result column="event_type_" property="eventType" />
        <result column="publish_time_" property="publishTime" />
        <result column="publish_state_" property="publishState" />
        <result column="publisher_id_" property="publisherId" />
        <result column="file_urls_" property="fileUrls" />
        <result column="remark_" property="remark" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>
    <resultMap id="InformationDtoMap" extends="BaseResultMap" type="com.smartPark.business.emergency.information.entity.dto.EmergencyInformationPublishDTO">

    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from safe_emergency_information_publish where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from safe_emergency_information_publish where id_ = #{id} 
    </select>

    <select id="selectDtoPage" parameterType="com.smartPark.business.emergency.information.entity.vo.EmergencyInformationPublishVo"
            resultMap="InformationDtoMap">
        select
            seip.*
        from
            safe_emergency_information_publish seip
        where
            seip.deleted_ = 0
        <if test="informationPublishVo.title != null and informationPublishVo.title != ''">
            and seip.title_ like concat('%',#{informationPublishVo.title},'%')
        </if>
        <if test="informationPublishVo.informationType != null ">
            and seip.information_type_ = #{informationPublishVo.informationType}
        </if>
        <if test="informationPublishVo.publishWay != null and informationPublishVo.publishWay != ''">
          and seip.publish_way_ like concat('%',#{informationPublishVo.publishWay},'%')
        </if>
        <if test="informationPublishVo.eventNo != null and informationPublishVo.eventNo != ''">
            and seip.event_no_ like concat('%',#{informationPublishVo.eventNo},'%')
        </if>
        <if test="informationPublishVo.eventType != null and informationPublishVo.eventType != ''">
            and seip.event_type_ = #{informationPublishVo.eventType}
        </if>
        <if test="informationPublishVo.startTime != null">
            <![CDATA[ and seip.publish_time_ >= #{informationPublishVo.startTime} ]]>
        </if>
        <if test="informationPublishVo.endTime != null">
            <![CDATA[ and seip.publish_time_ <= #{informationPublishVo.endTime} ]]>
        </if>
        order by seip.publish_time_ desc
    </select>
</mapper>
