<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.emergency.supply.mapper.EmergencySupplyRefLibraryMapper">
    <resultMap id="BaseResultMap" type="com.smartPark.business.emergency.supply.entity.EmergencySupplyRefLibrary">
        <id column="id_" jdbcType="INTEGER" property="id"/>
        <result column="supply_id_" jdbcType="INTEGER" property="supplyId"/>
        <result column="library_id_" jdbcType="INTEGER" property="libraryId"/>
        <result column="quantity_" jdbcType="INTEGER" property="quantity"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from safe_emergency_supply_ref_library
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from safe_emergency_supply_ref_library
        where id_ = #{id}
    </select>
</mapper>
