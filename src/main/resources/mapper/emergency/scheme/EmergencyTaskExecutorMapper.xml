<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.emergency.scheme.mapper.EmergencyTaskExecutorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.emergency.scheme.entity.EmergencyTaskExecutor">
        <id column="id_" property="id" />
        <result column="executor_id_" property="executorId" />
        <result column="executor_name_" property="executorName" />
        <result column="emergency_people_type_" property="emergencyPeopleType" />
        <result column="executor_team_id_" property="executorTeamId" />
        <result column="task_id_" property="taskId" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from safe_emergency_task_executor where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from safe_emergency_task_executor where id_ = #{id} 
    </select>

    <select id="selectByTaskId" resultMap="BaseResultMap">
        select * from safe_emergency_task_executor where deleted_ = 0 and task_id_ = #{taskId}
    </select>
</mapper>
