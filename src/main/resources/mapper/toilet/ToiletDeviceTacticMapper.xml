<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.toilet.mapper.ToiletDeviceTacticMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.toilet.entity.ToiletDeviceTactic">
        <id column="id_" property="id" />
        <result column="device_id_" property="deviceId" />
        <result column="device_code_" property="deviceCode" />
        <result column="trigger_device_id_" property="triggerDeviceId" />
        <result column="trigger_device_code_" property="triggerDeviceCode" />
        <result column="trigger_prop_key_" property="triggerPropKey" />
        <result column="trigger_prop_name_" property="triggerPropName" />
        <result column="trigger_logic_" property="triggerLogic" />
        <result column="trigger_prop_value_" property="triggerPropValue" />
        <result column="control_switch_" property="controlSwitch" />
        <result column="control_duration_" property="controlDuration" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from livable_toilet_device_tactic where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from livable_toilet_device_tactic where id_ = #{id} 
    </select>


</mapper>
