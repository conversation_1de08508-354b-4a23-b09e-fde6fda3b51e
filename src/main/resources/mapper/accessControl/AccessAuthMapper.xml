<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.accessControl.mapper.AccessAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.accessControl.entity.AccessAuth">
        <id column="id_" property="id" />
        <result column="access_control_id_" property="accessControlId" />
        <result column="device_code_" property="deviceCode" />
        <result column="person_id_" property="personId" />
        <result column="person_name_" property="personName" />
        <result column="person_id_card_" property="personIdCard" />
        <result column="person_phone_" property="personPhone" />
        <result column="auth_type_" property="authType" />
        <result column="auth_status_" property="authStatus" />
        <result column="auth_start_time_" property="authStartTime" />
        <result column="auth_end_time_" property="authEndTime" />
        <result column="enter_permission_" property="enterPermission" />
        <result column="exit_permission_" property="exitPermission" />
        <result column="time_limit_type_" property="timeLimitType" />
        <result column="custom_time_limit_" property="customTimeLimit" />
        <result column="authorizer_id_" property="authorizerId" />
        <result column="authorizer_name_" property="authorizerName" />
        <result column="revoker_id_" property="revokerId" />
        <result column="revoker_name_" property="revokerName" />
        <result column="revoke_time_" property="revokeTime" />
        <result column="revoke_reason_" property="revokeReason" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <!-- 授权VO映射结果 -->
    <resultMap id="AuthVoResultMap" type="com.smartPark.business.accessControl.entity.dto.AccessAuthVo" extends="BaseResultMap">
        <result column="device_name" property="deviceName" />
        <result column="install_location" property="installLocation" />
        <result column="owner_enterprise_name" property="ownerEnterpriseName" />
        <result column="last_use_time" property="lastUseTime" />
        <result column="use_count" property="useCount" />
    </resultMap>

    <!-- 分页查询门禁授权记录列表 -->
    <select id="queryListByPage" resultMap="AuthVoResultMap">
        SELECT 
            aa.*,
            ac.device_name_,
            ac.install_location_,
            ac.owner_enterprise_name_,
            (SELECT ah.access_time_ FROM safe_access_history ah 
             WHERE ah.device_code_ = aa.device_code_ 
             AND ah.person_id_card_ = aa.person_id_card_ 
             AND ah.deleted_ = 0 
             ORDER BY ah.access_time_ DESC LIMIT 1) as last_use_time,
            (SELECT COUNT(*) FROM safe_access_history ah 
             WHERE ah.device_code_ = aa.device_code_ 
             AND ah.person_id_card_ = aa.person_id_card_ 
             AND ah.deleted_ = 0) as use_count
        FROM safe_access_auth aa
        LEFT JOIN safe_access_control ac ON aa.device_code_ = ac.device_code_ AND ac.deleted_ = 0
        WHERE aa.deleted_ = 0
        <if test="accessAuthVo.personNameKey != null and accessAuthVo.personNameKey != ''">
            AND aa.person_name_ LIKE CONCAT('%', #{accessAuthVo.personNameKey}, '%')
        </if>
        <if test="accessAuthVo.personIdCardKey != null and accessAuthVo.personIdCardKey != ''">
            AND aa.person_id_card_ LIKE CONCAT('%', #{accessAuthVo.personIdCardKey}, '%')
        </if>
        <if test="accessAuthVo.personPhoneKey != null and accessAuthVo.personPhoneKey != ''">
            AND aa.person_phone_ LIKE CONCAT('%', #{accessAuthVo.personPhoneKey}, '%')
        </if>
        <if test="accessAuthVo.deviceCodeKey != null and accessAuthVo.deviceCodeKey != ''">
            AND aa.device_code_ LIKE CONCAT('%', #{accessAuthVo.deviceCodeKey}, '%')
        </if>
        <if test="accessAuthVo.deviceNameKey != null and accessAuthVo.deviceNameKey != ''">
            AND ac.device_name_ LIKE CONCAT('%', #{accessAuthVo.deviceNameKey}, '%')
        </if>
        <if test="accessAuthVo.authTypeList != null and accessAuthVo.authTypeList.size() > 0">
            AND aa.auth_type_ IN
            <foreach collection="accessAuthVo.authTypeList" item="authType" open="(" close=")" separator=",">
                #{authType}
            </foreach>
        </if>
        <if test="accessAuthVo.authStatusList != null and accessAuthVo.authStatusList.size() > 0">
            AND aa.auth_status_ IN
            <foreach collection="accessAuthVo.authStatusList" item="authStatus" open="(" close=")" separator=",">
                #{authStatus}
            </foreach>
        </if>
        <if test="accessAuthVo.timeLimitTypeList != null and accessAuthVo.timeLimitTypeList.size() > 0">
            AND aa.time_limit_type_ IN
            <foreach collection="accessAuthVo.timeLimitTypeList" item="timeLimitType" open="(" close=")" separator=",">
                #{timeLimitType}
            </foreach>
        </if>
        <if test="accessAuthVo.accessControlIdList != null and accessAuthVo.accessControlIdList.size() > 0">
            AND aa.access_control_id_ IN
            <foreach collection="accessAuthVo.accessControlIdList" item="accessControlId" open="(" close=")" separator=",">
                #{accessControlId}
            </foreach>
        </if>
        <if test="accessAuthVo.startTime != null">
            AND aa.auth_start_time_ >= #{accessAuthVo.startTime}
        </if>
        <if test="accessAuthVo.endTime != null">
            AND aa.auth_end_time_ &lt;= #{accessAuthVo.endTime}
        </if>
        ORDER BY aa.create_time_ DESC
    </select>

    <!-- 根据授权类型统计数量 -->
    <select id="countByAuthType" resultType="map">
        SELECT 
            aa.auth_type_ as key,
            COUNT(*) as count
        FROM safe_access_auth aa
        WHERE aa.deleted_ = 0
        <if test="accessAuthVo.authStatusList != null and accessAuthVo.authStatusList.size() > 0">
            AND aa.auth_status_ IN
            <foreach collection="accessAuthVo.authStatusList" item="authStatus" open="(" close=")" separator=",">
                #{authStatus}
            </foreach>
        </if>
        GROUP BY aa.auth_type_
    </select>

    <!-- 根据授权状态统计数量 -->
    <select id="countByAuthStatus" resultType="map">
        SELECT 
            aa.auth_status_ as key,
            COUNT(*) as count
        FROM safe_access_auth aa
        WHERE aa.deleted_ = 0
        GROUP BY aa.auth_status_
    </select>

    <!-- 统计即将过期的授权数量(7天内过期) -->
    <select id="countWillExpire" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_auth aa
        WHERE aa.deleted_ = 0 
        AND aa.auth_status_ = 1
        AND aa.auth_end_time_ BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
    </select>

    <!-- 统计已过期的授权数量 -->
    <select id="countExpired" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_auth aa
        WHERE aa.deleted_ = 0 
        AND aa.auth_end_time_ &lt; NOW()
    </select>

    <!-- 统计有效授权数量 -->
    <select id="countValid" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_auth aa
        WHERE aa.deleted_ = 0 
        AND aa.auth_status_ = 1
        AND aa.auth_end_time_ > NOW()
    </select>

    <!-- 统计总授权数量 -->
    <select id="countTotal" resultType="long">
        SELECT COUNT(*) FROM safe_access_auth WHERE deleted_ = 0
    </select>

    <!-- 根据设备统计授权数量 -->
    <select id="countByDevice" resultType="map">
        SELECT 
            aa.device_code_ as key,
            COUNT(*) as count
        FROM safe_access_auth aa
        WHERE aa.deleted_ = 0
        <if test="accessAuthVo.authStatusList != null and accessAuthVo.authStatusList.size() > 0">
            AND aa.auth_status_ IN
            <foreach collection="accessAuthVo.authStatusList" item="authStatus" open="(" close=")" separator=",">
                #{authStatus}
            </foreach>
        </if>
        GROUP BY aa.device_code_
    </select>

    <!-- 根据时间统计授权趋势 -->
    <select id="countTrendByTime" resultType="map">
        SELECT 
            <if test="accessAuthVo.dateFormat != null and accessAuthVo.dateFormat != ''">
                DATE_FORMAT(aa.create_time_, #{accessAuthVo.dateFormat}) as time_key,
            </if>
            <if test="accessAuthVo.dateFormat == null or accessAuthVo.dateFormat == ''">
                DATE_FORMAT(aa.create_time_, '%Y-%m-%d') as time_key,
            </if>
            COUNT(*) as count
        FROM safe_access_auth aa
        WHERE aa.deleted_ = 0
        <if test="accessAuthVo.startTime != null">
            AND aa.create_time_ >= #{accessAuthVo.startTime}
        </if>
        <if test="accessAuthVo.endTime != null">
            AND aa.create_time_ &lt;= #{accessAuthVo.endTime}
        </if>
        GROUP BY time_key
        ORDER BY time_key
    </select>

    <!-- 查询即将过期的授权列表 -->
    <select id="queryWillExpireList" resultMap="AuthVoResultMap">
        SELECT 
            aa.*,
            ac.device_name_,
            ac.install_location_,
            ac.owner_enterprise_name_
        FROM safe_access_auth aa
        LEFT JOIN safe_access_control ac ON aa.device_code_ = ac.device_code_ AND ac.deleted_ = 0
        WHERE aa.deleted_ = 0 
        AND aa.auth_status_ = 1
        AND aa.auth_end_time_ BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
        ORDER BY aa.auth_end_time_
    </select>

    <!-- 查询导出数据 -->
    <select id="queryList4Export" resultMap="AuthVoResultMap">
        SELECT 
            aa.*,
            ac.device_name_,
            ac.install_location_,
            ac.owner_enterprise_name_
        FROM safe_access_auth aa
        LEFT JOIN safe_access_control ac ON aa.device_code_ = ac.device_code_ AND ac.deleted_ = 0
        WHERE aa.deleted_ = 0
        <if test="accessAuthVo.personNameKey != null and accessAuthVo.personNameKey != ''">
            AND aa.person_name_ LIKE CONCAT('%', #{accessAuthVo.personNameKey}, '%')
        </if>
        <if test="accessAuthVo.deviceCodeKey != null and accessAuthVo.deviceCodeKey != ''">
            AND aa.device_code_ LIKE CONCAT('%', #{accessAuthVo.deviceCodeKey}, '%')
        </if>
        ORDER BY aa.create_time_ DESC
    </select>

    <!-- 批量撤销授权 -->
    <update id="batchRevokeAuth">
        UPDATE safe_access_auth 
        SET auth_status_ = 0,
            revoke_time_ = NOW(),
            revoker_id_ = #{revokerId},
            revoker_name_ = #{revokerName},
            revoke_reason_ = #{revokeReason},
            modify_time_ = NOW()
        WHERE id_ IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND deleted_ = 0
    </update>

</mapper>
