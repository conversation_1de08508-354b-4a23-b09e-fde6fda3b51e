<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.accessControl.mapper.AccessControlMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.accessControl.entity.AccessControl">
        <id column="id_" property="id" />
        <result column="device_code_" property="deviceCode" />
        <result column="device_name_" property="deviceName" />
        <result column="device_type_" property="deviceType" />
        <result column="install_location_" property="installLocation" />
        <result column="device_status_" property="deviceStatus" />
        <result column="control_status_" property="controlStatus" />
        <result column="communication_status_" property="communicationStatus" />
        <result column="monitor_device_code_" property="monitorDeviceCode" />
        <result column="owner_enterprise_id_" property="ownerEnterpriseId" />
        <result column="owner_enterprise_name_" property="ownerEnterpriseName" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <!-- 设备详情映射结果 -->
    <resultMap id="DeviceResultMap" type="com.smartPark.business.accessControl.entity.dto.AccessControlDeviceDTO" extends="BaseResultMap">
        <result column="sbmc" property="sbmc" />
        <result column="bsm" property="bsm" />
        <result column="szjd" property="szjd" />
        <result column="szsq" property="szsq" />
        <result column="szdywg" property="szdywg" />
        <result column="area_path" property="areaPath" />
        <result column="status" property="status" />
        <result column="alarm_state" property="alarmState" />
        <result column="obj_x" property="objX" />
        <result column="obj_y" property="objY" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="today_enter_count" property="todayEnterCount" />
        <result column="today_exit_count" property="todayExitCount" />
        <result column="current_auth_count" property="currentAuthCount" />
        <result column="last_access_time" property="lastAccessTime" />
        <result column="last_access_person" property="lastAccessPerson" />
        <result column="last_access_type" property="lastAccessType" />
    </resultMap>

    <!-- 分页查询门禁设备列表 -->
    <select id="queryListByPage" resultMap="DeviceResultMap">
        SELECT 
            ac.*,
            d.name as sbmc,
            d.code as bsm,
            d.area_path,
            d.longitude,
            d.latitude,
            d.alarm_state,
            CASE WHEN d.status = 1 THEN 1 ELSE 0 END as status,
            (SELECT COUNT(*) FROM safe_access_history ah 
             WHERE ah.device_code_ = ac.device_code_ 
             AND ah.access_type_ = 1 
             AND DATE(ah.access_time_) = CURDATE() 
             AND ah.deleted_ = 0) as today_enter_count,
            (SELECT COUNT(*) FROM safe_access_history ah 
             WHERE ah.device_code_ = ac.device_code_ 
             AND ah.access_type_ = 2 
             AND DATE(ah.access_time_) = CURDATE() 
             AND ah.deleted_ = 0) as today_exit_count,
            (SELECT COUNT(*) FROM safe_access_auth aa 
             WHERE aa.device_code_ = ac.device_code_ 
             AND aa.auth_status_ = 1 
             AND aa.auth_end_time_ > NOW() 
             AND aa.deleted_ = 0) as current_auth_count,
            (SELECT ah.access_time_ FROM safe_access_history ah 
             WHERE ah.device_code_ = ac.device_code_ 
             AND ah.deleted_ = 0 
             ORDER BY ah.access_time_ DESC LIMIT 1) as last_access_time,
            (SELECT ah.person_name_ FROM safe_access_history ah 
             WHERE ah.device_code_ = ac.device_code_ 
             AND ah.deleted_ = 0 
             ORDER BY ah.access_time_ DESC LIMIT 1) as last_access_person,
            (SELECT ah.access_type_ FROM safe_access_history ah 
             WHERE ah.device_code_ = ac.device_code_ 
             AND ah.deleted_ = 0 
             ORDER BY ah.access_time_ DESC LIMIT 1) as last_access_type
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.deleted_ = 0
        <if test="accessControlVo.deviceCodeKey != null and accessControlVo.deviceCodeKey != ''">
            AND ac.device_code_ LIKE CONCAT('%', #{accessControlVo.deviceCodeKey}, '%')
        </if>
        <if test="accessControlVo.deviceNameKey != null and accessControlVo.deviceNameKey != ''">
            AND ac.device_name_ LIKE CONCAT('%', #{accessControlVo.deviceNameKey}, '%')
        </if>
        <if test="accessControlVo.deviceTypeList != null and accessControlVo.deviceTypeList.size() > 0">
            AND ac.device_type_ IN
            <foreach collection="accessControlVo.deviceTypeList" item="deviceType" open="(" close=")" separator=",">
                #{deviceType}
            </foreach>
        </if>
        <if test="accessControlVo.deviceStatusList != null and accessControlVo.deviceStatusList.size() > 0">
            AND ac.device_status_ IN
            <foreach collection="accessControlVo.deviceStatusList" item="deviceStatus" open="(" close=")" separator=",">
                #{deviceStatus}
            </foreach>
        </if>
        <if test="accessControlVo.controlStatusList != null and accessControlVo.controlStatusList.size() > 0">
            AND ac.control_status_ IN
            <foreach collection="accessControlVo.controlStatusList" item="controlStatus" open="(" close=")" separator=",">
                #{controlStatus}
            </foreach>
        </if>
        <if test="accessControlVo.communicationStatusList != null and accessControlVo.communicationStatusList.size() > 0">
            AND ac.communication_status_ IN
            <foreach collection="accessControlVo.communicationStatusList" item="communicationStatus" open="(" close=")" separator=",">
                #{communicationStatus}
            </foreach>
        </if>
        <if test="accessControlVo.ownerEnterpriseName != null and accessControlVo.ownerEnterpriseName != ''">
            AND ac.owner_enterprise_name_ LIKE CONCAT('%', #{accessControlVo.ownerEnterpriseName}, '%')
        </if>
        <if test="accessControlVo.installLocation != null and accessControlVo.installLocation != ''">
            AND ac.install_location_ LIKE CONCAT('%', #{accessControlVo.installLocation}, '%')
        </if>
        <if test="accessControlVo.startTime != null">
            AND ac.create_time_ >= #{accessControlVo.startTime}
        </if>
        <if test="accessControlVo.endTime != null">
            AND ac.create_time_ &lt;= #{accessControlVo.endTime}
        </if>
        ORDER BY ac.create_time_ DESC
    </select>

    <!-- 根据设备编码查询设备详情 -->
    <select id="findDeviceByDeviceCode" resultMap="DeviceResultMap">
        SELECT 
            ac.*,
            d.name as sbmc,
            d.code as bsm,
            d.area_path,
            d.longitude,
            d.latitude,
            d.alarm_state,
            CASE WHEN d.status = 1 THEN 1 ELSE 0 END as status
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.device_code_ = #{deviceCode} AND ac.deleted_ = 0
    </select>

    <!-- 根据ID查询设备详情 -->
    <select id="findDeviceById" resultMap="DeviceResultMap">
        SELECT 
            ac.*,
            d.name as sbmc,
            d.code as bsm,
            d.area_path,
            d.longitude,
            d.latitude,
            d.alarm_state,
            CASE WHEN d.status = 1 THEN 1 ELSE 0 END as status
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.id_ = #{id} AND ac.deleted_ = 0
    </select>

    <!-- 根据状态统计设备数量 -->
    <select id="countByStatus" resultType="com.smartPark.business.manhole.entity.vo.CountByStateDTO">
        SELECT 
            CASE WHEN d.status = 1 THEN '在线' ELSE '离线' END as state,
            COUNT(*) as count
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.deleted_ = 0
        <if test="accessControlVo.deviceTypeList != null and accessControlVo.deviceTypeList.size() > 0">
            AND ac.device_type_ IN
            <foreach collection="accessControlVo.deviceTypeList" item="deviceType" open="(" close=")" separator=",">
                #{deviceType}
            </foreach>
        </if>
        GROUP BY d.status
    </select>

    <!-- 根据设备类型统计设备数量 -->
    <select id="countByDeviceType" resultType="map">
        SELECT 
            ac.device_type_ as key,
            COUNT(*) as count
        FROM safe_access_control ac
        WHERE ac.deleted_ = 0
        <if test="accessControlVo.deviceStatusList != null and accessControlVo.deviceStatusList.size() > 0">
            AND ac.device_status_ IN
            <foreach collection="accessControlVo.deviceStatusList" item="deviceStatus" open="(" close=")" separator=",">
                #{deviceStatus}
            </foreach>
        </if>
        GROUP BY ac.device_type_
    </select>

    <!-- 根据通讯状态统计设备数量 -->
    <select id="countByCommunicationStatus" resultType="map">
        SELECT 
            ac.communication_status_ as key,
            COUNT(*) as count
        FROM safe_access_control ac
        WHERE ac.deleted_ = 0
        GROUP BY ac.communication_status_
    </select>

    <!-- 根据控制状态统计设备数量 -->
    <select id="countByControlStatus" resultType="map">
        SELECT 
            ac.control_status_ as key,
            COUNT(*) as count
        FROM safe_access_control ac
        WHERE ac.deleted_ = 0
        GROUP BY ac.control_status_
    </select>

    <!-- 统计设备总数 -->
    <select id="countTotal" resultType="long">
        SELECT COUNT(*) FROM safe_access_control WHERE deleted_ = 0
    </select>

    <!-- 统计在线设备数 -->
    <select id="countOnline" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.deleted_ = 0 AND d.status = 1
    </select>

    <!-- 统计离线设备数 -->
    <select id="countOffline" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.deleted_ = 0 AND (d.status = 0 OR d.status IS NULL)
    </select>

    <!-- 统计告警设备数 -->
    <select id="countAlarm" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.deleted_ = 0 AND d.alarm_state = 1
    </select>

    <!-- 查询导出数据 -->
    <select id="queryList4Export" resultMap="DeviceResultMap">
        SELECT 
            ac.*,
            d.name as sbmc,
            d.code as bsm,
            d.area_path,
            d.longitude,
            d.latitude,
            d.alarm_state,
            CASE WHEN d.status = 1 THEN 1 ELSE 0 END as status
        FROM safe_access_control ac
        LEFT JOIN base_device d ON ac.device_code_ = d.code
        WHERE ac.deleted_ = 0
        <if test="accessControlVo.deviceCodeKey != null and accessControlVo.deviceCodeKey != ''">
            AND ac.device_code_ LIKE CONCAT('%', #{accessControlVo.deviceCodeKey}, '%')
        </if>
        <if test="accessControlVo.deviceNameKey != null and accessControlVo.deviceNameKey != ''">
            AND ac.device_name_ LIKE CONCAT('%', #{accessControlVo.deviceNameKey}, '%')
        </if>
        ORDER BY ac.create_time_ DESC
    </select>

</mapper>
