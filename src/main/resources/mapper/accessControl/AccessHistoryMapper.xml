<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.accessControl.mapper.AccessHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.accessControl.entity.AccessHistory">
        <id column="id_" property="id" />
        <result column="access_control_id_" property="accessControlId" />
        <result column="device_code_" property="deviceCode" />
        <result column="device_name_" property="deviceName" />
        <result column="person_id_" property="personId" />
        <result column="person_name_" property="personName" />
        <result column="person_id_card_" property="personIdCard" />
        <result column="person_phone_" property="personPhone" />
        <result column="access_type_" property="accessType" />
        <result column="recognition_method_" property="recognitionMethod" />
        <result column="access_status_" property="accessStatus" />
        <result column="failure_reason_" property="failureReason" />
        <result column="access_time_" property="accessTime" />
        <result column="temperature_" property="temperature" />
        <result column="wear_mask_" property="wearMask" />
        <result column="capture_image_path_" property="captureImagePath" />
        <result column="install_location_" property="installLocation" />
        <result column="owner_enterprise_name_" property="ownerEnterpriseName" />
        <result column="create_time_" property="createTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <!-- 历史VO映射结果 -->
    <resultMap id="HistoryVoResultMap" type="com.smartPark.business.accessControl.entity.dto.AccessHistoryVo" extends="BaseResultMap">
        <result column="area_path" property="areaPath" />
        <result column="szjd" property="szjd" />
        <result column="szsq" property="szsq" />
        <result column="szdywg" property="szdywg" />
    </resultMap>

    <!-- 分页查询进出门历史记录列表 -->
    <select id="queryListByPage" resultMap="HistoryVoResultMap">
        SELECT 
            ah.*,
            d.area_path
        FROM safe_access_history ah
        LEFT JOIN base_device d ON ah.device_code_ = d.code
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.personNameKey != null and accessHistoryVo.personNameKey != ''">
            AND ah.person_name_ LIKE CONCAT('%', #{accessHistoryVo.personNameKey}, '%')
        </if>
        <if test="accessHistoryVo.personIdCardKey != null and accessHistoryVo.personIdCardKey != ''">
            AND ah.person_id_card_ LIKE CONCAT('%', #{accessHistoryVo.personIdCardKey}, '%')
        </if>
        <if test="accessHistoryVo.personPhoneKey != null and accessHistoryVo.personPhoneKey != ''">
            AND ah.person_phone_ LIKE CONCAT('%', #{accessHistoryVo.personPhoneKey}, '%')
        </if>
        <if test="accessHistoryVo.deviceCodeKey != null and accessHistoryVo.deviceCodeKey != ''">
            AND ah.device_code_ LIKE CONCAT('%', #{accessHistoryVo.deviceCodeKey}, '%')
        </if>
        <if test="accessHistoryVo.deviceNameKey != null and accessHistoryVo.deviceNameKey != ''">
            AND ah.device_name_ LIKE CONCAT('%', #{accessHistoryVo.deviceNameKey}, '%')
        </if>
        <if test="accessHistoryVo.accessTypeList != null and accessHistoryVo.accessTypeList.size() > 0">
            AND ah.access_type_ IN
            <foreach collection="accessHistoryVo.accessTypeList" item="accessType" open="(" close=")" separator=",">
                #{accessType}
            </foreach>
        </if>
        <if test="accessHistoryVo.recognitionMethodList != null and accessHistoryVo.recognitionMethodList.size() > 0">
            AND ah.recognition_method_ IN
            <foreach collection="accessHistoryVo.recognitionMethodList" item="recognitionMethod" open="(" close=")" separator=",">
                #{recognitionMethod}
            </foreach>
        </if>
        <if test="accessHistoryVo.accessStatusList != null and accessHistoryVo.accessStatusList.size() > 0">
            AND ah.access_status_ IN
            <foreach collection="accessHistoryVo.accessStatusList" item="accessStatus" open="(" close=")" separator=",">
                #{accessStatus}
            </foreach>
        </if>
        <if test="accessHistoryVo.accessControlIdList != null and accessHistoryVo.accessControlIdList.size() > 0">
            AND ah.access_control_id_ IN
            <foreach collection="accessHistoryVo.accessControlIdList" item="accessControlId" open="(" close=")" separator=",">
                #{accessControlId}
            </foreach>
        </if>
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        <if test="accessHistoryVo.temperatureMin != null">
            AND ah.temperature_ >= #{accessHistoryVo.temperatureMin}
        </if>
        <if test="accessHistoryVo.temperatureMax != null">
            AND ah.temperature_ &lt;= #{accessHistoryVo.temperatureMax}
        </if>
        <if test="accessHistoryVo.wearMask != null">
            AND ah.wear_mask_ = #{accessHistoryVo.wearMask}
        </if>
        ORDER BY ah.access_time_ DESC
    </select>

    <!-- 根据进出类型统计数量 -->
    <select id="countByAccessType" resultType="map">
        SELECT 
            ah.access_type_ as key,
            COUNT(*) as count
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        GROUP BY ah.access_type_
    </select>

    <!-- 根据识别方式统计数量 -->
    <select id="countByRecognitionMethod" resultType="map">
        SELECT 
            ah.recognition_method_ as key,
            COUNT(*) as count
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        GROUP BY ah.recognition_method_
    </select>

    <!-- 根据进出状态统计数量 -->
    <select id="countByAccessStatus" resultType="map">
        SELECT 
            ah.access_status_ as key,
            COUNT(*) as count
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        GROUP BY ah.access_status_
    </select>

    <!-- 根据设备统计进出次数 -->
    <select id="countByDevice" resultType="map">
        SELECT 
            ah.device_code_ as key,
            COUNT(*) as count
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        GROUP BY ah.device_code_
    </select>

    <!-- 根据时间统计进出趋势 -->
    <select id="countTrendByTime" resultType="map">
        SELECT 
            <if test="accessHistoryVo.dateFormat != null and accessHistoryVo.dateFormat != ''">
                DATE_FORMAT(ah.access_time_, #{accessHistoryVo.dateFormat}) as time_key,
            </if>
            <if test="accessHistoryVo.dateFormat == null or accessHistoryVo.dateFormat == ''">
                DATE_FORMAT(ah.access_time_, '%Y-%m-%d') as time_key,
            </if>
            COUNT(*) as count,
            SUM(CASE WHEN ah.access_type_ = 1 THEN 1 ELSE 0 END) as enter_count,
            SUM(CASE WHEN ah.access_type_ = 2 THEN 1 ELSE 0 END) as exit_count
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        GROUP BY time_key
        ORDER BY time_key
    </select>

    <!-- 根据人员统计进出次数 -->
    <select id="countByPerson" resultType="map">
        SELECT 
            ah.person_id_card_ as key,
            ah.person_name_ as person_name,
            COUNT(*) as count,
            SUM(CASE WHEN ah.access_type_ = 1 THEN 1 ELSE 0 END) as enter_count,
            SUM(CASE WHEN ah.access_type_ = 2 THEN 1 ELSE 0 END) as exit_count
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        GROUP BY ah.person_id_card_, ah.person_name_
        ORDER BY count DESC
    </select>

    <!-- 统计今日进入人数 -->
    <select id="countTodayEnter" resultType="long">
        SELECT COUNT(DISTINCT ah.person_id_card_) 
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0 
        AND ah.access_type_ = 1
        AND ah.access_status_ = 1
        AND DATE(ah.access_time_) = CURDATE()
        <if test="accessHistoryVo.deviceCode != null and accessHistoryVo.deviceCode != ''">
            AND ah.device_code_ = #{accessHistoryVo.deviceCode}
        </if>
    </select>

    <!-- 统计今日外出人数 -->
    <select id="countTodayExit" resultType="long">
        SELECT COUNT(DISTINCT ah.person_id_card_) 
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0 
        AND ah.access_type_ = 2
        AND ah.access_status_ = 1
        AND DATE(ah.access_time_) = CURDATE()
        <if test="accessHistoryVo.deviceCode != null and accessHistoryVo.deviceCode != ''">
            AND ah.device_code_ = #{accessHistoryVo.deviceCode}
        </if>
    </select>

    <!-- 统计总进出次数 -->
    <select id="countTotal" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
    </select>

    <!-- 统计成功进出次数 -->
    <select id="countSuccess" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0 
        AND ah.access_status_ = 1
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
    </select>

    <!-- 统计失败进出次数 -->
    <select id="countFailed" resultType="long">
        SELECT COUNT(*) 
        FROM safe_access_history ah
        WHERE ah.deleted_ = 0 
        AND ah.access_status_ = 0
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
    </select>

    <!-- 查询最近进出记录 -->
    <select id="queryRecentList" resultMap="HistoryVoResultMap">
        SELECT 
            ah.*,
            d.area_path
        FROM safe_access_history ah
        LEFT JOIN base_device d ON ah.device_code_ = d.code
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.deviceCode != null and accessHistoryVo.deviceCode != ''">
            AND ah.device_code_ = #{accessHistoryVo.deviceCode}
        </if>
        <if test="accessHistoryVo.personIdCard != null and accessHistoryVo.personIdCard != ''">
            AND ah.person_id_card_ = #{accessHistoryVo.personIdCard}
        </if>
        ORDER BY ah.access_time_ DESC
        LIMIT #{limit}
    </select>

    <!-- 查询导出数据 -->
    <select id="queryList4Export" resultMap="HistoryVoResultMap">
        SELECT 
            ah.*,
            d.area_path
        FROM safe_access_history ah
        LEFT JOIN base_device d ON ah.device_code_ = d.code
        WHERE ah.deleted_ = 0
        <if test="accessHistoryVo.personNameKey != null and accessHistoryVo.personNameKey != ''">
            AND ah.person_name_ LIKE CONCAT('%', #{accessHistoryVo.personNameKey}, '%')
        </if>
        <if test="accessHistoryVo.deviceCodeKey != null and accessHistoryVo.deviceCodeKey != ''">
            AND ah.device_code_ LIKE CONCAT('%', #{accessHistoryVo.deviceCodeKey}, '%')
        </if>
        <if test="accessHistoryVo.startTime != null">
            AND ah.access_time_ >= #{accessHistoryVo.startTime}
        </if>
        <if test="accessHistoryVo.endTime != null">
            AND ah.access_time_ &lt;= #{accessHistoryVo.endTime}
        </if>
        ORDER BY ah.access_time_ DESC
    </select>

    <!-- 根据设备编码查询最后进出记录 -->
    <select id="findLastAccessByDeviceCode" resultMap="HistoryVoResultMap">
        SELECT 
            ah.*,
            d.area_path
        FROM safe_access_history ah
        LEFT JOIN base_device d ON ah.device_code_ = d.code
        WHERE ah.device_code_ = #{deviceCode} 
        AND ah.deleted_ = 0
        ORDER BY ah.access_time_ DESC
        LIMIT 1
    </select>

</mapper>
