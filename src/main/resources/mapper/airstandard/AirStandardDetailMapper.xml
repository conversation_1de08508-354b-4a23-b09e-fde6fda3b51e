<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.airstandard.mapper.AirStandardDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.airstandard.entity.AirStandardDetail">
        <id column="id_" property="id" />
        <result column="standard_id_" property="standardId" />
        <result column="properties_key_" property="propertiesKey" />
        <result column="properties_name_" property="propertiesName" />
        <result column="day_ave_peak_" property="dayAvePeak" />
        <result column="hour_ave_peak_" property="hourAvePeak" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from livable_air_standard_detail where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from livable_air_standard_detail where id_ = #{id} 
    </select>

    <select id="queryDetailByStandardId" parameterType="long" resultMap="BaseResultMap">
        select d.* from livable_air_standard_detail d where d.standard_id_ = #{standardId}
    </select>
</mapper>
