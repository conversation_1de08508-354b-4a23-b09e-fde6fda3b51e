<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.emergency.department.mapper.DepartmentResourcesRefMapper">

    <select id="selectPage" resultType="com.smartPark.business.emergency.department.entity.vo.DepartmentResourcesRefVo">
        SELECT 
            r.*
        FROM
            safe_department_resources_ref r
        <where>
            <if test="departmentResourcesRefVo != null">
                <if test="departmentResourcesRefVo.departmentId != null">
                    AND r.department_id_ = #{departmentResourcesRefVo.departmentId}
                </if>
                <if test="departmentResourcesRefVo.type != null">
                    AND r.type_ = #{departmentResourcesRefVo.type}
                </if>
                <if test="departmentResourcesRefVo.refSupplyId != null">
                    AND r.ref_supply_id_ = #{departmentResourcesRefVo.refSupplyId}
                </if>
                <if test="departmentResourcesRefVo.refDepartmentId != null">
                    AND r.ref_department_id_ = #{departmentResourcesRefVo.refDepartmentId}
                </if>
            </if>
        </where>
        group by r.ref_supply_id_
    </select>

</mapper> 