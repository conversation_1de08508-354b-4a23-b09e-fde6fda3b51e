<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.autoIrrigate.mapper.IrrigateTimeStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.autoIrrigate.entity.IrrigateTimeStatistics">
        <id column="id_" property="id" />
        <result column="device_code_" property="deviceCode" />
        <result column="statistics_time_" property="statisticsTime" />
        <result column="use_time_" property="useTime" />
        <result column="last_open_time_" property="lastOpenTime" />
        <result column="start_time_" property="startTime" />
        <result column="end_time_" property="endTime" />
        <result column="last_switch_state_" property="lastSwitchState" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>
    <resultMap id="TimeStatisticsDtoMap" extends="BaseResultMap" type="com.smartPark.business.autoIrrigate.entity.dto.IrrigateTimeStatisticsDTO">
        <result column="dateTimeStr" property="dateTimeStr" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from livable_irrigate_time_statistics where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from livable_irrigate_time_statistics where id_ = #{id} 
    </select>

    <select id="countTimeUseGroupByDate" parameterType="com.smartPark.business.autoIrrigate.entity.vo.IrrigateAnalyzeVo"
            resultMap="TimeStatisticsDtoMap">
        select t.* from (
        select
        <if test="irrigateAnalyzeVo.queryType != null and irrigateAnalyzeVo.queryType != ''">
            <if test="irrigateAnalyzeVo.queryType == 'year'">
                date_format(lits.statistics_time_, '%Y') as dateTimeStr,
            </if>
            <if test="irrigateAnalyzeVo.queryType == 'month'">
                date_format(lits.statistics_time_, '%Y-%m') as dateTimeStr,
            </if>
            <if test="irrigateAnalyzeVo.queryType == 'day'">
                date_format(lits.statistics_time_, '%Y-%m-%d') as dateTimeStr,
            </if>
        </if>
        sum(ifnull(lits.use_time_, 0)) as use_time_
        from livable_irrigate_time_statistics lits
        where lits.deleted_ = 0
        <if test="irrigateAnalyzeVo.startTime != null">
            <![CDATA[
            and lits.statistics_time_ >= #{irrigateAnalyzeVo.startTime}
            ]]>
        </if>
        <if test="irrigateAnalyzeVo.endTime != null">
            <![CDATA[
            and lits.statistics_time_ <= #{irrigateAnalyzeVo.endTime}
            ]]>
        </if>
        <if test="irrigateAnalyzeVo.irrigateDeviceList != null and irrigateAnalyzeVo.irrigateDeviceList.size() != 0">
            and lits.device_code_ in
            <foreach collection="irrigateAnalyzeVo.irrigateDeviceList" item="item" index="index" open="(" close=")" separator=",">
                #{item.deviceCode}
            </foreach>
        </if>
        <if test="irrigateAnalyzeVo.queryType != null and irrigateAnalyzeVo.queryType != ''">
            <if test="irrigateAnalyzeVo.queryType == 'year'">
                group by date_format(lits.statistics_time_, '%Y')
            </if>
            <if test="irrigateAnalyzeVo.queryType == 'month'">
                group by date_format(lits.statistics_time_, '%Y-%m')
            </if>
            <if test="irrigateAnalyzeVo.queryType == 'day'">
                group by date_format(lits.statistics_time_, '%Y-%m-%d')
            </if>
        </if>
        ) t where 1=1
        <if test="irrigateAnalyzeVo.dateTimeStr != null and irrigateAnalyzeVo.dateTimeStr != ''">
            and t.dateTimeStr = #{irrigateAnalyzeVo.dateTimeStr}
        </if>
    </select>

    <select id="countTimeUse" parameterType="com.smartPark.business.autoIrrigate.entity.vo.IrrigateAnalyzeVo" resultType="java.lang.Double">
        select
        sum(ifnull(lits.use_time_, 0)) as use_time_
        from livable_irrigate_time_statistics lits
        where lits.deleted_ = 0
        <if test="irrigateAnalyzeVo.startTime != null">
            <![CDATA[
            and lits.statistics_time_ >= #{irrigateAnalyzeVo.startTime}
            ]]>
        </if>
        <if test="irrigateAnalyzeVo.endTime != null">
            <![CDATA[
            and lits.statistics_time_ <= #{irrigateAnalyzeVo.endTime}
            ]]>
        </if>
        <if test="irrigateAnalyzeVo.irrigateDeviceList != null and irrigateAnalyzeVo.irrigateDeviceList.size() != 0">
            and lits.device_code_ in
            <foreach collection="irrigateAnalyzeVo.irrigateDeviceList" item="item" index="index" open="(" close=")" separator=",">
                #{item.deviceCode}
            </foreach>
        </if>
    </select>
</mapper>
