<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.autoIrrigate.mapper.IrrigateDeviceRefMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.autoIrrigate.entity.IrrigateDeviceRef">
        <id column="id_" property="id" />
        <result column="device_code_" property="deviceCode" />
        <result column="device_type_" property="deviceType" />
        <result column="parent_device_code_" property="parentDeviceCode" />
        <result column="parent_device_type_" property="parentDeviceType" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from livable_irrigate_device_ref where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from livable_irrigate_device_ref where id_ = #{id} 
    </select>


</mapper>
