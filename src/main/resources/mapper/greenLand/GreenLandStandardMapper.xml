<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.greenLand.mapper.GreenLandStandardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.greenLand.entity.GreenLandStandard">
        <id column="id_" property="id" />
        <result column="land_area_" property="landArea" />
        <result column="land_rate_" property="landRate" />
        <result column="green_rate_" property="greenRate" />
        <result column="effect_start_time_" property="effectStartTime" />
        <result column="effect_end_time_" property="effectEndTime" />
        <result column="version_" property="version" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="deleted_" property="deleted" />
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from livable_green_land_standard where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from livable_green_land_standard where id_ = #{id} 
    </select>


</mapper>
