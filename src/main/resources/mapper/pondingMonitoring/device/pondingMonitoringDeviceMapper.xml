<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.pondingMonitoring.device.mapper.PondingMonitoringDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.pondingMonitoring.device.entity.PondingMonitoringDevice">
        <id column="id" property="id" />
        <result column="device_code_" property="deviceCode" />
        <result column="use_status" property="useStatus" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted"/>
    </resultMap>
    
    <select id="queryListByPage" resultType="com.smartPark.business.pondingMonitoring.device.entity.vo.PondingMonitoringDeviceVo">
        SELECT
            a.id_ id,
            a.device_code_ deviceCode,
            b.sbmc,
            d.status,
            d.alarm_state alarmState,
            b.use_status useStatus,
            b.bsm,
            c.owner_enterprise_name ownerEnterpriseName,
            b.area_path areaPath,
            c.objX,
            c.objY
        FROM
            livable_ponding_monitoring_device a
                LEFT JOIN base_device_extend_info b ON a.device_code_ = b.device_id
                LEFT JOIN base_monitor_point_obj_v c ON b.dwbsm = c.obj_id
                LEFT JOIN base_device d ON a.device_code_ = d.`code`
        WHERE
          a.deleted_ = 0
        <if test="pondingMonitoringDeviceVo.deviceCode != null and pondingMonitoringDeviceVo.deviceCode != ''">
            AND a.device_code_ LIKE CONCAT('%',#{pondingMonitoringDeviceVo.deviceCode},'%')
        </if>
        <if test="pondingMonitoringDeviceVo.sbmc != null and pondingMonitoringDeviceVo.sbmc != ''">
            AND b.sbmc LIKE CONCAT('%',#{pondingMonitoringDeviceVo.sbmc},'%')
        </if>
        <if test="pondingMonitoringDeviceVo.areaPaths != null and pondingMonitoringDeviceVo.areaPaths.size() > 0">
            and b.area_path in
            <foreach collection="pondingMonitoringDeviceVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                #{areaPath}
            </foreach>
        </if>
        <if test="pondingMonitoringDeviceVo.szjd != null and pondingMonitoringDeviceVo.szjd != ''">
            AND b.szjd =  #{pondingMonitoringDeviceVo.szjd}
        </if>
        <if test="pondingMonitoringDeviceVo.szsq != null and pondingMonitoringDeviceVo.szsq != ''">
            AND b.szsq =  #{pondingMonitoringDeviceVo.szsq}
        </if>
        <if test="pondingMonitoringDeviceVo.szdywg != null and pondingMonitoringDeviceVo.szdywg != ''">
            AND b.szdywg =  #{pondingMonitoringDeviceVo.szdywg}
        </if>
        <if test="pondingMonitoringDeviceVo.areaPath != null and pondingMonitoringDeviceVo.areaPath != ''">
            AND b.area_path =  #{pondingMonitoringDeviceVo.areaPath}
        </if>
        <if test="pondingMonitoringDeviceVo.bsm != null and pondingMonitoringDeviceVo.bsm != ''">
            AND b.bsm LIKE CONCAT('%',#{pondingMonitoringDeviceVo.bsm},'%')
        </if>
        <if test="pondingMonitoringDeviceVo.status == 1 ">
            AND d.`status` =  1
        </if>
        <if test="pondingMonitoringDeviceVo.status == 0 ">
            AND d.`status` !=  1
        </if>
        <if test="pondingMonitoringDeviceVo.useStatus != null">
            AND b.use_status =  #{pondingMonitoringDeviceVo.useStatus}
        </if>
        ORDER BY a.modify_time_ DESC
    </select>

    <select id="getAreas" resultType="com.smartPark.common.entity.deviceArea.DeviceArea">
        SELECT
            b.szjd,
            b.szsq,
            b.szdywg
        FROM
            livable_ponding_monitoring_device a
                JOIN base_device_extend_info b
                    ON a.device_code_ = b.device_id
        WHERE
            a.deleted_ = 0
    </select>

    <select id="selectOnline" resultType="com.smartPark.business.pondingMonitoring.device.entity.PondingMonitoringDevice">
        SELECT
            a.id_ id,
            a.device_code_ deviceCode,
            d.status
        FROM
            livable_ponding_monitoring_device a
                LEFT JOIN base_device d ON a.device_code_ = d.`code`
        WHERE
            a.deleted_ = 0
            and d.status = 1
    </select>

</mapper>
