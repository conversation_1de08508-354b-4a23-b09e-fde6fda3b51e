<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.pondingMonitoring.device.mapper.PondingMonitoringDeviceAlarmMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.common.alarm.entity.Alarm">
        <id column="id_" property="id" />
        <id column="code_" property="code" />
        <result column="rule_engine_id_" property="ruleEngineId" />
        <result column="model_" property="model" />
        <result column="device_code_" property="deviceCode" />
        <result column="alarm_type_" property="alarmType" />
        <result column="push_status_" property="pushStatus" />
        <result column="work_no_" property="workNo" />
        <result column="status_" property="status" />
        <result column="level_" property="level" />
        <result column="content_" property="content" />
        <result column="source_json_" property="sourceJson" />
        <result column="alarm_time_" property="alarmTime" />
        <result column="alarm_data_" property="alarmData" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="findPondingMonitoringDeviceList" resultType="com.smartPark.business.pondingMonitoring.device.entity.vo.PondingMonitoringDeviceAlarmVo">
        SELECT
            a.id_ id,
            a.code_ code,
            a.device_code_ deviceCode,
            c.sbmc,
            c.bsm,
            c.obj_id objId,
            c.area_path areaPath,
            a.alarm_type_ alarmType,
            a.level_ level,
            a.push_status_ pushStatus,
            c.area_path areaPath,
            a.alarm_time_ alarmTime,
            a.work_no_ workNo,
            a.content_ content
        FROM
            base_alarm a
--                 LEFT JOIN livable_air_quality_device b ON a.device_code_ = b.device_code_
--                 AND b.deleted_ = 0
                LEFT JOIN base_device_extend_info c ON a.device_code_ = c.device_id
        WHERE
        <if test="pondingMonitoringDeviceAlarmVo.model != null and pondingMonitoringDeviceAlarmVo.model != ''">
            a.model_ = #{pondingMonitoringDeviceAlarmVo.model}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.id != null ">
            AND a.id_ = #{pondingMonitoringDeviceAlarmVo.id}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.deviceCodeKey != null and pondingMonitoringDeviceAlarmVo.deviceCodeKey != ''">
            AND a.device_code_ LIKE CONCAT('%',#{pondingMonitoringDeviceAlarmVo.deviceCodeKey},'%')
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.deviceCode != null and pondingMonitoringDeviceAlarmVo.deviceCode != ''">
            AND a.device_code_ = #{pondingMonitoringDeviceAlarmVo.deviceCode}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.szjd != null and pondingMonitoringDeviceAlarmVo.szjd != ''">
            AND c.szjd =  #{pondingMonitoringDeviceAlarmVo.szjd}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.szsq != null and pondingMonitoringDeviceAlarmVo.szsq != ''">
            AND c.szsq =  #{pondingMonitoringDeviceAlarmVo.szsq}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.szdywg != null and pondingMonitoringDeviceAlarmVo.szdywg != ''">
            AND c.szdywg =  #{pondingMonitoringDeviceAlarmVo.szdywg}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.areaPaths != null and pondingMonitoringDeviceAlarmVo.areaPaths.size() > 0">
            AND c.area_path IN
            <foreach collection="pondingMonitoringDeviceAlarmVo.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                #{areaPath}
            </foreach>
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.areaPath != null and pondingMonitoringDeviceAlarmVo.areaPath != ''">
            AND c.area_path =  #{pondingMonitoringDeviceAlarmVo.areaPath}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.bsm != null and pondingMonitoringDeviceAlarmVo.bsm != ''">
            AND c.bsm LIKE CONCAT('%',#{pondingMonitoringDeviceAlarmVo.bsm},'%')
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.alarmType != null ">
            AND a.alarm_type_ = #{pondingMonitoringDeviceAlarmVo.alarmType}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.level != null ">
            AND a.level_ = #{pondingMonitoringDeviceAlarmVo.level}
        </if>
        <if test="pondingMonitoringDeviceAlarmVo.pushStatus != null ">
            AND a.push_status_ = #{pondingMonitoringDeviceAlarmVo.pushStatus}
        </if>
        ORDER BY a.alarm_time_ DESC
    </select>
</mapper>
