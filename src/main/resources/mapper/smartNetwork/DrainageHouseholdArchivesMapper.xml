<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.smartNetwork.mapper.DrainageHouseholdArchivesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.smartNetwork.entity.DrainageHouseholdArchives">
        <id column="id_" property="id" />
        <result column="obj_id_" property="objId" />
        <result column="pipe_line_obj_id_" property="pipeLineObjId" />
        <result column="enterprise_type_" property="enterpriseType" />
        <result column="address_" property="address" />
        <result column="catchment_area_" property="catchmentArea" />
        <result column="legal_person_" property="legalPerson" />
        <result column="annual_output_value_" property="annualOutputValue" />
        <result column="employees_num" property="employeesNum" />
        <result column="area_" property="area" />
        <result column="licence_no_" property="licenceNo" />
        <result column="drainage_allowance_" property="drainageAllowance" />
        <result column="drainage_household_archives_level_" property="drainageHouseholdArchivesLevel" />
        <result column="drainage_household_archives_type_" property="drainageHouseholdArchivesType" />
        <result column="license_start_time_" property="licenseStartTime" />
        <result column="license_end_time_" property="licenseEndTime" />
        <result column="license_issuance_time_" property="licenseIssuanceTime" />
        <result column="license_url_" property="licenseUrl" />
        <result column="sewage_treatment_method_" property="sewageTreatmentMethod" />
        <result column="affiliated_sewage_plant_" property="affiliatedSewagePlant" />
        <result column="process_" property="process" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="creator_id_" property="creatorId" />
        <result column="modify_id_" property="modifyId" />
        <result column="deleted_" property="deleted" />

        <result column="archives_no_" property="archivesNo"/>
        <result column="archives_name_" property="archivesName"/>
        <result column="contact_person_" property="contactPerson"/>
        <result column="contact_phone_" property="contactPhone"/>
        <result column="points_" property="points"/>
        <result column="szjd_" property="szjd"/>
        <result column="szsq_" property="szsq"/>
        <result column="szdywg_" property="szdywg"/>
        <result column="area_path_" property="areaPath"/>

        <association property="objInfo" javaType="com.smartPark.common.entity.device.ObjInfo">
            <id column="obj_id" jdbcType="VARCHAR" property="objId"/>
            <result column="obj_name" property="objName"/>
            <result column="init_date" jdbcType="TIMESTAMP" property="initDate"/>
            <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate"/>
            <result column="contact_person" property="contactPerson"/>
            <result column="contact_phone" property="contactPhone"/>
            <result column="obj_state" jdbcType="INTEGER" property="objState"/>
            <result column="data_source" jdbcType="INTEGER" property="dataSource"/>
            <result column="dept_name" property="deptName"/>
            <result column="first_obj_category_name" property="firstObjCategoryName"/>
            <result column="second_obj_category_name" property="secondObjCategoryName"/>
            <result column="objX" jdbcType="DOUBLE" property="objX"/>
            <result column="objY" jdbcType="DOUBLE" property="objY"/>
            <result column="op_enterprise_name" property="opEnterpricseName"/>
            <result column="owner_enterprise_name" property="ownerEnterpriseName"/>
            <result column="szjd" property="szjd"/>
            <result column="szsq" property="szsq"/>
            <result column="szdywg" property="szdywg"/>
            <result column="area_path" property="areaPath"/>
            <result column="remark" property="remark"/>
        </association>
    </resultMap>

    <resultMap id="vo" type="com.smartPark.business.smartNetwork.entity.vo.DrainageHouseholdArchivesVo" extends="BaseResultMap">
        <result column="pipe_line_name" property="pipeLineName" />
    </resultMap>

    <select id="selectPage" resultMap="vo">
        select safe_house_hold_archives.*,base.*,base2.obj_name pipe_line_name
        from safe_house_hold_archives
        left join base_monitor_point_obj_v base on safe_house_hold_archives.obj_id_ = base.obj_id
        left join base_monitor_point_obj_v base2 on safe_house_hold_archives.pipe_line_obj_id_ = base2.obj_id
        <where>
            deleted_ = 0
            <if test="drainageHouseholdArchives.drainageHouseholdArchivesLevel != null and drainageHouseholdArchives.drainageHouseholdArchivesLevel != ''">
                and safe_house_hold_archives.drainage_household_archives_level_ = #{drainageHouseholdArchives.drainageHouseholdArchivesLevel}
            </if>
            <if test="drainageHouseholdArchives.drainageHouseholdArchivesType != null and drainageHouseholdArchives.drainageHouseholdArchivesType != ''">
                and safe_house_hold_archives.drainage_household_archives_type_ = #{drainageHouseholdArchives.drainageHouseholdArchivesType}
            </if>
            <!--<if test="drainageHouseholdArchives.objInfo != null">
                <if test="drainageHouseholdArchives.objInfo.objName != null and drainageHouseholdArchives.objInfo.objName != ''">
                    and base.obj_name like concat('%', #{drainageHouseholdArchives.objInfo.objName}, '%')
                </if>
                <if test="drainageHouseholdArchives.objInfo.objId != null and drainageHouseholdArchives.objInfo.objId != ''">
                    and safe_house_hold_archives.obj_id_ like concat('%', #{drainageHouseholdArchives.objInfo.objId}, '%')
                </if>
                <if test="drainageHouseholdArchives.objInfo.szjd != null and drainageHouseholdArchives.objInfo.szjd != ''">
                    and base.szjd = #{drainageHouseholdArchives.objInfo.szjd}
                </if>
                <if test="drainageHouseholdArchives.objInfo.szsq != null and drainageHouseholdArchives.objInfo.szsq != ''">
                    and base.szsq = #{drainageHouseholdArchives.objInfo.szsq}
                </if>
                <if test="drainageHouseholdArchives.objInfo.szdywg != null and drainageHouseholdArchives.objInfo.szdywg != ''">
                    and base.szdywg = #{drainageHouseholdArchives.objInfo.szdywg}
                </if>
                <if test="drainageHouseholdArchives.objInfo.areaPaths != null and drainageHouseholdArchives.objInfo.areaPaths.size() > 0">
                    and base.area_path in
                    <foreach item="item" index="index" collection="drainageHouseholdArchives.objInfo.areaPaths" open="("
                             separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="drainageHouseholdArchives.areaPaths != null and drainageHouseholdArchives.areaPaths.size() > 0">
                and base.area_path in
                <foreach collection="drainageHouseholdArchives.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                    #{areaPath}
                </foreach>
            </if>-->

            <if test="drainageHouseholdArchives.archivesName != null and drainageHouseholdArchives.archivesName != ''">
                and archives_name_ like concat('%', #{drainageHouseholdArchives.archivesName}, '%')
            </if>
            <if test="drainageHouseholdArchives.licenceNo != null and drainageHouseholdArchives.licenceNo != ''">
                and licence_no_ like concat('%', #{drainageHouseholdArchives.licenceNo}, '%')
            </if>
            <if test="drainageHouseholdArchives.affiliatedSewagePlant != null and drainageHouseholdArchives.affiliatedSewagePlant != ''">
                and affiliated_sewage_plant_ like concat('%', #{drainageHouseholdArchives.affiliatedSewagePlant}, '%')
            </if>
            <if test="drainageHouseholdArchives.enterpriseType != null and drainageHouseholdArchives.enterpriseType != ''">
                and enterprise_type_ like concat('%', #{drainageHouseholdArchives.enterpriseType}, '%')
            </if>
            <if test="drainageHouseholdArchives.pipeLineObjName != null and drainageHouseholdArchives.pipeLineObjName != ''">
                and base2.obj_name like concat('%', #{drainageHouseholdArchives.pipeLineObjName}, '%')
            </if>
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageHouseholdArchives.drainageAllowanceMin)">
                AND drainage_allowance_ &gt;= #{drainageHouseholdArchives.drainageAllowanceMin}
            </if>
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(drainageHouseholdArchives.drainageAllowanceMax)">
                AND drainage_allowance_ &lt;= #{drainageHouseholdArchives.drainageAllowanceMax}
            </if>
            <if test="drainageHouseholdArchives.szjd != null and drainageHouseholdArchives.szjd != ''">
                and szjd_ = #{drainageHouseholdArchives.szjd}
            </if>
            <if test="drainageHouseholdArchives.szsq != null and drainageHouseholdArchives.szsq != ''">
                and szsq_ = #{drainageHouseholdArchives.szsq}
            </if>
            <if test="drainageHouseholdArchives.szdywg != null and drainageHouseholdArchives.szdywg != ''">
                and szdywg_ = #{drainageHouseholdArchives.szdywg}
            </if>
            <if test="drainageHouseholdArchives.areaPaths != null and drainageHouseholdArchives.areaPaths.size() != 0">
                and area_path_ in
                <foreach collection="drainageHouseholdArchives.areaPaths" index="index" item="areaPath" open="(" close=")" separator=",">
                    #{areaPath}
                </foreach>
            </if>
        </where>
        order by modify_time_ desc, create_time_ desc
    </select>
    <select id="getArchivesInfo" resultMap="vo">
        select safe_house_hold_archives.*,base.*,base2.obj_name pipe_line_name
        from safe_house_hold_archives
                 left join base_monitor_point_obj_v base on safe_house_hold_archives.obj_id_ = base.obj_id
                 left join base_monitor_point_obj_v base2 on safe_house_hold_archives.pipe_line_obj_id_ = base2.obj_id
        <where>
            safe_house_hold_archives.id_ = #{id}
        </where>
    </select>


</mapper>
