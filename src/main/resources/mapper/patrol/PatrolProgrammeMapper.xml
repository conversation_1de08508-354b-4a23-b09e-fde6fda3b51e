<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.patrol.mapper.PatrolProgrammeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.patrol.entity.PatrolProgramme">
        <id column="id_" property="id" />
        <result column="code_" property="code" />
        <result column="name_" property="name" />
        <result column="category_id_" property="categoryId" />
        <result column="src_" property="src" />
        <result column="status_" property="status" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.smartPark.business.patrol.entity.vo.PatrolProgrammeVo">
        <result column="nickname" property="creatorName" />
        <result column="categoryName" property="categoryName" />
    </resultMap>

    <select id="findMaxCode" resultType="java.lang.String">
        SELECT
        MAX( a.code_ )
        FROM
        safe_patrol_programme a
        where a.code_ like CONCAT(#{pre},'%')
    </select>

    <select id="queryListByPage" resultMap="BaseResultMapVo">
        SELECT
            a.*,
            b.nickname,
            c.name_ categoryName
        FROM
            safe_patrol_programme a
        LEFT JOIN base_user b ON a.creator_id_ = b.id
        LEFT JOIN safe_patrol_category c ON a.category_id_ = c.id_
        where a.deleted_ = 0
        <if test="entity.code != null and entity.code != ''">
            AND a.code_ LIKE CONCAT('%',#{entity.code},'%')
        </if>
        <if test="entity.name != null and entity.name != ''">
            AND a.name_ LIKE CONCAT('%',#{entity.name},'%')
        </if>
        <if test="entity.categoryId != null">
            AND a.category_id_ = #{entity.categoryId}
        </if>
        <if test="entity.status != null">
            AND a.status_ = #{entity.status}
        </if>
        ORDER BY a.modify_time_ DESC
    </select>

</mapper>
