<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.common.device.mapper.DeviceStatisticsDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.common.entity.device.DeviceStatisticsDetail">
        <id column="id_" property="id" />
        <result column="statistics_id_" property="statisticsId" />
        <result column="record_time_" property="recordTime" />
        <result column="device_type_id_" property="deviceTypeId" />
        <result column="device_type_name_" property="deviceTypeName" />
        <result column="device_unit_id_" property="deviceUnitId" />
        <result column="device_unit_name_" property="deviceUnitName" />
        <result column="device_type_code_" property="deviceTypeCode" />
        <result column="device_unit_code_" property="deviceUnitCode" />
        <result column="online_codes_" property="onlineCodes" />
        <result column="offline_codes_" property="offlineCodes" />
        <result column="online_num_" property="onlineNum" />
        <result column="offline_num_" property="offlineNum" />
        <result column="create_time_" property="createTime" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
    </resultMap>

    <select id="countDeviceStatisticsDetail" parameterType="list" resultType="java.util.Map">
        select
            bdu.id as unitId,
            bdu.name as unitName,
            bdu.code as unitCode,
            bdt.id as typeId,
            bdt.name as typeName,
            count(distinct bd.code) as num
        from
            base_device_type bdt ,
            base_device_unit bdu ,
            base_device bd
        where
            bdt.id = bdu.deviceType_id
          and bdu.id = bd.device_unit_id
            <if test="deviceCodeList != null and deviceCodeList.size() != 0">
                and bd.code in
                <foreach collection="deviceCodeList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        group by bdt.id,bdu.id
    </select>
</mapper>
