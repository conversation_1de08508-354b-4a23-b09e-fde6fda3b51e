<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--命名空间应该是对应接口的包名+接口名 -->
<mapper namespace="com.smartPark.common.device.mapper.DeviceApplicationModelRefMapper">
    <resultMap id="deviceApplicationModelRef" type="com.smartPark.common.entity.device.DeviceApplicationModelRef">
        <id column="id_" jdbcType="BIGINT" property="id"/>
        <result column="device_code_" jdbcType="LONGVARCHAR" property="deviceCode"/>
        <result column="application_id_" jdbcType="BIGINT" property="applicationId"/>
        <result column="model_id_" jdbcType="INTEGER" property="modelId"/>
        <result column="data_source_" jdbcType="INTEGER" property="dataSource"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="creator_id_" jdbcType="BIGINT" property="creatorId"/>
        <result column="delete_status_" jdbcType="INTEGER" property="deleteStatus"/>
    </resultMap>

    <resultMap id="deviceApplicationModelRefDTO" type="com.smartPark.common.entity.device.dto.DeviceApplicationModelRefDTO" extends="deviceApplicationModelRef">
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>

    <select id="selectOnline" resultMap="deviceApplicationModelRefDTO">
        SELECT
            a.*,
            b.status
        FROM
            base_device_application_model_ref a
                JOIN base_device b ON a.device_code_ = b.`code`
        WHERE
            a.delete_status_ = 0
    </select>

    <select id="queryDeviceModelIds" resultType="java.lang.String">
        select model_id_ from base_device_application_model_ref where device_code_ = #{deviceCode} and delete_status_ = 0
    </select>
</mapper>
