<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.electronicfence.mapper.CarAlarmMapper">
    <resultMap id="BaseResultMap" type="com.smartPark.business.electronicfence.entity.dto.CarAlarmDTO">
        <id column="id_" property="id"/>
        <id column="code_" property="code"/>
        <result column="rule_engine_id_" property="ruleEngineId"/>
        <result column="model_" property="model"/>
        <result column="device_code_" property="deviceCode"/>
        <result column="alarm_type_" property="alarmType"/>
        <result column="push_status_" property="pushStatus"/>
        <result column="work_no_" property="workNo"/>
        <result column="status_" property="status"/>
        <result column="level_" property="level"/>
        <result column="content_" property="content"/>
        <result column="source_json_" property="sourceJson"/>
        <result column="alarm_time_" property="alarmTime"/>
        <result column="alarm_data_" property="alarmData"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
        <result column="sbmc" property="sbmc"/>
        <association property="carDTO" javaType="com.smartPark.business.electronicfence.entity.dto.CarDTO">
            <result column="car_no_" property="carNo"/>
        </association>
    </resultMap>

       <select id="queryPage" resultMap="BaseResultMap">
        SELECT a.id_,
               a.code_,
               a.device_code_,
               a.alarm_type_,
               a.level_,
               a.push_status_,
               a.alarm_time_,
               a.work_no_,
               b.car_no_,
               a.content_
        FROM base_alarm a
                 left join livable_car_alarm_extend b on a.id_ = b.alarm_id_
        <where>
            <if test="carAlarmDTO.model != null and carAlarmDTO.model != ''">
                a.model_ = #{carAlarmDTO.model}
            </if>
            <if test="carAlarmDTO.id != null">
                AND a.id_ = #{carAlarmDTO.id}
            </if>
            <if test="carAlarmDTO.deviceCode != null and carAlarmDTO.deviceCode != ''">
                AND a.device_code_ LIKE CONCAT('%', #{carAlarmDTO.deviceCode}, '%')
            </if>
            <!--            <if test="carAlarmDTO.szjd != null and carAlarmDTO.szjd != ''">-->
            <!--                AND c.szjd = #{carAlarmDTO.szjd}-->
            <!--            </if>-->
            <!--            <if test="carAlarmDTO.szsq != null and carAlarmDTO.szsq != ''">-->
            <!--                AND c.szsq = #{carAlarmDTO.szsq}-->
            <!--            </if>-->
            <!--            <if test="carAlarmDTO.szdywg != null and carAlarmDTO.szdywg != ''">-->
            <!--                AND c.szdywg = #{carAlarmDTO.szdywg}-->
            <!--            </if>-->
            <!--            <if test="carAlarmDTO.areaPaths != null and carAlarmDTO.areaPaths.size() > 0">-->
            <!--                AND c.area_path IN-->
            <!--                <foreach collection="carAlarmDTO.areaPaths" index="index" item="areaPath" open="(" close=")"-->
            <!--                         separator=",">-->
            <!--                    #{areaPath}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(carAlarmDTO.modelIds)">
                AND a.model_ IN
                <foreach collection="carAlarmDTO.modelIds" index="index" item="mid" open="(" close=")" separator=",">
                    #{mid}
                </foreach>
            </if>
            <!--            <if test="carAlarmDTO.areaPath != null and carAlarmDTO.areaPath != ''">-->
            <!--                AND c.area_path = #{carAlarmDTO.areaPath}-->
            <!--            </if>-->
            <!--            <if test="carAlarmDTO.bsm != null and carAlarmDTO.bsm != ''">-->
            <!--                AND c.bsm LIKE CONCAT('%', #{carAlarmDTO.bsm}, '%')-->
            <!--            </if>-->
            <if test="carAlarmDTO.alarmType != null and carAlarmDTO.alarmType != ''">
                AND a.alarm_type_ = #{carAlarmDTO.alarmType}
            </if>
            <if test="carAlarmDTO.level != null">
                AND a.level_ = #{carAlarmDTO.level}
            </if>
            <if test="carAlarmDTO.pushStatus != null">
                AND a.push_status_ = #{carAlarmDTO.pushStatus}
            </if>
            <if test="carAlarmDTO.carDTO != null">
                <if test="carAlarmDTO.carDTO.carNo != null and carAlarmDTO.carDTO.carNo != ''">
                    AND b.car_no_ like concat('%', #{carAlarmDTO.carDTO.carNo}, '%')
                </if>
            </if>
        </where>
        ORDER BY a.alarm_time_ DESC
    </select>

    <select id="findById" resultMap="BaseResultMap">
        SELECT a.id_,
               a.code_,
               a.device_code_,
               a.alarm_type_,
               a.level_,
               a.push_status_,
               a.alarm_time_,
               a.work_no_,
               b.car_no_,
               c.sbmc as sbmc,
               a.content_
        FROM base_alarm a
                 left join livable_car_alarm_extend b on a.id_ = b.alarm_id_
                 left join base_device_extend_info c on a.device_code_ = c.device_id
        where a.id_ = #{id}
    </select>

    <select id="rank" resultType="com.smartPark.business.electronicfence.entity.dto.AlarmCountDTO">
        SELECT
            b.car_no_ carNo,
            count( 1 ) num
        FROM
            base_alarm a
                left join livable_car_alarm_extend b on a.id_ = b.alarm_id_
                left JOIN livable_car c ON b.car_id_ = c.id_
        WHERE
            c.deleted_ = 0
          AND b.car_no_ IS NOT NULL
        <if test="queryStartTime != null">
                <![CDATA[
                  AND a.alarm_time_ >= #{queryStartTime}
                ]]>
        </if>
        <if test="queryEndTime != null">
            <![CDATA[
                    AND a.alarm_time_ <= #{queryEndTime}
                ]]>
        </if>
        GROUP BY
            b.car_no_
        ORDER BY
            num DESC
            LIMIT 5
    </select>

    <select id="detail4Page" resultType="com.smartPark.business.electronicfence.entity.dto.CarAlarmDTO">
        SELECT
        a.id_,
        a.alarm_time_,
        b.car_no_,
        a.device_code_,
        a.alarm_type_,
        a.content_,
        c.sbmc sbmc
        FROM base_alarm a
        left join livable_car_alarm_extend b on a.id_ = b.alarm_id_
        left join base_device_extend_info c on a.device_code_ = c.device_id
        <where>
            <if test="carAlarmDTO.model != null and carAlarmDTO.model != ''">
                a.model_ = #{carAlarmDTO.model}
            </if>
            <if test="carAlarmDTO.alarmType != null and carAlarmDTO.alarmType != ''">
                AND a.alarm_type_ = #{carAlarmDTO.alarmType}
            </if>
            <if test="carAlarmDTO.queryStartTime != null">
                <![CDATA[
                  AND a.alarm_time_ >= #{carAlarmDTO.queryStartTime}
                ]]>
            </if>
            <if test="carAlarmDTO.queryEndTime != null">
                <![CDATA[
                    AND a.alarm_time_ <= #{carAlarmDTO.queryEndTime}
                ]]>
            </if>
        </where>
        ORDER BY a.alarm_time_ DESC
    </select>
</mapper>
