<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.prowl.mapper.ProwlPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.prowl.entity.ProwlPlan">
        <id column="id_" property="id" />
        <result column="plan_no_" property="planNo" />
        <result column="plan_title_" property="planTitle" />
        <result column="note_" property="note" />
        <result column="time_type_" property="timeType" />
        <result column="time_values_" property="timeValues" />
        <result column="action_time_" property="actionTime" />
        <result column="action_id_" property="actionId" />
        <result column="plan_status_" property="planStatus" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <resultMap id="BaseResultMapVo" type="com.smartPark.business.prowl.entity.vo.ProwlPlanVo" extends="BaseResultMap">
        <result column="nickname" property="actionName" />
    </resultMap>
    <select id="queryListByPage" resultMap="BaseResultMapVo">
        SELECT
        a.*,
        b.nickname
        FROM
        safe_prowl_plan a
        LEFT JOIN base_user b ON a.action_id_ = b.id
        where a.deleted_ = 0
        <if test="entity.planTitle != null and entity.planTitle != ''">
            AND a.plan_title_ LIKE CONCAT('%',#{entity.planTitle},'%')
        </if>
        <if test="entity.planNo != null and entity.planNo != ''">
            AND a.plan_no_ LIKE CONCAT('%',#{entity.planNo},'%')
        </if>
        <if test="entity.planStatus != null">
            AND a.plan_status_ = #{entity.planStatus}
        </if>
        <if test="entity.szjd != null and entity.szjd != ''">
            and a.szjd = #{entity.szjd,jdbcType=VARCHAR}
        </if>
        <if test="entity.szsq != null and entity.szsq != ''">
            and a.szsq = #{entity.szsq,jdbcType=VARCHAR}
        </if>
        <if test="entity.szdywg != null and entity.szdywg != ''">
            and a.szdywg = #{entity.szdywg,jdbcType=VARCHAR}
        </if>
        ORDER BY a.modify_time_ DESC
    </select>

</mapper>
