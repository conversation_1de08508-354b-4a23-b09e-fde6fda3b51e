<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.toilet.mapper.LivableToiletStageStandardStatMapper">
  <resultMap id="BaseResultMap" type="com.smartPark.business.toilet.entity.LivableToiletStageStandardStat">
    <!--@mbg.generated-->
    <!--@Table livable_toilet_stage_standard_stat-->
    <id column="id_" jdbcType="BIGINT" property="id" />
    <result column="date_" jdbcType="TIMESTAMP" property="date" />
    <result column="toilet_code_" jdbcType="VARCHAR" property="toiletCode" />
    <result column="current_stage_standard" jdbcType="INTEGER" property="currentStageStandard" />
    <result column="last_stage_standard" jdbcType="INTEGER" property="lastStageStandard" />
    <result column="same_stage_standard" jdbcType="INTEGER" property="sameStageStandard" />
    <result column="current_stage_standard_ratio" jdbcType="INTEGER" property="currentStageStandardRatio" />
    <result column="last_stage_standard_ratio" jdbcType="INTEGER" property="lastStageStandardRatio" />
    <result column="same_stage_standard_ratio" jdbcType="INTEGER" property="sameStageStandardRatio" />
    <result column="creator_id_" jdbcType="BIGINT" property="creatorId" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_id_" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted_" jdbcType="INTEGER" property="deleted" />
    <result column="time" jdbcType="VARCHAR" property="time" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id_, date_, toilet_code_, current_stage_standard, last_stage_standard, same_stage_standard, 
    current_stage_standard_ratio, last_stage_standard_ratio, same_stage_standard_ratio, 
    creator_id_, create_time_, modify_id_, modify_time_, deleted_
  </sql>

    <select id="queryStandardTotal" resultType="java.lang.Integer">
        select IFNULL(SUM(current_stage_standard),0) from livable_toilet_stage_standard_stat where DATE(date_) BETWEEN #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
    </select>

  <select id="dateStats" resultType="com.smartPark.business.toilet.entity.vo.ToiletFlowTimeStatusVo">
    select
        DATE_FORMAT(date_,'%Y-%m-%d') as `time`,
        IFNULL(SUM(current_stage_standard),0) as `count`
    from livable_toilet_stage_standard_stat
    where date_ BETWEEN #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
    <if test="toiletCode != null and toiletCode != ''">
      and toilet_code_ = #{toiletCode,jdbcType=VARCHAR}
    </if>
    group by `time`
  </select>

  <select id="monthStats" resultType="com.smartPark.business.toilet.entity.vo.ToiletFlowTimeStatusVo">
    select
        DATE_FORMAT(date_,'%Y-%m') as `time`,
        IFNULL(SUM(current_stage_standard),0) as `count`
        from livable_toilet_stage_standard_stat
    where date_ BETWEEN #{startMonth,jdbcType=VARCHAR} and #{endMonth,jdbcType=VARCHAR}
    <if test="toiletCode != null and toiletCode != ''">
      and toilet_code_ = #{toiletCode,jdbcType=VARCHAR}
    </if>
    group by `time`
  </select>

  <select id="yearStats" resultType="com.smartPark.business.toilet.entity.vo.ToiletFlowTimeStatusVo">
    select
        DATE_FORMAT(date_,'%Y') as `time`,
        IFNULL(SUM(current_stage_standard),0) as `count`
    from livable_toilet_stage_standard_stat
    where date_ BETWEEN #{startYear,jdbcType=VARCHAR} and #{endYear,jdbcType=VARCHAR}
    <if test="toiletCode != null and toiletCode != ''">
      and toilet_code_ = #{toiletCode,jdbcType=VARCHAR}
    </if>
    group by `time`
  </select>



  <select id="queryPage" resultMap="BaseResultMap">
      <if test="vo.timeType != null">
          select
          <if test="vo.timeType == 1">DATE_FORMAT(date_,'%Y-%m-%d') as `time`,</if>
          <if test="vo.timeType == 2">DATE_FORMAT(date_,'%Y-%m') as `time`,</if>
          <if test="vo.timeType == 3">DATE_FORMAT(date_,'%Y') as `time`,</if>
          SUM(current_stage_standard) as `current_stage_standard`,
          SUM(last_stage_standard) as `last_stage_standard`,
          SUM(same_stage_standard) as `same_stage_standard`,
          SUM(current_stage_no_standard) as `current_stage_no_standard`,
          SUM(last_stage_no_standard) as `last_stage_no_standard`,
          SUM(same_stage_no_standard) as `same_stage_no_standard`
          from livable_toilet_stage_standard_stat
          <where>
              <if test="vo.timeType == 1">
                  date_ BETWEEN #{vo.startDate,jdbcType=VARCHAR} and #{vo.endDate,jdbcType=VARCHAR}
              </if>
              <if test="vo.timeType == 2">
                  and date_ BETWEEN #{vo.startDate,jdbcType=VARCHAR} and #{vo.endDate,jdbcType=VARCHAR}
              </if>
              <if test="vo.timeType == 3">
                  and date_ BETWEEN #{vo.startDate,jdbcType=VARCHAR} and #{vo.endDate,jdbcType=VARCHAR}
              </if>
              <if test="vo.toiletCode != null and vo.toiletCode != ''">
                  and toilet_code_ = #{vo.toiletCode,jdbcType=VARCHAR}
              </if>
          </where>
          group by `time`
          order by `time` desc
      </if>
  </select>

  <select id="queryStatByDate" resultMap="BaseResultMap">
      select
          SUM(current_stage_standard) as `current_stage_standard`,
          SUM(last_stage_standard) as `last_stage_standard`,
          SUM(same_stage_standard) as `same_stage_standard`,
          SUM(current_stage_no_standard) as `current_stage_no_standard`,
          SUM(last_stage_no_standard) as `last_stage_no_standard`,
          SUM(same_stage_no_standard) as `same_stage_no_standard`
          from livable_toilet_stage_standard_stat
      <where>
          DATE(date_) BETWEEN #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
          <if test="toiletCode != null and toiletCode != ''">
              and toilet_code_ = #{toiletCode,jdbcType=VARCHAR}
          </if>
      </where>
    </select>

  <select id="queryStatByType" resultMap="BaseResultMap">
      select
          SUM(current_stage_standard) as `current_stage_standard` ,
          SUM(current_stage_no_standard) as `current_stage_no_standard`
      from livable_toilet_stage_standard_stat
      <where>
          <if test="timeType == 1">
              DATE_FORMAT(date_,'%Y-%m-%d') = #{startDate,jdbcType=VARCHAR}
          </if>
          <if test="timeType == 2">
              and DATE_FORMAT(date_,'%Y-%m') = #{startDate,jdbcType=VARCHAR}
          </if>
          <if test="timeType == 3">
              and DATE_FORMAT(date_,'%Y') = #{startDate,jdbcType=VARCHAR}
          </if>
          <if test="toiletCode != null and toiletCode != ''">
              and toilet_code_ = #{toiletCode,jdbcType=VARCHAR}
          </if>
      </where>
    </select>
</mapper>