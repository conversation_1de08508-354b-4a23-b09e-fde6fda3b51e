<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.intelligentparking.overview.mapper.ParkingSpaceUseRankMapper">

  <resultMap id="BaseResultMap" type="com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUseRank">
    <id column="id_" jdbcType="BIGINT" property="id" />
    <result column="parking_lot_id_" jdbcType="BIGINT" property="parkingLotId" />
    <result column="parking_num_" jdbcType="INTEGER" property="parkingNum" />
    <result column="used_num_" jdbcType="INTEGER" property="usedNum" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted_" jdbcType="BIGINT" property="deleted" />
    <result column="parkingRate" property="parkingRate" />
    <result column="parkingLotName" property="parkingLotName" />
  </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select * from traffic_parking_space_use_rank where deleted_ = 0 order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select * from traffic_parking_space_use_rank where id_ = #{id} 
    </select>

  <select id="selectParkingSpaceUseRank" resultMap="BaseResultMap">
      <!--      保留四位小数-->

      select
          case
              when sum(a.parking_num_) = 0 then 0
              else round(sum(a.used_num_)/ sum(a.parking_num_),4)
            end as parkingRate,
             a.parking_lot_id_,
            c.obj_name  as parkingLotName
      from traffic_parking_space_use_period a
               left join traffic_parking_lot b on b.id_ = a.parking_lot_id_ and b.deleted_ = 0
                left join base_monitor_point_obj_v c on c.obj_id = b.obj_id_
      where a.deleted_ = 0 and a.statistic_time_ > DATE_SUB(now(), interval 7 day)
      <if test="param.parkingLotIds != null and param.parkingLotIds.size > 0">
          and a.parking_lot_id_ in
          <foreach collection="param.parkingLotIds" item="item" index="index" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      group by a.parking_lot_id_
      order by parkingRate desc limit 5
  </select>

</mapper>
