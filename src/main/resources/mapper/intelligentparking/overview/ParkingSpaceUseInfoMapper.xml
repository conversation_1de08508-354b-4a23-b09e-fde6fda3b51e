<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.intelligentparking.overview.mapper.ParkingSpaceUseInfoMapper">
    <resultMap id="BaseResultMap" type="com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUseInfo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu May 11 16:45:31 CST 2023.
        -->
        <id column="id_" jdbcType="BIGINT" property="id"/>
        <result column="parking_lot_id_" jdbcType="BIGINT" property="parkingLotId"/>
        <result column="parking_num_" jdbcType="INTEGER" property="parkingNum"/>
        <result column="used_num_" jdbcType="INTEGER" property="usedNum"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="deleted_" jdbcType="BIGINT" property="deleted"/>
    </resultMap>

    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from traffic_parking_space_use_info
        where deleted_ = 0
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from traffic_parking_space_use_info
        where id_ = #{id}
    </select>

    <select id="selectParkingSpaceUseInfo" resultMap="BaseResultMap">
        select * from (select sum(used_num_) as used_num_
                       from traffic_parking_space_use_info
        where deleted_ = 0
        <if test="param.parkingLotIds != null and param.parkingLotIds.size > 0">
            and parking_lot_id_ in
            <foreach collection="param.parkingLotIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) a
            join

        (select sum(parking_num_) as parking_num_
         from traffic_parking_lot_floor where deleted_ = 0
        <if test="param.parkingLotIds != null and param.parkingLotIds.size > 0">
            and parking_lot_id_ in
            <foreach collection="param.parkingLotIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        )b

    </select>

    <select id="selectParkingSpaceUseInfos" resultMap="BaseResultMap">
        select parking_lot_id_,
               used_num_
        from traffic_parking_space_use_info
        where deleted_ = 0
        <if test="ids != null and ids.size > 0">
            and parking_lot_id_ in
            <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
