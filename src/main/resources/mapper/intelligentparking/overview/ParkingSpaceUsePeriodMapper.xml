<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.intelligentparking.overview.mapper.ParkingSpaceUsePeriodMapper">
    <resultMap id="BaseResultMap"
               type="com.smartPark.business.intelligentparking.overview.entity.ParkingSpaceUsePeriod">
        <id column="id_" jdbcType="BIGINT" property="id"/>
        <result column="parking_lot_id_" jdbcType="BIGINT" property="parkingLotId"/>
        <result column="parking_num_" jdbcType="INTEGER" property="parkingNum"/>
        <result column="used_num_" jdbcType="INTEGER" property="usedNum"/>
        <result column="statistic_time_" jdbcType="TIMESTAMP" property="statisticTime"/>
        <result column="create_time_" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="deleted_" jdbcType="BIGINT" property="deleted"/>
    </resultMap>


    <select id="selectPage" resultMap="BaseResultMap">
        select *
        from traffic_parking_space_use_period
        where deleted_ = 0
        order by modify_time_ desc, create_time_ desc
    </select>

    <select id="getOneById" resultMap="BaseResultMap">
        select *
        from traffic_parking_space_use_period
        where id_ = #{id}
    </select>

    <select id="selectParkingSpaceUsePeriod" resultMap="BaseResultMap">
        select SUM(used_num_)  as used_num_,
        date_format(statistic_time_, '%Y-%m-%d %H:00:00') as statistic_time_
        from traffic_parking_space_use_period
        where deleted_ = 0
        <if test="param.parkingLotIds != null and param.parkingLotIds.size > 0">
            and parking_lot_id_ in
            <foreach collection="param.parkingLotIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and statistic_time_ &gt;= #{param.startTime}
        and statistic_time_ &lt;= #{param.endTime}
<!--        date_format('2008-08-08 22:23:01', '%Y%m%d%H%i%s');-->
        GROUP BY date_format(statistic_time_, '%Y-%m-%d %H:00:00')
        order by statistic_time_ ASC
    </select>

    <sql id="sumTable">
        (SELECT
            statistic_time_,
            sum( parking_num_ ) parking_num_,
            sum( used_num_ ) used_num_,
            sum( used_num_ ) / sum( parking_num_ )*100 parkRadio
        FROM
            traffic_parking_space_use_period a
        <include refid="commonCountWhere"></include>
        GROUP BY
            statistic_time_)
    </sql>

    <sql id="commonCountWhere">
        <where>
            <if test="lotIds != null and lotIds.size > 0">
                and a.parking_lot_id_ in
                <foreach collection="lotIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and a.statistic_time_ &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and a.statistic_time_ &lt;= #{endTime}
            </if>
        </where>
    </sql>

    <select id="countGroupByTime" resultType="com.smartPark.business.intelligentparking.overview.entity.dto.ParkingStatisticsDTO">
        SELECT
            <if test="dateType == 1">
                DATE_FORMAT( a.statistic_time_, '%Y-%m-%d %H:00' ) timeStr,
            </if>
            <if test="dateType == 2">
                DATE_FORMAT( a.statistic_time_, '%Y-%m-%d' ) timeStr,
            </if>
            <if test="dateType == 3">
                DATE_FORMAT( a.statistic_time_, '%Y-%m' ) timeStr,
            </if>
            <if test="dateType == 4">
                DATE_FORMAT( a.statistic_time_, '%Y' ) timeStr,
            </if>
            max( a.parking_num_ ) parkSum,
            ROUND(avg( a.used_num_ )) parkUseNum,
            avg( a.parkRadio ) parkRadio
        FROM
            <include refid="sumTable"></include> a
        GROUP BY
        <if test="dateType == 1">
            DATE_FORMAT( a.statistic_time_, '%Y-%m-%d %H:00' )
        </if>
        <if test="dateType == 2">
            DATE_FORMAT( a.statistic_time_, '%Y-%m-%d' )
        </if>
        <if test="dateType == 3">
            DATE_FORMAT( a.statistic_time_, '%Y-%m' )
        </if>
        <if test="dateType == 4">
            DATE_FORMAT( a.statistic_time_, '%Y' )
        </if>
    </select>


    <select id="countByTime" resultType="java.lang.Double">
        SELECT
        avg( a.parkRadio ) parkRadio
        FROM
        <include refid="sumTable"></include> a
    </select>

    <select id="parkingRank" resultType="com.smartPark.business.intelligentparking.overview.entity.dto.ParkingStatisticsRankDTO">
        SELECT
            c.obj_name lotName,
            avg( a.used_num_ / a.parking_num_ )*100 part
        FROM
            traffic_parking_space_use_period a
        LEFT JOIN traffic_parking_lot b ON a.parking_lot_id_ = b.id_
        LEFT JOIN base_monitor_point_obj_v c on c.obj_id = b.obj_id_
        <include refid="commonCountWhere"></include>
        GROUP BY
            a.parking_lot_id_
        ORDER BY
            part DESC
            LIMIT 5
    </select>

    <select id="selectMaxTime" resultType="java.util.Date">
        SELECT
            max( statistic_time_ )
        FROM
            traffic_parking_space_use_period
    </select>
    <select id="countGroupByTimeAndParkId" resultType="com.smartPark.business.intelligentparking.overview.entity.dto.ParkingStatisticsDTO">
        select
        parking_lot_id_,
        <if test="dateType == 1">
            DATE_FORMAT( statistic_time_, '%Y-%m-%d %H:00' ) timeStr,
        </if>
        <if test="dateType == 2">
            DATE_FORMAT(statistic_time_, '%Y-%m-%d' ) timeStr,
        </if>
        <if test="dateType == 3">
            DATE_FORMAT( statistic_time_, '%Y-%m' ) timeStr,
        </if>
        <if test="dateType == 4">
            DATE_FORMAT( statistic_time_, '%Y' ) timeStr,
        </if>
        max( parking_num_ ) parkSum,
        ROUND(avg( used_num_ )) parkUseNum,
        avg( used_num_ / parking_num_ * 100 ) parkRadio
        FROM
        traffic_parking_space_use_period a
        <include refid="commonCountWhere"></include>
        GROUP BY
        parking_lot_id_,
        <if test="dateType == 1">
            DATE_FORMAT( a.statistic_time_, '%Y-%m-%d %H:00' )
        </if>
        <if test="dateType == 2">
            DATE_FORMAT( a.statistic_time_, '%Y-%m-%d' )
        </if>
        <if test="dateType == 3">
            DATE_FORMAT( a.statistic_time_, '%Y-%m' )
        </if>
        <if test="dateType == 4">
            DATE_FORMAT( a.statistic_time_, '%Y' )
        </if>

    </select>
    <select id="selectParkingSpaceUsePeriodWithParkId" resultMap="BaseResultMap">
        select parking_lot_id_, SUM(used_num_)  as used_num_,
        date_format(statistic_time_, '%Y-%m-%d %H:00:00') as statistic_time_
        from traffic_parking_space_use_period
        where deleted_ = 0
        <if test="param.parkingLotIds != null and param.parkingLotIds.size > 0">
            and parking_lot_id_ in
            <foreach collection="param.parkingLotIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and statistic_time_ &gt;= #{param.startTime}
        and statistic_time_ &lt;= #{param.endTime}
        <!--        date_format('2008-08-08 22:23:01', '%Y%m%d%H%i%s');-->
        GROUP BY parking_lot_id_, date_format(statistic_time_, '%Y-%m-%d %H:00:00')
        order by statistic_time_ ASC
    </select>
</mapper>
