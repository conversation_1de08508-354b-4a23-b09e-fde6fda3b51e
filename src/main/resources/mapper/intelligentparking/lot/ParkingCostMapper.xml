<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.intelligentparking.lot.mapper.ParkingCostMapper">

    <select id="queryParkingCostPage" resultType="com.smartPark.business.intelligentparking.lot.vo.ParkingCostVO">
        SELECT 
            pc.id_ as id,
            pc.cost_type as costType,
            pc.amount,
            pc.parking_lot_id as parkingLotId,
            pcu.obj_name as parkingLotName,
            pc.create_time_ as createTime,
            pc.creator_id_ as creatorId,
            plu.username as creatorName
        FROM
            traffic_parking_cost pc
        LEFT JOIN
            traffic_parking_lot pl ON pc.parking_lot_id = pl.id_
        LEFT JOIN
            base_monitor_point_obj_v pcu ON pl.obj_id_ = pcu.obj_id
        LEFT JOIN
            base_user plu ON pc.creator_id_ = plu.id
        <where>
            <if test="param.costType != null">
                AND pc.cost_type = #{param.costType}
            </if>
            <if test="param.parkingLotId != null">
                AND pc.parking_lot_id = #{param.parkingLotId}
            </if>
            <if test="param.creatorId != null and param.creatorId != ''">
                AND pc.creator_id_ LIKE CONCAT('%', #{param.creatorId}, '%')
            </if>
            <if test="@com.smartPark.common.utils.MybatisUtil@isNotEmpty(param.lotIds)">
                AND pc.parking_lot_id IN
                <foreach collection="param.lotIds" item="lotId" index="index" open="(" separator="," close=")">
                    #{lotId}
                </foreach>
            </if>
        </where>
        ORDER BY pc.create_time_ DESC
    </select>
</mapper> 