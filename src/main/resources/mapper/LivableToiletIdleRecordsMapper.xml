<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.toilet.mapper.LivableToiletIdleRecordsMapper">
  <resultMap id="BaseResultMap" type="com.smartPark.business.toilet.entity.LivableToiletIdleRecords">
    <!--@mbg.generated-->
    <!--@Table livable_toilet_idle_records-->
    <id column="id_" jdbcType="BIGINT" property="id" />
    <result column="pit_id_" jdbcType="VARCHAR" property="pitId" />
    <result column="toilet_type_" jdbcType="INTEGER" property="toiletType" />
    <result column="pit_states_" jdbcType="INTEGER" property="pitStates" />
    <result column="device_code_" jdbcType="VARCHAR" property="deviceCode" />
    <result column="toilet_code_" jdbcType="VARCHAR" property="toiletCode" />
    <result column="creator_id_" jdbcType="BIGINT" property="creatorId" />
    <result column="create_time_" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_id_" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_time_" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="deleted_" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id_, pit_id_, toilet_type_, pit_states_, toilet_code_,device_code_, creator_id_, create_time_,
    modify_id_, modify_time_, deleted_
  </sql>
</mapper>