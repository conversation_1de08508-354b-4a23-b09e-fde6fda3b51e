<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.pass.mapper.PassViolationOverviewMapper">
    <select id="selectBasicIndex" resultType="java.lang.Long">
        select count(*)
        from traffic_pass_violation tpv
        <where>
            <if test="passViolationVo.type != null">
                and tpv.type_ = #{passViolationVo.type,jdbcType=INTEGER}
            </if>
            <if test="passViolationVo.szjd != null and passViolationVo.szjd != ''">
                and tpv.szjd_ = #{passViolationVo.szjd,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.szsq != null and passViolationVo.szsq != ''">
                and tpv.szsq_ = #{passViolationVo.szsq,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.szdywg != null and passViolationVo.szdywg != ''">
                and tpv.szdywg_ = #{passViolationVo.szdywg,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.startTime != null">
                and tpv.record_time_ <![CDATA[>=]]> #{passViolationVo.startTime}
            </if>
            <if test="passViolationVo.endTime != null">
                and tpv.record_time_ <![CDATA[<=]]> #{passViolationVo.endTime}
            </if>
            <if test="passViolationVo.type == 1 and passViolationVo.category != null and passViolationVo.category != ''">
                and tpv.category_ = #{passViolationVo.category}
            </if>
        </where>
    </select>


    <select id="trendByTime" resultType="com.smartPark.business.pass.entity.vo.TrendByTimeVO">
        select count(*)                                                                     count,
               DATE_FORMAT(record_time_, #{passViolationVo.dateFormat,jdbcType=VARCHAR}) as time
        from traffic_pass_violation tpv
        <where>
            <if test="passViolationVo.type != null">
                and tpv.type_ = #{passViolationVo.type,jdbcType=INTEGER}
            </if>
            <if test="passViolationVo.szjd != null and passViolationVo.szjd != ''">
                and tpv.szjd_ = #{passViolationVo.szjd,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.szsq != null and passViolationVo.szsq != ''">
                and tpv.szsq_ = #{passViolationVo.szsq,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.szdywg != null and passViolationVo.szdywg != ''">
                and tpv.szdywg_ = #{passViolationVo.szdywg,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.startTime != null">
                and tpv.record_time_ <![CDATA[>=]]> #{passViolationVo.startTime}
            </if>
            <if test="passViolationVo.endTime != null">
                and tpv.record_time_ <![CDATA[<=]]> #{passViolationVo.endTime}
            </if>
            <if test="passViolationVo.type == 1 and passViolationVo.category != null and passViolationVo.category != ''">
                and tpv.category_ = #{passViolationVo.category}
            </if>
        </where>
        group by time
        order by time
    </select>

    <select id="countByName" resultType="com.smartPark.business.pass.entity.vo.CountByNameVO">
        select count(*)            count,
               tpv.${passViolationVo.groupByField} as name
        from traffic_pass_violation tpv
        <where>
            <if test="passViolationVo.type != null">
                and tpv.type_ = #{passViolationVo.type,jdbcType=INTEGER}
            </if>
            <if test="passViolationVo.szjd != null and passViolationVo.szjd != ''">
                and tpv.szjd_ = #{passViolationVo.szjd,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.szsq != null and passViolationVo.szsq != ''">
                and tpv.szsq_ = #{passViolationVo.szsq,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.szdywg != null and passViolationVo.szdywg != ''">
                and tpv.szdywg_ = #{passViolationVo.szdywg,jdbcType=VARCHAR}
            </if>
            <if test="passViolationVo.startTime != null">
                and tpv.record_time_ <![CDATA[>=]]> #{passViolationVo.startTime}
            </if>
            <if test="passViolationVo.endTime != null">
                and tpv.record_time_ <![CDATA[<=]]> #{passViolationVo.endTime}
            </if>
            <if test="passViolationVo.type == 1 and passViolationVo.category != null and passViolationVo.category != ''">
                and tpv.category_ = #{passViolationVo.category}
            </if>
        </where>
        group by name
        order by count desc
    </select>
</mapper>
