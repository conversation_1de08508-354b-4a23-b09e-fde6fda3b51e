<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smartPark.business.pass.mapper.PassLampMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.smartPark.business.pass.entity.PassLamp">
        <id column="id_" property="id" />
        <result column="name_" property="name" />
        <result column="code_" property="code" />
        <result column="manufactor_" property="manufactor" />
        <result column="model_" property="model" />
        <result column="status_" property="status" />
        <result column="online_status_" property="onlineStatus" />
        <result column="programme_" property="programme" />
        <result column="phase_" property="phase" />
        <result column="note_" property="note" />
        <result column="objX_" property="objx" />
        <result column="objY_" property="objy" />
        <result column="szjd_" property="szjd" />
        <result column="szsq_" property="szsq" />
        <result column="szdywg_" property="szdywg" />
        <result column="area_path_" property="areaPath" />
        <result column="creator_id_" property="creatorId" />
        <result column="create_time_" property="createTime" />
        <result column="modify_id_" property="modifyId" />
        <result column="modify_time_" property="modifyTime" />
        <result column="remark_" property="remark" />
        <result column="deleted_" property="deleted" />
    </resultMap>

    <select id="getArea" resultType="com.smartPark.common.entity.deviceArea.DeviceArea">
        SELECT
            a.szsq_ szsq,
            a.szjd_ szjd,
            a.szdywg_ szdywg
        FROM
            traffic_pass_lamp a
        WHERE
            a.deleted_ = 0
    </select>

</mapper>
