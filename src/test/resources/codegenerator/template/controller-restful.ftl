package ${basePackage}.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.smartPark.common.base.model.RequestModel;
import ${basePackage}.entity.${modelNameUpperCamel};
import ${basePackage}.service.${modelNameUpperCamel}Service;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * ${modelNameUpperCamel}管理
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
@RestController
@RequestMapping("${modelNameLowerCamel}")
public class ${modelNameUpperCamel}Controller extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private ${modelNameUpperCamel}Service ${modelNameLowerCamel}Service;

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("getPage")
    @ApiOperation("查询分页")
    public RestMessage selectPage(@RequestBody RequestModel<${modelNameUpperCamel}> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<${modelNameUpperCamel}> record = ${modelNameLowerCamel}Service.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    @ApiOperation("查询单条")
    public RestMessage selectOne(@PathVariable Serializable id) {
        return RestBuilders.successBuilder((this.${modelNameLowerCamel}Service.getOneById(id))).build();
    }

    /**
     * 新增数据
     *
     * @param ${modelNameLowerCamel} 实体对象
     * @return 新增结果
     */
    @PostMapping
    @ApiOperation("新增")
    public RestMessage insert(@RequestBody ${modelNameUpperCamel} ${modelNameLowerCamel}) {
        return RestBuilders.successBuilder().success((this.${modelNameLowerCamel}Service.saveOne(${modelNameLowerCamel}))).build();
    }

    /**
     * 修改数据
     *
     * @param ${modelNameLowerCamel} 实体对象
     * @return 修改结果
     */
    @PutMapping
    @ApiOperation("修改单条")
    public RestMessage update(@RequestBody ${modelNameUpperCamel} ${modelNameLowerCamel}) {
        return RestBuilders.successBuilder().success(this.${modelNameLowerCamel}Service.updateOne(${modelNameLowerCamel})).build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(this.${modelNameLowerCamel}Service.deleteByIds(idList)).build();
    }

}

