package ${basePackage}.entity.dto;

import ${basePackage}.entity.${modelNameUpperCamel};
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ${modelNameUpperCamel}实体类DTO
 *
 * <AUTHOR>
 * @date ${date}
 */

@Data
@Accessors(chain = true)
public class ${modelNameUpperCamel}DTO extends ${modelNameUpperCamel} {
    /**
     * 创建时间
     */
    private String createTimeStr;

    public ${modelNameUpperCamel}DTO(${modelNameUpperCamel} ${modelNameLowerCamel}) {
        //this.setName(${modelNameLowerCamel}.getName());
    }
}
