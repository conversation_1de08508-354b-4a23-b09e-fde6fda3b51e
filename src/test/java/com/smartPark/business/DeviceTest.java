package com.smartPark.business;

import com.alibaba.fastjson.JSON;
import com.smartPark.BaseServiceApplication;
import com.smartPark.common.device.service.DeviceService;
import com.smartPark.common.device.service.DeviceUnitService;
import com.smartPark.common.device.service.ProjectSchemeService;
import com.smartPark.common.entity.device.Device;
import com.smartPark.common.entity.device.DevicePropertyStatus;
import com.smartPark.common.entity.device.DeviceUnit;
import com.smartPark.common.entity.device.ProjectScheme;
import com.smartPark.common.redis.RedisConstant;
import com.smartPark.common.redis.event.CacheEventSource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static site.morn.framework.context.CommonConstant.Config.FRAMEWORK_BASE_PACKAGES;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = BaseServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ComponentScan(basePackages = {FRAMEWORK_BASE_PACKAGES, "com.smartPark"}, excludeFilters = {@ComponentScan.Filter(type = FilterType.REGEX,pattern = "com.smartPark.sanzhi.*")})
@EntityScan({FRAMEWORK_BASE_PACKAGES, "com.smartPark"})
@ServletComponentScan
public class DeviceTest {

    @Resource
    private DeviceService deviceService;

    @Autowired
    private DeviceUnitService deviceUnitService;

    @Autowired
    private ProjectSchemeService projectSchemeService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;

    @Test
    public void syncDevice(){
        Device device = new Device();
        device.setCode("0000202302211529");
        deviceService.addDevice(device);
    }

    @Test
    public void getDeviceStatus(){
        Device device = new Device();
        device.setCode("004A77012400A8CB");
        List<DevicePropertyStatus> devicePropertyStatus = deviceService.getDevicePropertyStatus(device);
        System.out.println(devicePropertyStatus);
    }

    @Test
    public void testRedis(){
//        Device device = deviceService.getDeviceByCode("0000202302211529");
//        redisTemplate.opsForHash().put("device:","0000202302211529",device);
        Device device1 = (Device) redisTemplate.opsForHash().get("device:", "0000202302211529");
        System.out.println(device1);
    }


    @Test
    public void testEventRecieve() throws InterruptedException {
        MessageListenerAdapter receiveMessage = new MessageListenerAdapter(this, "receiveMessageForSyn");
        receiveMessage.afterPropertiesSet();
//        redisMessageListenerContainer.addMessageListener(
//                receiveMessage,
//                new PatternTopic(RedisConstant.DELETE_CACHE_EVENT_TOPIC));
//        deviceService.publishEvent(RedisConstant.DEVICE_CACHE_KEY,"0000202302211529");
        Thread.sleep(Integer.MAX_VALUE);
    }

    public void receiveMessageForSyn(String message){
        CacheEventSource source = JSON.parseObject(message, CacheEventSource.class);
        System.out.println(source);
    }

    @Test
    public void deviceUpdate(){
        Device device = new Device();
        device.setCode("0000202302211529");
        deviceService.updateDevice(device);
    }

    @Test
    public void deviceUnitUpdate(){
        DeviceUnit deviceUnit = new DeviceUnit();
        deviceUnit.setCode("DXDB-470AA1-01-0051");
        deviceUnitService.updateDeviceUnit(deviceUnit);
    }

    @Test
    public void projectSchemeUpdate(){
        ProjectScheme projectScheme = new ProjectScheme();
        projectScheme.setProjectId(33l);
        projectSchemeService.updateProjectSchemes(projectScheme);
    }


}
