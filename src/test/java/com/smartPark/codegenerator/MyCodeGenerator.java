package com.smartPark.codegenerator;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.smartPark.common.utils.YamlUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2020/4/11 21:01
 */
public class MyCodeGenerator {

    //  修改作者
    private static String author = "zhengwen";
    //    实体类名称
    //private static String entityName = "DeployCar";
    private static String entityName = Config.MODEL_NAME;
    // 该模块包名  controller entity 等都在其下
    private static String moduleName = Config.MODULE_PACKAGE.replace(".", "");
    //    表名
    private static String tableName = Config.TABLE_NAME;
    //    parentPath 一般不需要修改
    private static String parentPath = Config.PARENT_PACKAGE;

    private static String projectPackage = "/sanzhi-safe-service";


    //    配置文件
    private static String resourcePath = "application-dev.yml";
    //    数据库信息配置
    private static String dataSourceUrl = (String) YamlUtils.get(resourcePath, "spring.datasource.druid.url");
    private static String dataSourceUsername = (String) YamlUtils.get(resourcePath, "spring.datasource.druid.username");
    private static String dataSourcePassword = (String) YamlUtils.get(resourcePath, "spring.datasource.druid.password");


    /**
     * RUN THIS
     */
    public static void main(String[] args) {

//        数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setUrl(dataSourceUrl);
        dataSourceConfig.setDriverName("com.mysql.cj.jdbc.Driver");
        dataSourceConfig.setUsername(dataSourceUsername);
        //dataSourceConfig.setPassword(dataSourcePassword);
        dataSourceConfig.setPassword("jcZuC0jj23fB4DWE3Bnu");
        dataSourceConfig.setDbType(DbType.MYSQL);
//        dataSourceConfig.setSchemaName("SchemaName");

        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

//        设置数据源信息
        mpg.setDataSource(dataSourceConfig);

        // 全局配置
        GlobalConfig globalConfig = new GlobalConfig();
        String projectPath = System.getProperty("user.dir");
        String modulePath = projectPath + projectPackage;
        globalConfig.setOutputDir(modulePath + "/src/main/java");
        globalConfig.setAuthor(author);
        globalConfig.setOpen(true);
        globalConfig.setEntityName(entityName);
        // service 命名方式
        globalConfig.setServiceName(entityName + "Service");
        globalConfig.setControllerName(entityName + "Controller");
        // service impl 命名方式
        globalConfig.setServiceImplName(entityName + "ServiceImpl");
        // 自定义文件命名，注意 %s 会自动填充表实体属性！
        globalConfig.setMapperName(globalConfig.getEntityName() + "Mapper");
        globalConfig.setXmlName(globalConfig.getEntityName() + "Mapper");
        globalConfig.setFileOverride(true);
        globalConfig.setActiveRecord(true);
        // XML 二级缓存
        globalConfig.setEnableCache(false);
        // XML ResultMap
        globalConfig.setBaseResultMap(true);
        // XML columList
        globalConfig.setBaseColumnList(false);
        mpg.setGlobalConfig(globalConfig);

        PackageConfig packageConfig = new PackageConfig();
        //pc.setModuleName(scanner("模块名"));
        packageConfig.setParent(parentPath);
        packageConfig.setEntity("entity");
        packageConfig.setService("service");
        packageConfig.setServiceImpl("service.impl");
        packageConfig.setMapper("mapper");
        packageConfig.setModuleName(moduleName);
        mpg.setPackageInfo(packageConfig);

        //自定义需要填充的字段
//        List<TableFill> tableFillList = new ArrayList<>();
        //如 每张表都有一个创建时间、修改时间
        //而且这基本上就是通用的了，新增时，创建时间和修改时间同时修改
        //修改时，修改时间会修改，
        //虽然像Mysql数据库有自动更新几只，但像ORACLE的数据库就没有了，
        //使用公共字段填充功能，就可以实现，自动按场景更新了。
        //如下是配置
//        TableFill createField = new TableFill("gmt_create", FieldFill.INSERT);
//        TableFill modifiedField = new TableFill("gmt_modified", FieldFill.INSERT_UPDATE);
//        tableFillList.add(createField);
//        tableFillList.add(modifiedField);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };
        List<FileOutConfig> focList = new ArrayList<>();
        focList.add(new FileOutConfig("/templates/mapper.xml.ftl") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输入文件名称
                return modulePath + "/src/main/resources/mapper/"
                    + moduleName + "/" + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);
        mpg.setTemplate(new TemplateConfig().setXml(null));

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        strategy.setEntityLombokModel(true);
        strategy.setEntityTableFieldAnnotationEnable(true);
        // 设置逻辑删除键
        strategy.setLogicDeleteFieldName("deleted");
        // 指定生成的bean的数据库表名`
        strategy.setInclude(tableName);
        //strategy.setSuperEntityColumns("id");
        // 驼峰转连字符
        strategy.setControllerMappingHyphenStyle(false);
        strategy.setRestControllerStyle(true);
        mpg.setStrategy(strategy);
        // 选择 freemarker 引擎需要指定如下加，注意 pom 依赖必须有！
        mpg.setTemplateEngine(new FreemarkerTemplateEngine());
        mpg.execute();
    }

}

